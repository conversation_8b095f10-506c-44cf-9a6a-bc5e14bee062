import { p as push, V as copy_payload, W as assign_payload, q as pop, O as escape_html, M as ensure_array_like, J as attr_class, P as stringify } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { T as Table, a as Table_header, c as Table_row, d as Table_head, b as Table_body, e as Table_cell } from './table-row-CyhLzMgE.js';
import { R as Root, D as Dialog_content } from './index7-BURUpWjT.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { C as Clock } from './clock-BHOPwoCS.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { E as Eye } from './eye-B2tdw2__.js';
import { R as Rotate_cw } from './rotate-cw-CWqzplUz.js';
import { h as html } from './html-FW6Ia4bL.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './index2-Cut0V_vU.js';
import './dialog-description2-rfr-pd9k.js';

function _page($$payload, $$props) {
  push();
  let isLoading = true;
  let isRetrying = false;
  let queueStats = {
    waiting: 0,
    processing: 0,
    completed: 0,
    failed: 0
  };
  let recentJobs = [];
  let selectedEmail = null;
  let emailContent = { html: "", text: "", subject: "" };
  let showEmailDialog = false;
  async function loadQueueStatus() {
    isLoading = true;
    try {
      const response = await fetch("/api/email/queue-status");
      if (response.ok) {
        const data = await response.json();
        queueStats = data.queue;
        recentJobs = data.recentJobs;
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to load queue status");
      }
    } catch (error) {
      console.error("Error loading queue status:", error);
      toast.error("Failed to load queue status");
    } finally {
      isLoading = false;
    }
  }
  function formatDate(dateString) {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleString();
  }
  function getJobStatusBadgeClass(status) {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "waiting":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }
  async function processAllJobs() {
    try {
      const response = await fetch("/api/email/process-queue", { method: "POST" });
      if (response.ok) {
        toast.success("Processing all jobs");
        await loadQueueStatus();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to process jobs");
      }
    } catch (error) {
      console.error("Error processing jobs:", error);
      toast.error("Failed to process jobs");
    }
  }
  async function clearFailedJobs() {
    try {
      const response = await fetch("/api/email/clear-failed", { method: "POST" });
      if (response.ok) {
        toast.success("Failed jobs cleared");
        await loadQueueStatus();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to clear failed jobs");
      }
    } catch (error) {
      console.error("Error clearing failed jobs:", error);
      toast.error("Failed to clear failed jobs");
    }
  }
  async function viewEmailContent(email) {
    selectedEmail = email;
    showEmailDialog = true;
    try {
      const response = await fetch(`/api/email/view?id=${email.id}`);
      if (response.ok) {
        emailContent = await response.json();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to load email content");
      }
    } catch (error) {
      console.error("Error loading email content:", error);
      toast.error("Failed to load email content");
    }
  }
  async function retryFailedEmail(email) {
    if (!email || !email.id) return;
    isRetrying = true;
    try {
      const response = await fetch("/api/email/retry-failed", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: email.id })
      });
      if (response.ok) {
        toast.success("Email has been requeued");
        await loadQueueStatus();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to retry email");
      }
    } catch (error) {
      console.error("Error retrying email:", error);
      toast.error("Failed to retry email");
    } finally {
      isRetrying = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="space-y-6"><div class="flex items-center justify-between"><h2 class="text-3xl font-bold tracking-tight">Email Queue</h2> <!---->`;
    Button($$payload2, {
      variant: "outline",
      size: "sm",
      onclick: loadQueueStatus,
      disabled: isLoading,
      children: ($$payload3) => {
        if (isLoading) {
          $$payload3.out += "<!--[-->";
          $$payload3.out += `<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>`;
        } else {
          $$payload3.out += "<!--[!-->";
          Refresh_cw($$payload3, { class: "mr-2 h-4 w-4" });
        }
        $$payload3.out += `<!--]--> Refresh`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"><!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_content($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Waiting</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(queueStats.waiting)}</h3></div> <div class="rounded-full bg-yellow-100 p-2">`;
            Clock($$payload4, { class: "h-5 w-5 text-yellow-600" });
            $$payload4.out += `<!----></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_content($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Processing</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(queueStats.processing)}</h3></div> <div class="rounded-full bg-blue-100 p-2">`;
            Refresh_cw($$payload4, { class: "h-5 w-5 text-blue-600" });
            $$payload4.out += `<!----></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_content($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Completed</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(queueStats.completed)}</h3></div> <div class="rounded-full bg-green-100 p-2">`;
            Circle_check_big($$payload4, { class: "h-5 w-5 text-green-600" });
            $$payload4.out += `<!----></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_content($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Failed</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(queueStats.failed)}</h3></div> <div class="rounded-full bg-red-100 p-2">`;
            Circle_x($$payload4, { class: "h-5 w-5 text-red-600" });
            $$payload4.out += `<!----></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <div class="flex flex-wrap gap-4"><!---->`;
    Button($$payload2, {
      variant: "default",
      onclick: processAllJobs,
      children: ($$payload3) => {
        Refresh_cw($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Process All Jobs`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Button($$payload2, {
      variant: "destructive",
      onclick: clearFailedJobs,
      disabled: queueStats.failed === 0,
      children: ($$payload3) => {
        Triangle_alert($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Clear Failed Jobs`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Recent Jobs`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Most recent jobs in the email queue`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            if (isLoading) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex h-40 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
            } else if (recentJobs.length === 0) {
              $$payload4.out += "<!--[1-->";
              $$payload4.out += `<div class="text-muted-foreground flex h-40 items-center justify-center"><p>No jobs in the queue</p></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `<!---->`;
              Table($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Table_header($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Table_row($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->`;
                          Table_head($$payload7, {
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->ID`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> <!---->`;
                          Table_head($$payload7, {
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->Type`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> <!---->`;
                          Table_head($$payload7, {
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->Recipient`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> <!---->`;
                          Table_head($$payload7, {
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->Status`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> <!---->`;
                          Table_head($$payload7, {
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->Created At`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> <!---->`;
                          Table_head($$payload7, {
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->Actions`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!---->`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Table_body($$payload5, {
                    children: ($$payload6) => {
                      const each_array = ensure_array_like(recentJobs);
                      $$payload6.out += `<!--[-->`;
                      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                        let job = each_array[$$index];
                        $$payload6.out += `<!---->`;
                        Table_row($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            Table_cell($$payload7, {
                              class: "font-mono text-xs",
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(job.id || "-")}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Table_cell($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(job.type || "-")}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Table_cell($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(job.to || job.email || "-")}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Table_cell($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<span${attr_class(`rounded-full px-2 py-1 text-xs ${stringify(getJobStatusBadgeClass(job.status || "waiting"))}`)}>${escape_html(job.status || "waiting")}</span>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Table_cell($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(formatDate(job.createdAt))}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Table_cell($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<div class="flex space-x-2"><!---->`;
                                Button($$payload8, {
                                  variant: "outline",
                                  size: "sm",
                                  onclick: () => viewEmailContent(job),
                                  children: ($$payload9) => {
                                    Eye($$payload9, { class: "h-4 w-4" });
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out += `<!----> `;
                                if (job.status === "failed") {
                                  $$payload8.out += "<!--[-->";
                                  $$payload8.out += `<!---->`;
                                  Button($$payload8, {
                                    variant: "outline",
                                    size: "sm",
                                    onclick: () => retryFailedEmail(job),
                                    disabled: isRetrying,
                                    children: ($$payload9) => {
                                      Rotate_cw($$payload9, { class: "h-4 w-4" });
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload8.out += `<!---->`;
                                } else {
                                  $$payload8.out += "<!--[!-->";
                                }
                                $$payload8.out += `<!--]--></div>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <!---->`;
    Root($$payload2, {
      get open() {
        return showEmailDialog;
      },
      set open($$value) {
        showEmailDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_content($$payload3, {
          class: "max-w-3xl",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Email Content`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html(selectedEmail?.type || "Email")} to ${escape_html(selectedEmail?.to || selectedEmail?.email || "recipient")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="mb-4"><div class="flex border-b"><button${attr_class(`px-4 py-2 ${"border-primary border-b-2 font-medium"}`)}>HTML</button> <button${attr_class(`px-4 py-2 ${""}`)}>Text</button> <button${attr_class(`px-4 py-2 ${""}`)}>JSON</button></div> <div class="mt-4">`;
            {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="h-96 overflow-auto rounded-md border p-4">`;
              if (emailContent.html) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div class="prose prose-sm max-w-none">${html(emailContent.html)}</div>`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `<p class="text-muted-foreground">No HTML content available</p>`;
              }
              $$payload4.out += `<!--]--></div>`;
            }
            $$payload4.out += `<!--]--></div></div> <!---->`;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => showEmailDialog = false,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Close`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                if (selectedEmail?.status === "failed") {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<!---->`;
                  Button($$payload5, {
                    onclick: () => retryFailedEmail(selectedEmail),
                    disabled: isRetrying,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Retry Email`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DeFp2NWv.js.map
