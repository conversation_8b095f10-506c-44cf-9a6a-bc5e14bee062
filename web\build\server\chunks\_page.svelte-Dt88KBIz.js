import { p as push, M as ensure_array_like, N as attr, O as escape_html, Q as bind_props, q as pop, P as stringify } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { H as HelpSearch } from './HelpSearch-8uDSfRza.js';
import { H as HelpArticleCard, B as Book_open } from './HelpArticleCard-CzuyIqVY.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { C as Credit_card } from './credit-card-8KNeZIt3.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { C as Circle_help } from './circle-help-Bsq6Onfx.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { V as Video } from './video-CIe5QbIt.js';
import { L as Lightbulb } from './lightbulb-CGr-7VNq.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import { M as Mail } from './mail-Brqxil2x.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './client-dNyMPa8V.js';
import './search-input-CbGkN9s9.js';
import './index-server-CezSOnuG.js';
import './search-B0oHlTPS.js';
import './Icon-A4vzmk-O.js';
import './index14-C2WSwUih.js';
import './scroll-lock-BkBz2nVp.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './badge-C9pSznab.js';

function HelpCategoryCard($$payload, $$props) {
  push();
  let { category, className = "" } = $$props;
  $$payload.out += `<!---->`;
  Card($$payload, {
    class: `h-full ${stringify(className)}`,
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="mb-2 flex items-center gap-3"><div class="bg-primary/10 text-primary rounded-full p-2">`;
          if (category.icon === "BookOpen") {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<!---->`;
            Book_open($$payload3, { class: "h-5 w-5" });
            $$payload3.out += `<!---->`;
          } else if (category.icon === "FileText") {
            $$payload3.out += "<!--[1-->";
            $$payload3.out += `<!---->`;
            File_text($$payload3, { class: "h-5 w-5" });
            $$payload3.out += `<!---->`;
          } else if (category.icon === "CreditCard") {
            $$payload3.out += "<!--[2-->";
            $$payload3.out += `<!---->`;
            Credit_card($$payload3, { class: "h-5 w-5" });
            $$payload3.out += `<!---->`;
          } else if (category.icon === "Shield") {
            $$payload3.out += "<!--[3-->";
            $$payload3.out += `<!---->`;
            Shield($$payload3, { class: "h-5 w-5" });
            $$payload3.out += `<!---->`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<!---->`;
            Circle_help($$payload3, { class: "h-5 w-5" });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]--></div> <!---->`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(category.name)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> `;
          if (category.description) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<!---->`;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(category.description)}`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_footer($$payload2, {
        class: "flex justify-between",
        children: ($$payload3) => {
          $$payload3.out += `<div class="text-muted-foreground text-sm">${escape_html(category.articleCount ?? 0)} article${escape_html(category.articleCount !== 1 ? "s" : "")}</div> <a${attr("href", `/help/category/${stringify(category.slug)}`)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Browse `;
          Arrow_right($$payload3, { class: "ml-1 h-4 w-4" });
          $$payload3.out += `<!----></a>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const supportOptions = [
    {
      icon: Message_square,
      title: "Live Chat",
      description: "Chat with our support team in real-time during business hours.",
      action: "Start Chat",
      link: "#chat"
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us an email and we'll respond within 24 hours.",
      action: "Email Us",
      link: "mailto:<EMAIL>"
    }
  ];
  const videoTutorials = [
    {
      title: "Getting Started with Hirli",
      thumbnail: "/images/tutorials/getting-started.jpg",
      duration: "5:32",
      link: "/tutorials/getting-started"
    },
    {
      title: "Creating an Effective Resume",
      thumbnail: "/images/tutorials/resume-creation.jpg",
      duration: "8:45",
      link: "/tutorials/resume-creation"
    },
    {
      title: "Optimizing Your Job Search",
      thumbnail: "/images/tutorials/job-search.jpg",
      duration: "6:18",
      link: "/tutorials/job-search"
    },
    {
      title: "Using Auto Apply Effectively",
      thumbnail: "/images/tutorials/auto-apply.jpg",
      duration: "7:22",
      link: "/tutorials/auto-apply"
    }
  ];
  const each_array = ensure_array_like(data.categories);
  const each_array_1 = ensure_array_like(data.featuredArticles);
  const each_array_2 = ensure_array_like(data.recentArticles);
  const each_array_3 = ensure_array_like(videoTutorials);
  const each_array_4 = ensure_array_like(supportOptions);
  SEO($$payload, {
    title: "Help Center | Hirli",
    description: "Find answers, guides, and support for using Hirli's job application automation platform. Get help with your account, resume building, and job applications.",
    keywords: "help center, support, tutorials, guides, FAQ, job application help, Hirli support"
  });
  $$payload.out += `<!----> <div class="container mx-auto px-4 py-16"><div class="mx-auto max-w-6xl"><div class="mb-12 text-center"><h1 class="mb-4 text-4xl font-bold">Help Center</h1> <p class="text-muted-foreground mx-auto max-w-2xl text-lg">Find answers to your questions and learn how to make the most of Hirli's features.</p> <div class="mx-auto mt-8 max-w-2xl">`;
  HelpSearch($$payload, { className: "w-full" });
  $$payload.out += `<!----></div></div> <div class="mb-16"><h2 class="mb-8 text-2xl font-semibold">Help Categories</h2> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let category = each_array[$$index];
    HelpCategoryCard($$payload, { category });
  }
  $$payload.out += `<!--]--></div></div> <div class="mb-16"><h2 class="mb-8 text-2xl font-semibold">Featured Articles</h2> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let article = each_array_1[$$index_1];
    HelpArticleCard($$payload, { article });
  }
  $$payload.out += `<!--]--></div></div> <div class="mb-16"><h2 class="mb-8 text-2xl font-semibold">Recently Updated</h2> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let article = each_array_2[$$index_2];
    HelpArticleCard($$payload, { article });
  }
  $$payload.out += `<!--]--></div></div> <div class="mb-16"><div class="mb-8 flex items-center justify-between"><h2 class="text-2xl font-semibold">Video Tutorials</h2> <a href="/tutorials" class="text-primary inline-flex items-center text-sm font-medium hover:underline">View all tutorials `;
  Arrow_right($$payload, { class: "ml-1 h-4 w-4" });
  $$payload.out += `<!----></a></div> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
    let video = each_array_3[$$index_3];
    $$payload.out += `<a${attr("href", video.link)} class="group"><div class="bg-muted relative mb-3 aspect-video overflow-hidden rounded-lg"><div class="absolute inset-0 flex items-center justify-center">`;
    Video($$payload, {
      class: "h-12 w-12 text-white opacity-80 transition-opacity group-hover:opacity-100"
    });
    $$payload.out += `<!----></div> <div class="absolute bottom-2 right-2 rounded bg-black/70 px-2 py-1 text-xs text-white">${escape_html(video.duration)}</div></div> <h3 class="group-hover:text-primary font-medium transition-colors">${escape_html(video.title)}</h3></a>`;
  }
  $$payload.out += `<!--]--></div></div> <div class="mb-16"><h2 class="mb-8 text-2xl font-semibold">Need More Help?</h2> <div class="grid gap-6 md:grid-cols-3"><!--[-->`;
  for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
    let option = each_array_4[$$index_4];
    Card($$payload, {
      children: ($$payload2) => {
        Card_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<div class="bg-primary/10 mb-2 flex h-10 w-10 items-center justify-center rounded-full"><!---->`;
            option.icon?.($$payload3, { class: "text-primary h-5 w-5" });
            $$payload3.out += `<!----></div> `;
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(option.title)}`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(option.description)}`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Card_footer($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<a${attr("href", option.link)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">${escape_html(option.action)} `;
            Arrow_right($$payload3, { class: "ml-1 h-4 w-4" });
            $$payload3.out += `<!----></a>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  $$payload.out += `<!--]--></div></div> <div class="bg-muted/50 rounded-lg border p-8"><div class="flex flex-col gap-6 md:flex-row md:items-center"><div class="md:flex-1"><h2 class="mb-2 text-2xl font-semibold">Explore Our Documentation</h2> <p class="text-muted-foreground mb-4">For more detailed information, check out our comprehensive documentation with
            step-by-step guides and advanced tips.</p> <div class="flex flex-wrap gap-3"><a href="/faq" class="text-primary inline-flex items-center text-sm font-medium hover:underline">`;
  Circle_help($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Frequently Asked Questions</a> <a href="/docs" class="text-primary inline-flex items-center text-sm font-medium hover:underline">`;
  File_text($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Documentation</a> <a href="/blog" class="text-primary inline-flex items-center text-sm font-medium hover:underline">`;
  Lightbulb($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Blog &amp; Tips</a></div></div> <div>`;
  Button($$payload, {
    variant: "default",
    class: "w-full md:w-auto",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Contact Support Team`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Dt88KBIz.js.map
