import { p as push, V as copy_payload, W as assign_payload, q as pop, M as ensure_array_like, aa as maybe_selected, N as attr, O as escape_html } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { B as Button } from './button-CrucCo1G.js';
import { B as Badge } from './badge-C9pSznab.js';
import { I as Input } from './input-DF0gPqYN.js';
import { P as Progress } from './progress-DR0SfStT.js';
import { R as Root, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as <PERSON><PERSON>_dialog_footer, f as Alert_dialog_cancel, g as <PERSON><PERSON>_dialog_action } from './index11-Dmn3AdIN.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { g as goto } from './client-dNyMPa8V.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { p as parseProfileData, m as migrateProfileData, c as calculateProfileCompletion } from './profileHelpers-m3Uw-RPd.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { S as Search } from './search-B0oHlTPS.js';
import { U as Users } from './users-e7-Uhkka.js';
import { U as User } from './user-DpDpidvb.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { S as Star } from './star-DbHO3Z_L.js';
import { S as Square_pen } from './square-pen-DCE_ltl5.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './dialog-overlay-CspOQRJq.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './dialog-description2-rfr-pd9k.js';
import './index2-Cut0V_vU.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  let profiles = [...data.profiles];
  let errorMessage = "";
  let showLimitError = false;
  let limitErrorMessage = "";
  let deleteDialogOpen = false;
  let profileToDelete = null;
  let isDeleting = false;
  let searchQuery = data.filters.search;
  let selectedJobType = data.filters.jobType;
  let selectedIndustry = data.filters.industry;
  let selectedOwner = data.filters.owner;
  let isLoading = false;
  let isCreatingProfile = false;
  let profileLimitReached = false;
  let profileLimit = 5;
  let currentProfileCount = profiles.length;
  let searchTimeout;
  function applyFilters() {
    if (isLoading) return;
    isLoading = true;
    const params = new URLSearchParams();
    if (searchQuery) params.set("search", searchQuery);
    if (selectedJobType) params.set("jobType", selectedJobType);
    if (selectedIndustry) params.set("industry", selectedIndustry);
    if (selectedOwner && selectedOwner !== "all") params.set("owner", selectedOwner);
    params.set("page", "1");
    const url = params.toString() ? `?${params.toString()}` : "";
    goto(url, {}).finally(() => {
      isLoading = false;
    });
  }
  function handleSearchInput() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(
      () => {
        applyFilters();
      },
      300
    );
  }
  function goToPage(page) {
    if (isLoading) return;
    const params = new URLSearchParams(window.location.search);
    params.set("page", page.toString());
    goto(`?${params.toString()}`, {});
  }
  async function navigateToProfile() {
    if (isCreatingProfile) return;
    try {
      isCreatingProfile = true;
      errorMessage = "";
      showLimitError = false;
      limitErrorMessage = "";
      console.log("Creating new profile...");
      const response = await fetch("/api/profiles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: "New Profile" })
      });
      console.log("API response status:", response.status);
      const result = await response.json();
      console.log("API response data:", result);
      if (!response.ok) {
        if (response.status === 403 && result.limitReached) {
          console.log("Profile limit reached:", result.error);
          profileLimitReached = true;
          currentProfileCount = result.currentCount || currentProfileCount;
          profileLimit = result.limit || profileLimit;
          showLimitError = true;
          limitErrorMessage = result.error;
          toast.error(result.error);
          return;
        }
        errorMessage = result.error || "Failed to create profile";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
      if (result.profileId) {
        console.log(`Profile created successfully: ${result.profileId}`);
        if (result.profile) {
          profiles = [
            ...profiles,
            {
              ...result.profile,
              data: null,
              team: null,
              user: data.user,
              defaultDocument: null
            }
          ];
        }
        toast.success("Profile created successfully!");
        goto(`/dashboard/settings/profile/${result.profileId}`);
      } else {
        errorMessage = result.error || "No profile ID returned";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error creating profile:", error);
      errorMessage = error instanceof Error ? error.message : "Unknown error";
      toast.error(errorMessage);
    } finally {
      isCreatingProfile = false;
    }
  }
  function navigateToUpgrade() {
    goto();
  }
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  function openDeleteDialog(profile) {
    profileToDelete = profile;
    deleteDialogOpen = true;
  }
  async function deleteProfile() {
    if (!profileToDelete) return;
    try {
      isDeleting = true;
      const response = await fetch(`/api/profile/${profileToDelete.id}`, { method: "DELETE" });
      if (!response.ok) {
        let errorMessage2 = "Failed to delete profile";
        let errorDetails = "";
        try {
          const errorData = await response.json();
          errorMessage2 = errorData.error || errorMessage2;
          errorDetails = errorData.details || "";
        } catch (e) {
          errorMessage2 = response.statusText || errorMessage2;
        }
        console.error("Profile deletion failed:", {
          status: response.status,
          message: errorMessage2,
          details: errorDetails,
          profileId: profileToDelete.id
        });
        throw new Error(errorMessage2);
      }
      profiles = profiles.filter((p) => p.id !== profileToDelete.id);
      toast.success("Profile deleted successfully");
      deleteDialogOpen = false;
      profileToDelete = null;
    } catch (error) {
      console.error("Error deleting profile:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete profile");
    } finally {
      isDeleting = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Profiles - Hirli",
      description: "Create and manage your resume profiles for different job types and industries. Organize your job applications with customized profiles.",
      keywords: "resume profiles, job applications, career profiles, job search, resume management",
      url: "https://hirli.com/dashboard/settings/profile"
    });
    $$payload2.out += `<!----> <div class="border-border flex flex-col gap-6 border-b p-6"><div class="flex items-center justify-between"><div><h2 class="text-2xl font-bold">Profiles</h2> <p class="text-muted-foreground">Create and manage your profiles for job applications, automations, and more.</p></div> `;
    if (profileLimitReached) {
      $$payload2.out += "<!--[-->";
      Button($$payload2, {
        onclick: navigateToUpgrade,
        variant: "default",
        class: "gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",
        children: ($$payload3) => {
          Plus($$payload3, { class: "h-4 w-4" });
          $$payload3.out += `<!----> Upgrade Plan`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
      Button($$payload2, {
        onclick: navigateToProfile,
        disabled: isCreatingProfile,
        class: "gap-2",
        children: ($$payload3) => {
          if (isCreatingProfile) {
            $$payload3.out += "<!--[-->";
            Loader_circle($$payload3, { class: "h-4 w-4 animate-spin" });
            $$payload3.out += `<!----> Creating...`;
          } else {
            $$payload3.out += "<!--[!-->";
            Plus($$payload3, { class: "h-4 w-4" });
            $$payload3.out += `<!----> Create New Profile`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    }
    $$payload2.out += `<!--]--></div> <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between"><div class="flex flex-1 gap-3"><div class="relative max-w-sm flex-1">`;
    Search($$payload2, {
      class: "text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
    });
    $$payload2.out += `<!----> `;
    Input($$payload2, {
      placeholder: "Search profiles...",
      oninput: handleSearchInput,
      class: "pl-9",
      get value() {
        return searchQuery;
      },
      set value($$value) {
        searchQuery = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----></div> `;
    if (data.filters.jobTypes.length > 0) {
      $$payload2.out += "<!--[-->";
      const each_array = ensure_array_like(data.filters.jobTypes);
      $$payload2.out += `<select class="border-input bg-background ring-offset-background h-9 w-[180px] rounded-md border px-3 py-1 text-sm">`;
      $$payload2.select_value = selectedJobType;
      $$payload2.out += `<option value=""${maybe_selected($$payload2, "")}>All Job Types</option><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let jobType = each_array[$$index];
        $$payload2.out += `<option${attr("value", jobType)}${maybe_selected($$payload2, jobType)}>${escape_html(jobType)}</option>`;
      }
      $$payload2.out += `<!--]-->`;
      $$payload2.select_value = void 0;
      $$payload2.out += `</select>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    if (data.filters.industries.length > 0) {
      $$payload2.out += "<!--[-->";
      const each_array_1 = ensure_array_like(data.filters.industries);
      $$payload2.out += `<select class="border-input bg-background ring-offset-background h-9 w-[180px] rounded-md border px-3 py-1 text-sm">`;
      $$payload2.select_value = selectedIndustry;
      $$payload2.out += `<option value=""${maybe_selected($$payload2, "")}>All Industries</option><!--[-->`;
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        let industry = each_array_1[$$index_1];
        $$payload2.out += `<option${attr("value", industry)}${maybe_selected($$payload2, industry)}>${escape_html(industry)}</option>`;
      }
      $$payload2.out += `<!--]-->`;
      $$payload2.select_value = void 0;
      $$payload2.out += `</select>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    if (data.hasTeamAccess) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<select class="border-input bg-background ring-offset-background h-9 w-[150px] rounded-md border px-3 py-1 text-sm">`;
      $$payload2.select_value = selectedOwner;
      $$payload2.out += `<option value="all"${maybe_selected($$payload2, "all")}>All Profiles</option><option value="user"${maybe_selected($$payload2, "user")}>My Profiles</option><option value="team"${maybe_selected($$payload2, "team")}>Team Profiles</option>`;
      $$payload2.select_value = void 0;
      $$payload2.out += `</select>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> `;
    Badge($$payload2, {
      variant: "secondary",
      class: "text-sm",
      children: ($$payload3) => {
        $$payload3.out += `<!---->${escape_html(profiles.length)} profile${escape_html(profiles.length !== 1 ? "s" : "")}`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <div class="p-6">`;
    if (showLimitError || profileLimitReached) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="mb-6 rounded-lg border border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 p-6 shadow-sm"><div class="flex items-start gap-4"><div class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 text-orange-600"><svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg></div> <div class="flex-1"><h3 class="mb-2 text-lg font-semibold text-orange-900">Profile Limit Reached</h3> <p class="mb-4 text-orange-800">${escape_html(limitErrorMessage || `You've reached your profile limit of ${profileLimit}. Upgrade your plan to create more profiles and unlock additional features.`)}</p> <div class="flex gap-3">`;
      Button($$payload2, {
        onclick: navigateToUpgrade,
        class: "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Upgrade Plan`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Button($$payload2, {
        variant: "outline",
        onclick: () => {
          showLimitError = false;
          profileLimitReached = false;
        },
        class: "border-orange-300 text-orange-700 hover:bg-orange-100",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Dismiss`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div></div></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    if (profiles && profiles.length > 0) {
      $$payload2.out += "<!--[-->";
      const each_array_2 = ensure_array_like(profiles);
      $$payload2.out += `<div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3"><!--[-->`;
      for (let $$index_3 = 0, $$length = each_array_2.length; $$index_3 < $$length; $$index_3++) {
        let profile = each_array_2[$$index_3];
        const parsedData = profile.data?.data ? parseProfileData(profile.data.data) : {};
        const profileData = migrateProfileData(parsedData);
        const completion = calculateProfileCompletion(profileData);
        const isTeamProfile = profile.team && profile.userId !== data.user.id;
        $$payload2.out += `<!---->`;
        Card($$payload2, {
          class: "group relative gap-0 overflow-hidden p-0 transition-all hover:shadow-md",
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card_header($$payload3, {
              class: "border-border flex items-start justify-between border-b !p-4",
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex items-center gap-3"><div class="bg-primary/20 text-primary flex h-12 w-12 items-center justify-center rounded-full">`;
                if (isTeamProfile) {
                  $$payload4.out += "<!--[-->";
                  Users($$payload4, { class: "h-6 w-6" });
                } else {
                  $$payload4.out += "<!--[!-->";
                  User($$payload4, { class: "h-6 w-6" });
                }
                $$payload4.out += `<!--]--></div> <div><h3 class="text-lg font-semibold leading-tight">${escape_html(profile.name)}</h3> `;
                if (isTeamProfile) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<p class="text-muted-foreground text-sm">Team: ${escape_html(profile.team.name)}</p>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  $$payload4.out += `<p class="text-muted-foreground text-sm">Personal Profile</p>`;
                }
                $$payload4.out += `<!--]--></div></div> `;
                if (profileData.jobType) {
                  $$payload4.out += "<!--[-->";
                  Badge($$payload4, {
                    variant: "secondary",
                    class: "bg-primary/10 text-primary border-primary/20",
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(profileData.jobType)}`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_content($$payload3, {
              class: "p-0",
              children: ($$payload4) => {
                Progress($$payload4, {
                  value: completion,
                  class: "h-2 w-full rounded-none"
                });
                $$payload4.out += `<!----> <div class="mt-1 flex items-center justify-between px-4 text-sm"><span class="text-muted-foreground">Profile Completion</span> <span class="font-medium">${escape_html(completion)}%</span></div> <div class="p-4"><div class="mb-4 space-y-3">`;
                if (profileData.personalInfo?.fullName || profileData.fullName) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="flex items-center gap-3"><div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full">`;
                  User($$payload4, { class: "h-4 w-4" });
                  $$payload4.out += `<!----></div> <div class="flex-1"><p class="font-medium">${escape_html(profileData.personalInfo?.fullName || profileData.fullName)}</p> `;
                  if (profileData.personalInfo?.location || profileData.location) {
                    $$payload4.out += "<!--[-->";
                    $$payload4.out += `<p class="text-muted-foreground flex items-center gap-1 text-sm">`;
                    Map_pin($$payload4, { class: "h-3 w-3" });
                    $$payload4.out += `<!----> ${escape_html(profileData.personalInfo?.location || profileData.location)}</p>`;
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--></div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (profileData.industry) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="flex items-center gap-3"><div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full">`;
                  Briefcase($$payload4, { class: "h-4 w-4" });
                  $$payload4.out += `<!----></div> <span class="font-medium">${escape_html(profileData.industry)}</span></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (profile.defaultDocument) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="flex items-center gap-3"><div class="bg-muted flex h-8 w-8 items-center justify-center rounded-full">`;
                  File_text($$payload4, { class: "h-4 w-4" });
                  $$payload4.out += `<!----></div> <span class="font-medium">${escape_html(profile.defaultDocument.label)}</span></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> `;
                if (profileData.skillsData?.list && profileData.skillsData.list.length > 0) {
                  $$payload4.out += "<!--[-->";
                  const each_array_3 = ensure_array_like(profileData.skillsData.list.slice(0, 4));
                  $$payload4.out += `<div class="mb-4"><h4 class="text-muted-foreground mb-2 text-sm font-medium">Top Skills</h4> <div class="flex flex-wrap gap-1"><!--[-->`;
                  for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {
                    let skill = each_array_3[$$index_2];
                    Badge($$payload4, {
                      variant: "outline",
                      class: "bg-primary/5 text-xs",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(skill)}`;
                      },
                      $$slots: { default: true }
                    });
                  }
                  $$payload4.out += `<!--]--> `;
                  if (profileData.skillsData.list.length > 4) {
                    $$payload4.out += "<!--[-->";
                    Badge($$payload4, {
                      variant: "outline",
                      class: "bg-muted text-xs",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->+${escape_html(profileData.skillsData.list.length - 4)}`;
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--></div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (profileData.jobPreferences) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="mb-4"><h4 class="text-muted-foreground mb-2 text-sm font-medium">Preferences</h4> <div class="flex flex-wrap gap-1">`;
                  if (profileData.jobPreferences.jobSearchStatus) {
                    $$payload4.out += "<!--[-->";
                    Badge($$payload4, {
                      variant: "outline",
                      class: "text-xs",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(profileData.jobPreferences.jobSearchStatus)}`;
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--> `;
                  if (profileData.jobPreferences.remotePreference) {
                    $$payload4.out += "<!--[-->";
                    Badge($$payload4, {
                      variant: "outline",
                      class: "text-xs",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(profileData.jobPreferences.remotePreference)}`;
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--></div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> <div class="border-muted flex items-center justify-between border-t pt-4"><div class="text-muted-foreground flex items-center gap-1 text-xs">`;
                Clock($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!----> ${escape_html(formatDate(profile.updatedAt))}</div> <div class="flex items-center gap-1">`;
                Star($$payload4, { class: "text-primary h-4 w-4 fill-current" });
                $$payload4.out += `<!----> <span class="text-sm font-medium">${escape_html(completion)}%</span></div></div></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_footer($$payload3, {
              class: "bg-muted/50 flex gap-0 border-t !p-0",
              children: ($$payload4) => {
                Button($$payload4, {
                  variant: "ghost",
                  class: "flex-1 rounded-none",
                  onclick: () => goto(`/dashboard/settings/profile/${profile.id}`),
                  children: ($$payload5) => {
                    Square_pen($$payload5, { class: "mr-2 h-4 w-4" });
                    $$payload5.out += `<!----> Edit`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <div class="border-border border-r"></div> `;
                Button($$payload4, {
                  variant: "ghost",
                  class: "text-destructive hover:text-destructive hover:bg-destructive/10 flex-1 rounded-none",
                  onclick: () => openDeleteDialog(profile),
                  children: ($$payload5) => {
                    Trash_2($$payload5, { class: "mr-2 h-4 w-4" });
                    $$payload5.out += `<!----> Delete`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      }
      $$payload2.out += `<!--]--></div> `;
      if (data.pagination.totalPages > 1) {
        $$payload2.out += "<!--[-->";
        const each_array_4 = ensure_array_like(Array(Math.min(data.pagination.totalPages, 7)));
        $$payload2.out += `<div class="mt-8 flex items-center justify-center gap-2">`;
        Button($$payload2, {
          variant: "outline",
          size: "sm",
          disabled: !data.pagination.hasPrevPage || isLoading,
          onclick: () => goToPage(data.pagination.page - 1),
          class: "gap-1",
          children: ($$payload3) => {
            Chevron_left($$payload3, { class: "h-4 w-4" });
            $$payload3.out += `<!----> Previous`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <div class="flex items-center gap-1"><!--[-->`;
        for (let i = 0, $$length = each_array_4.length; i < $$length; i++) {
          each_array_4[i];
          const pageNum = data.pagination.totalPages <= 7 ? i + 1 : data.pagination.page <= 4 ? i + 1 : data.pagination.page >= data.pagination.totalPages - 3 ? data.pagination.totalPages - 6 + i : data.pagination.page - 3 + i;
          if (pageNum >= 1 && pageNum <= data.pagination.totalPages) {
            $$payload2.out += "<!--[-->";
            Button($$payload2, {
              variant: data.pagination.page === pageNum ? "default" : "outline",
              size: "sm",
              onclick: () => goToPage(pageNum),
              disabled: isLoading,
              class: "h-8 w-8 p-0",
              children: ($$payload3) => {
                $$payload3.out += `<!---->${escape_html(pageNum)}`;
              },
              $$slots: { default: true }
            });
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        }
        $$payload2.out += `<!--]--></div> `;
        Button($$payload2, {
          variant: "outline",
          size: "sm",
          disabled: !data.pagination.hasNextPage || isLoading,
          onclick: () => goToPage(data.pagination.page + 1),
          class: "gap-1",
          children: ($$payload3) => {
            $$payload3.out += `<!---->Next `;
            Chevron_right($$payload3, { class: "h-4 w-4" });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]-->`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="border-border flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">`;
      File_text($$payload2, { class: "text-muted-foreground mb-4 h-16 w-16" });
      $$payload2.out += `<!----> <h3 class="mb-2 text-xl font-semibold">No profiles found</h3> <p class="text-muted-foreground mb-6 max-w-md text-sm">`;
      if (searchQuery || selectedJobType || selectedIndustry) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `No profiles match your current filters. Try adjusting your search criteria.`;
      } else {
        $$payload2.out += "<!--[!-->";
        $$payload2.out += `Create your first resume profile to start applying for jobs and managing your
          applications.`;
      }
      $$payload2.out += `<!--]--></p> `;
      if (profileLimitReached) {
        $$payload2.out += "<!--[-->";
        Button($$payload2, {
          onclick: navigateToUpgrade,
          variant: "default",
          class: "gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",
          children: ($$payload3) => {
            $$payload3.out += `<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg> Upgrade Plan`;
          },
          $$slots: { default: true }
        });
      } else {
        $$payload2.out += "<!--[!-->";
        Button($$payload2, {
          onclick: navigateToProfile,
          disabled: isCreatingProfile,
          class: "gap-2",
          children: ($$payload3) => {
            if (isCreatingProfile) {
              $$payload3.out += "<!--[-->";
              Loader_circle($$payload3, { class: "h-4 w-4 animate-spin" });
              $$payload3.out += `<!----> Creating...`;
            } else {
              $$payload3.out += "<!--[!-->";
              Plus($$payload3, { class: "h-4 w-4" });
              $$payload3.out += `<!----> Create New Profile`;
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]--></div>`;
    }
    $$payload2.out += `<!--]--></div> <!---->`;
    Root($$payload2, {
      get open() {
        return deleteDialogOpen;
      },
      set open($$value) {
        deleteDialogOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Alert_dialog_content($$payload3, {
          class: "gap-0 p-0 sm:max-w-[425px]",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Alert_dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Alert_dialog_title($$payload5, {
                  class: "border-border border-b p-2",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Delete Profile`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Alert_dialog_description($$payload4, {
              class: "p-2",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Are you sure you want to delete the profile "${escape_html(profileToDelete?.name)}"? This action cannot be
      undone.`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Alert_dialog_footer($$payload4, {
              class: "border-border border-t p-2",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Alert_dialog_cancel($$payload5, {
                  onclick: () => deleteDialogOpen = false,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Alert_dialog_action($$payload5, {
                  onclick: deleteProfile,
                  disabled: isDeleting,
                  class: isDeleting ? "cursor-not-allowed opacity-70" : "bg-destructive text-destructive-foreground hover:bg-destructive/90",
                  children: ($$payload6) => {
                    if (isDeleting) {
                      $$payload6.out += "<!--[-->";
                      Loader_circle($$payload6, { class: "mr-2 h-4 w-4 animate-spin" });
                      $$payload6.out += `<!----> Deleting...`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `Delete`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CLhcHgxT.js.map
