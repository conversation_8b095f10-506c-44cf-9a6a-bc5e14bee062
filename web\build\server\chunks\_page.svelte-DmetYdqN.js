import { p as push, Q as bind_props, q as pop, M as ensure_array_like, O as escape_html, V as copy_payload, W as assign_payload, K as fallback, ab as store_mutate, S as store_get, N as attr, T as unsubscribe_stores } from './index3-CqUPEnZw.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { R as Root$1, d as Dialog_overlay, P as Portal, D as Dialog_content } from './index7-BURUpWjT.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import '@simplewebauthn/browser';
import { L as Label } from './label-Dt8gTF_8.js';
import { I as Input } from './input-DF0gPqYN.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { K as Key, C as Computer, F as Fingerprint, S as Smartphone } from './smartphone-DMr_b2fp.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { I as Info } from './info-Ce09B-Yv.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import 'clsx';
import { j as zodClient } from './zod-DfpldWlD.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import { B as Button } from './button-CrucCo1G.js';
import { L as Lock } from './lock-Dkt3avTK.js';
import { L as Log_out } from './log-out-D_M-0N-U.js';
import { G as Globe } from './globe-B6sBOhFF.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { M as Monitor } from './monitor-_9b3qg6F.js';
import { L as Laptop } from './laptop-CJiJM9e7.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { o as objectType, s as stringType } from './types-D78SXuvm.js';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './index2-Cut0V_vU.js';
import './dialog-description2-rfr-pd9k.js';
import './constants-BaiUsPxc.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './client-dNyMPa8V.js';
import './stringify-DWCARkQV.js';
import './index-DjwFQdT_.js';

function Passkeys($$payload, $$props) {
  push();
  let passkeys = fallback($$props["passkeys"], () => [], true);
  let showAddDialog = false;
  let showRemoveDialog = false;
  let passkeyName = "";
  let isRegistering = false;
  function formatDate(dateString) {
    if (!dateString) {
      return "Unknown date";
    }
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + " " + date.toLocaleTimeString();
    } catch (e) {
      console.error("Error formatting date:", e);
      return "Invalid date";
    }
  }
  {
    if (!passkeys) {
      console.error("Passkeys is null or undefined");
      passkeys = [];
    } else if (!Array.isArray(passkeys)) {
      console.error("Passkeys is not an array:", passkeys);
      passkeys = [];
    } else {
      console.log("Passkeys component has", passkeys.length, "passkeys");
      if (passkeys.length > 0) {
        console.log("First passkey:", passkeys[0]);
      }
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="border-border mb-4 flex items-center justify-between border-b p-4"><div class="flex flex-col"><h4 class="text-md font-normal">Passkeys</h4> <p class="text-muted-foreground text-sm">Manage passkeys for passwordless sign-in.</p></div> <button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50">`;
    Plus($$payload2, { class: "h-4 w-4" });
    $$payload2.out += `<!----> Add Passkey</button></div> <div class="space-y-6 p-6 pt-0">`;
    if (passkeys.length === 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="rounded-lg border border-dashed p-8 text-center">`;
      Fingerprint($$payload2, {
        class: "text-muted-foreground mx-auto mb-4 h-10 w-10"
      });
      $$payload2.out += `<!----> <h3 class="mb-2 text-lg font-medium">No passkeys added yet</h3> <p class="text-muted-foreground mb-4 text-sm">Add a passkey to sign in without a password using your device's biometric authentication or
        PIN.</p> <div class="mx-auto mb-6 max-w-md"><div class="text-muted-foreground mb-2 text-sm font-medium">Benefits of passkeys:</div> <ul class="text-muted-foreground list-disc space-y-1 pl-6 text-left text-sm"><li>No passwords to remember or type</li> <li>More secure than passwords</li> <li>Can't be phished or stolen in data breaches</li> <li>Works across your devices</li></ul></div> <button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground mx-auto inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50">`;
      Plus($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> Add Your First Passkey</button></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
      const each_array = ensure_array_like(passkeys);
      $$payload2.out += `<div class="space-y-4"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let passkey = each_array[$$index];
        Card($$payload2, {
          children: ($$payload3) => {
            Card_content($$payload3, {
              class: "flex items-center justify-between rounded-lg",
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex w-full items-center gap-4">`;
                Key($$payload4, { class: "text-primary h-8 w-8" });
                $$payload4.out += `<!----> <div><p class="font-medium">${escape_html(passkey.name || "Unnamed Passkey")}</p> <p class="text-muted-foreground text-xs">Created: ${escape_html(formatDate(passkey.createdAt))}</p> <p class="text-muted-foreground text-xs">Last used: ${escape_html(formatDate(passkey.lastUsed))}</p> `;
                if (passkey.id) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<p class="text-muted-foreground text-xs">ID: ${escape_html(typeof passkey.id === "string" && passkey.id.length > 10 ? passkey.id.substring(0, 10) + "..." : passkey.id)}</p>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div></div> <button type="button" class="text-destructive hover:text-destructive/90 hover:bg-destructive/10 inline-flex h-8 w-8 items-center justify-center rounded-md p-0">`;
                Trash_2($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----></button>`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]--></div>`;
    }
    $$payload2.out += `<!--]--> <div class="bg-muted/40 mt-12 rounded-lg border p-4"><div class="flex items-start gap-4">`;
    Info($$payload2, { class: "text-primary mt-1 h-6 w-6" });
    $$payload2.out += `<!----> <div><h3 class="font-medium">About Passkeys</h3> <p class="text-muted-foreground text-sm">Passkeys are a more secure alternative to passwords. They use biometric authentication
          (like fingerprint or face recognition) or a device PIN to sign you in without having to
          remember or type a password.</p> <p class="text-muted-foreground mt-2 text-sm">Your passkey is stored securely on your device and can't be phished or stolen in a data
          breach.</p> <p class="text-muted-foreground mt-2 text-sm"><strong>Tip:</strong> Add passkeys to multiple devices to ensure you can always sign in, even
          if one device is lost or unavailable.</p> <p class="text-muted-foreground mt-2 text-sm"><a href="https://passkeys.com/what-are-passkeys/" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">Learn more about passkeys</a></p></div></div></div></div> `;
    Root$1($$payload2, {
      get open() {
        return showAddDialog;
      },
      set open($$value) {
        showAddDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_content($$payload4, {
              class: "p-0 sm:max-w-[425px]",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  class: "border-border gap-1 border-b p-4",
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Add Passkey`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Create a new passkey for passwordless sign-in.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="grid gap-4 px-4"><div class="space-y-2">`;
                Label($$payload5, {
                  for: "passkey-name",
                  class: "text-sm font-medium leading-none",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Passkey Name`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Input($$payload5, {
                  id: "passkey-name",
                  type: "text",
                  placeholder: "e.g., Work Laptop, Personal Phone",
                  class: "border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm",
                  get value() {
                    return passkeyName;
                  },
                  set value($$value) {
                    passkeyName = $$value;
                    $$settled = false;
                  }
                });
                $$payload5.out += `<!----> <p class="text-muted-foreground text-xs">Give your passkey a name to help you identify it later</p></div> <div class="bg-muted rounded-md p-3 text-sm"><p class="font-medium">What happens next?</p> <ul class="text-muted-foreground mt-2 list-disc pl-5 text-xs"><li>Your device will prompt you to create a passkey</li> <li>You may need to use your fingerprint, face, or device PIN</li> <li>This passkey will be stored securely on your current device</li></ul></div></div> `;
                Dialog_footer($$payload5, {
                  class: "border-border border-t p-2",
                  children: ($$payload6) => {
                    $$payload6.out += `<button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"${attr("disabled", isRegistering, true)}>Cancel</button> <button type="button" class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"${attr("disabled", isRegistering, true)}>${escape_html("Create Passkey")}</button>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Root$1($$payload2, {
      get open() {
        return showRemoveDialog;
      },
      set open($$value) {
        showRemoveDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_content($$payload4, {
              class: "sm:max-w-[425px]",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Remove Passkey`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Are you sure you want to remove this passkey? This action cannot be undone.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="py-4">`;
                {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--></div> `;
                Dialog_footer($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<button type="button" class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button type="button" class="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Remove Passkey</button>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { passkeys });
  pop();
}
function Password_change($$payload, $$props) {
  push();
  var $$store_subs;
  const passwordSchema = objectType({
    currentPassword: stringType().min(1, "Current password is required"),
    newPassword: stringType().min(8, "Password must be at least 8 characters"),
    confirmPassword: stringType().min(1, "Please confirm your password")
  }).refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"]
  });
  let passwordForm = $$props["passwordForm"];
  const form = superForm(passwordForm, {
    validators: zodClient(passwordSchema),
    dataType: "json",
    onUpdated: ({ form: form2, result }) => {
      if (form2.valid && result.type === "success") {
        toast.success("Password updated successfully");
        resetForm();
      }
    },
    onError: () => {
      toast.error("Failed to update password");
    }
  });
  const { form: formData, enhance, errors, submitting } = form;
  function resetForm() {
    formData.update((f) => ({
      ...f,
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    }));
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<form method="POST" action="?/changePassword"><div class="flex flex-col justify-between"><div class="border-border flex flex-col border-b p-4"><h4 class="text-md font-normal">Change Password</h4> <p class="text-muted-foreground text-sm">Update your account password.</p></div> <div class="flex flex-col gap-6 p-4"><div class="space-y-4"><div class="space-y-2"><label for="currentPassword" class="text-sm font-medium leading-none">Current Password</label> `;
    Input($$payload2, {
      id: "currentPassword",
      type: "password",
      placeholder: "Enter your current password",
      get value() {
        return store_get($$store_subs ??= {}, "$formData", formData).currentPassword;
      },
      set value($$value) {
        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).currentPassword = $$value);
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    if (store_get($$store_subs ??= {}, "$errors", errors).currentPassword) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).currentPassword)}</p>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="space-y-2"><label for="newPassword" class="text-sm font-medium leading-none">New Password</label> `;
    Input($$payload2, {
      id: "newPassword",
      type: "password",
      placeholder: "Enter your new password",
      get value() {
        return store_get($$store_subs ??= {}, "$formData", formData).newPassword;
      },
      set value($$value) {
        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).newPassword = $$value);
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    if (store_get($$store_subs ??= {}, "$errors", errors).newPassword) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).newPassword)}</p>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="space-y-2"><label for="confirmPassword" class="text-sm font-medium leading-none">Confirm New Password</label> `;
    Input($$payload2, {
      id: "confirmPassword",
      type: "password",
      placeholder: "Confirm your new password",
      get value() {
        return store_get($$store_subs ??= {}, "$formData", formData).confirmPassword;
      },
      set value($$value) {
        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).confirmPassword = $$value);
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    if (store_get($$store_subs ??= {}, "$errors", errors).confirmPassword) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).confirmPassword)}</p>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div></div> <div class="bg-muted/40 rounded-lg border p-4"><div class="flex items-start gap-4">`;
    Lock($$payload2, { class: "text-primary mt-1 h-5 w-5" });
    $$payload2.out += `<!----> <div><h4 class="font-medium">Password Security Tips</h4> <ul class="text-muted-foreground mt-2 list-disc space-y-1 pl-5 text-xs"><li>Use at least 8 characters</li> <li>Include uppercase and lowercase letters</li> <li>Add numbers and special characters</li> <li>Avoid using personal information</li> <li>Don't reuse passwords across different sites</li></ul></div></div></div></div> <div class="flex justify-end p-6">`;
    Button($$payload2, {
      type: "submit",
      disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
      children: ($$payload3) => {
        $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Updating..." : "Update Password")}`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></form>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { passwordForm });
  pop();
}
function Sessions($$payload, $$props) {
  push();
  let sessions = fallback($$props["sessions"], () => [], true);
  let showLogoutAllDialog = false;
  function formatRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.round(diffMs / 1e3);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);
    if (diffSec < 60) {
      return "just now";
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin > 1 ? "s" : ""} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour > 1 ? "s" : ""} ago`;
    } else if (diffDay < 30) {
      return `${diffDay} day${diffDay > 1 ? "s" : ""} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }
  function getDeviceIcon(device) {
    if (device.toLowerCase().includes("iphone") || device.toLowerCase().includes("android") || device.toLowerCase().includes("mobile")) {
      return Smartphone;
    } else if (device.toLowerCase().includes("ipad") || device.toLowerCase().includes("tablet")) {
      return Monitor;
    } else {
      return Laptop;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array = ensure_array_like(sessions);
    $$payload2.out += `<div class="border-border flex flex-col justify-between border-b p-4"><div class="flex flex-col"><h4 class="text-md font-normal">Active Sessions</h4> <p class="text-muted-foreground text-sm">Manage your active sessions across devices.</p></div> `;
    if (sessions.filter((s) => !s.isCurrent).length > 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<button class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">`;
      Log_out($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> Log Out All Other Sessions</button>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="p-4"><div class="space-y-4"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let session = each_array[$$index];
      Card($$payload2, {
        children: ($$payload3) => {
          Card_content($$payload3, {
            class: "border-border flex items-center justify-between rounded-lg",
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex items-start gap-4"><!---->`;
              getDeviceIcon(session.device)?.($$payload4, { class: "text-primary mt-1 h-8 w-8" });
              $$payload4.out += `<!----> <div class="space-y-1"><div class="flex items-center"><p class="font-medium">${escape_html(session.device)}</p> `;
              if (session.isCurrent) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<span class="bg-success/20 text-success ml-2 rounded-full px-2 py-0.5 text-xs font-medium">Current</span>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--></div> <div class="text-muted-foreground flex items-center gap-4 text-sm"><div class="flex items-center gap-1">`;
              Globe($$payload4, { class: "h-3.5 w-3.5" });
              $$payload4.out += `<!----> <span>${escape_html(session.browser)} on ${escape_html(session.os)}</span></div> <div class="flex items-center gap-1">`;
              Map_pin($$payload4, { class: "h-3.5 w-3.5" });
              $$payload4.out += `<!----> <span>${escape_html(session.location)}</span></div></div> <div class="text-muted-foreground flex items-center gap-1 text-xs">`;
              Clock($$payload4, { class: "h-3 w-3" });
              $$payload4.out += `<!----> <span>Last active: ${escape_html(formatRelativeTime(session.lastActive))}</span></div> `;
              if (session.ip) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div class="text-muted-foreground text-xs">IP: ${escape_html(session.ip)}</div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--></div></div> `;
              if (!session.isCurrent) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<button class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-2 rounded-md border px-3 text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">`;
                Log_out($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> Log Out</button>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
    }
    $$payload2.out += `<!--]--></div></div> `;
    Root$1($$payload2, {
      get open() {
        return showLogoutAllDialog;
      },
      set open($$value) {
        showLogoutAllDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_content($$payload4, {
              class: "sm:max-w-[425px]",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Log Out All Other Sessions`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Are you sure you want to log out all other sessions? You will remain logged in on this
          device.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="py-4"><p class="text-muted-foreground text-sm">This will log you out from ${escape_html(sessions.filter((s) => !s.isCurrent).length)} other device(s).</p></div> `;
                Dialog_footer($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<button type="button" class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button type="button" class="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Log Out All Other Sessions</button>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { sessions });
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  console.log("Security page data:", data);
  console.log("Passkeys from data:", data.passkeys);
  console.log("User data from server:", data.userData);
  const tabs = [
    {
      id: "password",
      label: "Password",
      icon: Lock
    },
    // Two-factor and phone tabs temporarily disabled
    // { id: 'two-factor', label: 'Two-Factor', icon: Shield },
    // { id: 'phone', label: 'Phone', icon: Smartphone },
    { id: "passkeys", label: "Passkeys", icon: Key },
    {
      id: "sessions",
      label: "Sessions",
      icon: Computer
    }
  ];
  let activeTab = "password";
  SEO($$payload, {
    title: "Security Settings | Hirli",
    description: "Manage your account security settings, including active sessions, two-factor authentication, and connected devices.",
    keywords: "account security, two-factor authentication, 2FA, sessions, devices, login history",
    url: "https://hirli.com/dashboard/settings/security"
  });
  $$payload.out += `<!----> <div class="flex flex-row justify-between p-6"><div class="flex flex-col"><h2 class="text-lg font-semibold">Security</h2> <p class="text-foreground/80">Manage your account security settings, including password, two-factor authentication, and
      more.</p></div></div> <div class="grid gap-6"><div>`;
  Root($$payload, {
    value: activeTab,
    onValueChange: (value) => activeTab = value,
    children: ($$payload2) => {
      const each_array_1 = ensure_array_like(tabs);
      Tabs_list($$payload2, {
        class: "w-full",
        children: ($$payload3) => {
          const each_array = ensure_array_like(tabs);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let tab = each_array[$$index];
            Tabs_trigger($$payload3, {
              value: tab.id,
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                tab.icon?.($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> <span>${escape_html(tab.label)}</span>`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!--[-->`;
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        let tab = each_array_1[$$index_1];
        Tabs_content($$payload2, {
          value: tab.id,
          children: ($$payload3) => {
            if (tab.id === "password") {
              $$payload3.out += "<!--[-->";
              Password_change($$payload3, { passwordForm: data.passwordForm });
            } else if (tab.id === "passkeys") {
              $$payload3.out += "<!--[1-->";
              Passkeys($$payload3, { passkeys: data.passkeys });
            } else if (tab.id === "sessions") {
              $$payload3.out += "<!--[2-->";
              Sessions($$payload3, { sessions: data.sessions });
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DmetYdqN.js.map
