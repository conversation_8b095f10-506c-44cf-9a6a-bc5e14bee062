import { p as push, V as copy_payload, W as assign_payload, q as pop, S as store_get, O as escape_html, M as ensure_array_like, $ as attr_style, P as stringify, T as unsubscribe_stores } from './index3-CqUPEnZw.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { w as writable } from './index2-Cut0V_vU.js';
import { B as Button } from './button-CrucCo1G.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Badge } from './badge-C9pSznab.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { R as Root, a as Alert_dialog_content, b as Alert_dialog_header, e as Alert_dialog_footer, c as Alert_dialog_title, d as Alert_dialog_description, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { a as formatDate, f as formatDistanceToNow } from './utils-pWl1tgmi.js';
import { R as ResolvedKeywords } from './ResolvedKeywords-DvTGVMv0.js';
import { R as ResolvedLocations } from './ResolvedLocations-hOYLx-F1.js';
import { A as Arrow_left } from './arrow-left-DyZbJRhp.js';
import { C as Circle_stop } from './circle-stop-DEPHLFHi.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { T as Target } from './target-VMK77SRs.js';
import { D as Dollar_sign } from './dollar-sign-CXBwKToB.js';
import { B as Building } from './building-8WHBOPYC.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { P as Play } from './play-DKNYqs4c.js';
import 'clsx';
import './false-CRHihH2U.js';
import './index-DjwFQdT_.js';
import 'tailwind-merge';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './hidden-input-1eDzjGOB.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './dialog-description2-rfr-pd9k.js';
import 'date-fns';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const { data } = $$props;
  let automationRun = writable(data.automationRun);
  let selectedJobsForAutoApply = /* @__PURE__ */ new Set();
  let showAutoApplyConfirm = false;
  const mockJobs = [
    {
      id: "1",
      title: "Senior Frontend Developer",
      company: "TechCorp Inc.",
      location: "San Francisco, CA",
      salary: "$120k - $160k",
      salaryMin: 120,
      salaryMax: 160,
      employmentType: "full-time",
      postedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3).toISOString(),
      matchScore: 92,
      description: "We are looking for a Senior Frontend Developer to join our team. You will be responsible for building user interfaces using React, TypeScript, and modern web technologies.",
      skills: [
        "React",
        "TypeScript",
        "JavaScript",
        "CSS",
        "HTML",
        "Node.js"
      ],
      applyLink: "https://example.com/apply/1",
      applicationStatus: null
    },
    {
      id: "2",
      title: "Full Stack Engineer",
      company: "StartupXYZ",
      location: "Remote",
      salary: "$100k - $140k",
      salaryMin: 100,
      salaryMax: 140,
      employmentType: "full-time",
      postedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1e3).toISOString(),
      matchScore: 88,
      description: "Join our fast-growing startup as a Full Stack Engineer. Work with React, Node.js, and PostgreSQL to build scalable web applications.",
      skills: [
        "React",
        "Node.js",
        "PostgreSQL",
        "JavaScript",
        "AWS"
      ],
      applyLink: "https://example.com/apply/2",
      applicationStatus: null
    },
    {
      id: "3",
      title: "React Developer",
      company: "Digital Agency Co.",
      location: "New York, NY",
      salary: "$90k - $120k",
      salaryMin: 90,
      salaryMax: 120,
      employmentType: "full-time",
      postedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1e3).toISOString(),
      matchScore: 85,
      description: "We need a React Developer to help build modern web applications for our clients. Experience with Redux and TypeScript preferred.",
      skills: [
        "React",
        "Redux",
        "TypeScript",
        "CSS",
        "JavaScript"
      ],
      applyLink: "https://example.com/apply/3",
      applicationStatus: null
    },
    {
      id: "4",
      title: "Frontend Engineer",
      company: "Enterprise Solutions Ltd.",
      location: "Austin, TX",
      salary: "$110k - $150k",
      salaryMin: 110,
      salaryMax: 150,
      employmentType: "full-time",
      postedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1e3).toISOString(),
      matchScore: 78,
      description: "Looking for a Frontend Engineer to work on enterprise-level applications. Strong knowledge of React and modern JavaScript required.",
      skills: [
        "React",
        "JavaScript",
        "HTML",
        "CSS",
        "Git"
      ],
      applyLink: "https://example.com/apply/4",
      applicationStatus: null
    },
    {
      id: "5",
      title: "Software Developer",
      company: "MegaCorp Industries",
      location: "Seattle, WA",
      salary: "$95k - $130k",
      salaryMin: 95,
      salaryMax: 130,
      employmentType: "full-time",
      postedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1e3).toISOString(),
      matchScore: 72,
      description: "Join our development team to build innovative software solutions. Experience with React and backend technologies is a plus.",
      skills: ["React", "JavaScript", "Python", "SQL"],
      applyLink: "https://example.com/apply/5",
      applicationStatus: null
    }
  ];
  const jobsToDisplay = mockJobs.length > 0 ? mockJobs : data.jobs || [];
  function toggleJobSelection(jobId) {
    if (selectedJobsForAutoApply.has(jobId)) {
      selectedJobsForAutoApply.delete(jobId);
    } else {
      selectedJobsForAutoApply.add(jobId);
    }
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }
  function selectJobsByMatchScore(minScore = 80) {
    selectedJobsForAutoApply.clear();
    jobsToDisplay.forEach((job) => {
      if (job.matchScore && job.matchScore >= minScore) {
        selectedJobsForAutoApply.add(job.id);
      }
    });
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }
  function clearAllSelections() {
    selectedJobsForAutoApply.clear();
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }
  function showAutoApplyConfirmation() {
    if (selectedJobsForAutoApply.size === 0) {
      toast.error("Please select at least one job to enable auto-apply");
      return;
    }
    showAutoApplyConfirm = true;
  }
  async function confirmAutoApply() {
    try {
      const selectedJobs = Array.from(selectedJobsForAutoApply);
      console.log("Enabling auto-apply for jobs:", selectedJobs);
      toast.success(`Auto-apply enabled for ${selectedJobs.length} job${selectedJobs.length === 1 ? "" : "s"}`);
      showAutoApplyConfirm = false;
      automationRun.update((run) => ({
        ...run,
        autoApplyEnabled: true,
        selectedJobIds: selectedJobs
      }));
    } catch (error) {
      console.error("Error enabling auto-apply:", error);
      toast.error("Failed to enable auto-apply");
    }
  }
  async function stopAutomationRun() {
    try {
      const response = await fetch(`/api/automation/runs/${store_get($$store_subs ??= {}, "$automationRun", automationRun).id}/stop`, { method: "POST" });
      if (response.ok) {
        const updatedRun = await response.json();
        automationRun.set({
          ...store_get($$store_subs ??= {}, "$automationRun", automationRun),
          status: "stopped",
          stoppedAt: updatedRun.stoppedAt
        });
        toast.success("Automation run stopped");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to stop automation run");
      }
    } catch (error) {
      console.error("Error stopping automation run:", error);
      toast.error("An error occurred while stopping the automation run");
    }
  }
  async function refreshData() {
    try {
      const response = await fetch(`/api/automation/runs/${store_get($$store_subs ??= {}, "$automationRun", automationRun).id}`);
      if (response.ok) {
        const updatedRun = await response.json();
        automationRun.set(updatedRun);
        toast.success("Data refreshed");
        if (updatedRun.status === "running" || updatedRun.status === "pending") {
          setTimeout(refreshData, 5e3);
        }
      } else {
        toast.error("Failed to refresh data");
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("An error occurred while refreshing data");
    }
  }
  function getStatusVariant(status) {
    switch (status) {
      case "running":
        return "default";
      case "completed":
        return "outline";
      case "failed":
        return "destructive";
      case "stopped":
        return "secondary";
      default:
        return "secondary";
    }
  }
  function getStatusIcon(status) {
    switch (status) {
      case "running":
        return Play;
      case "completed":
        return Circle_check_big;
      case "failed":
        return Circle_x;
      case "stopped":
        return Circle_stop;
      case "pending":
        return Clock;
      default:
        return Clock;
    }
  }
  function calculateProgress(run) {
    if (run.status === "completed") return 100;
    if (run.status === "failed" || run.status === "stopped") return run.progress || 0;
    return run.progress || 0;
  }
  function getProfileData(profile) {
    if (!profile) return {};
    if (profile.data) {
      try {
        if (typeof profile.data.data === "string") {
          return JSON.parse(profile.data.data);
        }
        if (profile.data.data && typeof profile.data.data === "object") {
          return profile.data.data;
        }
        if (typeof profile.data === "string") {
          return JSON.parse(profile.data);
        }
        return profile.data;
      } catch (e) {
        console.error("Error parsing profile data:", e);
        return {};
      }
    }
    return profile;
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="container mt-6 flex max-w-full flex-col gap-10 p-6"><div class="flex flex-col gap-8"><div class="flex items-center gap-4">`;
    Button($$payload2, {
      variant: "ghost",
      onclick: () => window.location.href = "/dashboard/automation",
      children: ($$payload3) => {
        Arrow_left($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Back to Automation`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <h1 class="text-xl font-normal text-white">Automation Run Details</h1> `;
    Badge($$payload2, {
      variant: getStatusVariant(store_get($$store_subs ??= {}, "$automationRun", automationRun).status),
      class: "ml-2",
      children: ($$payload3) => {
        const StatusIcon = getStatusIcon(store_get($$store_subs ??= {}, "$automationRun", automationRun).status);
        $$payload3.out += `<!---->`;
        StatusIcon($$payload3, { class: "mr-1 h-3 w-3" });
        $$payload3.out += `<!----> ${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).status.charAt(0).toUpperCase() + store_get($$store_subs ??= {}, "$automationRun", automationRun).status.slice(1))}`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <div class="ml-auto flex items-center gap-2">`;
    if (store_get($$store_subs ??= {}, "$automationRun", automationRun).status === "running") {
      $$payload2.out += "<!--[-->";
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        onclick: stopAutomationRun,
        children: ($$payload3) => {
          Circle_stop($$payload3, { class: "mr-2 h-4 w-4" });
          $$payload3.out += `<!----> Stop Run`;
        },
        $$slots: { default: true }
      });
    } else if (store_get($$store_subs ??= {}, "$automationRun", automationRun).status === "pending") {
      $$payload2.out += "<!--[1-->";
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        disabled: true,
        children: ($$payload3) => {
          Clock($$payload3, { class: "mr-2 h-4 w-4" });
          $$payload3.out += `<!----> Pending`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    Button($$payload2, {
      variant: "outline",
      size: "sm",
      onclick: refreshData,
      children: ($$payload3) => {
        Refresh_cw($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Refresh`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></div> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Run Information`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Details about this automation run`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          class: "p-6 pt-0",
          children: ($$payload4) => {
            $$payload4.out += `<div class="mb-4"><div class="bg-secondary h-2 w-full rounded-full"><div class="bg-primary h-full rounded-full transition-all"${attr_style(`width: ${stringify(calculateProgress(store_get($$store_subs ??= {}, "$automationRun", automationRun)))}%`)}></div></div> <div class="mt-2 text-sm text-gray-400">Progress: ${escape_html(calculateProgress(store_get($$store_subs ??= {}, "$automationRun", automationRun)))}%</div></div> <div class="grid grid-cols-1 gap-6 md:grid-cols-2"><div><h3 class="mb-2 text-lg font-semibold">Profile</h3> <div class="rounded-lg border p-4"><div class="mb-2 text-lg font-medium">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile ? getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).fullName || "Unnamed Profile" : "Unnamed Profile")}</div> <div class="mb-4 text-sm text-gray-400">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile ? getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).title || getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).headline || "No title specified" : "No title specified")}</div> <div class="mb-2 text-sm font-medium text-gray-400">Resume</div> <div class="mb-4">`;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).profile?.resumes && Array.isArray(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile.resumes) && store_get($$store_subs ??= {}, "$automationRun", automationRun).profile.resumes.length > 0) {
              $$payload4.out += "<!--[-->";
              Badge($$payload4, {
                variant: "outline",
                children: ($$payload5) => {
                  File_text($$payload5, { class: "mr-1 h-3 w-3" });
                  $$payload5.out += `<!----> ${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile.resumes[0].document?.label || "Resume")}`;
                },
                $$slots: { default: true }
              });
            } else {
              $$payload4.out += "<!--[!-->";
              Badge($$payload4, {
                variant: "outline",
                class: "text-gray-400",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->No resume`;
                },
                $$slots: { default: true }
              });
            }
            $$payload4.out += `<!--]--></div> <div class="mb-2 text-sm font-medium text-gray-400">Skills</div> <div class="flex flex-wrap gap-1">`;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).profile && getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).skills && getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).skills.length > 0) {
              $$payload4.out += "<!--[-->";
              const each_array = ensure_array_like(getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).skills.slice(0, 5));
              $$payload4.out += `<!--[-->`;
              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                let skill = each_array[$$index];
                Badge($$payload4, {
                  variant: "secondary",
                  class: "text-xs",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(skill)}`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]--> `;
              if (getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).skills.length > 5) {
                $$payload4.out += "<!--[-->";
                Badge($$payload4, {
                  variant: "secondary",
                  class: "text-xs",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->+${escape_html(getProfileData(store_get($$store_subs ??= {}, "$automationRun", automationRun).profile).skills.length - 5)} more`;
                  },
                  $$slots: { default: true }
                });
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `<span class="text-gray-400">No skills specified</span>`;
            }
            $$payload4.out += `<!--]--></div></div></div> <div><h3 class="mb-2 text-lg font-semibold">Search Parameters</h3> <div class="rounded-lg border p-4"><div class="mb-4"><div class="text-sm font-medium text-gray-400">Keywords</div> <div>`;
            ResolvedKeywords($$payload4, {
              keywordIds: store_get($$store_subs ??= {}, "$automationRun", automationRun).keywords || "",
              fallback: "None specified"
            });
            $$payload4.out += `<!----></div></div> <div class="mb-4"><div class="text-sm font-medium text-gray-400">Location</div> <div>`;
            ResolvedLocations($$payload4, {
              locationIds: store_get($$store_subs ??= {}, "$automationRun", automationRun).location || "",
              fallback: "None specified"
            });
            $$payload4.out += `<!----></div></div> <div class="mb-4"><div class="text-sm font-medium text-gray-400">Started</div> <div>${escape_html(formatDate(store_get($$store_subs ??= {}, "$automationRun", automationRun).createdAt))} (${escape_html(formatDistanceToNow(new Date(store_get($$store_subs ??= {}, "$automationRun", automationRun).createdAt)))} ago)</div></div> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).stoppedAt) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div><div class="text-sm font-medium text-gray-400">Stopped</div> <div>${escape_html(formatDate(store_get($$store_subs ??= {}, "$automationRun", automationRun).stoppedAt))} (${escape_html(formatDistanceToNow(new Date(store_get($$store_subs ??= {}, "$automationRun", automationRun).stoppedAt)))} ago)</div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Automation Configuration`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Detailed automation parameters and settings`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          class: "p-6 pt-0",
          children: ($$payload4) => {
            $$payload4.out += `<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border p-4"><h4 class="mb-3 flex items-center text-sm font-medium text-gray-400">`;
            Target($$payload4, { class: "mr-2 h-4 w-4" });
            $$payload4.out += `<!----> Auto-Apply Status</h4> <div class="space-y-3"><div class="flex items-center justify-between"><span class="text-sm">Auto-Apply Enabled</span> `;
            Badge($$payload4, {
              variant: store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled ? "default" : "secondary",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled ? "Enabled" : "Disabled")}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div><span class="text-xs text-gray-400">Jobs Selected</span> <div class="text-sm">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).selectedJobIds?.length || 0)}</div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div><span class="text-xs text-gray-400">Max Jobs to Apply</span> <div class="text-sm">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).maxJobsToApply || 10)}</div></div> <div><span class="text-xs text-gray-400">Min Match Score</span> <div class="text-sm">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).minMatchScore || 70)}%</div></div></div></div> <div class="rounded-lg border p-4"><h4 class="mb-3 flex items-center text-sm font-medium text-gray-400">`;
            Dollar_sign($$payload4, { class: "mr-2 h-4 w-4" });
            $$payload4.out += `<!----> Salary &amp; Experience</h4> <div class="space-y-3">`;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).salaryMin || store_get($$store_subs ??= {}, "$automationRun", automationRun).salaryMax) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div><span class="text-xs text-gray-400">Salary Range</span> <div class="text-sm">$${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).salaryMin || 0)}k - $${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).salaryMax || 200)}k</div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).experienceLevelMin || store_get($$store_subs ??= {}, "$automationRun", automationRun).experienceLevelMax) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div><span class="text-xs text-gray-400">Experience Range</span> <div class="text-sm">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).experienceLevelMin || 0)} - ${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).experienceLevelMax || 10)} years</div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).remotePreference) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div><span class="text-xs text-gray-400">Remote Preference</span> <div class="text-sm capitalize">${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).remotePreference)}</div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> <div class="rounded-lg border p-4"><h4 class="mb-3 flex items-center text-sm font-medium text-gray-400">`;
            Building($$payload4, { class: "mr-2 h-4 w-4" });
            $$payload4.out += `<!----> Preferences</h4> <div class="space-y-3">`;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).jobTypes && store_get($$store_subs ??= {}, "$automationRun", automationRun).jobTypes.length > 0) {
              $$payload4.out += "<!--[-->";
              const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$automationRun", automationRun).jobTypes);
              $$payload4.out += `<div><span class="text-xs text-gray-400">Job Types</span> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
              for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                let jobType = each_array_1[$$index_1];
                Badge($$payload4, {
                  variant: "secondary",
                  class: "text-xs capitalize",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(jobType)}`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]--></div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).companySizePreference && store_get($$store_subs ??= {}, "$automationRun", automationRun).companySizePreference.length > 0) {
              $$payload4.out += "<!--[-->";
              const each_array_2 = ensure_array_like(store_get($$store_subs ??= {}, "$automationRun", automationRun).companySizePreference);
              $$payload4.out += `<div><span class="text-xs text-gray-400">Company Size</span> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
              for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                let size = each_array_2[$$index_2];
                Badge($$payload4, {
                  variant: "secondary",
                  class: "text-xs capitalize",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(size)}`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]--></div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).preferredCompanies && store_get($$store_subs ??= {}, "$automationRun", automationRun).preferredCompanies.length > 0) {
              $$payload4.out += "<!--[-->";
              const each_array_3 = ensure_array_like(store_get($$store_subs ??= {}, "$automationRun", automationRun).preferredCompanies.slice(0, 3));
              $$payload4.out += `<div><span class="text-xs text-gray-400">Preferred Companies</span> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
              for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                let company = each_array_3[$$index_3];
                Badge($$payload4, {
                  variant: "outline",
                  class: "text-xs",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(company)}`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]--> `;
              if (store_get($$store_subs ??= {}, "$automationRun", automationRun).preferredCompanies.length > 3) {
                $$payload4.out += "<!--[-->";
                Badge($$payload4, {
                  variant: "outline",
                  class: "text-xs",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->+${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).preferredCompanies.length - 3)} more`;
                  },
                  $$slots: { default: true }
                });
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--></div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (store_get($$store_subs ??= {}, "$automationRun", automationRun).excludeCompanies && store_get($$store_subs ??= {}, "$automationRun", automationRun).excludeCompanies.length > 0) {
              $$payload4.out += "<!--[-->";
              const each_array_4 = ensure_array_like(store_get($$store_subs ??= {}, "$automationRun", automationRun).excludeCompanies.slice(0, 3));
              $$payload4.out += `<div><span class="text-xs text-gray-400">Excluded Companies</span> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
              for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
                let company = each_array_4[$$index_4];
                Badge($$payload4, {
                  variant: "destructive",
                  class: "text-xs",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(company)}`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]--> `;
              if (store_get($$store_subs ??= {}, "$automationRun", automationRun).excludeCompanies.length > 3) {
                $$payload4.out += "<!--[-->";
                Badge($$payload4, {
                  variant: "destructive",
                  class: "text-xs",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->+${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).excludeCompanies.length - 3)} more`;
                  },
                  $$slots: { default: true }
                });
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--></div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <div><div class="mb-4 flex items-center justify-between"><div class="flex items-center gap-4"><h2 class="text-2xl font-semibold text-gray-300">Jobs Found (${escape_html(jobsToDisplay.length)})</h2> `;
    if (jobsToDisplay.length > 0 && !store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled) {
      $$payload2.out += "<!--[-->";
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        onclick: showAutoApplyConfirmation,
        disabled: selectedJobsForAutoApply.size === 0,
        class: "h-8 text-xs",
        children: ($$payload3) => {
          Target($$payload3, { class: "mr-1 h-3 w-3" });
          $$payload3.out += `<!----> Apply Selected (${escape_html(selectedJobsForAutoApply.size)})`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="flex items-center gap-2 text-sm text-gray-400"><span>Applied: ${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).jobsApplied || 0)}</span> <span>•</span> <span>Skipped: ${escape_html(store_get($$store_subs ??= {}, "$automationRun", automationRun).jobsSkipped || 0)}</span> <span>•</span> <span>Avg Match: ${escape_html(jobsToDisplay.length > 0 ? (jobsToDisplay.reduce((sum, job) => sum + (job.matchScore || 0), 0) / jobsToDisplay.length).toFixed(1) : 0)}%</span></div></div> `;
    if (jobsToDisplay.length === 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">`;
      Briefcase($$payload2, { class: "mb-4 h-12 w-12 text-gray-400" });
      $$payload2.out += `<!----> <h3 class="text-xl font-semibold text-gray-300">No jobs found yet</h3> <p class="mt-2 text-gray-400">`;
      if (store_get($$store_subs ??= {}, "$automationRun", automationRun).status === "running") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `The automation is still running. Jobs will appear here as they are found.`;
      } else if (store_get($$store_subs ??= {}, "$automationRun", automationRun).status === "pending") {
        $$payload2.out += "<!--[1-->";
        $$payload2.out += `The automation is pending. Jobs will appear here once it starts running.`;
      } else {
        $$payload2.out += "<!--[!-->";
        $$payload2.out += `No jobs were found during this automation run.`;
      }
      $$payload2.out += `<!--]--></p></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
      const each_array_5 = ensure_array_like(jobsToDisplay);
      if (!store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="mb-6 rounded-lg border border-blue-500/20 bg-blue-500/5 p-4"><div class="mb-4 flex items-center justify-between"><div><h3 class="text-lg font-medium text-blue-400">Enable Auto-Apply</h3> <p class="text-sm text-gray-400">Select jobs you want to automatically apply to</p></div> <div class="flex items-center gap-2"><span class="text-sm text-gray-400">${escape_html(selectedJobsForAutoApply.size)} selected</span></div></div> <div class="mb-4 flex flex-wrap gap-2">`;
        Button($$payload2, {
          variant: "outline",
          size: "sm",
          onclick: () => selectJobsByMatchScore(90),
          children: ($$payload3) => {
            $$payload3.out += `<!---->Select 90%+ Match`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Button($$payload2, {
          variant: "outline",
          size: "sm",
          onclick: () => selectJobsByMatchScore(80),
          children: ($$payload3) => {
            $$payload3.out += `<!---->Select 80%+ Match`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Button($$payload2, {
          variant: "outline",
          size: "sm",
          onclick: () => selectJobsByMatchScore(70),
          children: ($$payload3) => {
            $$payload3.out += `<!---->Select 70%+ Match`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Button($$payload2, {
          variant: "ghost",
          size: "sm",
          onclick: clearAllSelections,
          children: ($$payload3) => {
            $$payload3.out += `<!---->Clear All`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div> <div class="flex items-center gap-2">`;
        Button($$payload2, {
          onclick: showAutoApplyConfirmation,
          disabled: selectedJobsForAutoApply.size === 0,
          class: "bg-blue-600 hover:bg-blue-700",
          children: ($$payload3) => {
            Target($$payload3, { class: "mr-2 h-4 w-4" });
            $$payload3.out += `<!----> Enable Auto-Apply (${escape_html(selectedJobsForAutoApply.size)} jobs)`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> <div class="grid gap-4"><!--[-->`;
      for (let $$index_6 = 0, $$length = each_array_5.length; $$index_6 < $$length; $$index_6++) {
        let job = each_array_5[$$index_6];
        $$payload2.out += `<!---->`;
        Card($$payload2, {
          class: "flex flex-col",
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card_header($$payload3, {
              class: "p-6",
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex items-start justify-between"><div class="min-w-0 flex-1"><div class="flex items-center gap-2"><!---->`;
                Card_title($$payload4, {
                  class: "text-lg",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(job.title)}`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                if (job.matchScore) {
                  $$payload4.out += "<!--[-->";
                  Badge($$payload4, {
                    variant: "outline",
                    class: "text-xs",
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(job.matchScore)}% match`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (job.applicationStatus) {
                  $$payload4.out += "<!--[-->";
                  Badge($$payload4, {
                    variant: job.applicationStatus === "applied" ? "default" : "secondary",
                    class: "text-xs",
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(job.applicationStatus)}`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled && store_get($$store_subs ??= {}, "$automationRun", automationRun).selectedJobIds?.includes(job.id)) {
                  $$payload4.out += "<!--[-->";
                  Badge($$payload4, {
                    variant: "default",
                    class: "bg-blue-600 text-xs",
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->Auto-Apply Enabled`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> <!---->`;
                Card_description($$payload4, {
                  class: "mt-1",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center gap-1">`;
                    Building($$payload5, { class: "h-3 w-3" });
                    $$payload5.out += `<!----> ${escape_html(job.company)}</div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div> <div class="flex items-center gap-3">`;
                if (!store_get($$store_subs ??= {}, "$automationRun", automationRun).autoApplyEnabled) {
                  $$payload4.out += "<!--[-->";
                  Switch($$payload4, {
                    checked: selectedJobsForAutoApply.has(job.id),
                    onCheckedChange: (checked) => {
                      if (checked) {
                        toggleJobSelection(job.id);
                      } else {
                        toggleJobSelection(job.id);
                      }
                    }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (job.applyLink || job.url) {
                  $$payload4.out += "<!--[-->";
                  Button($$payload4, {
                    variant: "outline",
                    size: "sm",
                    onclick: () => window.open(job.applyLink || job.url, "_blank"),
                    children: ($$payload5) => {
                      External_link($$payload5, { class: "mr-2 h-4 w-4" });
                      $$payload5.out += `<!----> Apply`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_content($$payload3, {
              class: "p-6 pt-0",
              children: ($$payload4) => {
                $$payload4.out += `<div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">`;
                if (job.location) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div><div class="flex items-center gap-1 text-sm font-medium text-gray-400">`;
                  Map_pin($$payload4, { class: "h-3 w-3" });
                  $$payload4.out += `<!----> Location</div> <div class="text-sm">${escape_html(job.location)}</div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (job.salary || job.salaryMin || job.salaryMax) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div><div class="flex items-center gap-1 text-sm font-medium text-gray-400">`;
                  Dollar_sign($$payload4, { class: "h-3 w-3" });
                  $$payload4.out += `<!----> Salary</div> <div class="text-sm">`;
                  if (job.salary) {
                    $$payload4.out += "<!--[-->";
                    $$payload4.out += `${escape_html(job.salary)}`;
                  } else if (job.salaryMin && job.salaryMax) {
                    $$payload4.out += "<!--[1-->";
                    $$payload4.out += `$${escape_html(job.salaryMin)}k - $${escape_html(job.salaryMax)}k`;
                  } else if (job.salaryMin) {
                    $$payload4.out += "<!--[2-->";
                    $$payload4.out += `$${escape_html(job.salaryMin)}k+`;
                  } else if (job.salaryMax) {
                    $$payload4.out += "<!--[3-->";
                    $$payload4.out += `Up to $${escape_html(job.salaryMax)}k`;
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--></div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (job.employmentType) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div><div class="text-sm font-medium text-gray-400">Type</div> <div class="text-sm capitalize">${escape_html(job.employmentType)}</div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (job.postedDate || job.postedAt) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div><div class="flex items-center gap-1 text-sm font-medium text-gray-400">`;
                  Calendar($$payload4, { class: "h-3 w-3" });
                  $$payload4.out += `<!----> Posted</div> <div class="text-sm">${escape_html(formatDistanceToNow(new Date(job.postedDate || job.postedAt)))} ago</div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> `;
                if (job.description) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="mt-4"><div class="text-sm font-medium text-gray-400">Description</div> <div class="mt-1 line-clamp-3 text-sm">${escape_html(job.description)}</div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> `;
                if (job.skills && job.skills.length > 0) {
                  $$payload4.out += "<!--[-->";
                  const each_array_6 = ensure_array_like(job.skills.slice(0, 5));
                  $$payload4.out += `<div class="mt-4"><div class="text-sm font-medium text-gray-400">Skills</div> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
                  for (let $$index_5 = 0, $$length2 = each_array_6.length; $$index_5 < $$length2; $$index_5++) {
                    let skill = each_array_6[$$index_5];
                    Badge($$payload4, {
                      variant: "secondary",
                      class: "text-xs",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(skill)}`;
                      },
                      $$slots: { default: true }
                    });
                  }
                  $$payload4.out += `<!--]--> `;
                  if (job.skills.length > 5) {
                    $$payload4.out += "<!--[-->";
                    Badge($$payload4, {
                      variant: "secondary",
                      class: "text-xs",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->+${escape_html(job.skills.length - 5)} more`;
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--></div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      }
      $$payload2.out += `<!--]--></div>`;
    }
    $$payload2.out += `<!--]--></div></div> <!---->`;
    Root($$payload2, {
      get open() {
        return showAutoApplyConfirm;
      },
      set open($$value) {
        showAutoApplyConfirm = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Alert_dialog_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Alert_dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Alert_dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Confirm Auto-Apply`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Alert_dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Are you sure you want to enable auto-apply for ${escape_html(selectedJobsForAutoApply.size)} selected job${escape_html(selectedJobsForAutoApply.size === 1 ? "" : "s")}? <br/><br/> This will automatically submit applications to the selected jobs using your profile and resume.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Alert_dialog_footer($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Alert_dialog_cancel($$payload5, {
                  onclick: () => showAutoApplyConfirm = false,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Alert_dialog_action($$payload5, {
                  onclick: confirmAutoApply,
                  children: ($$payload6) => {
                    Target($$payload6, { class: "mr-2 h-4 w-4" });
                    $$payload6.out += `<!----> Enable Auto-Apply`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DWNSbFoW.js.map
