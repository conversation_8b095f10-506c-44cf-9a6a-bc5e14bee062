{"name": "@sanity/util", "version": "3.68.3", "description": "Utilities shared across projects of Sanity", "keywords": ["sanity", "cms", "headless", "realtime", "content", "util"], "homepage": "https://www.sanity.io/", "bugs": {"url": "https://github.com/sanity-io/sanity/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sanity-io/sanity.git", "directory": "packages/@sanity/util"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "sideEffects": false, "exports": {".": {"source": "./src/_exports/index.ts", "import": "./lib/index.mjs", "require": "./lib/index.js", "default": "./lib/index.js"}, "./fs": {"source": "./src/_exports/fs.ts", "import": "./lib/fs.mjs", "require": "./lib/fs.js", "default": "./lib/fs.js"}, "./client": {"source": "./src/_exports/client.ts", "import": "./lib/client.mjs", "require": "./lib/client.js", "default": "./lib/client.js"}, "./concurrency-limiter": {"source": "./src/_exports/concurrency-limiter.ts", "import": "./lib/concurrency-limiter.mjs", "require": "./lib/concurrency-limiter.js", "default": "./lib/concurrency-limiter.js"}, "./content": {"source": "./src/_exports/content.ts", "import": "./lib/content.mjs", "require": "./lib/content.js", "default": "./lib/content.js"}, "./createSafeJsonParser": {"source": "./src/_exports/createSafeJsonParser.ts", "import": "./lib/createSafeJsonParser.mjs", "require": "./lib/createSafeJsonParser.js", "default": "./lib/createSafeJsonParser.js"}, "./legacyDateFormat": {"source": "./src/_exports/legacyDateFormat.ts", "import": "./lib/legacyDateFormat.mjs", "require": "./lib/legacyDateFormat.js", "default": "./lib/legacyDateFormat.js"}, "./paths": {"source": "./src/_exports/paths.ts", "import": "./lib/paths.mjs", "require": "./lib/paths.js", "default": "./lib/paths.js"}, "./package.json": "./package.json"}, "main": "./lib/index.js", "module": "./lib/index.esm.js", "types": "./lib/index.d.ts", "typesVersions": {"*": {"fs": ["./lib/fs.d.ts"], "client": ["./lib/client.d.ts"], "concurrency-limiter": ["./lib/concurrency-limiter.d.ts"], "content": ["./lib/content.d.ts"], "createSafeJsonParser": ["./lib/createSafeJsonParser.d.ts"], "legacyDateFormat": ["./lib/legacyDateFormat.d.ts"], "paths": ["./lib/paths.d.ts"]}}, "files": ["lib", "src", "client.js", "concurrency-limiter.js", "content.js", "createSafeJsonParser.js", "fs.js", "legacyDateFormat.js", "paths.js"], "scripts": {"build": "pkg-utils build --strict --check --clean", "check:types": "tsc --project tsconfig.lib.json", "clean": "rimraf client.js concurrency-limiter.js content.js createSafeJsonParser.js fs.js legacyDateFormat.js lib paths.js", "prepublishOnly": "turbo run build", "test": "vitest run", "watch": "pkg-utils watch"}, "dependencies": {"@sanity/client": "^6.24.1", "@sanity/types": "3.68.3", "get-random-values-esm": "1.0.2", "moment": "^2.30.1", "rxjs": "^7.8.1"}, "devDependencies": {"@repo/package.config": "3.68.3", "@repo/test-config": "3.68.3", "rimraf": "^5.0.10", "vitest": "^2.1.8"}, "engines": {"node": ">=18"}, "gitHead": "ddf9201ac85f2de9e1a17b496ca0299ce423e50b"}