import { p as prisma } from './prisma-Cit_HrSw.js';
import { r as redirect } from './index-Ddp2AB5f.js';
import '@prisma/client';

const load = async ({ url }) => {
  const token = url.searchParams.get("token");
  if (!token) throw redirect(302, "/auth/forgot-password");
  const record = await prisma.passwordResetToken.findUnique({
    where: { token }
  });
  if (!record || record.expiresAt < /* @__PURE__ */ new Date()) {
    throw redirect(302, "/auth/forgot-password");
  }
  return { token };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 16;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BwXCcY4q.js')).default;
const server_id = "src/routes/auth/reset-password/+page.server.ts";
const imports = ["_app/immutable/nodes/16.DQpc4nH0.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/nZgk9enP.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=16-u9MJenMx.js.map
