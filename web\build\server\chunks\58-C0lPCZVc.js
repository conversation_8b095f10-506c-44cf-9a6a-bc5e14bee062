import { e as error } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const load = async ({ locals }) => {
  if (!locals.user) {
    throw error(401, "Unauthorized");
  }
  try {
    const applications = await prisma.application.findMany({
      where: { userId: locals.user.id },
      include: {
        interviewStages: true
        // Include interview stages instead of non-existent job relation
      },
      orderBy: { createdAt: "desc" },
      take: 100
      // Limit to recent applications
    }).catch((err) => {
      console.error("Error fetching applications:", err);
      return [];
    });
    const resumes = await prisma.resume.findMany({
      where: {
        document: {
          userId: locals.user.id
        }
      },
      include: {
        document: true
      },
      orderBy: { updatedAt: "desc" }
    }).catch((err) => {
      console.error("Error fetching resumes:", err);
      return [];
    });
    return {
      user: locals.user,
      applications,
      resumes
    };
  } catch (err) {
    console.error("Error loading analysis data:", err);
    throw error(500, "Failed to load analysis data");
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 58;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BtGcnToV.js')).default;
const server_id = "src/routes/dashboard/settings/analysis/+page.server.ts";
const imports = ["_app/immutable/nodes/58.DPE9vPbF.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/ByUTvV5u.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/B-l1ubNa.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/Csk_I0QV.js","_app/immutable/chunks/BLiq6Dlm.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/D1zde6Ej.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/iDciRV2n.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/CTO_B1Jk.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=58-C0lPCZVc.js.map
