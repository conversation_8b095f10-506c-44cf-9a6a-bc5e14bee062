{"version": 3, "file": "6-Ca-pxP8c.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/email/_layout.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/6.js"], "sourcesContent": ["import { r as redirect } from \"../../../../../../chunks/index.js\";\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  if (!user.isAdmin) {\n    throw redirect(302, \"/dashboard\");\n  }\n  return {};\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/admin/email/_layout.server.ts.js';\n\nexport const index = 6;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/admin/email/_layout.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/admin/email/+layout.server.ts\";\nexport const imports = [\"_app/immutable/nodes/6.qT6zyvox.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/DzJNq86D.js\",\"_app/immutable/chunks/D8pQCLOH.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACrB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,YAAY,CAAC;AACrC;AACA,EAAE,OAAO,EAAE;AACX,CAAC;;;;;;;ACRW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,8BAAmE,CAAC,EAAE;AAEjI,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACppB,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}