import { p as push, q as pop, O as escape_html, M as ensure_array_like, aa as maybe_selected, N as attr } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { U as UniversalDocumentViewer } from './UniversalDocumentViewer-Cv4LqmRL.js';
import 'clsx';
import { F as File_text } from './file-text-HttY5S5h.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { S as Sparkles } from './sparkles-E4-thk3U.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './button-CrucCo1G.js';
import './index-DjwFQdT_.js';
import './skeleton-C-NLefl9.js';
import './zoom-out-vip0ZIci.js';
import './Icon-A4vzmk-O.js';
import './rotate-cw-CWqzplUz.js';
import './external-link-ZBG7aazC.js';
import './download-CLn66Ope.js';

function ATSScoreCard($$payload, $$props) {
  push();
  $$payload.out += `<!---->`;
  Card($$payload, {
    class: "w-full",
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between space-y-0 pb-2",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_title($$payload3, {
            class: "text-md font-medium",
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex items-center">`;
              File_text($$payload4, { class: "mr-2 h-4 w-4 text-blue-500" });
              $$payload4.out += `<!----> ${escape_html("ATS Analysis")}</div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <button class="inline-flex h-8 w-8 items-center justify-center rounded-md p-0 text-gray-700 hover:bg-gray-100">`;
          Refresh_cw($$payload3, { class: "h-4 w-4" });
          $$payload3.out += `<!----> <span class="sr-only">Refresh</span></button>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          {
            $$payload3.out += "<!--[2-->";
            $$payload3.out += `<div class="py-4 text-center text-sm text-gray-500"><p>No analysis available for this resume.</p> <button class="mt-2 inline-flex h-9 items-center justify-center rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100">Run Analysis</button></div>`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  let document = data.document || null;
  let loading = !document;
  let activeTab = "overview";
  let selectedJobId = "";
  let jobs = [];
  SEO($$payload, {
    title: "ATS Optimization | Auto Apply",
    description: "Optimize your resume for ATS systems"
  });
  $$payload.out += `<!----> <div class="container mx-auto p-6"><div class="mb-6 flex items-center justify-between"><div><div class="flex items-center"><button class="mr-2 inline-flex h-10 items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100">`;
  Chevron_left($$payload, { class: "h-5 w-5" });
  $$payload.out += `<!----></button> <div><h1 class="text-3xl font-bold">ATS Optimization</h1> <p class="text-gray-500">Analyze and optimize your resume for ATS systems</p></div></div></div></div> `;
  if (loading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex h-64 items-center justify-center"><p class="text-lg text-gray-500">Loading document...</p></div>`;
  } else if (document) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="grid grid-cols-1 gap-6 lg:grid-cols-2"><div><!---->`;
    Card($$payload, {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Card_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card_title($$payload3, {
              class: "flex items-center",
              children: ($$payload4) => {
                File_text($$payload4, { class: "mr-2 h-5 w-5" });
                $$payload4.out += `<!----> ${escape_html(document.label)}`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->View your resume document`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Card_content($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<div style="height: 600px;">`;
            UniversalDocumentViewer($$payload3, { document });
            $$payload3.out += `<!----></div>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div> <div><!---->`;
    Root($$payload, {
      value: activeTab,
      onValueChange: (value) => activeTab = value,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Tabs_list($$payload2, {
          class: "grid w-full grid-cols-2",
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Tabs_trigger($$payload3, {
              value: "overview",
              children: ($$payload4) => {
                $$payload4.out += `<!---->General Analysis`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Tabs_trigger($$payload3, {
              value: "job-specific",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Job-Specific Analysis`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Tabs_content($$payload2, {
          value: "overview",
          class: "mt-4",
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Card_header($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Card_title($$payload5, {
                      class: "flex items-center",
                      children: ($$payload6) => {
                        Sparkles($$payload6, { class: "mr-2 h-5 w-5 text-blue-500" });
                        $$payload6.out += `<!----> ATS Analysis`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> <!---->`;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->See how your resume performs with ATS systems`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Card_content($$payload4, {
                  children: ($$payload5) => {
                    ATSScoreCard($$payload5, { resumeId: document.resume?.id || document.id });
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Tabs_content($$payload2, {
          value: "job-specific",
          class: "mt-4",
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Card_header($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Card_title($$payload5, {
                      class: "flex items-center",
                      children: ($$payload6) => {
                        Sparkles($$payload6, { class: "mr-2 h-5 w-5 text-blue-500" });
                        $$payload6.out += `<!----> Job-Specific Analysis`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> <!---->`;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->Analyze your resume against a specific job`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Card_content($$payload4, {
                  children: ($$payload5) => {
                    const each_array = ensure_array_like(jobs);
                    $$payload5.out += `<div class="mb-4"><label for="job-select" class="mb-2 block text-sm font-medium">Select a job to analyze against</label> <select id="job-select" class="w-full rounded-md border border-gray-300 p-2">`;
                    $$payload5.select_value = selectedJobId;
                    $$payload5.out += `<option value=""${maybe_selected($$payload5, "")}>Select a job...</option><!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let job = each_array[$$index];
                      $$payload5.out += `<option${attr("value", job.id)}${maybe_selected($$payload5, job.id)}>${escape_html(job.title)} at ${escape_html(job.company)}</option>`;
                    }
                    $$payload5.out += `<!--]-->`;
                    $$payload5.select_value = void 0;
                    $$payload5.out += `</select></div> `;
                    {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<div class="rounded-md bg-blue-50 p-4 text-sm text-blue-700"><p>Select a job to see how your resume matches the specific requirements.</p></div>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex h-64 items-center justify-center"><p class="text-lg text-red-500">Document not found</p></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CPvP6j-3.js.map
