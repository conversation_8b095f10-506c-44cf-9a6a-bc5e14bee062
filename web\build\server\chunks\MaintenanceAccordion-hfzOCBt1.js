import { p as push, q as pop, O as escape_html, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from './accordion-trigger-DwieKZVA.js';
import { B as Badge } from './badge-C9pSznab.js';
import { S as StatusTag, b as SeverityBadge, a as StatusBar } from './StatusBar-DynsEX84.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import { M as Message_square } from './message-square-D57Olt6y.js';

function MaintenanceAccordion($$payload, $$props) {
  push();
  const { incident, index } = $$props;
  function formatDate(date) {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(new Date(date));
  }
  function formatDateWithYear(date) {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric"
    }).format(new Date(date));
  }
  function calculateProgress() {
    if (!incident.startTime || !incident.endTime) return 0;
    const start = new Date(incident.startTime).getTime();
    const end = new Date(incident.endTime).getTime();
    const now = Date.now();
    if (now <= start) return 0;
    if (now >= end) return 100;
    return Math.round((now - start) / (end - start) * 100);
  }
  $$payload.out += `<div class="overflow-hidden rounded-lg border"><!---->`;
  Accordion_item($$payload, {
    value: `incident-${index}`,
    class: "border-0",
    children: ($$payload2) => {
      $$payload2.out += `<div class="bg-gray-50 dark:bg-gray-900"><!---->`;
      Accordion_trigger($$payload2, {
        class: "flex w-full items-center justify-between p-4 text-left",
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex w-full items-center gap-4 pr-4"><div class="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">`;
          Circle_alert($$payload3, {
            class: "h-4 w-4 text-red-500 dark:text-red-400"
          });
          $$payload3.out += `<!----></div> <div class="flex w-full flex-col gap-1"><h3 class="text-lg font-medium">${escape_html(incident.title)}</h3> <div class="mt-1 flex items-center justify-between gap-2"><div class="flex flex-wrap gap-2">`;
          StatusTag($$payload3, { status: incident.status });
          $$payload3.out += `<!----> `;
          if (incident.severity) {
            $$payload3.out += "<!--[-->";
            SeverityBadge($$payload3, { severity: incident.severity });
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></div> <span class="text-muted-foreground text-xs">Started ${escape_html(formatDate(incident.date || incident.startTime))}</span></div></div></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Accordion_content($$payload2, {
        class: "p-4 pt-0",
        children: ($$payload3) => {
          $$payload3.out += `<div class="border-t pt-4">`;
          if (incident.startTime && incident.endTime) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="mb-4"><div class="mb-2 flex items-center justify-between"><div class="text-xs text-gray-500">${escape_html(formatDateWithYear(incident.startTime))} → ${escape_html(formatDateWithYear(incident.endTime))}</div> `;
            StatusTag($$payload3, { status: incident.status });
            $$payload3.out += `<!----></div> `;
            StatusBar($$payload3, {
              startTime: incident.startTime,
              endTime: incident.endTime,
              status: incident.status,
              progress: incident.progress || calculateProgress(),
              showTimes: false
            });
            $$payload3.out += `<!----></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> <p class="whitespace-pre-line text-sm">${escape_html(incident.description)}</p> `;
          if (incident.affectedServices && incident.affectedServices.length > 0) {
            $$payload3.out += "<!--[-->";
            const each_array = ensure_array_like(incident.affectedServices);
            $$payload3.out += `<div class="mt-4"><h4 class="mb-2 text-sm font-medium">Affected Services</h4> <div class="flex flex-wrap gap-2"><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let service = each_array[$$index];
              Badge($$payload3, {
                variant: "outline",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(service)}`;
                },
                $$slots: { default: true }
              });
            }
            $$payload3.out += `<!--]--></div></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (incident.updates && incident.updates.length > 0) {
            $$payload3.out += "<!--[-->";
            const each_array_1 = ensure_array_like(incident.updates);
            $$payload3.out += `<div class="mt-6"><h4 class="mb-3 text-sm font-medium">Updates</h4> <div class="space-y-4"><!--[-->`;
            for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
              let update = each_array_1[$$index_2];
              $$payload3.out += `<div class="relative pl-6 before:absolute before:left-0 before:top-0 before:h-full before:w-0.5 before:bg-gray-200 dark:before:bg-gray-800"><div class="absolute left-[-4px] top-1 h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700"></div> <div class="mb-1 flex items-center justify-between"><div class="text-muted-foreground text-xs">${escape_html(formatDate(update.date || update.timestamp))}</div> `;
              if (update.status) {
                $$payload3.out += "<!--[-->";
                StatusTag($$payload3, { status: update.status, className: "ml-2" });
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]--></div> <p class="text-sm">${escape_html(update.message)}</p> `;
              if (update.comments && update.comments.length > 0) {
                $$payload3.out += "<!--[-->";
                const each_array_2 = ensure_array_like(update.comments);
                $$payload3.out += `<div class="mt-3 border-t pt-2"><h5 class="mb-2 flex items-center gap-1 text-xs font-medium">`;
                Message_square($$payload3, { class: "h-3 w-3" });
                $$payload3.out += `<!----> Comments</h5> <div class="space-y-2"><!--[-->`;
                for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                  let comment = each_array_2[$$index_1];
                  $$payload3.out += `<div class="bg-muted rounded-sm p-2 text-xs"><div class="mb-1 flex items-center justify-between"><span class="font-medium">${escape_html(comment.author || "System")}</span> <span class="text-muted-foreground text-[10px]">${escape_html(new Date(comment.timestamp).toLocaleString())}</span></div> <p>${escape_html(comment.text)}</p></div>`;
                }
                $$payload3.out += `<!--]--></div></div>`;
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]--></div>`;
            }
            $$payload3.out += `<!--]--></div></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  pop();
}

export { MaintenanceAccordion as M };
//# sourceMappingURL=MaintenanceAccordion-hfzOCBt1.js.map
