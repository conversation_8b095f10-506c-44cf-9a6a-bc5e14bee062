import 'clsx';
import { p as push, q as pop } from './index3-CqUPEnZw.js';
import './false-CRHihH2U.js';

function _page($$payload, $$props) {
  push();
  $$payload.out += `<div class="container mx-auto py-8"><div class="mx-auto max-w-md"><h1 class="mb-6 text-2xl font-bold">Admin Access</h1> `;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="my-8 flex justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BMkSwWNj.js.map
