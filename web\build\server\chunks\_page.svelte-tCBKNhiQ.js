import { p as push, V as copy_payload, W as assign_payload, q as pop, P as stringify, O as escape_html, M as ensure_array_like, N as attr } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { B as Button } from './button-CrucCo1G.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { B as Badge } from './badge-C9pSznab.js';
import { I as Input } from './input-DF0gPqYN.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { g as goto } from './client-dNyMPa8V.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { S as Scroll_area } from './scroll-area-Dn69zlyp.js';
import { R as Root, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { S as Search } from './search-B0oHlTPS.js';
import { S as Settings } from './settings-STaOxCkl.js';
import { X } from './x-DwZgpWRG.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { B as Bell } from './bell-C9_YgkSj.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import { I as Info } from './info-Ce09B-Yv.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './hidden-input-1eDzjGOB.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './index2-Cut0V_vU.js';
import './use-debounce.svelte-gxToHznJ.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  let serverNotifications = data.notifications;
  let pagination = data.pagination;
  let filters = data.filters;
  let unreadCount = data.unreadCount;
  let selectedType = filters.type || "all";
  let includeRead = filters.includeRead || false;
  let loading = false;
  let filterLoading = false;
  let searchQuery = "";
  let showFilters = false;
  let searchFilteredNotifications = (() => {
    if (!searchQuery.trim()) {
      return serverNotifications;
    }
    const query = searchQuery.toLowerCase().trim();
    return serverNotifications.filter((notification) => notification.title.toLowerCase().includes(query) || notification.message.toLowerCase().includes(query) || notification.type && notification.type.toLowerCase().includes(query));
  })();
  let hasServerFilters = /* @__PURE__ */ (() => {
    return selectedType !== "all" || includeRead;
  })();
  let hasSearchFilter = (() => {
    return searchQuery.trim() !== "";
  })();
  let hasActiveFilters = /* @__PURE__ */ (() => {
    return hasServerFilters || hasSearchFilter;
  })();
  function getNotificationIcon(type) {
    switch (type) {
      case "job":
        return Briefcase;
      case "application":
        return Circle_check_big;
      case "interview":
        return Message_square;
      case "error":
        return Triangle_alert;
      case "success":
        return Circle_check_big;
      case "message":
        return Message_square;
      case "info":
      case "system":
      default:
        return Info;
    }
  }
  function formatDate(dateString) {
    const date = new Date(dateString);
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1e3);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    if (diffSecs < 60) {
      return "just now";
    } else if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }
  async function markAsRead(id) {
    try {
      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "markAsRead", id })
      });
      if (response.ok) {
        data.notifications = data.notifications.map((notif) => {
          if (notif.id === id) {
            return { ...notif, read: true };
          }
          return notif;
        });
        if (data.unreadCount > 0) {
          data.unreadCount--;
        }
      } else {
        toast.error("Failed to mark notification as read");
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark notification as read");
    }
  }
  async function deleteNotification(id) {
    try {
      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "delete", id })
      });
      if (response.ok) {
        const deletedNotif = data.notifications.find((n) => n.id === id);
        data.notifications = data.notifications.filter((notif) => notif.id !== id);
        if (deletedNotif && !deletedNotif.read && data.unreadCount > 0) {
          data.unreadCount--;
        }
        toast.success("Notification deleted");
      } else {
        toast.error("Failed to delete notification");
      }
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast.error("Failed to delete notification");
    }
  }
  async function markAllAsRead() {
    loading = true;
    try {
      const response = await fetch("/api/notifications/mark-all-read", { method: "POST" });
      if (response.ok) {
        data.notifications = data.notifications.map((notif) => ({ ...notif, read: true }));
        data.unreadCount = 0;
        toast.success("All notifications marked as read");
      } else {
        toast.error("Failed to mark all notifications as read");
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error("Failed to mark all notifications as read");
    } finally {
      loading = false;
    }
  }
  function applyFilters(newType, newIncludeRead) {
    filterLoading = true;
    const searchParams = new URLSearchParams();
    searchParams.set("page", "1");
    const typeToUse = newType !== void 0 ? newType : selectedType;
    const includeReadToUse = newIncludeRead !== void 0 ? newIncludeRead : includeRead;
    if (typeToUse !== "all") {
      searchParams.set("type", typeToUse);
    }
    if (includeReadToUse) {
      searchParams.set("includeRead", "true");
    }
    goto(`?${searchParams.toString()}`);
  }
  function clearFilters() {
    searchQuery = "";
    applyFilters("all", false);
  }
  function goToPage(pageNum) {
    filterLoading = true;
    const url = new URL(window.location.href);
    url.searchParams.set("page", pageNum.toString());
    goto(url.search);
  }
  serverNotifications.map((notif) => ({
    ...notif,
    timestamp: new Date(notif.createdAt)
  }));
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Notifications - Hirli",
      description: "Manage your notifications and stay up-to-date with the latest news and updates from Hirli."
    });
    $$payload2.out += `<!----> <div class="flex flex-col"><div class="border-border flex items-center justify-between border-b p-4"><h1 class="text-2xl font-bold">Notifications</h1> <div class="flex items-center gap-3">`;
    if (unreadCount > 0) {
      $$payload2.out += "<!--[-->";
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        onclick: markAllAsRead,
        disabled: loading,
        children: ($$payload3) => {
          if (loading) {
            $$payload3.out += "<!--[-->";
            Refresh_cw($$payload3, { class: "mr-2 h-4 w-4 animate-spin" });
          } else {
            $$payload3.out += "<!--[!-->";
            Circle_check_big($$payload3, { class: "mr-2 h-4 w-4" });
          }
          $$payload3.out += `<!--]--> Mark All as Read`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div></div> <div class="border-border border-b"><div class="flex items-center gap-3 px-4 py-3"><div class="relative flex-1">`;
    Search($$payload2, {
      class: "text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
    });
    $$payload2.out += `<!----> `;
    Input($$payload2, {
      type: "text",
      placeholder: "Search notifications...",
      class: "pl-10",
      get value() {
        return searchQuery;
      },
      set value($$value) {
        searchQuery = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----></div> `;
    Button($$payload2, {
      variant: "outline",
      size: "sm",
      onclick: () => showFilters = !showFilters,
      class: `gap-2 ${stringify(showFilters ? "!bg-primary/10 border-border/50 border" : "")}`,
      children: ($$payload3) => {
        Settings($$payload3, { class: "h-4 w-4" });
        $$payload3.out += `<!----> Filters `;
        if (hasServerFilters) {
          $$payload3.out += "<!--[-->";
          Badge($$payload3, {
            variant: "secondary",
            class: "ml-1 h-5 w-5 rounded-full p-0 text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html((selectedType !== "all" ? 1 : 0) + (includeRead ? 1 : 0))}`;
            },
            $$slots: { default: true }
          });
        } else {
          $$payload3.out += "<!--[!-->";
        }
        $$payload3.out += `<!--]-->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    if (hasActiveFilters) {
      $$payload2.out += "<!--[-->";
      Button($$payload2, {
        variant: "ghost",
        size: "sm",
        onclick: clearFilters,
        disabled: filterLoading,
        class: "gap-2",
        children: ($$payload3) => {
          if (filterLoading) {
            $$payload3.out += "<!--[-->";
            Refresh_cw($$payload3, { class: "h-4 w-4 animate-spin" });
          } else {
            $$payload3.out += "<!--[!-->";
            X($$payload3, { class: "h-4 w-4" });
          }
          $$payload3.out += `<!--]--> Clear`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> `;
    if (showFilters) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="border-border flex flex-wrap items-center gap-4 border-t px-4 py-3"><div class="flex items-center gap-2">`;
      Label($$payload2, {
        for: "type-select",
        class: "text-sm font-medium",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Type:`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Root($$payload2, {
        type: "single",
        value: selectedType,
        onValueChange: (value) => {
          applyFilters(value, void 0);
        },
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Select_trigger($$payload3, {
            class: "w-40",
            id: "type-select",
            disabled: filterLoading,
            children: ($$payload4) => {
              if (filterLoading) {
                $$payload4.out += "<!--[-->";
                Refresh_cw($$payload4, { class: "mr-2 h-4 w-4 animate-spin" });
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--> <!---->`;
              Select_value($$payload4, {
                placeholder: selectedType === "all" ? "All Types" : selectedType.charAt(0).toUpperCase() + selectedType.slice(1)
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Select_content($$payload3, {
            class: "max-h-60",
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Select_item($$payload4, {
                value: "all",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->All Types`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "system",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->System`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "job",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Job`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "application",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Application`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "interview",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Interview`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "message",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Message`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "success",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Success`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "error",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Error`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "warning",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Warning`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Select_item($$payload4, {
                value: "info",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Info`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <div class="flex items-center space-x-2">`;
      Switch($$payload2, {
        id: "includeRead",
        checked: includeRead,
        disabled: filterLoading,
        onCheckedChange: (checked) => {
          applyFilters(void 0, checked);
        }
      });
      $$payload2.out += `<!----> `;
      Label($$payload2, {
        for: "includeRead",
        class: "text-sm",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Include Read `;
          if (filterLoading) {
            $$payload3.out += "<!--[-->";
            Refresh_cw($$payload3, { class: "ml-2 inline h-3 w-3 animate-spin" });
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> `;
    if (hasSearchFilter) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="border-border border-b px-4 py-2"><p class="text-muted-foreground text-sm">Showing ${escape_html(searchFilteredNotifications.length)} of ${escape_html(serverNotifications.length)} notifications for
        "${escape_html(searchQuery)}"</p></div>`;
    } else if (hasServerFilters) {
      $$payload2.out += "<!--[1-->";
      $$payload2.out += `<div class="border-border border-b px-4 py-2"><p class="text-muted-foreground text-sm">Showing ${escape_html(serverNotifications.length)} filtered notifications `;
      if (selectedType !== "all") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `• Type: ${escape_html(selectedType.charAt(0).toUpperCase() + selectedType.slice(1))}`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (includeRead) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `• Including Read`;
      } else {
        $$payload2.out += "<!--[!-->";
        $$payload2.out += `• Unread Only`;
      }
      $$payload2.out += `<!--]--></p></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    if (searchFilteredNotifications.length === 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<!---->`;
      Card($$payload2, {
        class: "m-4",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_content($$payload3, {
            class: "flex flex-col items-center justify-center py-12",
            children: ($$payload4) => {
              Bell($$payload4, {
                class: "text-muted-foreground mb-4 h-12 w-12 opacity-20"
              });
              $$payload4.out += `<!----> <h2 class="mb-2 text-xl font-semibold">${escape_html(searchQuery.trim() ? "No matching notifications" : "No notifications")}</h2> <p class="text-muted-foreground">${escape_html(searchQuery.trim() ? "Try adjusting your search or filters." : "You don't have any notifications yet.")}</p> `;
              if (hasActiveFilters) {
                $$payload4.out += "<!--[-->";
                Button($$payload4, {
                  variant: "outline",
                  size: "sm",
                  onclick: clearFilters,
                  class: "mt-3",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Clear filters`;
                  },
                  $$slots: { default: true }
                });
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="relative">`;
      if (filterLoading) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="bg-background/80 absolute inset-0 z-10 flex items-center justify-center backdrop-blur-sm"><div class="bg-card flex items-center gap-3 rounded-lg border p-4 shadow-lg">`;
        Refresh_cw($$payload2, { class: "h-5 w-5 animate-spin" });
        $$payload2.out += `<!----> <span class="text-sm font-medium">Applying filters...</span></div></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> <!---->`;
      Scroll_area($$payload2, {
        orientation: "vertical",
        class: "h-[calc(100vh-240px)] overflow-hidden p-4",
        children: ($$payload3) => {
          const each_array = ensure_array_like(searchFilteredNotifications);
          $$payload3.out += `<div class="space-y-4"><!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let notification = each_array[$$index];
            const IconComponent = getNotificationIcon(notification.type);
            $$payload3.out += `<!---->`;
            Card($$payload3, {
              class: notification.read ? "opacity-80 transition-opacity hover:opacity-100" : "",
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Card_content($$payload4, {
                  class: "p-4",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-start gap-4"><div class="bg-primary/10 flex-shrink-0 rounded-full p-2"><!---->`;
                    IconComponent($$payload5, { class: "text-primary h-5 w-5" });
                    $$payload5.out += `<!----></div> <div class="flex-1"><div class="flex flex-wrap items-center gap-2"><h3 class="text-base font-medium">${escape_html(notification.title)}</h3> `;
                    if (!notification.read) {
                      $$payload5.out += "<!--[-->";
                      Badge($$payload5, {
                        variant: "default",
                        class: "h-1.5 w-1.5 rounded-full p-0"
                      });
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> `;
                    if (notification.global) {
                      $$payload5.out += "<!--[-->";
                      Badge($$payload5, {
                        variant: "outline",
                        class: "text-xs",
                        children: ($$payload6) => {
                          $$payload6.out += `<!---->Global`;
                        },
                        $$slots: { default: true }
                      });
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> `;
                    Badge($$payload5, {
                      variant: "outline",
                      class: "text-xs capitalize",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(notification.type || "info")}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div> <p class="text-muted-foreground mt-1 text-sm">${escape_html(notification.message)}</p> `;
                    if (notification.url) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<a${attr("href", notification.url)} class="text-primary hover:text-primary/80 mt-2 inline-block text-sm">View Details</a>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> <p class="text-muted-foreground mt-2 text-xs">${escape_html(formatDate(notification.createdAt))}</p></div> <div class="flex items-center gap-2">`;
                    if (!notification.read) {
                      $$payload5.out += "<!--[-->";
                      Button($$payload5, {
                        variant: "ghost",
                        size: "sm",
                        class: "h-8 w-8 p-0",
                        onclick: () => markAsRead(notification.id),
                        children: ($$payload6) => {
                          Circle_check_big($$payload6, { class: "h-4 w-4" });
                        },
                        $$slots: { default: true }
                      });
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> `;
                    Button($$payload5, {
                      variant: "ghost",
                      size: "sm",
                      class: "text-destructive hover:text-destructive/80 h-8 w-8 p-0",
                      onclick: () => deleteNotification(notification.id),
                      children: ($$payload6) => {
                        Trash_2($$payload6, { class: "h-4 w-4" });
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    }
    $$payload2.out += `<!--]--> `;
    if (pagination.totalPages > 1 && !searchQuery.trim()) {
      $$payload2.out += "<!--[-->";
      const each_array_1 = ensure_array_like(Array(pagination.totalPages));
      $$payload2.out += `<div class="mt-6 flex items-center justify-center gap-2">`;
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        disabled: pagination.page === 1,
        onclick: () => goToPage(pagination.page - 1),
        class: "gap-1",
        children: ($$payload3) => {
          Chevron_left($$payload3, { class: "h-4 w-4" });
          $$payload3.out += `<!----> Previous`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <div class="flex items-center gap-1"><!--[-->`;
      for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
        each_array_1[i];
        if (pagination.totalPages <= 7 || i + 1 === 1 || i + 1 === pagination.totalPages || i + 1 >= pagination.page - 1 && i + 1 <= pagination.page + 1) {
          $$payload2.out += "<!--[-->";
          Button($$payload2, {
            variant: pagination.page === i + 1 ? "default" : "outline",
            size: "sm",
            onclick: () => goToPage(i + 1),
            class: "h-8 w-8 p-0",
            children: ($$payload3) => {
              $$payload3.out += `<!---->${escape_html(i + 1)}`;
            },
            $$slots: { default: true }
          });
        } else if (i + 1 === 2 && pagination.page > 3 || i + 1 === pagination.totalPages - 1 && pagination.page < pagination.totalPages - 2) {
          $$payload2.out += "<!--[1-->";
          $$payload2.out += `<div class="flex h-8 w-8 items-center justify-center">...</div>`;
        } else {
          $$payload2.out += "<!--[!-->";
        }
        $$payload2.out += `<!--]-->`;
      }
      $$payload2.out += `<!--]--></div> `;
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        disabled: pagination.page === pagination.totalPages,
        onclick: () => goToPage(pagination.page + 1),
        class: "gap-1",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Next `;
          Chevron_right($$payload3, { class: "h-4 w-4" });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-tCBKNhiQ.js.map
