import { p as push, V as copy_payload, W as assign_payload, q as pop, M as ensure_array_like, aa as maybe_selected, N as attr, O as escape_html } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { F as FEATURES } from './dynamic-registry-Cmy1Wm2Q.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { C as Chart_container, B as BarChart, a as Chart_tooltip } from './chart-tooltip-RMuhks-A.js';
import 'date-fns';
import './index-DwwMqnhu.js';
import { D as Database } from './database-DTyOQm04.js';
import { S as Search } from './search-B0oHlTPS.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { D as Download } from './download-CLn66Ope.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './index4-HpJcNJHQ.js';
import './index2-Cut0V_vU.js';
import './html-FW6Ia4bL.js';
import './clone-BRGVxGEr.js';
import 'svelte/store';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let usageData = [];
  let summaryData = [];
  let loading = false;
  let summaryLoading = false;
  let error = null;
  let summaryError = null;
  let tablesExist = true;
  let checkingTables = false;
  const mockUsageData = [
    {
      id: "usage1",
      userId: "user1",
      user: {
        id: "user1",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        plan: { name: "Pro Plan" }
      },
      featureId: "job_search",
      featureName: "Job Search",
      limitId: "job_searches_per_month",
      limitName: "Job Searches Per Month",
      used: 45,
      limit: 100,
      remaining: 55,
      percentUsed: 45,
      period: "2023-11",
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    },
    {
      id: "usage2",
      userId: "user2",
      user: {
        id: "user2",
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        plan: { name: "Basic Plan" }
      },
      featureId: "resume_scanner",
      featureName: "Resume Scanner",
      limitId: "resume_scans_per_month",
      limitName: "Resume Scans Per Month",
      used: 18,
      limit: 20,
      remaining: 2,
      percentUsed: 90,
      period: "2023-11",
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    },
    {
      id: "usage3",
      userId: "user3",
      user: {
        id: "user3",
        firstName: "Robert",
        lastName: "Johnson",
        email: "<EMAIL>",
        plan: { name: "Premium Plan" }
      },
      featureId: "job_save",
      featureName: "Job Save",
      limitId: "saved_jobs",
      limitName: "Saved Jobs",
      used: 75,
      limit: 200,
      remaining: 125,
      percentUsed: 37.5,
      period: "2023-11",
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    },
    {
      id: "usage4",
      userId: "user4",
      user: {
        id: "user4",
        firstName: "Emily",
        lastName: "Williams",
        email: "<EMAIL>",
        plan: { name: "Free Plan" }
      },
      featureId: "resume_builder",
      featureName: "Resume Builder",
      limitId: "resume_versions",
      limitName: "Resume Versions",
      used: 3,
      limit: 3,
      remaining: 0,
      percentUsed: 100,
      period: "2023-11",
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    },
    {
      id: "usage5",
      userId: "user5",
      user: {
        id: "user5",
        firstName: "Michael",
        lastName: "Brown",
        email: "<EMAIL>",
        plan: { name: "Pro Plan" }
      },
      featureId: "application_tracker",
      featureName: "Application Tracker",
      limitId: "applications_per_month",
      limitName: "Applications Per Month",
      used: 12,
      limit: 50,
      remaining: 38,
      percentUsed: 24,
      period: "2023-11",
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    }
  ];
  const mockSummaryData = [
    {
      id: "job_search",
      name: "Job Search",
      totalUsed: 145,
      userCount: 28,
      periods: [{ period: "2023-11", used: 145 }]
    },
    {
      id: "resume_scanner",
      name: "Resume Scanner",
      totalUsed: 87,
      userCount: 15,
      periods: [{ period: "2023-11", used: 87 }]
    },
    {
      id: "job_save",
      name: "Job Save",
      totalUsed: 320,
      userCount: 42,
      periods: [{ period: "2023-11", used: 320 }]
    },
    {
      id: "resume_builder",
      name: "Resume Builder",
      totalUsed: 56,
      userCount: 22,
      periods: [{ period: "2023-11", used: 56 }]
    },
    {
      id: "application_tracker",
      name: "Application Tracker",
      totalUsed: 98,
      userCount: 18,
      periods: [{ period: "2023-11", used: 98 }]
    }
  ];
  let featureId = "";
  let limitId = "";
  let period = "";
  let userId = "";
  let planId = "";
  let page = 1;
  let limit = 50;
  let groupBy = "feature";
  let pagination = {
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasMore: false
  };
  let selectedFeature = FEATURES.find((f) => f.id === featureId);
  let availableLimits = selectedFeature?.limits || [];
  async function checkFeatureTables() {
    checkingTables = true;
    error = null;
    summaryError = null;
    try {
      const response = await fetch("/api/admin/feature-usage/check");
      if (!response.ok) throw new Error("Failed to check feature tables");
      const data = await response.json();
      tablesExist = data.tablesExist;
      if (tablesExist) {
        await Promise.all([fetchUsageData(), fetchSummaryData()]);
      } else {
        const errorMsg = "Feature usage tables do not exist yet. Start using features to generate usage data.";
        error = errorMsg;
        summaryError = errorMsg;
        loading = false;
        summaryLoading = false;
      }
    } catch (err) {
      console.error("Error checking feature tables:", err);
      const errorMsg = err.message || "Failed to check feature tables";
      error = errorMsg;
      summaryError = errorMsg;
      loading = false;
      summaryLoading = false;
    } finally {
      checkingTables = false;
    }
  }
  function buildQueryParams() {
    const params = new URLSearchParams();
    if (featureId) params.append("featureId", featureId);
    if (limitId) params.append("limitId", limitId);
    if (period) params.append("period", period);
    if (userId) params.append("userId", userId);
    if (planId) params.append("planId", planId);
    params.append("page", page.toString());
    params.append("limit", limit.toString());
    return params;
  }
  async function fetchUsageData() {
    loading = true;
    error = null;
    try {
      const params = buildQueryParams();
      const response = await fetch(`/api/admin/feature-usage?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Failed to fetch usage data");
      }
      const data = await response.json();
      usageData = data;
      pagination = {
        page,
        limit,
        total: data.length,
        // This should come from the API in a real implementation
        totalPages: Math.ceil(data.length / limit),
        hasMore: data.length > limit
      };
    } catch (err) {
      console.error("Error fetching usage data:", err);
      error = err.message || "Failed to fetch usage data";
      if (process.env.NODE_ENV !== "production") {
        console.log("Using mock data as fallback");
        usageData = mockUsageData;
        pagination = {
          page: 1,
          limit: 50,
          total: mockUsageData.length,
          totalPages: Math.ceil(mockUsageData.length / 50),
          hasMore: mockUsageData.length > 50
        };
      }
    } finally {
      loading = false;
    }
  }
  async function fetchSummaryData() {
    summaryLoading = true;
    summaryError = null;
    try {
      const params = new URLSearchParams();
      params.append("groupBy", groupBy);
      const response = await fetch(`/api/admin/feature-usage/summary?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Failed to fetch summary data");
      }
      const data = await response.json();
      summaryData = data;
    } catch (err) {
      console.error("Error fetching summary data:", err);
      summaryError = err.message || "Failed to fetch summary data";
      if (process.env.NODE_ENV !== "production") {
        console.log("Using mock summary data as fallback");
        summaryData = mockSummaryData;
      }
    } finally {
      summaryLoading = false;
    }
  }
  function applyFilters() {
    page = 1;
    fetchUsageData();
    fetchSummaryData();
  }
  function resetFilters() {
    featureId = "";
    limitId = "";
    period = "";
    userId = "";
    planId = "";
    page = 1;
    applyFilters();
  }
  function exportData(format = "csv") {
    if (!tablesExist) {
      toast.error("Feature usage tables do not exist yet. Cannot export data.");
      return;
    }
    const params = buildQueryParams();
    params.append("format", format);
    window.open(`/api/admin/feature-usage/export?${params.toString()}`, "_blank");
  }
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
  }
  function formatPeriod(period2) {
    if (!period2) return "N/A";
    const [year, month] = period2.split("-");
    if (month) {
      const date = new Date(parseInt(year), parseInt(month) - 1, 1);
      return date.toLocaleDateString("en-US", { month: "long", year: "numeric" });
    }
    return year;
  }
  function getUserName(user) {
    if (user.firstName || user.lastName) {
      return `${user.firstName || ""} ${user.lastName || ""}`.trim();
    }
    return user.email;
  }
  function goToPage(newPage) {
    page = newPage;
    fetchUsageData();
  }
  function nextPage() {
    if (page < pagination.totalPages) {
      page += 1;
      fetchUsageData();
    }
  }
  function prevPage() {
    if (page > 1) {
      page -= 1;
      fetchUsageData();
    }
  }
  const chartConfig = {
    totalUsed: { label: "Total Usage", color: "var(--chart-1)" },
    userCount: { label: "User Count", color: "var(--chart-2)" }
  };
  const usageChartData = () => {
    return summaryData.map((item) => ({ name: item.name, totalUsed: item.totalUsed }));
  };
  const userChartData = () => {
    return summaryData.map((item) => ({ name: item.name, userCount: item.userCount }));
  };
  async function initializeFeatureData() {
    try {
      toast.loading("Initializing feature data...");
      const response = await fetch("/api/admin/initialize-features", { method: "POST" });
      const result = await response.json();
      if (result.success) {
        toast.dismiss();
        toast.success("Feature data initialized successfully!");
        console.log("Feature initialization results:", result.results);
        await checkFeatureTables();
      } else {
        toast.dismiss();
        toast.error(`Failed to initialize feature data: ${result.error}`);
      }
    } catch (error2) {
      console.error("Error initializing feature data:", error2);
      toast.dismiss();
      toast.error(`Error initializing feature data: ${error2.message}`);
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, { title: "Feature Usage Admin" });
    $$payload2.out += `<!----> <div class="border-border flex flex-col gap-1 border-b p-4"><div class="flex items-center justify-between"><div class="flex flex-col"><h2 class="text-lg font-semibold">Feature Usage Admin</h2> <p class="text-foreground/70">Monitor and analyze feature usage across all users.</p></div> `;
    Button($$payload2, {
      variant: "outline",
      size: "sm",
      onclick: initializeFeatureData,
      children: ($$payload3) => {
        Database($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Initialize Feature Data`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> `;
    if (checkingTables) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex justify-center py-16">`;
      Loader_circle($$payload2, { class: "text-primary h-12 w-12 animate-spin" });
      $$payload2.out += `<!----></div>`;
    } else if (!tablesExist) {
      $$payload2.out += "<!--[1-->";
      $$payload2.out += `<!---->`;
      Card($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_header($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Feature Usage Not Available`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Feature usage tracking is not set up yet.`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex flex-col items-center justify-center py-8 text-center">`;
              Circle_alert($$payload4, { class: "text-muted-foreground mb-4 h-12 w-12" });
              $$payload4.out += `<!----> <h3 class="mb-2 text-xl font-semibold">No Feature Usage Data</h3> <p class="text-muted-foreground mb-6 max-w-md">Feature usage tables have not been created yet. You can initialize feature data to start
          tracking usage.</p> <div class="flex flex-col gap-2 sm:flex-row">`;
              Button($$payload4, {
                variant: "default",
                onclick: initializeFeatureData,
                children: ($$payload5) => {
                  Database($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Initialize Feature Data`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Button($$payload4, {
                variant: "outline",
                onclick: checkFeatureTables,
                children: ($$payload5) => {
                  Refresh_cw($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Check Again`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div></div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="space-y-8"><!---->`;
      Card($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_header($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Filters`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Filter feature usage data by various criteria.`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_content($$payload3, {
            children: ($$payload4) => {
              const each_array = ensure_array_like(FEATURES);
              const each_array_1 = ensure_array_like(availableLimits);
              $$payload4.out += `<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-4"><div class="space-y-2">`;
              Label($$payload4, {
                for: "feature",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Feature`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <div class="relative"><select id="feature" class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm">`;
              $$payload4.select_value = featureId;
              $$payload4.out += `<option value=""${maybe_selected($$payload4, "")}>All Features</option><!--[-->`;
              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                let feature = each_array[$$index];
                $$payload4.out += `<option${attr("value", feature.id)}${maybe_selected($$payload4, feature.id)}>${escape_html(feature.name)}</option>`;
              }
              $$payload4.out += `<!--]-->`;
              $$payload4.select_value = void 0;
              $$payload4.out += `</select> <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2"><svg class="h-4 w-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4 4 4-4"></path></svg></div></div></div> <div class="space-y-2">`;
              Label($$payload4, {
                for: "limit",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Limit`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <div class="relative"><select id="limit"${attr("disabled", !featureId, true)} class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm">`;
              $$payload4.select_value = limitId;
              $$payload4.out += `<option value=""${maybe_selected($$payload4, "")}>All Limits</option><!--[-->`;
              for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                let limit2 = each_array_1[$$index_1];
                $$payload4.out += `<option${attr("value", limit2.id)}${maybe_selected($$payload4, limit2.id)}>${escape_html(limit2.name)}</option>`;
              }
              $$payload4.out += `<!--]-->`;
              $$payload4.select_value = void 0;
              $$payload4.out += `</select> <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2"><svg class="h-4 w-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4 4 4-4"></path></svg></div></div></div> <div class="space-y-2">`;
              Label($$payload4, {
                for: "period",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Period`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Input($$payload4, {
                id: "period",
                type: "month",
                placeholder: "YYYY-MM",
                get value() {
                  return period;
                },
                set value($$value) {
                  period = $$value;
                  $$settled = false;
                }
              });
              $$payload4.out += `<!----></div> <div class="space-y-2">`;
              Label($$payload4, {
                for: "userId",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->User ID`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Input($$payload4, {
                id: "userId",
                placeholder: "User ID",
                get value() {
                  return userId;
                },
                set value($$value) {
                  userId = $$value;
                  $$settled = false;
                }
              });
              $$payload4.out += `<!----></div></div> <div class="mt-4 flex justify-end space-x-2">`;
              Button($$payload4, {
                variant: "outline",
                onclick: resetFilters,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Reset`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Button($$payload4, {
                onclick: applyFilters,
                children: ($$payload5) => {
                  Search($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Apply Filters`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_header($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex items-center justify-between"><div><!---->`;
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Usage Summary`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Overview of feature usage across all users.`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div> <div class="flex items-center space-x-2"><div class="relative w-[140px]"><select class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm">`;
              $$payload4.select_value = groupBy;
              $$payload4.out += `<option value="feature"${maybe_selected($$payload4, "feature")}>By Feature</option><option value="limit"${maybe_selected($$payload4, "limit")}>By Limit</option>`;
              $$payload4.select_value = void 0;
              $$payload4.out += `</select> <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2"><svg class="h-4 w-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4 4 4-4"></path></svg></div></div> `;
              Button($$payload4, {
                variant: "outline",
                size: "sm",
                onclick: fetchSummaryData,
                disabled: summaryLoading,
                children: ($$payload5) => {
                  Refresh_cw($$payload5, {
                    class: summaryLoading ? "h-4 w-4 animate-spin" : "h-4 w-4"
                  });
                  $$payload5.out += `<!----> <span class="sr-only">Refresh</span>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div></div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_content($$payload3, {
            children: ($$payload4) => {
              if (summaryLoading) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div class="flex justify-center py-8">`;
                Loader_circle($$payload4, { class: "text-primary h-8 w-8 animate-spin" });
                $$payload4.out += `<!----></div>`;
              } else if (summaryError) {
                $$payload4.out += "<!--[1-->";
                $$payload4.out += `<div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm"><p>Error loading summary data: ${escape_html(summaryError)}</p></div>`;
              } else if (summaryData.length === 0) {
                $$payload4.out += "<!--[2-->";
                $$payload4.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available.</p></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `<div class="grid gap-6 md:grid-cols-2"><div class="rounded-md border p-4"><h3 class="mb-4 text-lg font-medium">Total Usage by ${escape_html("Feature")}</h3> `;
                if (usageChartData().length === 0) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="flex h-[300px] items-center justify-center"><div class="text-muted-foreground text-sm">No data available</div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  $$payload4.out += `<!---->`;
                  Chart_container($$payload4, {
                    config: chartConfig,
                    class: "h-[300px] w-full",
                    children: ($$payload5) => {
                      {
                        let tooltip = function($$payload6) {
                          $$payload6.out += `<!---->`;
                          Chart_tooltip($$payload6, {});
                          $$payload6.out += `<!---->`;
                        };
                        BarChart($$payload5, {
                          data: usageChartData(),
                          x: "name",
                          axis: "x",
                          series: [
                            {
                              key: "totalUsed",
                              label: chartConfig.totalUsed.label,
                              color: chartConfig.totalUsed.color
                            }
                          ],
                          props: {
                            xAxis: { format: (d) => d.slice(0, 10) }
                          },
                          tooltip,
                          $$slots: { tooltip: true }
                        });
                      }
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                }
                $$payload4.out += `<!--]--></div> <div class="rounded-md border p-4"><h3 class="mb-4 text-lg font-medium">User Count by ${escape_html("Feature")}</h3> `;
                if (userChartData().length === 0) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="flex h-[300px] items-center justify-center"><div class="text-muted-foreground text-sm">No data available</div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  $$payload4.out += `<!---->`;
                  Chart_container($$payload4, {
                    config: chartConfig,
                    class: "h-[300px] w-full",
                    children: ($$payload5) => {
                      {
                        let tooltip = function($$payload6) {
                          $$payload6.out += `<!---->`;
                          Chart_tooltip($$payload6, {});
                          $$payload6.out += `<!---->`;
                        };
                        BarChart($$payload5, {
                          data: userChartData(),
                          x: "name",
                          axis: "x",
                          series: [
                            {
                              key: "userCount",
                              label: chartConfig.userCount.label,
                              color: chartConfig.userCount.color
                            }
                          ],
                          props: {
                            xAxis: { format: (d) => d.slice(0, 10) }
                          },
                          tooltip,
                          $$slots: { tooltip: true }
                        });
                      }
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                }
                $$payload4.out += `<!--]--></div></div>`;
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_header($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex items-center justify-between"><div><!---->`;
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Usage Data`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Detailed feature usage data for all users.`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div> <div class="flex flex-wrap items-center gap-2">`;
              Button($$payload4, {
                variant: "outline",
                size: "sm",
                onclick: () => exportData("csv"),
                children: ($$payload5) => {
                  Download($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Export CSV`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Button($$payload4, {
                variant: "outline",
                size: "sm",
                onclick: () => exportData("json"),
                children: ($$payload5) => {
                  Download($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Export JSON`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Button($$payload4, {
                variant: "outline",
                size: "sm",
                onclick: fetchUsageData,
                disabled: loading,
                children: ($$payload5) => {
                  Refresh_cw($$payload5, {
                    class: loading ? "h-4 w-4 animate-spin" : "h-4 w-4"
                  });
                  $$payload5.out += `<!----> <span class="sr-only">Refresh</span>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div></div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_content($$payload3, {
            children: ($$payload4) => {
              if (loading) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div class="flex justify-center py-8">`;
                Loader_circle($$payload4, { class: "text-primary h-8 w-8 animate-spin" });
                $$payload4.out += `<!----></div>`;
              } else if (error) {
                $$payload4.out += "<!--[1-->";
                $$payload4.out += `<div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm"><p>Error loading usage data: ${escape_html(error)}</p></div>`;
              } else if (usageData.length === 0) {
                $$payload4.out += "<!--[2-->";
                $$payload4.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available.</p></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
                const each_array_2 = ensure_array_like(usageData);
                $$payload4.out += `<div class="overflow-x-auto"><table class="w-full border-collapse"><thead><tr class="border-b"><th class="px-4 py-2 text-left">User</th><th class="px-4 py-2 text-left">Feature</th><th class="px-4 py-2 text-left">Limit</th><th class="px-4 py-2 text-right">Used</th><th class="px-4 py-2 text-left">Period</th><th class="px-4 py-2 text-left">Last Updated</th></tr></thead><tbody><!--[-->`;
                for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                  let usage = each_array_2[$$index_2];
                  $$payload4.out += `<tr class="hover:bg-muted/50 border-b"><td class="px-4 py-2"><div class="flex flex-col"><span class="font-medium">${escape_html(getUserName(usage.user))}</span> <span class="text-muted-foreground text-xs">${escape_html(usage.user.email)}</span> <span class="text-muted-foreground text-xs">Plan: ${escape_html(usage.user.plan?.name || "No Plan")}</span></div></td><td class="px-4 py-2">${escape_html(usage.featureName)}</td><td class="px-4 py-2">${escape_html(usage.limitName)}</td><td class="px-4 py-2 text-right font-medium">${escape_html(usage.used)}</td><td class="px-4 py-2">${escape_html(formatPeriod(usage.period))}</td><td class="px-4 py-2">${escape_html(formatDate(usage.updatedAt))}</td></tr>`;
                }
                $$payload4.out += `<!--]--></tbody></table></div> `;
                if (pagination.totalPages > 0) {
                  $$payload4.out += "<!--[-->";
                  const each_array_3 = ensure_array_like(Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNum = pagination.page <= 3 ? i + 1 : pagination.page >= pagination.totalPages - 2 ? pagination.totalPages - 4 + i : pagination.page - 2 + i;
                    return pageNum <= pagination.totalPages ? pageNum : null;
                  }).filter(Boolean));
                  $$payload4.out += `<div class="mt-4 flex flex-wrap items-center justify-between gap-4"><div class="text-muted-foreground text-sm">Showing ${escape_html((pagination.page - 1) * pagination.limit + 1)} to ${escape_html(Math.min(pagination.page * pagination.limit, pagination.total))} of ${escape_html(pagination.total)} entries</div> <div class="flex flex-wrap items-center gap-2">`;
                  Button($$payload4, {
                    variant: "outline",
                    size: "sm",
                    onclick: prevPage,
                    disabled: pagination.page === 1,
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->Previous`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!----> <!--[-->`;
                  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                    let pageNum = each_array_3[$$index_3];
                    Button($$payload4, {
                      variant: pageNum === pagination.page ? "default" : "outline",
                      size: "sm",
                      onclick: () => goToPage(pageNum),
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(pageNum)}`;
                      },
                      $$slots: { default: true }
                    });
                  }
                  $$payload4.out += `<!--]--> `;
                  Button($$payload4, {
                    variant: "outline",
                    size: "sm",
                    onclick: nextPage,
                    disabled: pagination.page === pagination.totalPages,
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->Next`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!----></div></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Ds-QOICy.js.map
