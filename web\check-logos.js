import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function checkLogos() {
  try {
    const companies = await prisma.company.findMany({
      where: { logoUrl: { not: null } },
      select: { name: true, logoUrl: true },
      take: 10,
    });

    console.log('Companies with logos:');
    companies.forEach((c) => {
      console.log(`- ${c.name}: ${c.logoUrl}`);

      // Extract filename for worker URL
      const filename = c.logoUrl.split('/').pop();
      console.log(
        `  Worker URL: https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/logos/${filename}`
      );
    });

    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

checkLogos();
