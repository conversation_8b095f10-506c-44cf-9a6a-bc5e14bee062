{"version": 3, "file": "77-CnkunBj2.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/search/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/77.js"], "sourcesContent": ["import { s as searchHelpArticles, g as getHelpArticles } from \"../../../../chunks/client2.js\";\nconst load = async ({ url }) => {\n  const query = url.searchParams.get(\"q\") || \"\";\n  try {\n    const searchResults = query ? await searchHelpArticles(query, 20) : [];\n    const allArticles = await getHelpArticles();\n    const articlesByCategory = allArticles.reduce((acc, article) => {\n      if (!acc[article.category]) {\n        acc[article.category] = {\n          name: getCategoryName(article.category),\n          slug: article.category,\n          icon: getCategoryIcon(article.category),\n          articles: []\n        };\n      }\n      acc[article.category].articles.push({\n        id: article._id,\n        title: article.title,\n        slug: article.slug.current\n      });\n      return acc;\n    }, {});\n    const categories = Object.values(articlesByCategory).sort(\n      (a, b) => a.name.localeCompare(b.name)\n    );\n    const formattedSearchResults = searchResults.map((article) => ({\n      ...article,\n      id: article._id,\n      slug: article.slug.current,\n      excerpt: article.description,\n      category: {\n        id: article.category,\n        name: getCategoryName(article.category),\n        slug: article.category,\n        icon: getCategoryIcon(article.category)\n      },\n      tags: article.tags?.map((tag) => ({\n        id: tag,\n        name: tag,\n        slug: tag.toLowerCase().replace(/\\s+/g, \"-\")\n      })) || []\n    }));\n    return {\n      query,\n      searchResults: formattedSearchResults,\n      categories,\n      resultCount: searchResults.length\n    };\n  } catch (error) {\n    console.error(\"Error searching help articles:\", error);\n    return {\n      query,\n      searchResults: [],\n      categories: [],\n      resultCount: 0\n    };\n  }\n};\nfunction getCategoryName(slug) {\n  const categoryMap = {\n    \"getting-started\": \"Getting Started\",\n    \"auto-apply\": \"Using Auto Apply\",\n    \"account-billing\": \"Account & Billing\",\n    troubleshooting: \"Troubleshooting\",\n    \"privacy-security\": \"Privacy & Security\"\n  };\n  return categoryMap[slug] || slug;\n}\nfunction getCategoryIcon(slug) {\n  const iconMap = {\n    \"getting-started\": \"BookOpen\",\n    \"auto-apply\": \"FileText\",\n    \"account-billing\": \"CreditCard\",\n    troubleshooting: \"HelpCircle\",\n    \"privacy-security\": \"Shield\"\n  };\n  return iconMap[slug] || \"HelpCircle\";\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/help/search/_page.server.ts.js';\n\nexport const index = 77;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/help/search/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/help/search/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/77.C1oE8xPH.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/C1V-jkz_.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/ByUTvV5u.js\",\"_app/immutable/chunks/CfcZq63z.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/jRvHGFcG.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/Cs0qIT7f.js\",\"_app/immutable/chunks/CBdr9r-W.js\",\"_app/immutable/chunks/BPr9JIwg.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/C8-oZ3V_.js\",\"_app/immutable/chunks/CsOU4yHs.js\",\"_app/immutable/chunks/BJwwRUaF.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/DETxXRrJ.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/CxmsTEaf.js\",\"_app/immutable/chunks/rNI1Perp.js\",\"_app/immutable/chunks/Ce6y1v79.js\"];\nexport const stylesheets = [\"_app/immutable/assets/scroll-area.bHHIbcsu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAChC,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;AAC/C,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE;AAC1E,IAAI,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE;AAC/C,IAAI,MAAM,kBAAkB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK;AACpE,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG;AAChC,UAAU,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,UAAU,IAAI,EAAE,OAAO,CAAC,QAAQ;AAChC,UAAU,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,UAAU,QAAQ,EAAE;AACpB,SAAS;AACT;AACA,MAAM,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC1C,QAAQ,EAAE,EAAE,OAAO,CAAC,GAAG;AACvB,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK;AAC5B,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;AAC3B,OAAO,CAAC;AACR,MAAM,OAAO,GAAG;AAChB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI;AAC7D,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;AAC3C,KAAK;AACL,IAAI,MAAM,sBAAsB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AACnE,MAAM,GAAG,OAAO;AAChB,MAAM,EAAE,EAAE,OAAO,CAAC,GAAG;AACrB,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO;AAChC,MAAM,OAAO,EAAE,OAAO,CAAC,WAAW;AAClC,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,OAAO,CAAC,QAAQ;AAC5B,QAAQ,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC/C,QAAQ,IAAI,EAAE,OAAO,CAAC,QAAQ;AAC9B,QAAQ,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ;AAC9C,OAAO;AACP,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM;AACxC,QAAQ,EAAE,EAAE,GAAG;AACf,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG;AACnD,OAAO,CAAC,CAAC,IAAI;AACb,KAAK,CAAC,CAAC;AACP,IAAI,OAAO;AACX,MAAM,KAAK;AACX,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,UAAU;AAChB,MAAM,WAAW,EAAE,aAAa,CAAC;AACjC,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO;AACX,MAAM,KAAK;AACX,MAAM,aAAa,EAAE,EAAE;AACvB,MAAM,UAAU,EAAE,EAAE;AACpB,MAAM,WAAW,EAAE;AACnB,KAAK;AACL;AACA,CAAC;AACD,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,iBAAiB,EAAE,iBAAiB;AACxC,IAAI,YAAY,EAAE,kBAAkB;AACpC,IAAI,iBAAiB,EAAE,mBAAmB;AAC1C,IAAI,eAAe,EAAE,iBAAiB;AACtC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;AAClC;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,iBAAiB,EAAE,UAAU;AACjC,IAAI,YAAY,EAAE,UAAU;AAC5B,IAAI,iBAAiB,EAAE,YAAY;AACnC,IAAI,eAAe,EAAE,YAAY;AACjC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY;AACtC;;;;;;;AC3EY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA8C,CAAC,EAAE;AAE5G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7wE,MAAC,WAAW,GAAG,CAAC,gDAAgD;AAChE,MAAC,KAAK,GAAG;;;;"}