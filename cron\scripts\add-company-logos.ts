// Add company logos from multiple sources (Clearbit + company websites) - Upload to R2 storage
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { uploadFile, BucketType } from "../lib/storage/r2Storage";

/**
 * Try to download logo from Clearbit
 */
async function tryDownloadFromClearbit(
  domain: string,
  companyName: string
): Promise<string | null> {
  try {
    const logoUrl = `https://logo.clearbit.com/${domain}`;
    logger.info(`   📥 Trying Clearbit: ${logoUrl}`);

    const response = await fetch(logoUrl);
    if (!response.ok) {
      logger.info(
        `   ❌ Clearbit logo not available for ${domain} (${response.status})`
      );
      return null;
    }

    // Get the image buffer
    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get("content-type") || "image/png";

    // Generate a clean filename
    const cleanCompanyName = companyName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

    const extension = contentType.includes("svg") ? "svg" : "png";
    const filename = `${cleanCompanyName}-${domain.replace(/\./g, "-")}.${extension}`;

    logger.info(`   📤 Uploading Clearbit logo to R2: ${filename}`);

    // Upload to R2 using our storage utility
    const uploadResult = await uploadFile(
      buffer,
      filename,
      contentType,
      "companyLogos"
    );

    if (uploadResult.success) {
      logger.info(
        `   ✅ Clearbit logo uploaded successfully: ${uploadResult.publicUrl}`
      );
      return uploadResult.publicUrl!;
    } else {
      logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
      return null;
    }
  } catch (error) {
    logger.error(`   ❌ Error with Clearbit logo for ${domain}:`, error);
    return null;
  }
}

// Global browser instance to reuse across searches
let globalBrowser: any = null;
let globalPage: any = null;

/**
 * Initialize browser once for all logo searches
 */
async function initializeBrowser() {
  if (!globalBrowser) {
    const { chromium } = await import("playwright");

    logger.info("🌐 Launching browser for logo searches...");
    globalBrowser = await chromium.launch({
      headless: false, // Turn off headless mode
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const context = await globalBrowser.newContext({
      userAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    });

    globalPage = await context.newPage();
  }
}

/**
 * Close browser when done with all searches
 */
async function closeBrowser() {
  if (globalBrowser) {
    await globalBrowser.close();
    globalBrowser = null;
    globalPage = null;
  }
}

/**
 * Try to find logo using Bing Images search
 */
async function tryBingImagesSearch(
  domain: string,
  companyName: string
): Promise<string | null> {
  try {
    logger.info(`   🔍 Searching Bing Images for: ${companyName}`);

    // Ensure browser is initialized
    await initializeBrowser();

    // Search for company logo on Bing Images
    const searchQuery = encodeURIComponent(`${domain} logo`);
    const bingUrl = `https://www.bing.com/images/search?q=${searchQuery}`;

    logger.info(`   🌐 Bing Images: ${bingUrl}`);

    await globalPage.goto(bingUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await globalPage.waitForTimeout(3000);

    // Extract image URLs from Bing Images results
    const imageUrls = await globalPage.evaluate(() => {
      // Bing Images uses specific selectors for the actual image results
      const imageElements = Array.from(document.querySelectorAll(".iusc"));
      const urls: string[] = [];

      imageElements.forEach((element: any) => {
        try {
          const dataAttr = element.getAttribute("m");
          if (dataAttr) {
            const data = JSON.parse(dataAttr);
            if (data.murl) {
              urls.push(data.murl);
            }
          }
        } catch (e) {
          // Skip invalid elements
        }
      });

      return urls.slice(0, 3); // Get first 3 images
    });

    if (imageUrls.length === 0) {
      logger.info(`   ❌ No images found in Bing Images results`);
      return null;
    }

    logger.info(`   🎯 Found ${imageUrls.length} potential logo images`);

    // Try the image URLs
    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];

      try {
        logger.info(`   📥 Trying image ${i + 1}: ${imageUrl}`);

        const imageResponse = await fetch(imageUrl, {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            Referer: "https://www.google.com/",
          },
          timeout: 8000, // 8 second timeout
        });

        if (
          imageResponse.ok &&
          imageResponse.headers.get("content-type")?.startsWith("image/")
        ) {
          const buffer = Buffer.from(await imageResponse.arrayBuffer());
          const contentType =
            imageResponse.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-bing-${Date.now()}.${extension}`;

          logger.info(`   📤 Uploading Bing logo to R2: ${filename}`);

          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(
              `   ✅ Bing logo uploaded successfully: ${uploadResult.publicUrl}`
            );
            return uploadResult.publicUrl!;
          } else {
            logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
          }
        }
      } catch (imageError) {
        logger.info(`   ⚠️ Failed to download image ${i + 1}: ${imageError}`);
        continue;
      }
    }

    logger.info(`   ❌ No valid logos found via Bing Images`);
    return null;
  } catch (error) {
    logger.error(
      `   ❌ Error searching Bing Images for ${companyName}:`,
      error
    );
    return null;
  }
}

/**
 * Check if a company is legitimate by validating domain and basic company info
 */
async function validateCompany(
  companyName: string,
  domain: string,
  website: string | null
): Promise<{ isValid: boolean; reason?: string }> {
  try {
    // Step 1: Check for obvious fake/spam indicators
    const suspiciousPatterns = [
      /test/i,
      /fake/i,
      /spam/i,
      /example/i,
      /placeholder/i,
      /temp/i,
      /dummy/i,
      /\d{10,}/, // Long numbers in company name
      /^[a-z]{1,3}$/i, // Very short names (1-3 chars)
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(companyName)) {
        return {
          isValid: false,
          reason: `Suspicious company name pattern: ${companyName}`,
        };
      }
    }

    // Step 2: Check domain format
    if (!domain || domain.length < 3) {
      return { isValid: false, reason: "Invalid domain format" };
    }

    // Step 3: Check for suspicious domains
    const suspiciousDomains = [
      "example.com",
      "test.com",
      "fake.com",
      "spam.com",
      "localhost",
      "127.0.0.1",
      "temp.com",
      "dummy.com",
    ];

    if (suspiciousDomains.includes(domain.toLowerCase())) {
      return { isValid: false, reason: `Suspicious domain: ${domain}` };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, reason: `Validation error: ${error}` };
  }
}

/**
 * Check if a domain exists using DNS lookup only (following cron pattern)
 * Avoids direct website access to prevent timeout issues
 */
async function checkDomainExists(domain: string): Promise<boolean> {
  try {
    // DNS-only validation (fast and reliable)
    const dns = await import("dns");
    const { promisify } = await import("util");
    const lookup = promisify(dns.lookup);

    try {
      await lookup(domain);
      return true;
    } catch (error) {
      // Try with www prefix
      try {
        await lookup(`www.${domain}`);
        return true;
      } catch (wwwError) {
        return false; // DNS doesn't exist at all
      }
    }
  } catch (error) {
    return false;
  }
}

/**
 * Extract domain from website URL
 */
function extractDomain(website: string): string | null {
  try {
    const url = new URL(
      website.startsWith("http") ? website : `https://${website}`
    );
    return url.hostname.replace(/^www\./, "");
  } catch (error) {
    return null;
  }
}

async function addCompanyLogos() {
  logger.info("🎨 Starting company logo processing");

  const prisma = new PrismaClient();

  try {
    // Get companies without logos that have websites, prioritizing those with active jobs
    // Since invalid companies were cleaned up, we can trust existing companies in DB
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: null,
        OR: [{ website: { not: null } }, { domain: { not: null } }],
        activeJobCount: { gt: 0 }, // Only companies with active jobs
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
        activeJobCount: true,
      },
      orderBy: {
        activeJobCount: "desc", // Process companies with most jobs first
      },
      // Process ALL companies with active jobs (no limit)
    });

    logger.info(`📊 Found ${companies.length} companies without logos`);

    if (companies.length === 0) {
      logger.info("✅ No companies need logos");
      return;
    }

    let logosAdded = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let processedCount = 0;

    logger.info(`🚀 Starting to process ${companies.length} companies...`);
    logger.info(
      `⏱️  Estimated time: ${Math.ceil((companies.length * 2) / 60)} minutes`
    );

    for (const company of companies) {
      processedCount++;

      // Progress logging every 10 companies
      if (processedCount % 10 === 0) {
        const progress = ((processedCount / companies.length) * 100).toFixed(1);
        const remaining = companies.length - processedCount;
        logger.info(
          `📈 Progress: ${processedCount}/${companies.length} (${progress}%) - ${remaining} remaining`
        );
        logger.info(
          `📊 Current stats: ✅ ${logosAdded} logos added, ⏭️ ${skippedCount} skipped, ❌ ${errorCount} errors`
        );
      }
      try {
        logger.info(
          `🏢 Processing: ${company.name} (${company.activeJobCount} jobs)`
        );

        // Try to get domain from company.domain first, then extract from website
        let domain = company.domain;
        if (!domain && company.website) {
          domain = extractDomain(company.website);
        }

        if (!domain) {
          logger.info(`   ❌ No valid domain for ${company.name}`);
          skippedCount++;
          continue;
        }

        logger.info(`   🌐 Processing domain: ${domain}`);

        // Step 1: Basic validation (companies in DB are already validated during scraping)
        logger.info(`   🔍 Basic validation for: ${company.name}`);
        const validation = await validateCompany(
          company.name,
          domain,
          company.website
        );

        if (!validation.isValid) {
          logger.info(`   ❌ Company validation failed: ${validation.reason}`);
          skippedCount++;
          continue;
        }

        // Skip domain existence check for companies already in DB (they were validated during scraping)
        logger.info(`   ✅ Company validated, proceeding with logo search`);

        // Try Method 1: Download Clearbit logo and upload to R2
        let r2LogoUrl = await tryDownloadFromClearbit(domain, company.name);

        // Try Method 2: If Clearbit fails, try Bing Images search
        if (!r2LogoUrl) {
          logger.info(`   🔄 Clearbit failed, trying Bing Images...`);
          r2LogoUrl = await tryBingImagesSearch(domain, company.name);
        }

        if (r2LogoUrl) {
          // Update company with R2 logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: r2LogoUrl,
              domain: domain, // Also update domain if it wasn't set
            },
          });

          logger.info(`   ✅ Added R2 logo: ${r2LogoUrl}`);
          logosAdded++;
        } else {
          logger.info(`   ❌ No logo found for ${domain} using any method`);
          skippedCount++;
        }

        // Small delay to be respectful to APIs and avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 100)); // 100ms delay
      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        errorCount++;
      }
    }

    // Summary
    logger.info("\n🎉 Company logo processing completed!");
    logger.info(`   ✅ Logos uploaded to R2: ${logosAdded}`);
    logger.info(`   ⏭️ Skipped: ${skippedCount}`);
    logger.info(`   ❌ Errors: ${errorCount}`);
    logger.info(`   📊 Total processed: ${companies.length}`);

    if (logosAdded > 0) {
      logger.info(
        `\n📈 Success rate: ${((logosAdded / companies.length) * 100).toFixed(1)}%`
      );
      logger.info(
        `🔗 All logos are now stored in R2 and accessible via worker URL`
      );
      logger.info(`📋 Methods used: Clearbit API + Bing Images search`);
    }
  } catch (error) {
    logger.error("❌ Error during logo processing:", error);
    throw error;
  } finally {
    // Close browser and disconnect from database
    await closeBrowser();
    await prisma.$disconnect();
  }
}

// Run the script
addCompanyLogos()
  .then(() => {
    logger.info("✅ Company logo script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Company logo script failed:", error);
    process.exit(1);
  });

export { addCompanyLogos };
