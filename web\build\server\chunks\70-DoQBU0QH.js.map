{"version": 3, "file": "70-DoQBU0QH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/usage/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/70.js"], "sourcesContent": ["import { r as redirect } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { getFeatureUsage } from \"../../../../../chunks/feature-usage.js\";\nimport { F as FEATURES } from \"../../../../../chunks/dynamic-registry.js\";\nimport { F as FeatureAccessLevel } from \"../../../../../chunks/features.js\";\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const userData = await prisma.user.findUnique({\n    where: { id: user.id },\n    select: {\n      id: true,\n      email: true,\n      name: true,\n      role: true,\n      image: true\n    }\n  });\n  if (!userData) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const simplifiedUser = {\n    id: userData.id,\n    email: userData.email,\n    name: userData.name,\n    role: userData.role,\n    image: userData.image\n  };\n  let featureUsage = [];\n  let usageTrends = {\n    currentMonthTotal: 0,\n    previousMonthTotal: 0,\n    trendPercent: 0,\n    trendDirection: \"stable\"\n  };\n  try {\n    const usageData = await getFeatureUsage(userData.id);\n    console.log(`Loaded ${usageData.length} feature usage records for user ${userData.id}`);\n    const now = /* @__PURE__ */ new Date();\n    const currentMonth = now.toISOString().substring(0, 7);\n    const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().substring(0, 7);\n    const currentMonthUsage = usageData.filter((u) => u.period === currentMonth);\n    const prevMonthUsage = usageData.filter((u) => u.period === prevMonth);\n    const currentTotalUsed = currentMonthUsage.reduce((sum, u) => sum + u.used, 0);\n    const prevTotalUsed = prevMonthUsage.reduce((sum, u) => sum + u.used, 0);\n    let usageTrendPercent = 0;\n    if (prevTotalUsed > 0) {\n      usageTrendPercent = Math.round((currentTotalUsed - prevTotalUsed) / prevTotalUsed * 100);\n    }\n    featureUsage = FEATURES.map((feature) => {\n      const featureUsageData = usageData.filter((usage2) => usage2.featureId === feature.id);\n      const usage = featureUsageData.length > 0 ? featureUsageData.map((usage2) => ({\n        limitId: usage2.limitId,\n        limitName: usage2.limitName,\n        used: usage2.used,\n        limit: usage2.limit === null ? \"unlimited\" : usage2.limit,\n        remaining: usage2.remaining,\n        percentUsed: usage2.percentUsed,\n        period: usage2.period,\n        description: usage2.description || \"\"\n      })) : feature.limits?.map((limit) => ({\n        limitId: limit.id,\n        limitName: limit.name,\n        used: 0,\n        limit: limit.defaultValue || \"unlimited\",\n        remaining: limit.defaultValue || \"unlimited\",\n        percentUsed: 0,\n        period: currentMonth,\n        description: limit.description || \"\",\n        placeholder: true\n        // Mark as placeholder\n      })) || [];\n      let accessLevel = FeatureAccessLevel.NotIncluded;\n      if (usage.length > 0) {\n        if (usage.some((u) => u.limit === \"unlimited\")) {\n          accessLevel = FeatureAccessLevel.Unlimited;\n        } else if (usage.every(\n          (u) => u.limit === \"unlimited\" || typeof u.limit === \"number\" && u.limit > 0\n        )) {\n          accessLevel = FeatureAccessLevel.Limited;\n        } else {\n          accessLevel = FeatureAccessLevel.Included;\n        }\n      }\n      const currentFeatureUsage = currentMonthUsage.filter((u) => u.featureId === feature.id).reduce((sum, u) => sum + u.used, 0);\n      const prevFeatureUsage = prevMonthUsage.filter((u) => u.featureId === feature.id).reduce((sum, u) => sum + u.used, 0);\n      let featureTrendPercent = 0;\n      if (prevFeatureUsage > 0) {\n        featureTrendPercent = Math.round(\n          (currentFeatureUsage - prevFeatureUsage) / prevFeatureUsage * 100\n        );\n      }\n      return {\n        ...feature,\n        usage,\n        accessLevel,\n        currentUsage: currentFeatureUsage,\n        previousUsage: prevFeatureUsage,\n        trendPercent: featureTrendPercent\n      };\n    });\n    usageTrends = {\n      currentMonthTotal: currentTotalUsed,\n      previousMonthTotal: prevTotalUsed,\n      trendPercent: usageTrendPercent,\n      trendDirection: usageTrendPercent > 0 ? \"up\" : usageTrendPercent < 0 ? \"down\" : \"stable\"\n    };\n    console.log(`Mapped ${featureUsage.length} features with usage data`);\n  } catch (error) {\n    console.error(\"Error loading feature usage:\", error);\n    featureUsage = FEATURES.map((feature) => ({\n      ...feature,\n      usage: feature.limits?.map((limit) => ({\n        limitId: limit.id,\n        limitName: limit.name,\n        used: 0,\n        limit: limit.defaultValue || \"unlimited\",\n        remaining: limit.defaultValue || \"unlimited\",\n        percentUsed: 0,\n        period: (/* @__PURE__ */ new Date()).toISOString().substring(0, 7),\n        description: limit.description || \"\",\n        placeholder: true\n      })) || [],\n      accessLevel: FeatureAccessLevel.NotIncluded,\n      currentUsage: 0,\n      previousUsage: 0,\n      trendPercent: 0\n    }));\n  }\n  return {\n    user: simplifiedUser,\n    featureUsage,\n    usageTrends\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/usage/_page.server.ts.js';\n\nexport const index = 70;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/usage/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/usage/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/70.8M8SCzH5.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/XnZcpgwi.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/Dq03aqGn.js\",\"_app/immutable/chunks/BHEV2D3b.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/BYB878do.js\",\"_app/immutable/chunks/BQ5jqT_2.js\",\"_app/immutable/chunks/CbynRejM.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Csk_I0QV.js\",\"_app/immutable/chunks/lirlZJ-b.js\",\"_app/immutable/chunks/DrGkVJ95.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/B-l1ubNa.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/CTO_B1Jk.js\",\"_app/immutable/chunks/iTBjRg9v.js\",\"_app/immutable/chunks/DigM95rE.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/DmZyh-PW.js\",\"_app/immutable/chunks/BwkAotBa.js\"];\nexport const stylesheets = [\"_app/immutable/assets/chart-tooltip.BTdU6mpn.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;;;;;AAKA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,KAAK,EAAE;AACb;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI,EAAE,EAAE,QAAQ,CAAC,EAAE;AACnB,IAAI,KAAK,EAAE,QAAQ,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI;AACvB,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI;AACvB,IAAI,KAAK,EAAE,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,kBAAkB,EAAE,CAAC;AACzB,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,cAAc,EAAE;AACpB,GAAG;AACH,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;AACxD,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,gCAAgC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3F,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1D,IAAI,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AACtG,IAAI,MAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,YAAY,CAAC;AAChF,IAAI,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC;AAC1E,IAAI,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAClF,IAAI,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5E,IAAI,IAAI,iBAAiB,GAAG,CAAC;AAC7B,IAAI,IAAI,aAAa,GAAG,CAAC,EAAE;AAC3B,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,GAAG,aAAa,IAAI,aAAa,GAAG,GAAG,CAAC;AAC9F;AACA,IAAI,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AAC7C,MAAM,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC;AAC5F,MAAM,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACpF,QAAQ,OAAO,EAAE,MAAM,CAAC,OAAO;AAC/B,QAAQ,SAAS,EAAE,MAAM,CAAC,SAAS;AACnC,QAAQ,IAAI,EAAE,MAAM,CAAC,IAAI;AACzB,QAAQ,KAAK,EAAE,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,WAAW,GAAG,MAAM,CAAC,KAAK;AACjE,QAAQ,SAAS,EAAE,MAAM,CAAC,SAAS;AACnC,QAAQ,WAAW,EAAE,MAAM,CAAC,WAAW;AACvC,QAAQ,MAAM,EAAE,MAAM,CAAC,MAAM;AAC7B,QAAQ,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI;AAC3C,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,MAAM;AAC5C,QAAQ,OAAO,EAAE,KAAK,CAAC,EAAE;AACzB,QAAQ,SAAS,EAAE,KAAK,CAAC,IAAI;AAC7B,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,KAAK,EAAE,KAAK,CAAC,YAAY,IAAI,WAAW;AAChD,QAAQ,SAAS,EAAE,KAAK,CAAC,YAAY,IAAI,WAAW;AACpD,QAAQ,WAAW,EAAE,CAAC;AACtB,QAAQ,MAAM,EAAE,YAAY;AAC5B,QAAQ,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AAC5C,QAAQ,WAAW,EAAE;AACrB;AACA,OAAO,CAAC,CAAC,IAAI,EAAE;AACf,MAAM,IAAI,WAAW,GAAG,kBAAkB,CAAC,WAAW;AACtD,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,WAAW,CAAC,EAAE;AACxD,UAAU,WAAW,GAAG,kBAAkB,CAAC,SAAS;AACpD,SAAS,MAAM,IAAI,KAAK,CAAC,KAAK;AAC9B,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,WAAW,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,GAAG;AACrF,SAAS,EAAE;AACX,UAAU,WAAW,GAAG,kBAAkB,CAAC,OAAO;AAClD,SAAS,MAAM;AACf,UAAU,WAAW,GAAG,kBAAkB,CAAC,QAAQ;AACnD;AACA;AACA,MAAM,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACjI,MAAM,MAAM,gBAAgB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3H,MAAM,IAAI,mBAAmB,GAAG,CAAC;AACjC,MAAM,IAAI,gBAAgB,GAAG,CAAC,EAAE;AAChC,QAAQ,mBAAmB,GAAG,IAAI,CAAC,KAAK;AACxC,UAAU,CAAC,mBAAmB,GAAG,gBAAgB,IAAI,gBAAgB,GAAG;AACxE,SAAS;AACT;AACA,MAAM,OAAO;AACb,QAAQ,GAAG,OAAO;AAClB,QAAQ,KAAK;AACb,QAAQ,WAAW;AACnB,QAAQ,YAAY,EAAE,mBAAmB;AACzC,QAAQ,aAAa,EAAE,gBAAgB;AACvC,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,WAAW,GAAG;AAClB,MAAM,iBAAiB,EAAE,gBAAgB;AACzC,MAAM,kBAAkB,EAAE,aAAa;AACvC,MAAM,YAAY,EAAE,iBAAiB;AACrC,MAAM,cAAc,EAAE,iBAAiB,GAAG,CAAC,GAAG,IAAI,GAAG,iBAAiB,GAAG,CAAC,GAAG,MAAM,GAAG;AACtF,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACzE,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AAC9C,MAAM,GAAG,OAAO;AAChB,MAAM,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,MAAM;AAC7C,QAAQ,OAAO,EAAE,KAAK,CAAC,EAAE;AACzB,QAAQ,SAAS,EAAE,KAAK,CAAC,IAAI;AAC7B,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,KAAK,EAAE,KAAK,CAAC,YAAY,IAAI,WAAW;AAChD,QAAQ,SAAS,EAAE,KAAK,CAAC,YAAY,IAAI,WAAW;AACpD,QAAQ,WAAW,EAAE,CAAC;AACtB,QAAQ,MAAM,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1E,QAAQ,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AAC5C,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC,CAAC,IAAI,EAAE;AACf,MAAM,WAAW,EAAE,kBAAkB,CAAC,WAAW;AACjD,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,aAAa,EAAE,CAAC;AACtB,MAAM,YAAY,EAAE;AACpB,KAAK,CAAC,CAAC;AACP;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;ACtIW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA2D,CAAC,EAAE;AAEzH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzuE,MAAC,WAAW,GAAG,CAAC,kDAAkD;AAClE,MAAC,KAAK,GAAG;;;;"}