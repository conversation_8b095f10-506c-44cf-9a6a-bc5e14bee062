import { p as push, q as pop, O as escape_html, M as ensure_array_like, P as stringify, Q as bind_props, N as attr, aa as maybe_selected, K as fallback, J as attr_class } from './index3-CqUPEnZw.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { R as Root$1, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { B as Button } from './button-CrucCo1G.js';
import { B as Badge } from './badge-C9pSznab.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { a as FeatureAccessLevel, F as FeatureCategory, L as LimitType } from './features-SWeUHekJ.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { A as Accordion_root, a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from './accordion-trigger-DwieKZVA.js';
import { C as Chevron_down } from './chevron-down-xGjWLrZH.js';
import { D as Dropdown_menu_item } from './dropdown-menu-item-DwivDmnZ.js';
import { C as Check } from './check-WP_4Msti.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import { D as Dropdown_menu_separator } from './dropdown-menu-separator-B5VQzuNH.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import { S as Save } from './save-Cfytkt-w.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './use-grace-area.svelte-CrXiOQDy.js';
import './index-DjwFQdT_.js';
import './index2-Cut0V_vU.js';
import './Icon-A4vzmk-O.js';
import './hidden-input-1eDzjGOB.js';

function FeatureAccessDropdown($$payload, $$props) {
  push();
  let currentLabel, buttonClass;
  let featureId = $$props["featureId"];
  let value = fallback($$props["value"], () => FeatureAccessLevel.NotIncluded, true);
  let onChange = $$props["onChange"];
  const accessLevels = [
    {
      value: FeatureAccessLevel.NotIncluded,
      label: "Not Included"
    },
    {
      value: FeatureAccessLevel.Included,
      label: "Included"
    },
    {
      value: FeatureAccessLevel.Limited,
      label: "Limited"
    },
    {
      value: FeatureAccessLevel.Unlimited,
      label: "Unlimited"
    }
  ];
  function handleSelect(newValue) {
    if (value !== newValue) {
      value = newValue;
      onChange(featureId, newValue);
    }
  }
  function getButtonClass(accessLevel) {
    switch (accessLevel) {
      case FeatureAccessLevel.NotIncluded:
        return "bg-gray-100 text-gray-700 hover:bg-gray-200";
      case FeatureAccessLevel.Included:
        return "bg-blue-100 text-blue-700 hover:bg-blue-200";
      case FeatureAccessLevel.Limited:
        return "bg-amber-100 text-amber-700 hover:bg-amber-200";
      case FeatureAccessLevel.Unlimited:
        return "bg-green-100 text-green-700 hover:bg-green-200";
      default:
        return "bg-gray-100 text-gray-700 hover:bg-gray-200";
    }
  }
  currentLabel = accessLevels.find((level) => level.value === value)?.label || "Not Included";
  buttonClass = getButtonClass(value);
  Root($$payload, {
    children: ($$payload2) => {
      Dropdown_menu_trigger($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<button${attr_class(`flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium ${stringify(buttonClass)} w-full`)} aria-label="Select feature access level"><span>${escape_html(currentLabel)}</span> `;
          Chevron_down($$payload3, { class: "h-4 w-4 ml-2" });
          $$payload3.out += `<!----></button>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Dropdown_menu_content($$payload2, {
        align: "start",
        class: "w-48",
        children: ($$payload3) => {
          const each_array = ensure_array_like(accessLevels);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let level = each_array[$$index];
            Dropdown_menu_item($$payload3, {
              onclick: () => handleSelect(level.value),
              class: "flex items-center cursor-pointer",
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex-1">${escape_html(level.label)}</div> `;
                if (value === level.value) {
                  $$payload4.out += "<!--[-->";
                  Check($$payload4, { class: "h-4 w-4 text-green-500" });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  bind_props($$props, { featureId, value, onChange });
  pop();
}
function FeatureLimitInput($$payload, $$props) {
  push();
  let displayValue;
  let featureId = $$props["featureId"];
  let limit = $$props["limit"];
  let value = fallback($$props["value"], () => limit.defaultValue, true);
  let onChange = $$props["onChange"];
  const commonValues = [
    { value: 5, label: "5" },
    { value: 10, label: "10" },
    { value: 25, label: "25" },
    { value: 50, label: "50" },
    { value: 100, label: "100" },
    { value: "unlimited", label: "Unlimited" }
  ];
  function handleSelect(newValue) {
    if (value !== newValue) {
      value = newValue;
      onChange(featureId, limit.id, newValue);
    }
  }
  displayValue = value === "unlimited" ? "Unlimited" : value.toString();
  $$payload.out += `<div class="flex items-center gap-2"><div class="flex-1">`;
  Label($$payload, {
    for: `${featureId}-${limit.id}`,
    class: "text-xs",
    children: ($$payload2) => {
      $$payload2.out += `<!---->${escape_html(limit.name)}`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <div class="text-muted-foreground text-xs">${escape_html(limit.description)}</div></div> <div class="w-32"><div class="flex items-center gap-2"><div class="relative w-full"><input${attr("id", `${featureId}-${limit.id}`)} type="text"${attr("value", displayValue)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 pr-8"/> `;
  Root($$payload, {
    children: ($$payload2) => {
      Dropdown_menu_trigger($$payload2, {
        class: "absolute right-1 top-1/2 -translate-y-1/2",
        children: ($$payload3) => {
          $$payload3.out += `<button type="button" class="h-6 w-6 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-gray-100 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring" aria-label="Select limit value">`;
          Chevron_down($$payload3, { class: "h-4 w-4" });
          $$payload3.out += `<!----></button>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Dropdown_menu_content($$payload2, {
        align: "end",
        class: "w-32",
        children: ($$payload3) => {
          const each_array = ensure_array_like(commonValues);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let option = each_array[$$index];
            Dropdown_menu_item($$payload3, {
              onclick: () => handleSelect(option.value),
              class: "flex items-center cursor-pointer",
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex-1">${escape_html(option.label)} ${escape_html(limit.unit || "")}</div> `;
                if (value === option.value) {
                  $$payload4.out += "<!--[-->";
                  Check($$payload4, { class: "h-4 w-4 text-green-500" });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="text-muted-foreground text-xs">${escape_html(limit.unit || "")}</div></div></div></div>`;
  bind_props($$props, { featureId, limit, value, onChange });
  pop();
}
function FeaturesTabContent($$payload, $$props) {
  push();
  function getCategoryBasedLimitName(category) {
    switch (category) {
      case FeatureCategory.Resume:
        return "Monthly Usage";
      case FeatureCategory.JobSearch:
        return "Saved Items";
      case FeatureCategory.Applications:
        return "Monthly Applications";
      case FeatureCategory.Team:
        return "Team Members";
      case FeatureCategory.Integration:
        return "API Calls";
      case FeatureCategory.Analytics:
        return "Reports";
      default:
        return "Usage Limit";
    }
  }
  function getCategoryBasedLimitDescription(category) {
    switch (category) {
      case FeatureCategory.Resume:
        return "Maximum number of uses per month";
      case FeatureCategory.JobSearch:
        return "Maximum number of items you can save";
      case FeatureCategory.Applications:
        return "Maximum number of applications per month";
      case FeatureCategory.Team:
        return "Maximum number of team members";
      case FeatureCategory.Integration:
        return "Maximum number of API calls per month";
      case FeatureCategory.Analytics:
        return "Maximum number of reports you can create";
      default:
        return "Maximum usage limit for this feature";
    }
  }
  function getCategoryBasedDefaultValue(category) {
    switch (category) {
      case FeatureCategory.Resume:
        return 10;
      case FeatureCategory.JobSearch:
        return 25;
      case FeatureCategory.Applications:
        return 20;
      case FeatureCategory.Team:
        return 3;
      case FeatureCategory.Integration:
        return 100;
      case FeatureCategory.Analytics:
        return 5;
      default:
        return 10;
    }
  }
  function getCategoryBasedLimitType(category) {
    switch (category) {
      case FeatureCategory.Resume:
        return LimitType.Monthly;
      case FeatureCategory.JobSearch:
        return LimitType.Total;
      case FeatureCategory.Applications:
        return LimitType.Monthly;
      case FeatureCategory.Team:
        return LimitType.Concurrent;
      case FeatureCategory.Integration:
        return LimitType.Monthly;
      case FeatureCategory.Analytics:
        return LimitType.Total;
      default:
        return LimitType.Total;
    }
  }
  function getCategoryBasedLimitUnit(category) {
    switch (category) {
      case FeatureCategory.Resume:
        return "uses";
      case FeatureCategory.JobSearch:
        return "items";
      case FeatureCategory.Applications:
        return "applications";
      case FeatureCategory.Team:
        return "members";
      case FeatureCategory.Integration:
        return "calls";
      case FeatureCategory.Analytics:
        return "reports";
      default:
        return "uses";
    }
  }
  const selectedPlan = void 0;
  let featuresByCategory = $$props["featuresByCategory"];
  let expandedCategories = $$props["expandedCategories"];
  let getFeatureAccessLevel = $$props["getFeatureAccessLevel"];
  let getFeatureLimitValue = $$props["getFeatureLimitValue"];
  let updateFeatureAccessLevel = $$props["updateFeatureAccessLevel"];
  let updateFeatureLimitValue = $$props["updateFeatureLimitValue"];
  $$payload.out += `<div class="force-overflow-y-auto max-h-[calc(100vh-300px)] overflow-y-auto p-4">`;
  Accordion_root($$payload, {
    class: "border-border w-full border",
    type: "multiple",
    value: expandedCategories,
    children: ($$payload2) => {
      const each_array = ensure_array_like(Object.entries(featuresByCategory));
      $$payload2.out += `<!--[-->`;
      for (let $$index_2 = 0, $$length = each_array.length; $$index_2 < $$length; $$index_2++) {
        let [category, features] = each_array[$$index_2];
        Accordion_item($$payload2, {
          value: category,
          children: ($$payload3) => {
            Accordion_trigger($$payload3, {
              class: "px-4 text-base capitalize",
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(category.replace("_", " "))} Features`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Accordion_content($$payload3, {
              class: "border-border flex flex-col border-t",
              children: ($$payload4) => {
                const each_array_1 = ensure_array_like(features);
                $$payload4.out += `<!--[-->`;
                for (let $$index_1 = 0, $$length2 = each_array_1.length; $$index_1 < $$length2; $$index_1++) {
                  let feature = each_array_1[$$index_1];
                  $$payload4.out += `<div class="grid grid-cols-12 items-center gap-4 p-4 pb-4"><div class="col-span-4"><div class="font-medium">${escape_html(feature.name)}</div> <div class="text-muted-foreground text-sm">${escape_html(feature.description)}</div></div> <div class="col-span-3">`;
                  FeatureAccessDropdown($$payload4, {
                    featureId: feature.id,
                    value: getFeatureAccessLevel(feature.id),
                    onChange: updateFeatureAccessLevel
                  });
                  $$payload4.out += `<!----></div> <div class="col-span-5">`;
                  if (getFeatureAccessLevel(feature.id) === FeatureAccessLevel.Limited) {
                    $$payload4.out += "<!--[-->";
                    $$payload4.out += `<div class="space-y-2">`;
                    if (feature.limits && feature.limits.length > 0) {
                      $$payload4.out += "<!--[-->";
                      const each_array_2 = ensure_array_like(feature.limits);
                      $$payload4.out += `<!--[-->`;
                      for (let $$index = 0, $$length3 = each_array_2.length; $$index < $$length3; $$index++) {
                        let limit = each_array_2[$$index];
                        FeatureLimitInput($$payload4, {
                          featureId: feature.id,
                          limit,
                          value: getFeatureLimitValue(feature.id, limit.id) || limit.defaultValue,
                          onChange: updateFeatureLimitValue
                        });
                      }
                      $$payload4.out += `<!--]-->`;
                    } else {
                      $$payload4.out += "<!--[!-->";
                      FeatureLimitInput($$payload4, {
                        featureId: feature.id,
                        limit: {
                          id: `${feature.id}_limit`,
                          name: getCategoryBasedLimitName(feature.category),
                          description: getCategoryBasedLimitDescription(feature.category),
                          defaultValue: getCategoryBasedDefaultValue(feature.category),
                          type: getCategoryBasedLimitType(feature.category),
                          unit: getCategoryBasedLimitUnit(feature.category)
                        },
                        value: getFeatureLimitValue(feature.id, `${feature.id}_limit`) || getCategoryBasedDefaultValue(feature.category),
                        onChange: updateFeatureLimitValue
                      });
                    }
                    $$payload4.out += `<!--]--></div>`;
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]--></div></div>`;
                }
                $$payload4.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  bind_props($$props, {
    featuresByCategory,
    expandedCategories,
    getFeatureAccessLevel,
    getFeatureLimitValue,
    updateFeatureAccessLevel,
    updateFeatureLimitValue,
    selectedPlan
  });
  pop();
}
function PlanDetailsTabContent($$payload, $$props) {
  push();
  let selectedPlan = $$props["selectedPlan"];
  let syncPlanWithStripe = $$props["syncPlanWithStripe"];
  let syncingWithStripe = $$props["syncingWithStripe"];
  let stripeMessage = $$props["stripeMessage"];
  function handleInputChange(field, value) {
    selectedPlan[field] = value;
    selectedPlan = { ...selectedPlan };
  }
  $$payload.out += `<div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div>`;
  Label($$payload, {
    for: "plan-name",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Plan Name`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="plan-name"${attr("value", selectedPlan.name)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
  Label($$payload, {
    for: "plan-id",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Plan ID`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="plan-id"${attr("value", selectedPlan.id)} readonly class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div></div> <div>`;
  Label($$payload, {
    for: "plan-description",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Description`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="plan-description"${attr("value", selectedPlan.description)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="grid grid-cols-2 gap-4"><div>`;
  Label($$payload, {
    for: "monthly-price",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Monthly Price (cents)`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="monthly-price" type="number"${attr("value", selectedPlan.monthlyPrice)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
  Label($$payload, {
    for: "annual-price",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Annual Price (cents)`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="annual-price" type="number"${attr("value", selectedPlan.annualPrice)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div></div> <div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div>`;
  Label($$payload, {
    for: "stripe-monthly-id",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Stripe Monthly Price ID`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="stripe-monthly-id"${attr("value", selectedPlan.stripePriceMonthlyId || "")} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
  Label($$payload, {
    for: "stripe-yearly-id",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Stripe Yearly Price ID`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <input id="stripe-yearly-id"${attr("value", selectedPlan.stripePriceYearlyId || "")} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div></div> <div>`;
  Button($$payload, {
    variant: "outline",
    onclick: syncPlanWithStripe,
    disabled: syncingWithStripe,
    class: "w-full",
    children: ($$payload2) => {
      Refresh_cw($$payload2, {
        class: `mr-2 h-4 w-4 ${syncingWithStripe ? "animate-spin" : ""}`
      });
      $$payload2.out += `<!----> Sync with Stripe`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  if (stripeMessage) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mt-2 text-sm text-green-600">${escape_html(stripeMessage)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="flex items-center space-x-2"><div class="flex items-center space-x-2">`;
  Switch($$payload, {
    id: "popular",
    checked: selectedPlan.popular || false,
    onCheckedChange: (checked) => handleInputChange("popular", checked)
  });
  $$payload.out += `<!----> `;
  Label($$payload, {
    for: "popular",
    class: "cursor-pointer",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Mark as popular plan`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div>`;
  Label($$payload, {
    for: "section",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Section`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <div class="relative"><select id="section" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50">`;
  $$payload.select_value = selectedPlan.section;
  $$payload.out += `<option value="pro"${maybe_selected($$payload, "pro")}>Pro</option><option value="teams"${maybe_selected($$payload, "teams")}>Teams</option>`;
  $$payload.select_value = void 0;
  $$payload.out += `</select></div></div></div>`;
  bind_props($$props, {
    selectedPlan,
    syncPlanWithStripe,
    syncingWithStripe,
    stripeMessage
  });
  pop();
}
function PreviewTabContent($$payload, $$props) {
  push();
  let selectedPlan = $$props["selectedPlan"];
  let formatPrice = $$props["formatPrice"];
  let features = $$props["features"];
  const each_array = ensure_array_like(selectedPlan.features.filter((f) => f.accessLevel !== FeatureAccessLevel.NotIncluded));
  $$payload.out += `<div class="space-y-6"><div class="rounded-lg border p-6"><h3 class="mb-2 text-lg font-medium">${escape_html(selectedPlan.name)}</h3> <p class="text-3xl font-bold">$${escape_html(formatPrice(selectedPlan.monthlyPrice))}</p> <p class="text-muted-foreground">per month</p> <div class="mt-6 space-y-4"><h4 class="font-medium">Features</h4> <ul class="space-y-2"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
    let planFeature = each_array[$$index_1];
    const feature = features.find((f) => f.id === planFeature.featureId);
    if (feature) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<li class="flex items-center text-sm">`;
      Check($$payload, { class: "mr-2 h-4 w-4 text-green-500" });
      $$payload.out += `<!----> <span>${escape_html(feature.name)}</span> `;
      if (planFeature.accessLevel === FeatureAccessLevel.Limited && planFeature.limits && feature.limits) {
        $$payload.out += "<!--[-->";
        const each_array_1 = ensure_array_like(planFeature.limits);
        $$payload.out += `<span class="text-muted-foreground ml-1">( <!--[-->`;
        for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
          let limitValue = each_array_1[$$index];
          const limit = feature.limits.find((l) => l.id === limitValue.limitId);
          if (limit) {
            $$payload.out += "<!--[-->";
            $$payload.out += `${escape_html(limitValue.value)}
                      ${escape_html(limit.unit)} `;
            if (limitValue !== planFeature.limits[planFeature.limits.length - 1]) {
              $$payload.out += "<!--[-->";
              $$payload.out += `,`;
            } else {
              $$payload.out += "<!--[!-->";
            }
            $$payload.out += `<!--]-->`;
          } else {
            $$payload.out += "<!--[!-->";
          }
          $$payload.out += `<!--]-->`;
        }
        $$payload.out += `<!--]--> )</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></li>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></ul></div> `;
  Button($$payload, {
    class: "mt-6 w-full",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Choose ${escape_html(selectedPlan.name)}`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="rounded-lg border p-6"><h3 class="mb-4 text-lg font-medium">Feature Check Integration</h3> <div class="space-y-4"><p class="text-sm">This plan's features are automatically connected to the feature check system. Here's how you
        can use feature checks in your components:</p> <div class="rounded-md bg-gray-50 p-4"><h4 class="mb-2 text-sm font-medium">Using the FeatureCheck Component</h4> <div class="rounded bg-gray-100 p-2 text-xs"><div>1. Import the component:</div> <div class="mt-1 pl-2">import FeatureCheck from '$lib/components/features/FeatureCheck.svelte';</div> <div class="mt-2">2. Use it in your component:</div> <div class="mt-1 pl-2">&lt;FeatureCheck featureId="feature_id"></div> <div class="pl-4">Content only visible with access</div> <div class="pl-2">&lt;/FeatureCheck></div></div></div> <div class="rounded-md bg-gray-50 p-4"><h4 class="mb-2 text-sm font-medium">Using the Feature Check with Limits</h4> <div class="rounded bg-gray-100 p-2 text-xs"><div>1. Import the component:</div> <div class="mt-1 pl-2">import FeatureCheck from '$lib/components/features/FeatureCheck.svelte';</div> <div class="mt-2">2. Use it with a limit:</div> <div class="mt-1 pl-2">&lt;FeatureCheck featureId="feature_id" limitId="limit_id"></div> <div class="pl-4">Content only visible if limit not reached</div> <div class="pl-2">&lt;/FeatureCheck></div></div></div></div></div> <div class="space-y-4"><div class="rounded-md border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-800"><div class="flex items-center">`;
  Circle_alert($$payload, { class: "mr-2 h-4 w-4" });
  $$payload.out += `<!----> <div class="font-medium">Preview</div></div> <div class="mt-2 pl-6">This is a preview of how the plan will appear to users. The actual appearance may vary
        depending on the UI implementation.</div></div> <div class="rounded-md border border-blue-200 bg-blue-50 p-4 text-sm text-blue-800"><div class="flex items-center"><div class="font-medium">Usage Tips</div></div> <div class="mt-2 pl-6"><ul class="list-disc space-y-1 pl-4"><li>Use the <strong>Features</strong> tab to configure which features are included in this plan</li> <li>Use the <strong>Plan Details</strong> tab to set pricing and other plan metadata</li> <li>Click <strong>Save Changes</strong> at the top of the page when you're done</li></ul></div></div></div></div>`;
  bind_props($$props, { selectedPlan, formatPrice, features });
  pop();
}
function _page($$payload, $$props) {
  push();
  let selectedPlan;
  let editablePlans = [];
  let loading = true;
  let error = null;
  let syncingWithStripe = false;
  let isSyncingFeatures = false;
  let stripeMessage = "";
  let selectedPlanId = "";
  async function loadPlans() {
    try {
      loading = true;
      error = null;
      try {
        const initResponse = await fetch("/api/admin/plans/initialize", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include"
        });
        if (!initResponse.ok) {
          console.warn(`Failed to initialize plans: ${initResponse.status}`);
        }
      } catch (initError) {
        console.warn("Error initializing plans:", initError);
      }
      const response = await fetch("/api/admin/plans", { credentials: "include" });
      let responseText;
      try {
        responseText = await response.text();
      } catch (e) {
        responseText = "";
      }
      if (!response.ok) {
        let errorMessage = "Failed to load plans";
        if (responseText) {
          try {
            const errorData = JSON.parse(responseText);
            errorMessage = errorData.error || errorMessage;
          } catch (parseError) {
            errorMessage = responseText;
          }
        }
        throw new Error(errorMessage);
      }
      try {
        editablePlans = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Error parsing plans JSON:", parseError);
        throw new Error("Invalid response format from server");
      }
      const supportFeatureIds = [
        "email_support",
        "priority_support",
        "dedicated_support"
      ];
      editablePlans.forEach((plan) => {
        plan.features = plan.features.filter((feature) => !supportFeatureIds.includes(feature.featureId));
      });
      if (editablePlans.length > 0) {
        selectedPlanId = editablePlans[0].id;
      }
    } catch (err) {
      console.error("Error loading plans:", err);
      error = err.message;
      editablePlans = [
        {
          id: "free",
          name: "Free",
          description: "Basic features for personal use",
          section: "pro",
          monthlyPrice: 0,
          annualPrice: 0,
          features: []
        },
        {
          id: "casual",
          name: "Casual",
          description: "For occasional job seekers",
          section: "pro",
          monthlyPrice: 999,
          annualPrice: 9990,
          features: []
        }
      ];
      const supportFeatureIds = [
        "email_support",
        "priority_support",
        "dedicated_support"
      ];
      editablePlans.forEach((plan) => {
        plan.features = plan.features.filter((feature) => !supportFeatureIds.includes(feature.featureId));
      });
      selectedPlanId = editablePlans[0].id;
    } finally {
      loading = false;
    }
  }
  let allFeatures = [];
  let featuresByCategory = {};
  let expandedCategories = [];
  function getFeatureAccessLevel(featureId) {
    if (!selectedPlan) return FeatureAccessLevel.NotIncluded;
    const planFeature = selectedPlan.features.find((f) => f.featureId === featureId);
    return planFeature?.accessLevel || FeatureAccessLevel.NotIncluded;
  }
  function getFeatureLimitValue(featureId, limitId) {
    if (!selectedPlan) return void 0;
    const planFeature = selectedPlan.features.find((f) => f.featureId === featureId);
    if (!planFeature || planFeature.accessLevel !== FeatureAccessLevel.Limited) return void 0;
    const limitValue = planFeature.limits?.find((l) => l.limitId === limitId);
    return limitValue?.value;
  }
  async function updateFeatureAccessLevel(featureId, accessLevel) {
    if (!selectedPlan) return;
    const featureIndex = selectedPlan.features.findIndex((f) => f.featureId === featureId);
    let limits;
    if (featureIndex >= 0) {
      selectedPlan.features[featureIndex].accessLevel = accessLevel;
      if (accessLevel !== FeatureAccessLevel.Limited) {
        selectedPlan.features[featureIndex].limits = void 0;
      } else if (!selectedPlan.features[featureIndex].limits) {
        const feature = allFeatures.find((f) => f.id === featureId);
        if (feature?.limits && feature.limits.length > 0) {
          limits = feature.limits.map((limit) => ({
            limitId: limit.id,
            value: limit.defaultValue || 10
          }));
          selectedPlan.features[featureIndex].limits = limits;
        } else {
          limits = [
            { limitId: "monthly_usage", value: 10 },
            { limitId: "max_items", value: 5 }
          ];
          selectedPlan.features[featureIndex].limits = limits;
        }
      } else {
        limits = selectedPlan.features[featureIndex].limits;
      }
    } else {
      const newFeature = { featureId, accessLevel };
      if (accessLevel === FeatureAccessLevel.Limited) {
        const feature = allFeatures.find((f) => f.id === featureId);
        if (feature?.limits && feature.limits.length > 0) {
          limits = feature.limits.map((limit) => ({
            limitId: limit.id,
            value: limit.defaultValue || 10
          }));
          newFeature.limits = limits;
        } else {
          limits = [
            { limitId: "monthly_usage", value: 10 },
            { limitId: "max_items", value: 5 }
          ];
          newFeature.limits = limits;
        }
      }
      selectedPlan.features.push(newFeature);
    }
    selectedPlan = { ...selectedPlan };
    try {
      const response = await fetch("/api/admin/plans", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          action: "update_feature",
          planId: selectedPlan.id,
          featureId,
          accessLevel,
          limits
        })
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error updating feature:", errorText);
        toast.error("Failed to update feature", {
          description: errorText || "An error occurred while updating the feature",
          duration: 5e3
        });
        await loadPlans();
      } else {
        const result = await response.json();
        toast.success("Feature updated", {
          description: result.message || `Feature ${featureId} updated successfully`,
          duration: 3e3
        });
      }
    } catch (error2) {
      console.error("Error updating feature:", error2);
      toast.error("Failed to update feature", {
        description: error2.message || "An error occurred while updating the feature",
        duration: 5e3
      });
      await loadPlans();
    }
  }
  async function updateFeatureLimitValue(featureId, limitId, value) {
    if (!selectedPlan) return;
    const featureIndex = selectedPlan.features.findIndex((f) => f.featureId === featureId);
    if (featureIndex >= 0 && selectedPlan.features[featureIndex].accessLevel === FeatureAccessLevel.Limited) {
      if (!selectedPlan.features[featureIndex].limits) {
        selectedPlan.features[featureIndex].limits = [];
      }
      const limitIndex = selectedPlan.features[featureIndex].limits.findIndex((l) => l.limitId === limitId);
      if (limitIndex >= 0) {
        selectedPlan.features[featureIndex].limits[limitIndex].value = value;
      } else {
        selectedPlan.features[featureIndex].limits.push({ limitId, value });
      }
      selectedPlan = { ...selectedPlan };
      try {
        const response = await fetch("/api/admin/plans", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({
            action: "update_feature",
            planId: selectedPlan.id,
            featureId,
            accessLevel: FeatureAccessLevel.Limited,
            limits: selectedPlan.features[featureIndex].limits
          })
        });
        if (!response.ok) {
          const errorText = await response.text();
          console.error("Error updating feature limit:", errorText);
          toast.error("Failed to update limit", {
            description: errorText || "An error occurred while updating the limit",
            duration: 5e3
          });
          await loadPlans();
        } else {
          const result = await response.json();
          toast.success("Limit updated", {
            description: result.message || `Limit ${limitId} updated successfully`,
            duration: 3e3
          });
        }
      } catch (error2) {
        console.error("Error updating feature limit:", error2);
        toast.error("Failed to update limit", {
          description: error2.message || "An error occurred while updating the limit",
          duration: 5e3
        });
        await loadPlans();
      }
    }
  }
  async function saveChanges() {
    try {
      const response = await fetch("/api/admin/plans", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ plans: editablePlans })
      });
      let responseText;
      try {
        responseText = await response.text();
      } catch (e) {
        responseText = "";
      }
      if (response.ok) {
        try {
          const result = JSON.parse(responseText);
          if (result.success) {
            toast.success("Changes saved successfully!", {
              description: "All plan changes have been saved to the database",
              duration: 3e3
            });
          } else {
            toast.error("Failed to save plans", {
              description: result.error || "An error occurred while saving plans",
              duration: 5e3
            });
          }
        } catch (parseError) {
          toast.success("Changes saved successfully!", {
            description: "All plan changes have been saved to the database",
            duration: 3e3
          });
        }
      } else {
        let errorMessage = "Failed to save plans";
        if (responseText) {
          try {
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            errorMessage = responseText;
          }
        }
        throw new Error(errorMessage);
      }
    } catch (error2) {
      console.error("Error saving plans:", error2);
      toast.error("Failed to save plans", {
        description: error2.message || "An unexpected error occurred",
        duration: 5e3
      });
    }
  }
  async function resetChanges() {
    try {
      loading = true;
      const response = await fetch("/api/admin/plans");
      if (!response.ok) {
        throw new Error("Failed to load plans");
      }
      editablePlans = await response.json();
      const supportFeatureIds = [
        "email_support",
        "priority_support",
        "dedicated_support"
      ];
      editablePlans.forEach((plan) => {
        plan.features = plan.features.filter((feature) => !supportFeatureIds.includes(feature.featureId));
      });
      selectedPlan = editablePlans.find((plan) => plan.id === selectedPlanId);
    } catch (err) {
      console.error("Error resetting plans:", err);
      editablePlans = [
        {
          id: "free",
          name: "Free",
          description: "Basic features for personal use",
          section: "pro",
          monthlyPrice: 0,
          annualPrice: 0,
          features: []
        },
        {
          id: "casual",
          name: "Casual",
          description: "For occasional job seekers",
          section: "pro",
          monthlyPrice: 999,
          annualPrice: 9990,
          features: []
        }
      ];
      selectedPlan = editablePlans.find((plan) => plan.id === selectedPlanId);
    } finally {
      loading = false;
    }
  }
  function formatPrice(cents) {
    return (cents / 100).toFixed(2);
  }
  function confirmSaveChanges() {
    if (confirm("Are you sure you want to save these changes? This will update the subscription plans for all users.")) {
      saveChanges();
    }
  }
  async function syncPlanWithStripe() {
    if (!selectedPlan) return;
    try {
      syncingWithStripe = true;
      stripeMessage = "";
      const response = await fetch("/api/admin/plans/sync-stripe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ planId: selectedPlan.id })
      });
      let responseText;
      try {
        responseText = await response.text();
      } catch (e) {
        responseText = "";
      }
      if (response.ok) {
        let result;
        try {
          result = JSON.parse(responseText);
          if (result.success && result.plan) {
            selectedPlan.stripePriceMonthlyId = result.plan.stripePriceMonthlyId;
            selectedPlan.stripePriceYearlyId = result.plan.stripePriceYearlyId;
            selectedPlan = { ...selectedPlan };
            stripeMessage = result.message || "Plan synced with Stripe successfully";
            toast.success("Plan synced with Stripe", { description: stripeMessage, duration: 3e3 });
          } else {
            stripeMessage = result.error || "Failed to sync plan with Stripe";
            toast.error("Failed to sync plan with Stripe", { description: stripeMessage, duration: 5e3 });
          }
        } catch (parseError) {
          stripeMessage = "Plan synced with Stripe successfully";
          toast.success("Plan synced with Stripe", { description: stripeMessage, duration: 3e3 });
        }
      } else {
        let errorMessage = "Failed to sync plan with Stripe";
        if (responseText) {
          try {
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            errorMessage = responseText;
          }
        }
        stripeMessage = errorMessage;
        alert(`Error: ${stripeMessage} (Status: ${response.status})`);
      }
    } catch (error2) {
      console.error("Error syncing plan with Stripe:", error2);
      stripeMessage = error2.message;
      toast.error("Failed to sync plan with Stripe", {
        description: error2.message || "An unexpected error occurred",
        duration: 5e3
      });
    } finally {
      syncingWithStripe = false;
    }
  }
  async function syncAllPlansWithStripe() {
    try {
      syncingWithStripe = true;
      stripeMessage = "";
      const response = await fetch("/api/admin/plans/sync-stripe", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "include"
      });
      let responseText;
      try {
        responseText = await response.text();
      } catch (e) {
        responseText = "";
      }
      if (response.ok) {
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (e) {
          result = {
            message: "Plans synced with Stripe successfully"
          };
        }
        await loadPlans();
        stripeMessage = result.message || "Plans synced with Stripe successfully";
        toast.success("Plans synced with Stripe", { description: stripeMessage, duration: 3e3 });
      } else {
        let errorMessage = "Failed to sync plans with Stripe";
        if (responseText) {
          try {
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            errorMessage = responseText;
          }
        }
        stripeMessage = errorMessage;
        alert(`Error: ${stripeMessage} (Status: ${response.status})`);
      }
    } catch (error2) {
      console.error("Error syncing plans with Stripe:", error2);
      stripeMessage = error2.message;
      toast.error("Failed to sync plans with Stripe", {
        description: error2.message || "An unexpected error occurred",
        duration: 5e3
      });
    } finally {
      syncingWithStripe = false;
    }
  }
  async function syncFeatures() {
    try {
      if (!confirm("This will sync features with plans in the database. Continue?")) {
        return;
      }
      isSyncingFeatures = true;
      toast.loading("Syncing features with plans...");
      const response = await fetch("/api/admin/features", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          action: "sync_features",
          // Add a flag to indicate this is a manual sync
          manual: true
        })
      });
      console.log("Sync features response status:", response.status);
      if (!response.ok) {
        console.error("Sync features response not OK:", response.statusText);
        toast.dismiss();
        toast.error("Failed to sync features", {
          description: `Error: ${response.status} ${response.statusText}`,
          duration: 5e3
        });
        return;
      }
      const result = await response.json();
      console.log("Sync features result:", result);
      if (result.success) {
        toast.dismiss();
        toast.success("Features synced successfully", {
          description: result.message || "All features have been synced across plans",
          duration: 3e3
        });
        await loadPlans();
      } else {
        toast.dismiss();
        toast.error("Failed to sync features", {
          description: result.error || "An error occurred while syncing features",
          duration: 5e3
        });
      }
    } catch (error2) {
      console.error("Error syncing features:", error2);
      toast.dismiss();
      toast.error("Failed to sync features", {
        description: error2.message || "An unexpected error occurred",
        duration: 5e3
      });
    } finally {
      isSyncingFeatures = false;
    }
  }
  async function loadPlansFromStripe() {
    try {
      loading = true;
      error = null;
      const response = await fetch("/api/admin/plans/load-from-stripe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include"
      });
      let responseText;
      try {
        responseText = await response.text();
      } catch (e) {
        responseText = "";
      }
      if (response.ok) {
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (e) {
          result = {
            message: "Successfully loaded plans from Stripe"
          };
        }
        await loadPlans();
        toast.success("Plans loaded from Stripe", {
          description: result.message || `Successfully loaded ${result.count} plans from Stripe`,
          duration: 3e3
        });
      } else {
        let errorMessage = "Failed to load plans from Stripe";
        if (responseText) {
          try {
            const errorResult = JSON.parse(responseText);
            errorMessage = errorResult.error || errorMessage;
          } catch (e) {
            errorMessage = responseText;
          }
        }
        error = errorMessage;
        alert(`Error: ${error} (Status: ${response.status})`);
      }
    } catch (error2) {
      console.error("Error loading plans from Stripe:", error2);
      alert(`Error loading plans from Stripe: ${error2.message}`);
    } finally {
      loading = false;
    }
  }
  selectedPlan = editablePlans.find((plan) => plan.id === selectedPlanId);
  SEO($$payload, { title: "Plan Management" });
  $$payload.out += `<!----> <div class="flex items-center justify-between gap-1 border-b px-4 py-2"><h2 class="text-lg font-semibold">Plan Management</h2> <div class="space-y-4"><div class="flex gap-2">`;
  Root($$payload, {
    children: ($$payload2) => {
      Dropdown_menu_trigger($$payload2, {
        children: ($$payload3) => {
          Button($$payload3, {
            variant: "outline",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Actions `;
              Chevron_down($$payload4, { class: "ml-2 h-4 w-4" });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Dropdown_menu_content($$payload2, {
        align: "end",
        children: ($$payload3) => {
          Dropdown_menu_item($$payload3, {
            onclick: () => window.location.href = "/dashboard/settings/admin/plans/view",
            children: ($$payload4) => {
              $$payload4.out += `<!---->View Plans`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: () => window.location.href = "/dashboard/settings/admin/plans/edit",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Edit Plans`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: resetChanges,
            children: ($$payload4) => {
              $$payload4.out += `<!---->Reset Changes`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_separator($$payload3, { class: "border-border my-2 border-b" });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: loading ? void 0 : loadPlansFromStripe,
            children: ($$payload4) => {
              External_link($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Load from Stripe `;
              if (loading) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<span class="text-muted-foreground ml-2">(Loading...)</span>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: syncingWithStripe ? void 0 : syncAllPlansWithStripe,
            children: ($$payload4) => {
              Refresh_cw($$payload4, {
                class: `mr-2 h-4 w-4 ${syncingWithStripe ? "animate-spin" : ""}`
              });
              $$payload4.out += `<!----> Sync All with Stripe `;
              if (syncingWithStripe) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<span class="text-muted-foreground ml-2">(Syncing...)</span>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: isSyncingFeatures ? void 0 : syncFeatures,
            children: ($$payload4) => {
              Refresh_cw($$payload4, {
                class: `mr-2 h-4 w-4 ${isSyncingFeatures ? "animate-spin" : ""}`
              });
              $$payload4.out += `<!----> Sync Features `;
              if (isSyncingFeatures) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<span class="text-muted-foreground ml-2">(Syncing...)</span>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_separator($$payload3, {});
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: confirmSaveChanges,
            children: ($$payload4) => {
              Save($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Save Changes`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div> `;
  if (loading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex h-64 items-center justify-center"><div class="text-center"><div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div> <p class="mt-4 text-lg">Loading plans...</p></div></div>`;
  } else if (error) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700"><p>Error loading plans: ${escape_html(error)}</p> <p class="mt-2">Using fallback data. Changes may not be saved correctly.</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="grid grid-cols-12">`;
  if (selectedPlan && !loading) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(editablePlans);
    $$payload.out += `<div class="border-border col-span-2 flex flex-col gap-2 border-r p-2"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let plan = each_array[$$index];
      Button($$payload, {
        variant: "outline",
        class: `w-full justify-start ${stringify(selectedPlanId === plan.id ? "background-primary/80" : "")}`,
        onclick: () => selectedPlanId = plan.id,
        children: ($$payload2) => {
          $$payload2.out += `<!---->${escape_html(plan.name)} `;
          if (plan.popular) {
            $$payload2.out += "<!--[-->";
            Badge($$payload2, {
              variant: "secondary",
              class: "bg-primary/10 text-primary ml-2",
              children: ($$payload3) => {
                $$payload3.out += `<!---->Popular`;
              },
              $$slots: { default: true }
            });
          } else {
            $$payload2.out += "<!--[!-->";
          }
          $$payload2.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    }
    $$payload.out += `<!--]--></div> <div class="col-span-10 flex flex-col"><div class="border-border flex items-center justify-between border-b p-4"><div class="flex flex-col gap-1"><h5>${escape_html(selectedPlan.name)} Plan</h5> <div class="text-muted-foreground text-sm">${escape_html(selectedPlan.description)}</div></div> <div class="flex items-center gap-4"><div class="text-right"><div class="text-muted-foreground text-sm">Monthly</div> <div class="text-xl font-bold">$${escape_html(formatPrice(selectedPlan.monthlyPrice))}</div></div> `;
    Arrow_right($$payload, { class: "text-muted-foreground h-4 w-4" });
    $$payload.out += `<!----> <div class="text-right"><div class="text-muted-foreground text-sm">Annual</div> <div class="text-xl font-bold">$${escape_html(formatPrice(selectedPlan.annualPrice / 12))}/mo</div></div></div></div> `;
    Root$1($$payload, {
      value: "features",
      class: "w-full",
      children: ($$payload2) => {
        Card_content($$payload2, {
          class: "border-border border-b p-0",
          children: ($$payload3) => {
            Tabs_list($$payload3, {
              class: "flex flex-row gap-2 divide-x",
              children: ($$payload4) => {
                Tabs_trigger($$payload4, {
                  value: "features",
                  class: "flex-1 border-none",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Features`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Tabs_trigger($$payload4, {
                  value: "details",
                  class: "flex-1 border-none",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Plan Details`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Tabs_trigger($$payload4, {
                  value: "preview",
                  class: "flex-1 border-none",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Preview`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Tabs_content($$payload2, {
          value: "features",
          children: ($$payload3) => {
            FeaturesTabContent($$payload3, {
              featuresByCategory,
              expandedCategories,
              getFeatureAccessLevel,
              getFeatureLimitValue,
              updateFeatureAccessLevel,
              updateFeatureLimitValue
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Tabs_content($$payload2, {
          value: "details",
          class: "h-full w-full p-4",
          children: ($$payload3) => {
            PlanDetailsTabContent($$payload3, {
              selectedPlan,
              syncPlanWithStripe,
              syncingWithStripe,
              stripeMessage
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Tabs_content($$payload2, {
          value: "preview",
          class: "p-4",
          children: ($$payload3) => {
            PreviewTabContent($$payload3, {
              selectedPlan,
              formatPrice,
              features: allFeatures
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CZE4lWi6.js.map
