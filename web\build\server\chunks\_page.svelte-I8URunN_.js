import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, M as ensure_array_like, K as fallback, O as escape_html, a0 as slot } from './index3-CqUPEnZw.js';
import { b as buttonVariants, B as Button } from './button-CrucCo1G.js';
import { R as Root$1, P as Portal, S as Sheet_overlay, a as Sheet_content, b as Sheet_header, c as Sheet_title, d as Sheet_description } from './index10-F28UXWIO.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root$2, D as Dialog_content } from './index7-BURUpWjT.js';
import { R as Root, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { I as Input } from './input-DF0gPqYN.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { c as createEventDispatcher } from './index-server-CezSOnuG.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { D as Dialog_trigger } from './dialog-trigger2-C3bA-tk4.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { g as goto } from './client-dNyMPa8V.js';
import { c as createFeatureAccess } from './index13-DOBlGKWb.js';
import { L as Lock } from './lock-Dkt3avTK.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { S as Sparkles } from './sparkles-E4-thk3U.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './dialog-overlay-CspOQRJq.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './dialog-description2-rfr-pd9k.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './hidden-input-1eDzjGOB.js';
import './index2-Cut0V_vU.js';
import './dialog-trigger-CNXm7UD7.js';
import './features-SWeUHekJ.js';
import './dynamic-registry-Cmy1Wm2Q.js';
import './index4-HpJcNJHQ.js';

function ResumeUpload($$payload, $$props) {
  push();
  let profiles = fallback($$props["profiles"], () => [], true);
  let selectedProfile = "";
  let file = null;
  let label = "";
  let isLoading = false;
  let open = false;
  const dispatch = createEventDispatcher();
  async function uploadResume() {
    if (!file || !selectedProfile && profiles.length > 0 || !label) return;
    isLoading = true;
    try {
      const formData = new FormData();
      formData.append("file", file);
      if (selectedProfile) {
        formData.append("profileId", selectedProfile);
        console.log("Uploading resume with profileId:", selectedProfile);
      }
      formData.append("label", label);
      formData.append("documentType", "resume");
      formData.append("parseIntoProfile", "true");
      console.log("Sending resume upload request with form data:", {
        file: file?.name,
        fileType: file?.type,
        fileSize: file?.size,
        profileId: selectedProfile,
        label,
        documentType: "resume",
        parseIntoProfile: true
      });
      const res = await fetch("/api/resume/upload", { method: "POST", body: formData });
      console.log("Response status:", res.status);
      if (res.ok) {
        const responseData = await res.json();
        console.log("Response from server:", responseData);
        const resumeId = responseData.resume?.id;
        console.log("Resume from server:", responseData.resume);
        toast.success("Resume uploaded", {
          description: "Your resume was uploaded successfully.",
          action: {
            label: "View",
            onClick: () => {
              dispatch("uploaded", { resumeId });
            }
          }
        });
        dispatch("resume-uploaded", responseData);
        open = false;
        resetForm();
      } else {
        toast.error("Upload failed", {
          description: "Please try again or check your file."
        });
      }
    } catch (err) {
      toast.error("Unexpected error", {
        description: "Something went wrong during upload."
      });
      console.error("Upload error:", err);
    } finally {
      isLoading = false;
    }
  }
  function resetForm() {
    selectedProfile = "";
    file = null;
    label = "";
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$2($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_trigger($$payload3, {
          class: buttonVariants({ variant: "outline" }),
          children: ($$payload4) => {
            $$payload4.out += `<!---->Upload Resume`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-md",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Upload a Resume`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Select a profile and upload your PDF resume.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="grid gap-4 py-4">`;
            if (profiles.length > 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div>`;
              Label($$payload4, {
                for: "profile",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Select Profile`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Root($$payload4, {
                selected: { value: selectedProfile },
                onSelectedChange: (v) => v && (selectedProfile = v.value),
                children: ($$payload5) => {
                  Select_trigger($$payload5, {
                    children: ($$payload6) => {
                      Select_value($$payload6, { placeholder: "Choose a profile" });
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Select_content($$payload5, {
                    children: ($$payload6) => {
                      const each_array = ensure_array_like(profiles);
                      $$payload6.out += `<!--[-->`;
                      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                        let profile = each_array[$$index];
                        Select_item($$payload6, {
                          value: String(profile.id),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->${escape_html(profile.name)}`;
                          },
                          $$slots: { default: true }
                        });
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div>`;
            Label($$payload4, {
              for: "label",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Resume Name`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "label",
              placeholder: "e.g. Senior Engineer Resume",
              get value() {
                return label;
              },
              set value($$value) {
                label = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div>`;
            Label($$payload4, {
              for: "resume-upload",
              children: ($$payload5) => {
                $$payload5.out += `<!---->PDF Resume`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "resume-upload",
              type: "file",
              accept: ".pdf",
              onchange: (e) => file = e.target?.files?.[0] || null,
              class: "w-full rounded border border-dashed bg-gray-800 px-4 py-2 text-sm text-white"
            });
            $$payload4.out += `<!----></div></div> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  onclick: uploadResume,
                  disabled: isLoading || !(file instanceof File) || profiles.length > 0 && !selectedProfile || !label,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html(isLoading ? "Uploading..." : "Upload")}`;
                  },
                  $$slots: { default: true }
                });
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { profiles });
  pop();
}
function FeatureGuard($$payload, $$props) {
  push();
  let userData = $$props["userData"];
  let featureId = $$props["featureId"];
  let limitId = fallback($$props["limitId"], void 0);
  let showUpgradeButton = fallback($$props["showUpgradeButton"], true);
  let upgradeButtonText = fallback($$props["upgradeButtonText"], "Upgrade Plan");
  let upgradeButtonLink = fallback($$props["upgradeButtonLink"], "/dashboard/settings/billing");
  let limitReachedMessage = fallback($$props["limitReachedMessage"], "You have reached the limit for this feature.");
  let notIncludedMessage = fallback($$props["notIncludedMessage"], "This feature is not included in your current plan.");
  let featureAccess;
  let hasAccess = false;
  let hasReachedLimit = false;
  let message = "";
  function updateFeatureAccess() {
    try {
      if (userData) {
        featureAccess = createFeatureAccess(userData);
        hasAccess = featureAccess.hasAccess(featureId);
        if (hasAccess && limitId) {
          hasReachedLimit = featureAccess.hasReachedLimit(featureId, limitId);
          if (hasReachedLimit) {
            const limitValue = featureAccess.getLimitValue(featureId, limitId);
            message = `${limitReachedMessage} (Limit: ${limitValue})`;
          } else {
            message = "";
          }
        } else if (!hasAccess) {
          message = notIncludedMessage;
        } else {
          message = "";
        }
      }
    } catch (error) {
      console.error("Error in FeatureGuard:", error);
      hasAccess = false;
      hasReachedLimit = false;
      message = "Error checking feature access.";
    }
  }
  userData && featureId && updateFeatureAccess();
  limitId && updateFeatureAccess();
  if (hasAccess && !hasReachedLimit) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", {}, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center"><div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">`;
    if (hasReachedLimit) {
      $$payload.out += "<!--[-->";
      Triangle_alert($$payload, { class: "text-warning h-6 w-6" });
    } else {
      $$payload.out += "<!--[!-->";
      Lock($$payload, { class: "text-muted-foreground h-6 w-6" });
    }
    $$payload.out += `<!--]--></div> <h3 class="mb-2 text-lg font-medium">`;
    if (hasReachedLimit) {
      $$payload.out += "<!--[-->";
      $$payload.out += `Limit Reached`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `Feature Not Available`;
    }
    $$payload.out += `<!--]--></h3> <p class="text-muted-foreground mb-4 max-w-md">${escape_html(message)}</p> `;
    if (showUpgradeButton) {
      $$payload.out += "<!--[-->";
      Button($$payload, {
        variant: "outline",
        onclick: () => goto(),
        children: ($$payload2) => {
          $$payload2.out += `<!---->${escape_html(upgradeButtonText)}`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, {
    userData,
    featureId,
    limitId,
    showUpgradeButton,
    upgradeButtonText,
    upgradeButtonLink,
    limitReachedMessage,
    notIncludedMessage
  });
  pop();
}
function ATSAnalysisButton($$payload, $$props) {
  push();
  const { userData, isParsed, disabled } = $$props;
  FeatureGuard($$payload, {
    userData,
    featureId: "ats_optimization",
    limitId: "ats_scans_monthly",
    showUpgradeButton: true,
    upgradeButtonText: "Upgrade for ATS Analysis",
    limitReachedMessage: "You've reached your monthly limit for ATS scans",
    notIncludedMessage: "ATS Analysis is not included in your current plan",
    children: ($$payload2) => {
      Button($$payload2, {
        variant: "outline",
        size: "sm",
        class: "flex items-center gap-2",
        disabled: disabled || !isParsed,
        children: ($$payload3) => {
          {
            $$payload3.out += "<!--[!-->";
            Sparkles($$payload3, { class: "h-4 w-4 text-blue-500" });
            $$payload3.out += `<!----> Analyze with ATS`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let showDetails = false;
  let resume = null;
  let searchTerm = "";
  let selectedProfile = "";
  function filteredResumes() {
    return data.resumes.filter((r) => {
      const label = r.document?.label || "";
      const labelMatch = label.toLowerCase().includes(searchTerm.toLowerCase());
      const profileMatch = r.profile?.name?.toLowerCase().includes(selectedProfile.toLowerCase()) || !selectedProfile;
      return labelMatch && profileMatch;
    });
  }
  function openResumeDetails(resumeData) {
    resume = resumeData;
    showDetails = true;
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="container mx-auto mt-6 flex flex-col gap-4 p-6"><div class="mb-6 flex flex-col items-center justify-between gap-8"><div class="flex w-full flex-row items-start justify-between gap-8"><div class="flex flex-col gap-2"><h1 class="text-foreground text-2xl font-bold">Resume Workspace</h1> <p class="text-muted-foreground text-md">Manage resume parsing, optimization, and performance data</p></div> `;
    ResumeUpload($$payload2, { profiles: data.profiles });
    $$payload2.out += `<!----></div> <div class="mb-6 flex w-full flex-col justify-between gap-6 sm:flex-row">`;
    Input($$payload2, {
      id: "searchTerm",
      type: "text",
      class: "flex w-full flex-col gap-2 rounded border px-4 py-2 text-sm sm:w-[300px]",
      placeholder: "Search resumes by label",
      get value() {
        return searchTerm;
      },
      set value($$value) {
        searchTerm = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> <div class="flex w-full flex-col gap-2 sm:w-[200px]">`;
    Root($$payload2, {
      class: "rounded border px-4 py-2 text-sm",
      get value() {
        return selectedProfile;
      },
      set value($$value) {
        selectedProfile = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Select_trigger($$payload3, {
          children: ($$payload4) => {
            Select_value($$payload4, { placeholder: "All Profiles" });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Select_content($$payload3, {
          children: ($$payload4) => {
            Select_item($$payload4, {
              value: "all",
              children: ($$payload5) => {
                $$payload5.out += `<!---->All Profiles`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Select_item($$payload4, {
              value: "completed",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Completed`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Select_item($$payload4, {
              value: "incomplete",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Incomplete`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></div> `;
    if (data.resumes.length === 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="py-10 text-center"><p class="text-muted-foreground">No resumes available. Please upload a resume to get started.</p> `;
      ResumeUpload($$payload2, { profiles: data.profiles });
      $$payload2.out += `<!----></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
      const each_array = ensure_array_like(filteredResumes());
      $$payload2.out += `<div class="mb-12 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let r = each_array[$$index];
        Card($$payload2, {
          onclick: () => openResumeDetails(r),
          class: "hover:border-primary cursor-pointer",
          children: ($$payload3) => {
            Card_header($$payload3, {
              children: ($$payload4) => {
                Card_title($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(r.document?.label || "Unnamed Resume")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Card_description($$payload4, {
                  class: "text-muted-foreground",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Uploaded: ${escape_html(new Date(r.createdAt).toLocaleDateString())}`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Card_content($$payload3, {
              class: "text-muted-foreground text-sm",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Profile: ${escape_html(r.profile?.name ?? "N/A")} `;
                if (!r.profile) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<p class="text-destructive mt-2 text-xs">This resume is not connected to a profile.</p>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]--></div>`;
    }
    $$payload2.out += `<!--]--></div> `;
    Root$1($$payload2, {
      get open() {
        return showDetails;
      },
      set open($$value) {
        showDetails = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal($$payload3, {
          children: ($$payload4) => {
            Sheet_overlay($$payload4, {});
            $$payload4.out += `<!----> `;
            Sheet_content($$payload4, {
              class: "bg-background text-foreground w-[500px] max-w-full overflow-y-auto p-6",
              children: ($$payload5) => {
                if (resume) {
                  $$payload5.out += "<!--[-->";
                  Sheet_header($$payload5, {
                    children: ($$payload6) => {
                      Sheet_title($$payload6, {
                        class: "mb-2 text-xl font-semibold",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Resume Overview`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Sheet_description($$payload6, {
                        class: "text-muted-foreground mb-6",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->View score, associated profiles, recent job search activity, and more.`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <div class="space-y-6"><div><h2 class="text-muted-foreground mb-2 text-sm font-medium">Metadata</h2> <div class="space-y-1 text-sm"><p><strong>Label:</strong> ${escape_html(resume.document?.label || "Unnamed Resume")}</p> <p><strong>Uploaded:</strong> ${escape_html(new Date(resume.createdAt).toLocaleDateString())}</p> <p><strong>Filename:</strong> ${escape_html(resume.document?.fileName || resume.document?.fileUrl?.split("/").pop() || "Unknown")}</p> <p><strong>Score:</strong> <span class="bg-warning text-warning-foreground inline-block rounded px-2 py-0.5 text-xs">${escape_html(resume.score ? `${resume.score}%` : "N/A")}</span></p> `;
                  if (resume.document?.fileUrl) {
                    $$payload5.out += "<!--[-->";
                    Button($$payload5, {
                      variant: "ghost",
                      size: "sm",
                      onclick: () => window.open(`/uploads${resume.document.fileUrl}`, "_blank"),
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->Download PDF`;
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload5.out += "<!--[!-->";
                  }
                  $$payload5.out += `<!--]--></div></div> <div><h2 class="text-muted-foreground mb-2 text-sm font-medium">Associated Profile</h2> <div class="space-y-1 text-sm"><p><strong>Name:</strong> ${escape_html(resume.profile?.name)}</p> `;
                  Button($$payload5, {
                    size: "sm",
                    variant: "secondary",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Open Profile`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></div></div> <div><h2 class="text-muted-foreground mb-2 text-sm font-medium">Latest Job Search</h2> `;
                  if (resume.jobSearch) {
                    $$payload5.out += "<!--[-->";
                    $$payload5.out += `<div class="space-y-1 text-sm"><p><strong>Title:</strong> ${escape_html(resume.jobSearch.title)}</p> <p><strong>Location:</strong> ${escape_html(resume.jobSearch.location)}</p> <p><strong>Applications:</strong> ${escape_html(resume.jobSearch._count?.results ?? 0)} total</p></div>`;
                  } else {
                    $$payload5.out += "<!--[!-->";
                    $$payload5.out += `<p class="text-muted-foreground text-sm">No recent job search activity.</p>`;
                  }
                  $$payload5.out += `<!--]--></div> <div class="border-border border-t pt-4"><h2 class="text-muted-foreground mb-2 text-sm font-medium">Actions</h2> <div class="flex flex-wrap gap-2">`;
                  Button($$payload5, {
                    size: "sm",
                    onclick: () => window.location.href = `/dashboard/resumes/${resume.id}/optimization`,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Optimization`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Button($$payload5, {
                    size: "sm",
                    onclick: () => window.location.href = `/dashboard/resumes/${resume.id}/recommendations`,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Recommendations`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Button($$payload5, {
                    size: "sm",
                    onclick: () => window.location.href = `/dashboard/resumes/${resume.id}/raw`,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Raw PDF`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  if (resume.isParsed) {
                    $$payload5.out += "<!--[-->";
                    ATSAnalysisButton($$payload5, {
                      resumeId: resume.id,
                      userData: data.user,
                      isParsed: resume.isParsed
                    });
                  } else {
                    $$payload5.out += "<!--[!-->";
                  }
                  $$payload5.out += `<!--]--></div></div></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-I8URunN_.js.map
