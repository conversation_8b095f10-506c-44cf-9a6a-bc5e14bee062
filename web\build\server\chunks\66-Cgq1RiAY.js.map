{"version": 3, "file": "66-Cgq1RiAY.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/profile/_id_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/66.js"], "sourcesContent": ["import { r as redirect, e as error } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst load = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const { id } = params;\n  if (!id) {\n    throw error(400, \"Profile ID is required\");\n  }\n  try {\n    console.log(\"Loading profile with ID:\", id);\n    console.log(\"User ID:\", user.id);\n    const profile = await prisma.profile.findUnique({\n      where: {\n        id,\n        userId: user.id\n        // Ensure the profile belongs to the user\n      },\n      include: {\n        data: true,\n        defaultDocument: true\n      }\n    });\n    console.log(\"Profile found:\", profile ? \"Yes\" : \"No\");\n    if (!profile) {\n      throw error(404, \"Profile not found\");\n    }\n    console.log(\"Fetching documents for user:\", user.id);\n    const documents = await prisma.document.findMany({\n      where: {\n        userId: user.id,\n        // Filter for documents that have a resume relation\n        resume: {\n          isNot: null\n        }\n      },\n      select: {\n        id: true,\n        label: true,\n        resume: true,\n        updatedAt: true\n      },\n      orderBy: {\n        updatedAt: \"desc\"\n      }\n    });\n    console.log(\"Documents found:\", documents.length);\n    return {\n      profile,\n      documents\n    };\n  } catch (err) {\n    console.error(\"Error loading profile data:\", err);\n    console.error(\"Error details:\", JSON.stringify(err, null, 2));\n    throw error(\n      500,\n      `Failed to load profile data: ${err instanceof Error ? err.message : \"Unknown error\"}`\n    );\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/profile/_id_/_page.server.ts.js';\n\nexport const index = 66;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/profile/_id_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/profile/[id]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/66.b05OTSxl.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/WD4kvFhR.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/hrXlVaSN.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/DzJNq86D.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/Dqigtbi1.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/CVVv9lPb.js\",\"_app/immutable/chunks/BHzYYMdu.js\",\"_app/immutable/chunks/DumgozFE.js\",\"_app/immutable/chunks/D9yI7a4E.js\",\"_app/immutable/chunks/Dmwghw4a.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/CDeW2UsS.js\",\"_app/immutable/chunks/DW5gea7N.js\",\"_app/immutable/chunks/B5tu6DNS.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/-vfp2Q9I.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/T7uRAIbG.js\",\"_app/immutable/chunks/CWA2dVWH.js\",\"_app/immutable/chunks/26EXiO5K.js\",\"_app/immutable/chunks/G5Oo-PmU.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/VNuMAkuB.js\",\"_app/immutable/chunks/BnikQ10_.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/DQB68x0Z.js\",\"_app/immutable/chunks/DRGimm5x.js\",\"_app/immutable/chunks/D1zde6Ej.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/C2AK_5VT.js\",\"_app/immutable/chunks/6BxQgNmX.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/B_6ivTD3.js\",\"_app/immutable/chunks/8b74MdfD.js\",\"_app/immutable/chunks/BgDjIxoO.js\",\"_app/immutable/chunks/Dz4exfp3.js\",\"_app/immutable/chunks/6UJoWgvL.js\",\"_app/immutable/chunks/7AwcL9ec.js\",\"_app/immutable/chunks/Ce6y1v79.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/index.CV-KWLNP.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC;AAC9C;AACA,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,EAAE,CAAC;AAC/C,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,eAAe,EAAE;AACzB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;AACzD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AAC3C;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,EAAE,CAAC;AACxD,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB;AACA,QAAQ,MAAM,EAAE;AAChB,UAAU,KAAK,EAAE;AACjB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC;AACrD,IAAI,OAAO;AACX,MAAM,OAAO;AACb,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,CAAC;AACrD,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjE,IAAI,MAAM,KAAK;AACf,MAAM,GAAG;AACT,MAAM,CAAC,6BAA6B,EAAE,GAAG,YAAY,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,eAAe,CAAC;AAC3F,KAAK;AACL;AACA,CAAC;;;;;;;AC3DW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAkE,CAAC,EAAE;AAEhI,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACj7G,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,0CAA0C;AACvG,MAAC,KAAK,GAAG;;;;"}