import { f as fail, j as json, r as redirect } from './index-Ddp2AB5f.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import bcrypt__default__default from 'bcryptjs';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { g as getUserFromToken, v as verifySessionToken, c as cleanupUserSessions } from './auth-BPad-IlN.js';
import { authenticator } from 'otplib';
import 'qrcode';
import { v as verifyPasskeyRegistration, g as generatePasskeyRegistrationOptions } from './webauthn-8Tn9JPcG.js';
import 'ua-parser-js';
import { o as objectType, s as stringType, e as enumType, b as booleanType } from './types-D78SXuvm.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';
import '@prisma/client';
import 'jsonwebtoken';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import '@simplewebauthn/server';

authenticator.options = {
  window: 1,
  // Allow 1 step before and after current step (for clock drift)
  digits: 6
  // 6-digit codes
};
const passwordSchema = objectType({
  currentPassword: stringType().min(1, "Current password is required"),
  newPassword: stringType().min(8, "Password must be at least 8 characters"),
  confirmPassword: stringType().min(1, "Please confirm your password")
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});
objectType({
  enabled: booleanType().default(false),
  method: enumType(["app", "sms"]).default("app"),
  verificationCode: stringType().min(6, "Verification code is required").max(6).optional()
});
const passkeysSchema = objectType({
  name: stringType().min(1, "Passkey name is required")
});
objectType({
  phoneNumber: stringType().min(1, "Phone number is required"),
  verificationCode: stringType().min(6, "Verification code is required").max(6).optional()
});
const load = async ({ locals, cookies }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData2 = await prisma.user.findUnique({
    where: { id: user.id }
  });
  if (!userData2) {
    throw redirect(302, "/auth/sign-in");
  }
  const passwordForm = await superValidate(zod(passwordSchema));
  const preferences = userData2.preferences || {};
  const securityPrefs = preferences.security || {};
  const passkeysForm = await superValidate(zod(passkeysSchema));
  let passkeys = securityPrefs.passkeys || [];
  if (!Array.isArray(passkeys)) {
    console.error("Passkeys is not an array in user preferences:", passkeys);
    passkeys = [];
  }
  passkeys = passkeys.map((passkey) => {
    return {
      id: passkey.id || passkey.credentialID || "unknown-id",
      name: passkey.name || "Unnamed Passkey",
      credentialID: passkey.credentialID || passkey.id || "unknown-id",
      credentialPublicKey: passkey.credentialPublicKey || passkey.publicKey || "",
      counter: passkey.counter || 0,
      transports: passkey.transports || [],
      createdAt: passkey.createdAt || (/* @__PURE__ */ new Date()).toISOString(),
      lastUsed: passkey.lastUsed || passkey.createdAt || (/* @__PURE__ */ new Date()).toISOString()
    };
  });
  const sessions = await prisma.session.findMany({
    where: {
      userId: user.id,
      isRevoked: false,
      expires: { gt: /* @__PURE__ */ new Date() }
    },
    orderBy: { lastActive: "desc" }
  });
  const token = cookies.get("auth_token");
  const currentSession = await prisma.session.findFirst({
    where: {
      sessionToken: token,
      isRevoked: false,
      expires: { gt: /* @__PURE__ */ new Date() }
    }
  });
  if (!currentSession) {
    console.log("Warning: Current session not found for token:", token?.substring(0, 10) + "...");
  } else {
    console.log("Current session found:", currentSession.id);
  }
  const sessionsByDevice = /* @__PURE__ */ new Map();
  sessions.forEach((session) => {
    const deviceKey = `${session.device || ""}|${session.browser || ""}|${session.os || ""}`;
    const isCurrent = currentSession ? session.id === currentSession.id : session.token === token;
    if (isCurrent) {
      sessionsByDevice.set(deviceKey, {
        session,
        isCurrent: true
      });
    } else if (!sessionsByDevice.has(deviceKey)) {
      sessionsByDevice.set(deviceKey, {
        session,
        isCurrent: false
      });
    } else {
      const existing = sessionsByDevice.get(deviceKey);
      if (!existing.isCurrent && session.lastActive > existing.session.lastActive) {
        sessionsByDevice.set(deviceKey, {
          session,
          isCurrent: false
        });
      }
    }
  });
  const formattedSessions = Array.from(sessionsByDevice.values()).map(({ session, isCurrent }) => ({
    id: session.id,
    device: session.device || "Unknown device",
    browser: session.browser || "Unknown browser",
    os: session.os || "Unknown OS",
    ip: session.ip || "Unknown IP",
    location: session.location || "Unknown location",
    lastActive: session.lastActive,
    createdAt: session.expires,
    isCurrent
  }));
  return {
    passwordForm,
    passkeysForm,
    passkeys,
    sessions: formattedSessions,
    userData: {
      id: userData2.id,
      email: userData2.email,
      hasPreferences: !!userData2.preferences
    }
  };
};
const actions = {
  // Password change action
  changePassword: async ({ request, cookies }) => {
    const tokenData = await getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData2 = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData2) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(passwordSchema));
    if (!form.valid) {
      return fail(400, { passwordForm: form });
    }
    try {
      const userWithPassword = await prisma.user.findUnique({
        where: { id: userData2.id },
        select: { passwordHash: true }
      });
      if (!userWithPassword?.passwordHash) {
        return fail(400, {
          passwordForm: form,
          error: "Cannot update password for this account type"
        });
      }
      const isCurrentPasswordValid = await bcrypt__default__default.compare(
        form.data.currentPassword,
        userWithPassword.passwordHash
      );
      if (!isCurrentPasswordValid) {
        form.errors.currentPassword = ["Current password is incorrect"];
        return fail(400, { passwordForm: form });
      }
      const newPasswordHash = await bcrypt__default__default.hash(form.data.newPassword, 10);
      await prisma.user.update({
        where: { id: userData2.id },
        data: { passwordHash: newPasswordHash }
      });
      return { passwordForm: form, success: true };
    } catch (error) {
      console.error("Error updating password:", error);
      return fail(500, { passwordForm: form, error: "Failed to update password" });
    }
  },
  // Get registration options for a new passkey
  getRegistrationOptions: async ({ request, cookies }) => {
    console.log("getRegistrationOptions called");
    try {
      const tokenData = await getUserFromToken(cookies);
      console.log("Token data:", tokenData);
      if (!tokenData || !tokenData.email) {
        console.log("Unauthorized: No token data or email");
        return json({ error: "Unauthorized" }, { status: 401 });
      }
      console.log("Getting user data for email:", tokenData.email);
      const userData2 = await prisma.user.findUnique({
        where: { email: tokenData.email }
      });
      console.log("User data found:", !!userData2);
      if (!userData2) {
        console.log("Unauthorized: No user data found");
        return json({ error: "Unauthorized" }, { status: 401 });
      }
      try {
        console.log("Parsing request body");
        let name = "";
        const contentType = request.headers.get("content-type") || "";
        console.log("Content-Type:", contentType);
        if (contentType.includes("application/json")) {
          console.log("Parsing JSON");
          try {
            const body = await request.json();
            name = body.name;
            console.log("Name from JSON:", name);
          } catch (jsonError) {
            console.error("Error parsing JSON:", jsonError);
          }
        } else {
          console.log("Parsing FormData");
          try {
            const formData = await request.formData();
            name = formData.get("name");
            console.log("Name from FormData:", name);
          } catch (formDataError) {
            console.error("Error parsing FormData:", formDataError);
          }
        }
        console.log("Final passkey name:", name);
        if (!name) {
          console.log("Passkey name is required");
          return json({ error: "Passkey name is required" }, { status: 400 });
        }
        console.log("Getting existing passkeys");
        const preferences = userData2.preferences || {};
        const securityPrefs = preferences.security || {};
        const existingPasskeys = securityPrefs.passkeys || [];
        console.log("Existing passkeys count:", existingPasskeys.length);
        console.log("Generating registration options");
        const options = await generatePasskeyRegistrationOptions(
          userData2.id,
          userData2.email,
          existingPasskeys.map((p) => ({
            id: p.credentialID,
            publicKey: p.credentialPublicKey,
            transports: p.transports || []
          }))
        );
        console.log("Registration options generated");
        console.log("Storing challenge in database");
        const updatedPreferences = {
          ...preferences,
          security: {
            ...securityPrefs,
            currentChallenge: options.challenge
          }
        };
        await prisma.user.update({
          where: { id: userData2.id },
          data: {
            preferences: updatedPreferences
          }
        });
        console.log("Challenge stored in database");
        console.log("Returning options");
        return json(options);
      } catch (innerError) {
        console.error("Inner error in getRegistrationOptions:", innerError);
        console.error("Inner error stack:", innerError.stack);
        return json({ error: "Failed to process registration options" }, { status: 500 });
      }
    } catch (error) {
      console.error("Error generating passkey registration options:", error);
      console.error("Error stack:", error.stack);
      return json({ error: "Failed to generate passkey registration options" }, { status: 500 });
    }
  },
  // Verify passkey registration
  verifyRegistration: async ({ request, cookies }) => {
    const tokenData = await getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      return json({ error: "Unauthorized" }, { status: 401 });
    }
    const userData2 = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData2) {
      return json({ error: "Unauthorized" }, { status: 401 });
    }
    try {
      console.log("verifyRegistration called");
      let name = "";
      let registrationResponse = null;
      const contentType = request.headers.get("content-type") || "";
      console.log("Content-Type:", contentType);
      if (contentType.includes("application/json")) {
        console.log("Parsing JSON");
        try {
          const body = await request.json();
          name = body.name;
          registrationResponse = body.registrationResponse;
          console.log("Data from JSON:", { name, hasResponse: !!registrationResponse });
        } catch (jsonError) {
          console.error("Error parsing JSON:", jsonError);
        }
      } else {
        console.log("Parsing FormData");
        try {
          const formData = await request.formData();
          name = formData.get("name");
          const registrationResponseStr = formData.get("registrationResponse");
          if (registrationResponseStr) {
            registrationResponse = JSON.parse(registrationResponseStr);
          }
          console.log("Data from FormData:", { name, hasResponse: !!registrationResponse });
        } catch (formDataError) {
          console.error("Error parsing FormData:", formDataError);
        }
      }
      if (!name || !registrationResponse) {
        console.log("Invalid request data");
        return json({ error: "Invalid request data" }, { status: 400 });
      }
      const preferences = userData2.preferences || {};
      const securityPrefs = preferences.security || {};
      const challenge = securityPrefs.currentChallenge;
      if (!challenge) {
        return json({ error: "No challenge found" }, { status: 400 });
      }
      const verification = await verifyPasskeyRegistration(
        registrationResponse,
        challenge
      );
      if (!verification.verified || !verification.registrationInfo) {
        return json({ error: "Passkey verification failed" }, { status: 400 });
      }
      const existingPasskeys = securityPrefs.passkeys || [];
      const newPasskey = {
        id: verification.registrationInfo.credentialID,
        name,
        credentialID: Buffer.from(verification.registrationInfo.credentialID).toString("base64url"),
        credentialPublicKey: Buffer.from(
          verification.registrationInfo.credentialPublicKey
        ).toString("base64url"),
        counter: verification.registrationInfo.counter,
        transports: registrationResponse.response.transports || [],
        createdAt: (/* @__PURE__ */ new Date()).toISOString(),
        lastUsed: (/* @__PURE__ */ new Date()).toISOString()
      };
      const updatedPreferences = {
        ...preferences,
        security: {
          ...securityPrefs,
          passkeys: [...existingPasskeys, newPasskey],
          currentChallenge: null
          // Clear the challenge
        }
      };
      await prisma.user.update({
        where: { id: userData2.id },
        data: {
          preferences: updatedPreferences
        }
      });
      return json({
        success: true,
        passkeys: [...existingPasskeys, newPasskey]
      });
    } catch (error) {
      console.error("Error verifying passkey registration:", error);
      return json({ error: "Failed to verify passkey registration" }, { status: 500 });
    }
  },
  // Remove a passkey
  removePasskey: async ({ request, cookies }) => {
    const tokenData = await getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData2 = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData2) {
      throw redirect(302, "/auth/sign-in");
    }
    const formData = await request.formData();
    const passkeyId = formData.get("passkeyId")?.toString() || "";
    if (!passkeyId) {
      return fail(400, { error: "Passkey ID is required" });
    }
    try {
      const preferences = userData2.preferences || {};
      const securityPrefs = preferences.security || {};
      const existingPasskeys = securityPrefs.passkeys || [];
      const updatedPasskeys = existingPasskeys.filter((passkey) => passkey.id !== passkeyId);
      const updatedPreferences = {
        ...preferences,
        security: {
          ...securityPrefs,
          passkeys: updatedPasskeys
        }
      };
      await prisma.user.update({
        where: { id: userData2.id },
        data: {
          preferences: updatedPreferences
        }
      });
      return json({
        type: "success",
        data: { passkeys: updatedPasskeys }
      });
    } catch (error) {
      console.error("Error removing passkey:", error);
      return fail(500, { error: "Failed to remove passkey" });
    }
  },
  // Send verification code
  sendVerificationCode: async ({ request, cookies }) => {
    return json(
      { success: false, error: "Phone verification is currently disabled" },
      { status: 403 }
    );
  },
  // Update phone number
  updatePhone: async ({ request, cookies }) => {
    return { success: false, error: "Phone verification is currently disabled" };
  },
  verifyPhone: async ({ request, cookies }) => {
    return { success: false, error: "Phone verification is currently disabled" };
  },
  logoutSession: async ({ request, cookies }) => {
    const data = await request.formData();
    const sessionId = data.get("sessionId")?.toString();
    const token = cookies.get("auth_token");
    if (!sessionId) {
      return fail(400, { error: "Session ID is required" });
    }
    if (!token) {
      return fail(401, { error: "Unauthorized" });
    }
    try {
      const session = await prisma.session.findUnique({
        where: { id: sessionId },
        include: { user: true }
      });
      if (!session) {
        return fail(404, { error: "Session not found" });
      }
      const currentUser = await verifySessionToken(token);
      if (!currentUser || currentUser.id !== session.userId) {
        return fail(403, { error: "Forbidden" });
      }
      const currentSession = await prisma.session.findFirst({
        where: {
          OR: [{ sessionToken: token }, { token }],
          isRevoked: false
        }
      });
      if (currentSession && currentSession.id === sessionId) {
        return fail(400, { error: "Cannot log out of current session using this method" });
      }
      await prisma.session.update({
        where: { id: sessionId },
        data: { isRevoked: true }
      });
      console.log(`Session ${sessionId} revoked successfully`);
      return json({ type: "success" });
    } catch (error) {
      console.error("Error logging out session:", error);
      return fail(500, { error: "Failed to log out session" });
    }
  },
  // Log out all sessions except the current one
  logoutAllSessions: async ({ cookies, request }) => {
    const token = cookies.get("auth_token");
    if (!token) {
      return fail(401, { error: "Unauthorized" });
    }
    try {
      console.log("Request content type:", request.headers.get("content-type"));
      const user = await verifySessionToken(token);
      if (!user) {
        return fail(401, { error: "Unauthorized" });
      }
      try {
        await cleanupUserSessions(user.id);
        console.log("Cleaned up old sessions for user:", user.id);
      } catch (cleanupError) {
        console.error("Error cleaning up old sessions:", cleanupError);
      }
      const currentSession = await prisma.session.findFirst({
        where: {
          OR: [{ sessionToken: token }, { token }],
          isRevoked: false
        }
      });
      if (!currentSession) {
        console.log("Current session not found, but proceeding with logout of other sessions");
      } else {
        console.log(`Current session found: ${currentSession.id}`);
      }
      const result = await prisma.session.updateMany({
        where: {
          userId: user.id,
          id: currentSession ? { not: currentSession.id } : void 0,
          isRevoked: false
        },
        data: { isRevoked: true }
      });
      console.log(`Revoked ${result.count} other sessions`);
      return json({ type: "success", count: result.count });
    } catch (error) {
      console.error("Error logging out all sessions:", error);
      return fail(500, { error: "Failed to log out all sessions" });
    }
  },
  // Add passkey action
  addPasskey: async ({ request, cookies }) => {
    const tokenData = await getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      return fail(401, { error: "Unauthorized" });
    }
    const userData2 = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData2) {
      return fail(401, { error: "Unauthorized" });
    }
    const form = await superValidate(request, zod(passkeysSchema));
    if (!form.valid) {
      return fail(400, { passkeysForm: form });
    }
    try {
      return { passkeysForm: form };
    } catch (error) {
      console.error("Error adding passkey:", error);
      return fail(500, { passkeysForm: form, error: "Failed to add passkey" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 68;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DmetYdqN.js')).default;
const server_id = "src/routes/dashboard/settings/security/+page.server.ts";
const imports = ["_app/immutable/nodes/68.B4Ufwu_a.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/sDlmbjaf.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/bEtmAhPN.js","_app/immutable/chunks/CY_6SfHi.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/ByUTvV5u.js","_app/immutable/chunks/u-Lut8o2.js","_app/immutable/chunks/D85ENLd-.js","_app/immutable/chunks/CrHU05dq.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DHNQRrgO.js","_app/immutable/chunks/C8B1VUaq.js","_app/immutable/chunks/D1zde6Ej.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CHsAkgDv.js","_app/immutable/chunks/2KCyzleV.js","_app/immutable/chunks/w9xFoQXV.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=68-C2u6eNN2.js.map
