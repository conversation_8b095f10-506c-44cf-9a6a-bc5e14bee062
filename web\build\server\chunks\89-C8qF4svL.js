import { r as redirect, e as error } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { p as parseProfileData } from './profileHelpers-m3Uw-RPd.js';
import '@prisma/client';

const load = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const { id } = params;
  if (!id) {
    throw error(400, "Profile ID is required");
  }
  try {
    console.log("Loading profile with ID:", id);
    console.log("User ID:", user.id);
    const profile = await prisma.profile.findUnique({
      where: {
        id,
        userId: user.id
        // Ensure the profile belongs to the user
      },
      include: {
        data: true,
        defaultDocument: true
      }
    });
    console.log("Profile found:", profile ? "Yes" : "No");
    if (!profile) {
      throw error(404, "Profile not found");
    }
    console.log("Fetching documents for user:", user.id);
    const documents = await prisma.document.findMany({
      where: {
        userId: user.id,
        // Filter for documents that have a resume relation
        resume: {
          isNot: null
        }
      },
      select: {
        id: true,
        label: true,
        resume: true,
        updatedAt: true
      },
      orderBy: {
        updatedAt: "desc"
      }
    });
    console.log("Documents found:", documents.length);
    const formattedDocuments = documents.map((doc) => ({
      id: doc.id,
      label: doc.label || `Resume (${new Date(doc.updatedAt).toLocaleDateString()})`,
      isDefault: doc.id === profile.defaultDocumentId
    }));
    const profileData = profile.data ? parseProfileData(profile.data.data) : {};
    return {
      profile,
      profileData,
      documents: formattedDocuments,
      user
    };
  } catch (err) {
    console.error("Error loading profile:", err);
    throw error(500, "Failed to load profile");
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 89;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-cVy1TH26.js')).default;
const server_id = "src/routes/profile/[id]/+page.server.ts";
const imports = ["_app/immutable/nodes/89.oF16-Lxs.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BPr9JIwg.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/Ce6y1v79.js","_app/immutable/chunks/B_6ivTD3.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/DQB68x0Z.js","_app/immutable/chunks/mCB4pHNc.js","_app/immutable/chunks/B_tyjpYb.js"];
const stylesheets = ["_app/immutable/assets/scroll-area.bHHIbcsu.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=89-C8qF4svL.js.map
