import {type ForwardedRef, forwardRef, type HTMLProps} from 'react'

import {useStateLink} from './useStateLink'

/**
 * Props for the {@link StateLink} component.
 *
 * @public
 */
export interface StateLinkProps {
  /**
   * Whether to replace the current history entry instead of adding a new one.
   */
  replace?: boolean
  /**
   * The state to associate with the link.
   */
  state?: Record<string, unknown>
  /**
   * Whether to navigate to the index page of the app.
   */
  toIndex?: boolean
}

/**
 * A component that creates a link that updates the URL state.
 *
 * @remarks
 * This component uses the {@link useStateLink} hook
 * to create a link that updates the URL state.
 *
 * @param props - Props to pass to the `StateLink` component.
 *  See {@link StateLinkProps}.
 *
 * @public
 *
 * @example
 * ```tsx
 * function MyComponent() {
 *  return <StateLink state={{foo: 'bar'}}>Link</StateLink>
 * }
 * ```
 */
export const StateLink = forwardRef(function StateLink(
  props: StateLinkProps & Omit<HTMLProps<HTMLAnchorElement>, 'href'>,
  ref: ForwardedRef<HTMLAnchorElement>,
) {
  const {onClick: onClickProp, replace, state, target, toIndex = false, ...restProps} = props
  const {onClick, href} = useStateLink({
    onClick: onClickProp,
    replace,
    state,
    target,
    toIndex,
  })

  return <a {...restProps} href={href} onClick={onClick} ref={ref} />
})
