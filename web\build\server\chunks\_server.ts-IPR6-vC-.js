import { p as prisma } from './prisma-Cit_HrSw.js';
import { uploadFile } from './r2DocumentUpload-DZXqPhze.js';
import path from 'path';
import { e as ensureUniqueDocumentName } from './documentNameUniqueness-BVI8kP6m.js';
import { d as determineDocumentSource } from './documentSource-DXqQzUYc.js';
import { c as canCreateResume, t as trackDocumentUpload } from './resume-usage-B98ib_h-.js';
import '@prisma/client';
import '@aws-sdk/client-s3';
import './feature-usage-SYWaZZiX.js';
import './dynamic-registry-Cmy1Wm2Q.js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './features-SWeUHekJ.js';

const DOCUMENT_TYPE_TO_R2_TYPE = {
  resume: "resumes",
  cover_letter: "userDocuments",
  question_response: "userDocuments",
  letter_of_recommendation: "userDocuments",
  references: "userDocuments",
  employment_certification: "userDocuments",
  document: "userDocuments",
  default: "userDocuments"
};
async function uploadDocument(file, documentType, userId) {
  console.log("📤 Uploading document to R2:", {
    fileName: file.name,
    fileType: file.type,
    fileSize: file.size,
    documentType
  });
  const fileExtension = path.extname(file.name).toLowerCase() || ".pdf";
  const allowedExtensions = [".pdf", ".doc", ".docx"];
  if (!allowedExtensions.includes(fileExtension)) {
    console.error(`File type ${fileExtension} is not supported.`);
    throw new Error(
      `File type ${fileExtension} is not supported. Please upload a PDF or Word document.`
    );
  }
  console.log(`File extension ${fileExtension} is valid.`);
  const r2FileType = DOCUMENT_TYPE_TO_R2_TYPE[documentType] || DOCUMENT_TYPE_TO_R2_TYPE.default;
  const buffer = Buffer.from(await file.arrayBuffer());
  try {
    const uploadResult = await uploadFile(
      buffer,
      file.name,
      file.type || "application/octet-stream",
      r2FileType,
      userId
    );
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || "Upload failed");
    }
    console.log("✅ Document uploaded successfully to R2:", {
      fileKey: uploadResult.fileKey,
      publicUrl: uploadResult.publicUrl,
      fileSize: uploadResult.fileSize
    });
    return {
      originalFileName: file.name,
      filename: path.basename(uploadResult.fileKey),
      filePath: uploadResult.fileKey,
      // R2 file key
      publicPath: uploadResult.publicUrl,
      // R2 public URL
      fileSize: uploadResult.fileSize,
      contentType: uploadResult.contentType
    };
  } catch (error) {
    console.error("❌ Failed to upload document to R2:", error);
    throw new Error(
      `Failed to upload file: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
}
const POST = async ({ request, locals }) => {
  const user = locals.user;
  console.log("User in document upload:", user);
  if (!user) return new Response("Unauthorized", { status: 401 });
  if (!user.id) {
    console.error("User missing ID:", user);
    return new Response("User ID missing", { status: 400 });
  }
  try {
    const formData = await request.formData();
    const file = formData.get("file");
    const profileId = formData.get("profileId");
    let label = formData.get("label") || file.name;
    const documentType = formData.get("documentType") || "document";
    if (!file) {
      return new Response("Missing file", { status: 400 });
    }
    if (profileId) {
      const profile = await prisma.profile.findUnique({ where: { id: profileId } });
      if (!profile) {
        return new Response("Invalid profileId", { status: 404 });
      }
    }
    label = await ensureUniqueDocumentName(label, user.id, documentType);
    const isDev = process.env.NODE_ENV === "development" || process.env.VITE_DISABLE_FEATURE_LIMITS === "true";
    if (!isDev) {
      const canCreate = await canCreateResume(user.id);
      if (!canCreate) {
        return new Response(
          JSON.stringify({
            error: "Document limit reached",
            limitReached: true,
            message: "You have reached your document upload limit. Please upgrade your plan to upload more documents."
          }),
          {
            status: 403,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    } else {
      console.log("Development mode: Bypassing document limit check in document upload API");
    }
    try {
      console.log("Uploading document:", {
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        documentType
      });
      const uploadResult = await uploadDocument(file, documentType, user.id);
      console.log("Upload result:", uploadResult);
      console.log("Creating document with data:", {
        label,
        fileUrl: uploadResult.publicPath,
        userId: user.id,
        profileId: profileId || null,
        type: documentType
      });
      console.log("User ID:", user.id);
      console.log("Document data for Prisma:", {
        label,
        fileUrl: uploadResult.publicPath,
        filePath: uploadResult.filePath,
        fileName: uploadResult.originalFileName,
        type: documentType,
        contentType: uploadResult.contentType,
        fileSize: uploadResult.fileSize,
        storageType: "local",
        storageLocation: documentType,
        userId: user.id,
        profileId: profileId || void 0
      });
      let document;
      try {
        document = await prisma.document.create({
          data: {
            label,
            fileUrl: uploadResult.publicPath,
            filePath: uploadResult.filePath,
            fileName: uploadResult.originalFileName,
            type: documentType,
            contentType: uploadResult.contentType,
            fileSize: uploadResult.fileSize,
            storageType: "r2",
            // Updated to use R2 storage
            storageLocation: documentType,
            // Note: 'source' field is not in the Prisma schema, so we can't set it here
            userId: user.id,
            ...profileId ? { profileId } : {}
          }
        });
        console.log("Document created successfully:", document);
        await trackDocumentUpload(user.id);
        console.log("Document upload tracked for feature usage");
      } catch (dbError) {
        console.error("Error creating document in database:", dbError);
        throw new Error(`Database error: ${dbError.message}`);
      }
      const source = determineDocumentSource(document);
      return new Response(
        JSON.stringify({
          document: {
            ...document,
            source
            // Add the determined source information for the frontend
          },
          message: "Document uploaded successfully."
        }),
        {
          headers: { "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      if (error.message && error.message.includes("File type")) {
        return new Response(
          JSON.stringify({
            error: "Invalid file type",
            details: error.message
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("Error creating document:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to create document",
        details: error.message
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

export { POST };
//# sourceMappingURL=_server.ts-IPR6-vC-.js.map
