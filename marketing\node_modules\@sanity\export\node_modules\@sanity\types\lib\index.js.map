{"version": 3, "file": "index.js", "sources": ["../src/helpers.ts", "../src/reference/asserters.ts", "../src/assets/asserters.ts", "../src/crossDatasetReference/asserters.ts", "../src/documents/asserters.ts", "../src/markers/asserters.ts", "../src/mutations/asserters.ts", "../src/paths/asserters.ts", "../src/portableText/asserters.ts", "../src/schema/asserters.ts", "../src/schema/define.ts", "../src/search/types.ts", "../src/search/asserters.ts", "../src/slug/asserters.ts", "../src/transactionLog/asserters.ts", "../src/validation/asserters.ts"], "sourcesContent": ["export function isObject(obj: unknown): obj is Record<string, unknown> {\n  return typeof obj === 'object' && obj !== null && !Array.isArray(obj)\n}\n", "import {isObject} from '../helpers'\nimport {type Reference} from './types'\n\n/** @internal */\nexport function isReference(reference: unknown): reference is Reference {\n  return isObject(reference) && typeof reference._ref === 'string'\n}\n", "import {isObject} from '../helpers'\nimport {isReference} from '../reference'\nimport {type Image} from './types'\n\n/** @public */\nexport function isImage(value: unknown): value is Image {\n  return isObject(value) && isReference(value.asset) && value.asset._ref.startsWith('image-')\n}\n", "import {isObject} from '../helpers'\nimport {type CrossDatasetReferenceValue} from './types'\n\n/** @beta */\nexport function isCrossDatasetReference(\n  reference: unknown,\n): reference is CrossDatasetReferenceValue {\n  return (\n    isObject(reference) &&\n    typeof reference._ref === 'string' &&\n    typeof reference._dataset === 'string' &&\n    typeof reference._projectId === 'string'\n  )\n}\n", "import {isObject} from '../helpers'\nimport {type KeyedObject, type SanityDocument, type TypedObject} from './types'\n\n/** @public */\nexport function isSanityDocument(document: unknown): document is SanityDocument {\n  return (\n    isObject(document) && typeof document._id === 'string' && typeof document._type === 'string'\n  )\n}\n\n/** @public */\nexport function isTypedObject(obj: unknown): obj is TypedObject {\n  return isObject(obj) && typeof obj._type === 'string'\n}\n\n/** @public */\nexport function isKeyedObject(obj: unknown): obj is KeyedObject {\n  return isObject(obj) && typeof obj._key === 'string'\n}\n", "import {type ValidationMarker} from './types'\n\n/** @internal */\nexport function isValidationErrorMarker(\n  marker: ValidationMarker,\n): marker is ValidationMarker & {level: 'error'} {\n  return marker.level === 'error'\n}\n\n/** @internal */\nexport function isValidationWarningMarker(\n  marker: ValidationMarker,\n): marker is ValidationMarker & {level: 'warning'} {\n  return marker.level === 'warning'\n}\n\n/** @internal */\nexport function isValidationInfoMarker(\n  marker: ValidationMarker,\n): marker is ValidationMarker & {level: 'info'} {\n  return marker.level === 'info'\n}\n", "import {type TransactionLogMutation} from '../transactionLog'\nimport {\n  type CreateIfNotExistsMutation,\n  type CreateMutation,\n  type CreateOrReplaceMutation,\n  type DeleteMutation,\n  type Mutation,\n  type PatchMutation,\n} from './types'\n\n/** @internal */\nexport function isCreateMutation(\n  mutation: Mutation | TransactionLogMutation,\n): mutation is CreateMutation {\n  return 'create' in mutation\n}\n\n/** @internal */\nexport function isCreateIfNotExistsMutation(\n  mutation: Mutation | TransactionLogMutation,\n): mutation is CreateIfNotExistsMutation {\n  return 'createIfNotExists' in mutation\n}\n\n/** @internal */\nexport function isCreateOrReplaceMutation(\n  mutation: Mutation | TransactionLogMutation,\n): mutation is CreateOrReplaceMutation {\n  return 'createOrReplace' in mutation\n}\n\n/** @internal */\nexport function isDeleteMutation(\n  mutation: Mutation | TransactionLogMutation,\n): mutation is DeleteMutation {\n  return 'delete' in mutation\n}\n\n/** @internal */\nexport function isPatchMutation(\n  mutation: Mutation | TransactionLogMutation,\n): mutation is PatchMutation {\n  return 'patch' in mutation\n}\n", "import {type IndexTuple, type KeyedSegment, type PathSegment} from './types'\n\nconst reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/\nconst reIndexTuple = /^\\d*:\\d*$/\n\n/** @internal */\nexport function isIndexSegment(segment: PathSegment): segment is number {\n  return typeof segment === 'number' || (typeof segment === 'string' && /^\\[\\d+\\]$/.test(segment))\n}\n\n/** @internal */\nexport function isKeySegment(segment: PathSegment): segment is KeyedSegment {\n  if (typeof segment === 'string') {\n    return reKeySegment.test(segment.trim())\n  }\n\n  return typeof segment === 'object' && '_key' in segment\n}\n\n/** @internal */\nexport function isIndexTuple(segment: PathSegment): segment is IndexTuple {\n  if (typeof segment === 'string' && reIndexTuple.test(segment)) {\n    return true\n  }\n\n  if (!Array.isArray(segment) || segment.length !== 2) {\n    return false\n  }\n\n  const [from, to] = segment\n  return (typeof from === 'number' || from === '') && (typeof to === 'number' || to === '')\n}\n", "import {type PortableTextObject, type PortableTextSpan, type PortableTextTextBlock} from './types'\n\nfunction isRecord(value: unknown): value is Record<string, unknown> {\n  return !!value && (typeof value == 'object' || typeof value == 'function')\n}\n\n/**\n * Assert that a given object is a portable-text text-block type object\n *\n * @remarks\n * * The `markDefs` and `style` property of a block is optional.\n * * Block types can be named, so expect anything of the _type property.\n *\n * @alpha\n */\nexport function isPortableTextTextBlock<T = PortableTextSpan | PortableTextObject>(\n  value: unknown,\n): value is PortableTextTextBlock<T> {\n  return (\n    isRecord(value) &&\n    typeof value._type === 'string' && // block types can be named, so expect anything here.\n    Array.isArray(value.children) &&\n    value.children.every((child) => isRecord(child)) &&\n    ('markDefs' in value // optional property\n      ? Array.isArray(value.markDefs) && value.markDefs.every((def) => isRecord(def))\n      : true) &&\n    ('style' in value ? typeof value.style === 'string' : true) // optional property\n  )\n}\n\n/**\n * Assert that a given object is a portable-text span-type object\n *\n * @remarks\n * The `marks` property of a block is optional.\n *\n * @alpha\n */\nexport function isPortableTextSpan(value: unknown): value is PortableTextSpan {\n  return (\n    isRecord(value) &&\n    value._type === 'span' &&\n    typeof value.text === 'string' &&\n    ('marks' in value // optional property\n      ? Array.isArray(value.marks) && value.marks.every((mark) => typeof mark === 'string')\n      : true)\n  )\n}\n\n/**\n * Assert that a given object is a portable-text list-text-block-type object\n *\n * @remarks\n * Uses `isPortableTextTextBlock` and checks for `listItem` and `level`\n *\n * @see isPortableTextTextBlock\n *\n * @alpha\n */\nexport function isPortableTextListBlock<T = PortableTextSpan | PortableTextObject>(\n  value: unknown,\n): value is PortableTextTextBlock<T> {\n  return (\n    isPortableTextTextBlock(value) &&\n    'listItem' in value &&\n    typeof value.listItem === 'string' &&\n    'level' in value &&\n    Number.isInteger(value.level)\n  )\n}\n", "import {type CrossDatasetReferenceSchemaType} from '../crossDatasetReference'\nimport {type TitledListValue} from './definition'\nimport {\n  type ArraySchemaType,\n  type BaseSchemaType,\n  type BlockChildrenObjectField,\n  type BlockListObjectField,\n  type BlockSchemaType,\n  type BlockStyleObjectField,\n  type BooleanSchemaType,\n  type DeprecatedSchemaType,\n  type DeprecationConfiguration,\n  type FileSchemaType,\n  type ImageSchemaType,\n  type NumberSchemaType,\n  type ObjectSchemaType,\n  type ReferenceSchemaType,\n  type SchemaType,\n  type SpanSchemaType,\n  type StringSchemaType,\n} from './types'\n\nfunction isRecord(value: unknown): value is Record<string, unknown> {\n  return !!value && (typeof value == 'object' || typeof value == 'function')\n}\n\n/**\n * Returns wether or not the given type is a document type\n * (eg that it was defined as `type: 'document'`)\n *\n * @param type - Schema type to test\n * @returns True if type is a document type, false otherwise\n *\n * @public\n */\nexport function isDocumentSchemaType(type: unknown): type is ObjectSchemaType {\n  if (!isObjectSchemaType(type)) {\n    return false\n  }\n\n  let current: SchemaType | undefined = type as SchemaType\n  while (current) {\n    if (current.name === 'document') {\n      return true\n    }\n\n    current = current.type\n  }\n  return false\n}\n\n/** @internal */\nexport function isObjectSchemaType(type: unknown): type is ObjectSchemaType {\n  if (!isRecord(type)) return false\n  return type.jsonType === 'object'\n}\n\n/** @internal */\nexport function isArraySchemaType(type: unknown): type is ArraySchemaType {\n  if (!isRecord(type)) return false\n  return type.jsonType === 'array'\n}\n\n/** @internal */\nexport function isArrayOfBlocksSchemaType(\n  type: unknown,\n): type is ArraySchemaType<ObjectSchemaType> {\n  return isArraySchemaType(type) && type.of.some((memberType) => isBlockSchemaType(memberType))\n}\n\n/** @internal */\nexport function isArrayOfObjectsSchemaType(\n  type: unknown,\n): type is ArraySchemaType<ObjectSchemaType> {\n  return isArraySchemaType(type) && type.of.every((memberType) => isObjectSchemaType(memberType))\n}\n\n/** @internal */\nexport function isArrayOfPrimitivesSchemaType(type: unknown): type is ArraySchemaType {\n  return isArraySchemaType(type) && type.of.every((memberType) => isPrimitiveSchemaType(memberType))\n}\n\n/** @internal */\nexport function isBooleanSchemaType(type: unknown): type is BooleanSchemaType {\n  if (!isRecord(type)) return false\n  return type.jsonType === 'boolean'\n}\n\n/** @internal */\nexport function isStringSchemaType(type: unknown): type is StringSchemaType {\n  if (!isRecord(type)) return false\n  return type.jsonType === 'string'\n}\n\n/** @internal */\nexport function isNumberSchemaType(type: unknown): type is NumberSchemaType {\n  if (!isRecord(type)) return false\n  return type.jsonType === 'number'\n}\n\n/** @internal */\nexport function isPrimitiveSchemaType(\n  type: unknown,\n): type is BooleanSchemaType | StringSchemaType | NumberSchemaType {\n  return isBooleanSchemaType(type) || isStringSchemaType(type) || isNumberSchemaType(type)\n}\n\n/** @internal */\nexport function isReferenceSchemaType(type: unknown): type is ReferenceSchemaType {\n  return isRecord(type) && (type.name === 'reference' || isReferenceSchemaType(type.type))\n}\n\n/** @internal */\nexport function isImageSchemaType(type: unknown): type is ImageSchemaType {\n  return isRecord(type) && (type.name === 'image' || isImageSchemaType(type.type))\n}\n\n/** @internal */\nexport function isFileSchemaType(type: unknown): type is FileSchemaType {\n  return isRecord(type) && (type.name === 'file' || isFileSchemaType(type.type))\n}\n\n/** @internal */\nexport function isDeprecatedSchemaType<TSchemaType extends BaseSchemaType>(\n  type: TSchemaType,\n): type is DeprecatedSchemaType<TSchemaType> {\n  if (!isRecord(type)) return false\n  return typeof type.deprecated !== 'undefined'\n}\n\n/** @internal */\nexport function isDeprecationConfiguration(type: unknown): type is DeprecationConfiguration {\n  if (!isRecord(type)) return false\n  return typeof type.deprecated !== 'undefined'\n}\n\n/** @internal */\nexport function isCrossDatasetReferenceSchemaType(\n  type: unknown,\n): type is CrossDatasetReferenceSchemaType {\n  return (\n    isRecord(type) &&\n    (type.name === 'crossDatasetReference' || isCrossDatasetReferenceSchemaType(type.type))\n  )\n}\n\n/** @internal */\nexport function isTitledListValue(item: unknown): item is TitledListValue {\n  return typeof item === 'object' && item !== null && 'title' in item && 'value' in item\n}\n\n/** @internal */\nexport function isSpanSchemaType(type: unknown): type is SpanSchemaType {\n  if (!isRecord(type)) return false\n  // we check for `annotations` and `decorators` instead of `type.name` because\n  // schema names can technically change if someone extends the type\n  return Array.isArray(type.annotations) && Array.isArray(type.decorators)\n}\n\n/** @internal */\nexport function isBlockSchemaType(type: unknown): type is BlockSchemaType {\n  if (!isRecord(type)) return false\n  if (!Array.isArray(type.fields)) return false\n  const maybeSpanChildren = type.fields.find(isBlockChildrenObjectField)\n  const maybeStyle = type.fields.find(isBlockStyleObjectField)\n  const maybeList = type.fields.find(isBlockListObjectField)\n  return (\n    isBlockChildrenObjectField(maybeSpanChildren) &&\n    isBlockStyleObjectField(maybeStyle) &&\n    isBlockListObjectField(maybeList)\n  )\n}\n\n/** @internal */\nexport function isBlockStyleObjectField(field: unknown): field is BlockStyleObjectField {\n  if (!isRecord(field)) return false\n  if (field.name !== 'style') return false\n  return isRecord(field.type) && field.type.jsonType === 'string'\n}\n\n/** @internal */\nexport function isBlockListObjectField(field: unknown): field is BlockListObjectField {\n  if (!isRecord(field)) return false\n  if (field.name !== 'listItem') return false\n  return isRecord(field.type) && field.type.jsonType === 'string'\n}\n\n/** @internal */\nexport function isBlockChildrenObjectField(field: unknown): field is BlockChildrenObjectField {\n  if (!isRecord(field)) return false\n  if (field.name !== 'children') return false\n  if (!isArraySchemaType(field.type)) return false\n  // there will always be a span item in `SpanChildrenObjectField`\n  return field.type.of.some(isSpanSchemaType)\n}\n", "import {\n  type DefineArrayMemberBase,\n  type DefineSchemaBase,\n  type DefineSchemaOptions,\n  type MaybeAllowUnknownProps,\n  type NarrowPreview,\n  type StrictDefinition,\n  type WidenInitialValue,\n  type WidenValidation,\n} from './defineTypes'\nimport {type FieldDefinitionBase, type IntrinsicTypeName} from './definition'\nimport {type AutocompleteString} from './types'\n\n/**\n * Helper function for defining a Sanity type definition. This function does not do anything on its own;\n * it exists to check that your schema definition is correct, and help autocompletion in your IDE.\n *\n * This function will narrow the schema type down to fields and options based on the provided type-string.\n *\n * Schema types defined using `defineType` should typically be added to the Studio config under `schema.types`.\n * Defined types can be referenced by their `name`. This is referred to as a type-alias.\n *\n * When using type-aliases as `type`, `defineType` cannot know the base-type, so type-safety will be reduced.\n * If you know the base type of the type-alias, provide `defineOptions.aliasFor: <base type name>`.\n * This will enforce that the schema definition conforms with the provided type.\n *\n * By default, `defineType` only allows known properties and options.\n * Use `defineOptions.strict: false` to allow unknown properties and options.\n *\n * ### Basic usage\n *\n * ```ts\n * defineType({\n *   type: 'object',\n *   name: 'custom-object',\n *   fields: [ {type: 'string', name: 'title', title: 'Title'}],\n * })\n * ```\n *\n * ### Usage with aliasFor narrowing\n *\n * ```ts\n * defineType({\n *   type: 'custom-object',\n *   name: 'redefined-custom-object',\n *   options: {\n *     columns: 2\n *   }\n * }, {aliasFor: 'object' })\n * ```\n *\n * ### Allow unknown properties\n *\n * ```ts\n * defineType({\n *   type: 'custom-object',\n *   name: 'redefined-custom-object',\n *   allowsUnknownProperties: true\n *   options: {\n *     columns: 2,\n *     allowsUnknownOptions: true\n *   }\n * }, {strict: false})\n * ```\n * ### Maximum safety and best autocompletion\n *\n * Use {@link defineType}, {@link defineField} and {@link defineArrayMember}:\n *\n * ```ts\n *  defineType({\n *    type: 'object',\n *    name: 'custom-object',\n *    fields: [\n *      defineField({\n *        type: 'array',\n *        name: 'arrayField',\n *        title: 'Things',\n *        of: [\n *          defineArrayMember({\n *            type: 'object',\n *            name: 'type-name-in-array',\n *            fields: [defineField({type: 'string', name: 'title', title: 'Title'})],\n *          }),\n *        ],\n *      }),\n *    ],\n *  })\n * ```\n *\n * ## Note on type-safety in the current implementation\n *\n * Type-safety inside array-like properties (schema properties like `fields` and `of`) can only be guaranteed when\n * {@link defineField} and {@link defineArrayMember} are used to wrap each value in the array.\n *\n * For array-values without a function-wrapper, TypeScript will resolve to a union type of all possible properties across\n * all schema types. This result in less precise typing.\n *\n * ### Extending the Sanity Schema types\n *\n * If you want to extend the Sanity Schema types with your own properties or options to make them typesafe,\n * you can use [TypeScript declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html).\n *\n * With declaration merging, properties and options will be available in a type-safe manner, and\n * `strict: false` will not be necessary.\n *\n * #### Example: Add option to StringOptions\n *\n * ```ts\n * // string.ts\n *\n * //redeclare the sanity module\n * declare module 'sanity' {\n *  // redeclare StringOptions; it will be merged with StringOptions in the sanity module\n *  export interface StringOptions {\n *    myCustomOption?: boolean\n *  }\n * }\n *\n * // the option is now part of the StringOptions type, just as if it was declared in the sanity codebase:\n * defineType({\n *   type: 'string',\n *   name: 'my-string',\n *   options: {\n *     myCustomOption: true // this does not give an error anymore\n *   }\n * })\n *\n * ```\n *\n * #### Example: Add a schema definition to \"intrinsic-types\"\n *\n * ```ts\n * //my-custom-type-definition.ts\n *\n * // create a new schema definition based on object (we remove the ability to assign field, change the type add some options)\n *  export type MagicallyAddedDefinition = Omit<Schema.ObjectDefinition, 'type' | 'fields'> & {\n *    type: 'magically-added-type'\n *    options?: {\n *      sparkles?: boolean\n *    }\n *  }\n *\n *  // redeclares sanity module so we can add interfaces props to it\n * declare module 'sanity' {\n *     // redeclares IntrinsicDefinitions and adds a named definition to it\n *     // it is important that the key is the same as the type in the definition ('magically-added-type')\n *     export interface IntrinsicDefinitions {\n *       'magically-added-type': MagicallyAddedDefinition\n *     }\n * }\n *\n * // defineType will now narrow `type: 'magically-added-type'` to `MagicallyAddedDefinition`\n * defineType({\n *   type: 'magically-added-type'\n *   name: 'magic',\n *   options: {\n *     sparkles: true // this is allowed,\n *     //@ts-expect-error this is not allowed in MagicallyAddedDefinition.options\n *     sparks: true\n *   }\n * })\n * ```\n *\n * @param schemaDefinition - should be a valid schema type definition.\n * @param defineOptions - optional param to provide type hints for `schemaDefinition`.\n *\n * @see defineField\n * @see defineArrayMember\n * @see typed\n *\n * @beta\n */\nexport function defineType<\n  const TType extends IntrinsicTypeName | AutocompleteString,\n  const TName extends string,\n  TSelect extends Record<string, string> | undefined,\n  TPrepareValue extends Record<keyof TSelect, any> | undefined,\n  TAlias extends IntrinsicTypeName | undefined,\n  TStrict extends StrictDefinition,\n>(\n  schemaDefinition: {\n    type: TType\n    name: TName\n  } & DefineSchemaBase<TType, TAlias> &\n    NarrowPreview<TType, TAlias, TSelect, TPrepareValue> &\n    MaybeAllowUnknownProps<TStrict>,\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  defineOptions?: DefineSchemaOptions<TStrict, TAlias>,\n): typeof schemaDefinition {\n  return schemaDefinition\n}\n\n/**\n * Define a field within a document, object, image or file definition `fields` array.\n *\n * This function will narrow the schema type down to fields and options based on the provided\n * type-string.\n *\n * Using `defineField` is optional, but should provide improved autocompletion in your IDE, when building your schema.\n * Field-properties like `validation` and `initialValue`will also be more specific.\n *\n * See {@link defineType} for more examples.\n *\n * @param schemaField - should be a valid field type definition.\n * @param defineOptions - optional param to provide type hints for `schemaField`.\n *\n * @see defineField\n * @see defineArrayMember\n * @see typed\n *\n * @beta\n */\nexport function defineField<\n  const TType extends IntrinsicTypeName | AutocompleteString,\n  const TName extends string,\n  TSelect extends Record<string, string> | undefined,\n  TPrepareValue extends Record<keyof TSelect, any> | undefined,\n  TAlias extends IntrinsicTypeName | undefined,\n  TStrict extends StrictDefinition,\n>(\n  schemaField: {\n    type: TType\n    name: TName\n  } & DefineSchemaBase<TType, TAlias> &\n    NarrowPreview<TType, TAlias, TSelect, TPrepareValue> &\n    MaybeAllowUnknownProps<TStrict> &\n    FieldDefinitionBase,\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  defineOptions?: DefineSchemaOptions<TStrict, TAlias>,\n): typeof schemaField & WidenValidation & WidenInitialValue {\n  // TODO: re-evaluate the need for this cast\n  return schemaField as typeof schemaField & WidenValidation & WidenInitialValue\n}\n\n/**\n * Define an array item member type within an array definition `of`-array.\n *\n * This function will narrow the schema type down to fields and options based on the provided\n * `type` string.\n *\n * Using `defineArrayMember` is optional, but should provide improved autocompletion in your IDE, when building your schema.\n * Field properties like `validation` and `initialValue` will also be more specific.\n *\n * See {@link defineType} for example usage.\n *\n * @param arrayOfSchema - should be a valid `array.of` member definition.\n * @param defineOptions - optional param to provide type hints for `arrayOfSchema`.\n *\n * @see defineType\n * @see defineField\n * @see typed\n *\n * @beta\n */\nexport function defineArrayMember<\n  const TType extends IntrinsicTypeName | AutocompleteString,\n  const TName extends string,\n  TSelect extends Record<string, string> | undefined,\n  TPrepareValue extends Record<keyof TSelect, any> | undefined,\n  TAlias extends IntrinsicTypeName | undefined,\n  TStrict extends StrictDefinition,\n>(\n  arrayOfSchema: {\n    type: TType\n    /**\n     * When provided, `name` is used as `_type` for the array item when stored.\n     *\n     * Necessary when an array contains multiple entries with the same `type`, each with\n     * different configuration (title and initialValue for instance).\n     */\n    name?: TName\n  } & DefineArrayMemberBase<TType, TAlias> &\n    NarrowPreview<TType, TAlias, TSelect, TPrepareValue> &\n    MaybeAllowUnknownProps<TStrict>,\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  defineOptions?: DefineSchemaOptions<TStrict, TAlias>,\n): typeof arrayOfSchema & WidenValidation & WidenInitialValue {\n  // TODO: re-evaluate the need for this cast\n  return arrayOfSchema as typeof arrayOfSchema & WidenValidation & WidenInitialValue\n}\n\n/**\n * `typed` can be used to ensure that an object conforms to an exact interface.\n *\n * It can be useful when working with `defineType` and `defineField` on occasions where a wider type with\n * custom options or properties is required.\n *\n * ## Example  usage\n * ```ts\n *  defineField({\n *    type: 'string',\n *    name: 'nestedField',\n *    options: typed<StringOptions & {myCustomOption: boolean}>({\n *      layout: 'radio',\n *      // allowed\n *      myCustomOption: true,\n *      //@ts-expect-error unknownProp is not part of StringOptions & {myCustomOption: boolean}\n *      unknownProp: 'not allowed in typed context',\n *    }),\n *  }),\n * ```\n *\n * @param input - returned directly\n *\n * @internal\n */\nexport function typed<T>(input: T): T {\n  return input\n}\n", "/**\n * @public\n */\nexport const searchStrategies = ['groqLegacy', 'textSearch', 'groq2024'] as const\n\n/**\n * @public\n */\nexport type SearchStrategy = (typeof searchStrategies)[number]\n", "import {searchStrategies, type SearchStrategy} from './types'\n\n/**\n * @internal\n */\nexport function isSearchStrategy(\n  maybeSearchStrategy: unknown,\n): maybeSearchStrategy is SearchStrategy {\n  return searchStrategies.includes(maybeSearchStrategy as SearchStrategy)\n}\n", "import {isObject} from '../helpers'\nimport {type Slug} from './types'\n\n/**\n * Checks whether the given `thing` is a slug, eg an object with a `current` string property.\n *\n * @param thing - The thing to check\n * @returns True if slug, false otherwise\n * @public\n */\nexport function isSlug(thing: unknown): thing is Slug {\n  return isObject(thing) && typeof thing.current === 'string'\n}\n", "import {type Mutation} from '../mutations'\nimport {type CreateSquashedMutation, type TransactionLogMutation} from './types'\n\n/** @internal */\nexport function isCreateSquashedMutation(\n  mutation: Mutation | TransactionLogMutation,\n): mutation is CreateSquashedMutation {\n  return 'createSquashed' in mutation\n}\n", "import {type FormNodeValidation} from './types'\n\n/** @internal */\nexport function isValidationError(\n  node: FormNodeValidation,\n): node is FormNodeValidation & {level: 'error'} {\n  return node.level === 'error'\n}\n\n/** @internal */\nexport function isValidationWarning(\n  node: FormNodeValidation,\n): node is FormNodeValidation & {level: 'warning'} {\n  return node.level === 'warning'\n}\n\n/** @internal */\nexport function isValidationInfo(\n  node: FormNodeValidation,\n): node is FormNodeValidation & {level: 'info'} {\n  return node.level === 'info'\n}\n"], "names": ["isRecord"], "mappings": ";;AAAO,SAAS,SAAS,KAA8C;AAC9D,SAAA,OAAO,OAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG;AACtE;ACEO,SAAS,YAAY,WAA4C;AACtE,SAAO,SAAS,SAAS,KAAK,OAAO,UAAU,QAAS;AAC1D;ACDO,SAAS,QAAQ,OAAgC;AAC/C,SAAA,SAAS,KAAK,KAAK,YAAY,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,WAAW,QAAQ;AAC5F;ACHO,SAAS,wBACd,WACyC;AACzC,SACE,SAAS,SAAS,KAClB,OAAO,UAAU,QAAS,YAC1B,OAAO,UAAU,YAAa,YAC9B,OAAO,UAAU,cAAe;AAEpC;ACTO,SAAS,iBAAiB,UAA+C;AAE5E,SAAA,SAAS,QAAQ,KAAK,OAAO,SAAS,OAAQ,YAAY,OAAO,SAAS,SAAU;AAExF;AAGO,SAAS,cAAc,KAAkC;AAC9D,SAAO,SAAS,GAAG,KAAK,OAAO,IAAI,SAAU;AAC/C;AAGO,SAAS,cAAc,KAAkC;AAC9D,SAAO,SAAS,GAAG,KAAK,OAAO,IAAI,QAAS;AAC9C;ACfO,SAAS,wBACd,QAC+C;AAC/C,SAAO,OAAO,UAAU;AAC1B;AAGO,SAAS,0BACd,QACiD;AACjD,SAAO,OAAO,UAAU;AAC1B;AAGO,SAAS,uBACd,QAC8C;AAC9C,SAAO,OAAO,UAAU;AAC1B;ACVO,SAAS,iBACd,UAC4B;AAC5B,SAAO,YAAY;AACrB;AAGO,SAAS,4BACd,UACuC;AACvC,SAAO,uBAAuB;AAChC;AAGO,SAAS,0BACd,UACqC;AACrC,SAAO,qBAAqB;AAC9B;AAGO,SAAS,iBACd,UAC4B;AAC5B,SAAO,YAAY;AACrB;AAGO,SAAS,gBACd,UAC2B;AAC3B,SAAO,WAAW;AACpB;ACzCA,MAAM,eAAe,4BACf,eAAe;AAGd,SAAS,eAAe,SAAyC;AAC/D,SAAA,OAAO,WAAY,YAAa,OAAO,WAAY,YAAY,YAAY,KAAK,OAAO;AAChG;AAGO,SAAS,aAAa,SAA+C;AAC1E,SAAI,OAAO,WAAY,WACd,aAAa,KAAK,QAAQ,KAAK,CAAC,IAGlC,OAAO,WAAY,YAAY,UAAU;AAClD;AAGO,SAAS,aAAa,SAA6C;AACxE,MAAI,OAAO,WAAY,YAAY,aAAa,KAAK,OAAO;AACnD,WAAA;AAGT,MAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW;AACzC,WAAA;AAGH,QAAA,CAAC,MAAM,EAAE,IAAI;AACX,UAAA,OAAO,QAAS,YAAY,SAAS,QAAQ,OAAO,MAAO,YAAY,OAAO;AACxF;AC7BA,SAASA,WAAS,OAAkD;AAClE,SAAO,CAAC,CAAC,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS;AACjE;AAWO,SAAS,wBACd,OACmC;AACnC,SACEA,WAAS,KAAK,KACd,OAAO,MAAM,SAAU;AAAA,EACvB,MAAM,QAAQ,MAAM,QAAQ,KAC5B,MAAM,SAAS,MAAM,CAAC,UAAUA,WAAS,KAAK,CAAC,MAC9C,cAAc,QACX,MAAM,QAAQ,MAAM,QAAQ,KAAK,MAAM,SAAS,MAAM,CAAC,QAAQA,WAAS,GAAG,CAAC,IAC5E,QACH,WAAW,QAAQ,OAAO,MAAM,SAAU,WAAW;AAE1D;AAUO,SAAS,mBAAmB,OAA2C;AAE1E,SAAAA,WAAS,KAAK,KACd,MAAM,UAAU,UAChB,OAAO,MAAM,QAAS,aACrB,WAAW,QACR,MAAM,QAAQ,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,CAAC,SAAS,OAAO,QAAS,QAAQ,IAClF;AAER;AAYO,SAAS,wBACd,OACmC;AACnC,SACE,wBAAwB,KAAK,KAC7B,cAAc,SACd,OAAO,MAAM,YAAa,YAC1B,WAAW,SACX,OAAO,UAAU,MAAM,KAAK;AAEhC;AC/CA,SAAS,SAAS,OAAkD;AAClE,SAAO,CAAC,CAAC,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS;AACjE;AAWO,SAAS,qBAAqB,MAAyC;AACxE,MAAA,CAAC,mBAAmB,IAAI;AACnB,WAAA;AAGT,MAAI,UAAkC;AACtC,SAAO,WAAS;AACd,QAAI,QAAQ,SAAS;AACZ,aAAA;AAGT,cAAU,QAAQ;AAAA,EAAA;AAEb,SAAA;AACT;AAGO,SAAS,mBAAmB,MAAyC;AAC1E,SAAK,SAAS,IAAI,IACX,KAAK,aAAa,WADG;AAE9B;AAGO,SAAS,kBAAkB,MAAwC;AACxE,SAAK,SAAS,IAAI,IACX,KAAK,aAAa,UADG;AAE9B;AAGO,SAAS,0BACd,MAC2C;AACpC,SAAA,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,eAAe,kBAAkB,UAAU,CAAC;AAC9F;AAGO,SAAS,2BACd,MAC2C;AACpC,SAAA,kBAAkB,IAAI,KAAK,KAAK,GAAG,MAAM,CAAC,eAAe,mBAAmB,UAAU,CAAC;AAChG;AAGO,SAAS,8BAA8B,MAAwC;AAC7E,SAAA,kBAAkB,IAAI,KAAK,KAAK,GAAG,MAAM,CAAC,eAAe,sBAAsB,UAAU,CAAC;AACnG;AAGO,SAAS,oBAAoB,MAA0C;AAC5E,SAAK,SAAS,IAAI,IACX,KAAK,aAAa,YADG;AAE9B;AAGO,SAAS,mBAAmB,MAAyC;AAC1E,SAAK,SAAS,IAAI,IACX,KAAK,aAAa,WADG;AAE9B;AAGO,SAAS,mBAAmB,MAAyC;AAC1E,SAAK,SAAS,IAAI,IACX,KAAK,aAAa,WADG;AAE9B;AAGO,SAAS,sBACd,MACiE;AACjE,SAAO,oBAAoB,IAAI,KAAK,mBAAmB,IAAI,KAAK,mBAAmB,IAAI;AACzF;AAGO,SAAS,sBAAsB,MAA4C;AACzE,SAAA,SAAS,IAAI,MAAM,KAAK,SAAS,eAAe,sBAAsB,KAAK,IAAI;AACxF;AAGO,SAAS,kBAAkB,MAAwC;AACjE,SAAA,SAAS,IAAI,MAAM,KAAK,SAAS,WAAW,kBAAkB,KAAK,IAAI;AAChF;AAGO,SAAS,iBAAiB,MAAuC;AAC/D,SAAA,SAAS,IAAI,MAAM,KAAK,SAAS,UAAU,iBAAiB,KAAK,IAAI;AAC9E;AAGO,SAAS,uBACd,MAC2C;AAC3C,SAAK,SAAS,IAAI,IACX,OAAO,KAAK,aAAe,MADN;AAE9B;AAGO,SAAS,2BAA2B,MAAiD;AAC1F,SAAK,SAAS,IAAI,IACX,OAAO,KAAK,aAAe,MADN;AAE9B;AAGO,SAAS,kCACd,MACyC;AAEvC,SAAA,SAAS,IAAI,MACZ,KAAK,SAAS,2BAA2B,kCAAkC,KAAK,IAAI;AAEzF;AAGO,SAAS,kBAAkB,MAAwC;AACxE,SAAO,OAAO,QAAS,YAAY,SAAS,QAAQ,WAAW,QAAQ,WAAW;AACpF;AAGO,SAAS,iBAAiB,MAAuC;AACtE,SAAK,SAAS,IAAI,IAGX,MAAM,QAAQ,KAAK,WAAW,KAAK,MAAM,QAAQ,KAAK,UAAU,IAH3C;AAI9B;AAGO,SAAS,kBAAkB,MAAwC;AACpE,MAAA,CAAC,SAAS,IAAI,KACd,CAAC,MAAM,QAAQ,KAAK,MAAM,EAAU,QAAA;AACxC,QAAM,oBAAoB,KAAK,OAAO,KAAK,0BAA0B,GAC/D,aAAa,KAAK,OAAO,KAAK,uBAAuB,GACrD,YAAY,KAAK,OAAO,KAAK,sBAAsB;AACzD,SACE,2BAA2B,iBAAiB,KAC5C,wBAAwB,UAAU,KAClC,uBAAuB,SAAS;AAEpC;AAGO,SAAS,wBAAwB,OAAgD;AAEtF,SADI,CAAC,SAAS,KAAK,KACf,MAAM,SAAS,UAAgB,KAC5B,SAAS,MAAM,IAAI,KAAK,MAAM,KAAK,aAAa;AACzD;AAGO,SAAS,uBAAuB,OAA+C;AAEpF,SADI,CAAC,SAAS,KAAK,KACf,MAAM,SAAS,aAAmB,KAC/B,SAAS,MAAM,IAAI,KAAK,MAAM,KAAK,aAAa;AACzD;AAGO,SAAS,2BAA2B,OAAmD;AAG5F,SAFI,CAAC,SAAS,KAAK,KACf,MAAM,SAAS,cACf,CAAC,kBAAkB,MAAM,IAAI,IAAU,KAEpC,MAAM,KAAK,GAAG,KAAK,gBAAgB;AAC5C;ACtBgB,SAAA,WAQd,kBAQA,eACyB;AAClB,SAAA;AACT;AAsBgB,SAAA,YAQd,aASA,eAC0D;AAEnD,SAAA;AACT;AAsBgB,SAAA,kBAQd,eAcA,eAC4D;AAErD,SAAA;AACT;AA2BO,SAAS,MAAS,OAAa;AAC7B,SAAA;AACT;ACpTO,MAAM,mBAAmB,CAAC,cAAc,cAAc,UAAU;ACEhE,SAAS,iBACd,qBACuC;AAChC,SAAA,iBAAiB,SAAS,mBAAqC;AACxE;ACCO,SAAS,OAAO,OAA+B;AACpD,SAAO,SAAS,KAAK,KAAK,OAAO,MAAM,WAAY;AACrD;ACRO,SAAS,yBACd,UACoC;AACpC,SAAO,oBAAoB;AAC7B;ACLO,SAAS,kBACd,MAC+C;AAC/C,SAAO,KAAK,UAAU;AACxB;AAGO,SAAS,oBACd,MACiD;AACjD,SAAO,KAAK,UAAU;AACxB;AAGO,SAAS,iBACd,MAC8C;AAC9C,SAAO,KAAK,UAAU;AACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}