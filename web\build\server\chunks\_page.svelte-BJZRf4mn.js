import { p as push, Q as bind_props, q as pop, O as escape_html, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  $$payload.out += `<h1 class="mb-4 text-2xl font-semibold">Optimize Resume</h1> <div class="text-muted-foreground mb-6 text-sm">Dashboard > Resume > Optimize</div> `;
  Card($$payload, {
    class: "mb-6",
    children: ($$payload2) => {
      Card_header($$payload2, {
        children: ($$payload3) => {
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(data.resume.name)}`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<p class="text-muted-foreground mb-2 text-sm">Last updated: ${escape_html(new Date(data.resume.updatedAt).toLocaleDateString())}</p> `;
          if (data.optimization) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="space-y-4">`;
            if (data.optimization.score !== null) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<p><strong>Optimization Score:</strong> ${escape_html(data.optimization.score)}%</p>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--> `;
            if (data.optimization.summary) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div><p class="font-semibold">Summary:</p> <p class="text-muted-foreground text-sm">${escape_html(data.optimization.summary)}</p></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--> `;
            if (data.optimization.suggestions?.length) {
              $$payload3.out += "<!--[-->";
              const each_array = ensure_array_like(data.optimization.suggestions);
              $$payload3.out += `<div><p class="font-semibold">Suggestions:</p> <ul class="text-muted-foreground list-inside list-disc text-sm"><!--[-->`;
              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                let item = each_array[$$index];
                $$payload3.out += `<li><strong>${escape_html(item.section)}:</strong> ${escape_html(item.suggestion)}</li>`;
              }
              $$payload3.out += `<!--]--></ul></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<p class="text-muted-foreground text-sm italic">Optimization is pending. Please check back shortly.</p>`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BJZRf4mn.js.map
