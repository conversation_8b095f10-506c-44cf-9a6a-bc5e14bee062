import { p as push, q as pop, K as fallback, V as copy_payload, W as assign_payload, Q as bind_props, O as escape_html, ai as invalid_default_snippet, P as stringify, J as attr_class, _ as clsx, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { F as FeatureCategory, a as FeatureAccessLevel } from './features-SWeUHekJ.js';
import { S as Skeleton } from './skeleton-C-NLefl9.js';
import { A as Alert, a as Alert_title, b as Alert_description } from './alert-title-gIeEAof-.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { A as Accordion_root, a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from './accordion-trigger-DwieKZVA.js';
import { B as Badge } from './badge-C9pSznab.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { P as Progress } from './progress-DR0SfStT.js';
import { B as Button } from './button-CrucCo1G.js';
import { R as Root, D as Dialog_content } from './index7-BURUpWjT.js';
import { D as Dialog_trigger } from './dialog-trigger2-C3bA-tk4.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { R as Root$1, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import 'clsx';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './chevron-down-xGjWLrZH.js';
import './Icon-A4vzmk-O.js';
import './scroll-lock-BkBz2nVp.js';
import './dialog-overlay-CspOQRJq.js';
import './x-DwZgpWRG.js';
import './dialog-trigger-CNXm7UD7.js';
import './dialog-description2-rfr-pd9k.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './hidden-input-1eDzjGOB.js';

function UsageSummaryCard($$payload, $$props) {
  let title = $$props["title"];
  let value = $$props["value"];
  let subtitle = fallback($$props["subtitle"], "");
  let warning = fallback($$props["warning"], "");
  let showWarning = fallback($$props["showWarning"], false);
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        class: "pb-2",
        children: ($$payload3) => {
          Card_title($$payload3, {
            class: "text-sm font-medium",
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(title)}`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="text-2xl font-bold">${escape_html(value !== void 0 ? value : "N/A")}</div> `;
          if (subtitle) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="text-muted-foreground text-xs">${escape_html(subtitle)}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (showWarning && warning) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="text-destructive text-xs">${escape_html(warning)}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  bind_props($$props, {
    title,
    value,
    subtitle,
    warning,
    showWarning
  });
}
function UsageSummary($$payload, $$props) {
  push();
  let usageSummary = $$props["usageSummary"];
  let resumeUsage = fallback($$props["resumeUsage"], null);
  $$payload.out += `<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">`;
  UsageSummaryCard($$payload, {
    title: "Total Features",
    value: usageSummary.totalFeatures
  });
  $$payload.out += `<!----> `;
  UsageSummaryCard($$payload, {
    title: "Features Used",
    value: usageSummary.featuresUsed,
    subtitle: usageSummary.featuresUsed !== void 0 && usageSummary.totalFeatures !== void 0 && usageSummary.totalFeatures > 0 ? `${Math.round(usageSummary.featuresUsed / usageSummary.totalFeatures * 100)}% of total` : ""
  });
  $$payload.out += `<!----> `;
  UsageSummaryCard($$payload, {
    title: "Features with Limits",
    value: usageSummary.featuresWithLimits
  });
  $$payload.out += `<!----> `;
  UsageSummaryCard($$payload, {
    title: "Features at Limit",
    value: usageSummary.featuresAtLimit,
    warning: "Consider upgrading your plan",
    showWarning: usageSummary.featuresAtLimit > 0
  });
  $$payload.out += `<!----> `;
  if (resumeUsage && resumeUsage.used !== void 0) {
    $$payload.out += "<!--[-->";
    UsageSummaryCard($$payload, {
      title: "Resume Submissions",
      value: resumeUsage.used,
      subtitle: resumeUsage.limit ? `${resumeUsage.remaining} remaining this month` : "Unlimited",
      warning: "Consider upgrading your plan",
      showWarning: resumeUsage.limit && resumeUsage.remaining === 0
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { usageSummary, resumeUsage });
  pop();
}
function formatLimitValue(value, unit) {
  if (value === "unlimited") return "Unlimited";
  return unit ? `${value} ${unit}` : `${value}`;
}
function getProgressColor(percentUsed) {
  if (percentUsed === void 0) return "bg-primary";
  if (percentUsed >= 90) return "bg-destructive";
  if (percentUsed >= 70) return "bg-warning";
  return "bg-primary";
}
function getAccessLevelColor(accessLevel) {
  switch (accessLevel) {
    case FeatureAccessLevel.Included:
      return "bg-primary";
    case FeatureAccessLevel.Limited:
      return "bg-warning";
    case FeatureAccessLevel.Unlimited:
      return "bg-success";
    case FeatureAccessLevel.NotIncluded:
      return "bg-destructive";
    default:
      return "bg-muted";
  }
}
function formatAccessLevel(accessLevel) {
  switch (accessLevel) {
    case FeatureAccessLevel.Included:
      return "Included";
    case FeatureAccessLevel.Limited:
      return "Limited";
    case FeatureAccessLevel.Unlimited:
      return "Unlimited";
    case FeatureAccessLevel.NotIncluded:
      return "Not Included";
    default:
      return "Unknown";
  }
}
function formatCategoryName(category) {
  return category.split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
}
function ResetButton($$payload, $$props) {
  push();
  let onReset = $$props["onReset"];
  let featureId = fallback($$props["featureId"], void 0);
  let limitId = fallback($$props["limitId"], void 0);
  let featureName = fallback($$props["featureName"], void 0);
  let limitName = fallback($$props["limitName"], void 0);
  let loading = false;
  let dialogOpen = false;
  function getResetMessage() {
    if (featureId && limitId) {
      return `Are you sure you want to reset usage for ${featureName || featureId} - ${limitName || limitId}?`;
    } else if (featureId) {
      return `Are you sure you want to reset all usage for ${featureName || featureId}?`;
    } else {
      return "Are you sure you want to reset all feature usage?";
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root($$payload2, {
      get open() {
        return dialogOpen;
      },
      set open($$value) {
        dialogOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_trigger($$payload3, {
          asChild: true,
          children: invalid_default_snippet,
          $$slots: {
            default: ($$payload4, { builder }) => {
              Button($$payload4, {
                variant: "outline",
                size: "sm",
                class: "text-destructive hover:bg-destructive hover:text-destructive-foreground",
                builders: [builder],
                children: ($$payload5) => {
                  Trash_2($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Reset Usage`;
                },
                $$slots: { default: true }
              });
            }
          }
        });
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Reset Feature Usage`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html(getResetMessage())}
        This action cannot be undone.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  variant: "outline",
                  disabled: loading,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  variant: "destructive",
                  disabled: loading,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html("Reset")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, {
    onReset,
    featureId,
    limitId,
    featureName,
    limitName
  });
  pop();
}
function FeatureCard($$payload, $$props) {
  push();
  let feature = $$props["feature"];
  let onReset = $$props["onReset"];
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-start justify-between">`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(feature.name)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Badge($$payload3, {
            variant: "outline",
            class: getAccessLevelColor(feature.accessLevel),
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(formatAccessLevel(feature.accessLevel))}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> `;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(feature.description)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        children: ($$payload3) => {
          if (feature.limits && feature.limits.length > 0) {
            $$payload3.out += "<!--[-->";
            const each_array = ensure_array_like(feature.limits);
            $$payload3.out += `<div class="space-y-4"><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let limit = each_array[$$index];
              $$payload3.out += `<div class="space-y-2"><div class="flex justify-between text-sm"><span>${escape_html(limit.name)}</span> <span>${escape_html(limit.used)} / ${escape_html(formatLimitValue(limit.value, limit.unit))}</span></div> `;
              if (limit.value !== "unlimited" && typeof limit.value === "number") {
                $$payload3.out += "<!--[-->";
                Progress($$payload3, {
                  value: limit.percentUsed || 0,
                  max: 100,
                  class: getProgressColor(limit.percentUsed)
                });
              } else {
                $$payload3.out += "<!--[!-->";
                $$payload3.out += `<div class="text-muted-foreground text-xs">Unlimited usage</div>`;
              }
              $$payload3.out += `<!--]--></div>`;
            }
            $$payload3.out += `<!--]--></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<div class="text-muted-foreground text-sm">No usage limits for this feature.</div>`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_footer($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex w-full justify-end">`;
          ResetButton($$payload3, {
            onReset,
            featureId: feature.id,
            featureName: feature.name
          });
          $$payload3.out += `<!----></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  bind_props($$props, { feature, onReset });
  pop();
}
function FeatureCategoryAccordion($$payload, $$props) {
  push();
  let categories = fallback($$props["categories"], () => [], true);
  let featuresByCategory = fallback($$props["featuresByCategory"], () => ({}), true);
  let onReset = $$props["onReset"];
  Accordion_root($$payload, {
    type: "single",
    class: "w-full",
    children: ($$payload2) => {
      const each_array = ensure_array_like(categories);
      $$payload2.out += `<!--[-->`;
      for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
        let category = each_array[$$index_1];
        Accordion_item($$payload2, {
          value: category,
          children: ($$payload3) => {
            Accordion_trigger($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex items-center gap-2"><span>${escape_html(formatCategoryName(category))}</span> `;
                Badge($$payload4, {
                  variant: "outline",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(featuresByCategory[category].length)}`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Accordion_content($$payload3, {
              children: ($$payload4) => {
                const each_array_1 = ensure_array_like(featuresByCategory[category]);
                $$payload4.out += `<div class="grid grid-cols-1 gap-4 pt-4 md:grid-cols-2"><!--[-->`;
                for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                  let feature = each_array_1[$$index];
                  FeatureCard($$payload4, { feature, onReset });
                }
                $$payload4.out += `<!--]--></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  bind_props($$props, { categories, featuresByCategory, onReset });
  pop();
}
function FeatureTracker($$payload, $$props) {
  push();
  let features = fallback($$props["features"], () => [], true);
  let onTrackSuccess = fallback($$props["onTrackSuccess"], () => {
  });
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Card($$payload2, {
      class: "w-full",
      children: ($$payload3) => {
        Card_header($$payload3, {
          children: ($$payload4) => {
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Track Feature Usage`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Test tracking feature usage to see how it affects your limits`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Card_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<div class="grid gap-4"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "feature",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Feature`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Root$1($$payload4, {
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  id: "feature",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select a feature" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(features);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let feature = each_array[$$index];
                      Select_item($$payload6, {
                        value: feature.id,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(feature.name)}`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Card_footer($$payload3, {
          children: ($$payload4) => {
            Button($$payload4, {
              disabled: true,
              class: "w-full",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html("Track Usage")}`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { features, onTrackSuccess });
  pop();
}
function RefreshButton($$payload, $$props) {
  const { loading = false, onRefresh } = $$props;
  Button($$payload, {
    variant: "outline",
    size: "sm",
    onclick: onRefresh,
    disabled: loading,
    class: "ml-auto",
    children: ($$payload2) => {
      Refresh_cw($$payload2, {
        class: `mr-2 h-4 w-4 ${stringify(loading ? "animate-spin" : "")}`
      });
      $$payload2.out += `<!----> ${escape_html(loading ? "Refreshing..." : "Refresh")}`;
    },
    $$slots: { default: true }
  });
}
function _page($$payload, $$props) {
  push();
  let loading = true;
  let error = null;
  let usageData = null;
  let usageSummary = null;
  let featuresByCategory = {};
  let categories = [];
  let allFeatures = [];
  let resumeUsage = null;
  async function loadUsageData() {
    loading = true;
    error = null;
    try {
      console.log("Loading feature usage data...");
      const response = await fetch("/dashboard/usage?type=all");
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();
      console.log("API call complete:", data);
      if (data.features) {
        usageData = data.features;
        console.log("Feature usage data:", usageData);
        if (usageData.error) {
          error = usageData.error;
        } else if (!usageData.features || usageData.features.length === 0) {
          error = "No feature usage data available.";
        } else {
          allFeatures = usageData.features;
          featuresByCategory = {};
          usageData.features.forEach((feature) => {
            const category = feature.category || "other";
            if (!featuresByCategory[category]) {
              featuresByCategory[category] = [];
            }
            featuresByCategory[category].push(feature);
          });
          categories = Object.keys(featuresByCategory).sort((a, b) => {
            if (a === FeatureCategory.Core) return -1;
            if (b === FeatureCategory.Core) return 1;
            return a.localeCompare(b);
          });
        }
        usageSummary = {
          totalFeatures: allFeatures.length,
          featuresUsed: allFeatures.filter((f) => f.limits.some((l) => l.used > 0)).length,
          featuresWithLimits: allFeatures.filter((f) => f.limits.some((l) => l.value !== "unlimited")).length,
          featuresAtLimit: allFeatures.filter((f) => f.limits.some((l) => l.value !== "unlimited" && typeof l.value === "number" && l.used >= l.value)).length,
          topFeatures: []
        };
        console.log("Usage summary data:", usageSummary);
      }
      if (data.resume) {
        resumeUsage = data.resume;
        console.log("Resume usage data:", resumeUsage);
        if (resumeUsage && resumeUsage.used !== void 0 && allFeatures) {
          let resumeScannerFeature = allFeatures.find((f) => f.id === "resume_scanner");
          if (resumeScannerFeature) {
            const resumeScansLimit = resumeScannerFeature.limits.find((l) => l.id === "resume_scans_per_month");
            if (resumeScansLimit) {
              resumeScansLimit.used = resumeUsage.used || 0;
              resumeScansLimit.value = resumeUsage.limit || "unlimited";
              resumeScansLimit.remaining = resumeUsage.remaining || null;
              resumeScansLimit.percentUsed = resumeUsage.limit && resumeUsage.used !== void 0 ? Math.min(100, resumeUsage.used / resumeUsage.limit * 100) : null;
            } else {
              resumeScannerFeature.limits.push({
                id: "resume_scans_per_month",
                name: "Resume Scans",
                description: "Number of resumes you can scan per month",
                type: "monthly",
                unit: "scans",
                value: resumeUsage.limit || "unlimited",
                used: resumeUsage.used || 0,
                remaining: resumeUsage.remaining || null,
                percentUsed: resumeUsage.limit && resumeUsage.used !== void 0 ? Math.min(100, resumeUsage.used / resumeUsage.limit * 100) : null,
                period: (/* @__PURE__ */ new Date()).toISOString().substring(0, 7),
                // YYYY-MM
                lastUpdated: /* @__PURE__ */ new Date()
              });
            }
          } else if (allFeatures) {
            resumeScannerFeature = {
              id: "resume_scanner",
              name: "Resume Scanner",
              description: "Scan and analyze resumes",
              category: "resume",
              accessLevel: FeatureAccessLevel.Limited,
              limits: [
                {
                  id: "resume_scans_per_month",
                  name: "Resume Scans",
                  description: "Number of resumes you can scan per month",
                  type: "monthly",
                  unit: "scans",
                  value: resumeUsage.limit || "unlimited",
                  used: resumeUsage.used || 0,
                  remaining: resumeUsage.remaining || null,
                  percentUsed: resumeUsage.limit && resumeUsage.used !== void 0 ? Math.min(100, resumeUsage.used / resumeUsage.limit * 100) : null,
                  period: (/* @__PURE__ */ new Date()).toISOString().substring(0, 7),
                  // YYYY-MM
                  lastUpdated: /* @__PURE__ */ new Date()
                }
              ]
            };
            allFeatures.push(resumeScannerFeature);
            const category = "resume";
            if (!featuresByCategory[category]) {
              featuresByCategory[category] = [];
              categories = [...categories, category].sort((a, b) => {
                if (a === FeatureCategory.Core) return -1;
                if (b === FeatureCategory.Core) return 1;
                return a.localeCompare(b);
              });
            }
            featuresByCategory[category].push(resumeScannerFeature);
          }
        }
      }
    } catch (err) {
      console.error("Error loading feature usage data:", err);
      error = err.message || "Failed to load feature usage data.";
    } finally {
      console.log("Setting loading to false");
      loading = false;
    }
  }
  function handleRefresh() {
    console.log("Refresh button clicked, loading usage data...");
    loadUsageData();
  }
  $$payload.out += `<div class="container mx-auto p-6"><div class="space-y-6"><div class="flex items-center justify-between"><div><h2 class="text-3xl font-bold tracking-tight">Feature Usage</h2> <p class="text-muted-foreground">Track your feature usage and limits based on your subscription plan.</p></div> <div class="flex items-center gap-2">`;
  ResetButton($$payload, { onReset: handleRefresh });
  $$payload.out += `<!----> `;
  RefreshButton($$payload, { loading, onRefresh: handleRefresh });
  $$payload.out += `<!----></div></div> `;
  if (loading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="space-y-4">`;
    Skeleton($$payload, { class: "h-[200px] w-full" });
    $$payload.out += `<!----> <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">`;
    Skeleton($$payload, { class: "h-[150px] w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "h-[150px] w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "h-[150px] w-full" });
    $$payload.out += `<!----></div></div>`;
  } else if (error) {
    $$payload.out += "<!--[1-->";
    Alert($$payload, {
      variant: "destructive",
      children: ($$payload2) => {
        Circle_alert($$payload2, { class: "h-4 w-4" });
        $$payload2.out += `<!----> `;
        Alert_title($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->Error`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Alert_description($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(error)}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
    if (usageSummary) {
      $$payload.out += "<!--[-->";
      UsageSummary($$payload, { usageSummary, resumeUsage });
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <div class="text-muted-foreground mb-4 text-xs"><div class="flex items-center gap-2"><span>Loading state: ${escape_html(loading ? "Loading..." : "Complete")}</span> `;
    if (loading) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div> <div class="grid grid-cols-1 gap-6 lg:grid-cols-3"><div class="lg:col-span-1">`;
    FeatureTracker($$payload, {
      features: allFeatures,
      onTrackSuccess: handleRefresh
    });
    $$payload.out += `<!----> <div class="mt-4 rounded-md border p-4"><h3 class="mb-2 text-sm font-semibold">Debug Information</h3> <div class="space-y-2"><div><h4 class="text-xs font-medium">Resume Usage:</h4> <pre class="bg-muted overflow-auto rounded p-2 text-xs">
                  ${escape_html(resumeUsage ? JSON.stringify(resumeUsage, null, 2) : "No resume usage data available")}
                </pre></div> <div><h4 class="text-xs font-medium">API Status:</h4> <ul class="text-xs"><li>Loading: <span${attr_class(clsx(loading ? "font-semibold text-yellow-500" : "text-green-500"))}>${escape_html(loading ? "Yes" : "No")}</span></li> <li>Error: <span${attr_class(clsx(error ? "font-semibold text-red-500" : "text-green-500"))}>${escape_html(error ? error : "None")}</span></li> <li>Features loaded: <span${attr_class(clsx(allFeatures && allFeatures.length > 0 ? "text-green-500" : "font-semibold text-red-500"))}>${escape_html(allFeatures ? allFeatures.length : 0)}</span></li> <li>Categories: <span${attr_class(clsx(categories.length > 0 ? "text-green-500" : "font-semibold text-red-500"))}>${escape_html(categories.length > 0 ? categories.join(", ") : "None")}</span></li> <li>Usage data: <span${attr_class(clsx(usageData ? "text-green-500" : "font-semibold text-red-500"))}>${escape_html(usageData ? "Loaded" : "Not loaded")}</span></li> <li>Usage summary: <span${attr_class(clsx(usageSummary ? "text-green-500" : "font-semibold text-red-500"))}>${escape_html(usageSummary ? "Loaded" : "Not loaded")}</span></li> <li>Resume usage: <span${attr_class(clsx(resumeUsage ? "text-green-500" : "font-semibold text-red-500"))}>${escape_html(resumeUsage ? "Loaded" : "Not loaded")}</span></li></ul></div></div></div></div> <div class="lg:col-span-2">`;
    FeatureCategoryAccordion($$payload, {
      categories,
      featuresByCategory,
      onReset: handleRefresh
    });
    $$payload.out += `<!----></div></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BBlLtE-0.js.map
