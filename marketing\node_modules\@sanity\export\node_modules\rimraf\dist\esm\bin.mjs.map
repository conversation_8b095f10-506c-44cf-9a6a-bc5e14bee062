{"version": 3, "file": "bin.mjs", "sourceRoot": "", "sources": ["../../src/bin.mts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAA;AAEnC,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAExD,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;AAEvE,MAAM,eAAe,GAAG,GAAG,EAAE,CAC3B,OAAO,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAA;AAE5D,MAAM,CAAC,MAAM,IAAI,GAAG,kBAAkB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmC5C,CAAA;AAED,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AAC/C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;AAGzB,OAAO,EAAE,eAAe,EAAa,MAAM,UAAU,CAAA;AAErD,MAAM,MAAM,GAAG,KAAK,EAAE,EAAa,EAAE,CAAS,EAAE,EAAE,CAChD,IAAI,OAAO,CAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;AAEjD,MAAM,iBAAiB,GAAG,KAAK,EAC7B,IAA6E,EAC7E,KAAe,EACf,GAAuB,EACvB,EAAE;IACF,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;IACjD,IAAI,YAAY,GAAG,KAAK,CAAA;IACxB,IAAI,aAAa,GAAG,KAAK,CAAA;IACzB,MAAM,KAAK,GAA+B,EAAE,CAAA;IAC5C,IAAI,UAAU,GAAG,KAAK,CAAA;IACtB,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;QAC9B,IAAI,UAAU;YAAE,OAAM;QACtB,UAAU,GAAG,IAAI,CAAA;QACjB,IAAI,IAA0C,CAAA;QAC9C,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,EAAE,CAAA;QACd,CAAC;QACD,UAAU,GAAG,KAAK,CAAA;IACpB,CAAC,CAAA;IACD,MAAM,UAAU,GACd,CAAC,EAAsD,EAAE,EAAE,CAC3D,KAAK,EAAE,CAAS,EAAE,CAAiB,EAAoB,EAAE;QACvD,MAAM,CAAC,GAAG,IAAI,OAAO,CAAU,GAAG,CAAC,EAAE;YACnC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;gBACpB,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC7B,GAAG,CAAC,MAAM,CAAC,CAAA;gBACX,OAAO,MAAM,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QACF,YAAY,EAAE,CAAA;QACd,OAAO,CAAC,CAAA;IACV,CAAC,CAAA;IACH,MAAM,EAAE,GAAG,eAAe,CAAC;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,MAAM,EAAE,OAAO,CAAC,MAAM;KACvB,CAAC,CAAA;IACF,GAAG,CAAC,MAAM,GAAG,UAAU,CACrB,KAAK,EAAE,IAAY,EAAE,GAAmB,EAAoB,EAAE;QAC5D,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,KAAK,CAAA;QACd,CAAC;QACD,OAAO,CAAC,YAAY,EAAE,CAAC;YACrB,MAAM,CAAC,GAAG,CACR,MAAM,MAAM,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,0BAA0B,CAAC,CACvE,CAAC,IAAI,EAAE,CAAA;YACR,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAA;YACd,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzB,YAAY,GAAG,IAAI,CAAA;gBACnB,MAAK;YACP,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzB,aAAa,GAAG,IAAI,CAAA;gBACpB,OAAO,KAAK,CAAA;YACd,CAAC;iBAAM,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrC,MAAK;YACP,CAAC;iBAAM,CAAC;gBACN,SAAQ;YACV,CAAC;QACH,CAAC;QACD,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAClC,CAAC,CACF,CAAA;IACD,MAAM,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACtB,EAAE,CAAC,KAAK,EAAE,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,IAAI,GAAG,KAAK,EAAE,GAAG,IAAc,EAAE,EAAE;IACvC,MAAM,aAAa,GAAG,CAAC,CAAS,EAAE,EAAE;QAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;QAC7B,OAAO,IAAI,CAAA;IACb,CAAC,CAAA;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,GAAG,EAAE,CAAC;QACpD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAC7C,CAAC;IAED,MAAM,GAAG,GAAuB,EAAE,CAAA;IAClC,MAAM,KAAK,GAAa,EAAE,CAAA;IAC1B,IAAI,QAAQ,GAAG,KAAK,CAAA;IACpB,IAAI,IAAI,GAGgB,MAAM,CAAA;IAE9B,IAAI,WAAW,GAAG,KAAK,CAAA;IAEvB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,QAAQ,EAAE,CAAC;YACb,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACf,SAAQ;QACV,CAAC;QACD,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,QAAQ,GAAG,IAAI,CAAA;YACf,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAC1C,2DAA2D;YAC3D,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACjB,OAAO,CAAC,CAAA;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YACpB,OAAO,CAAC,CAAA;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACnD,WAAW,GAAG,IAAI,CAAA;YAClB,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,kBAAkB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACtD,WAAW,GAAG,KAAK,CAAA;YACnB,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YAC/C,GAAG,CAAC,MAAM,GAAG,aAAa,CAAA;YAC1B,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,cAAc,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YAClD,GAAG,CAAC,MAAM,GAAG,SAAS,CAAA;YACtB,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAA;YACf,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;YAC/C,GAAG,CAAC,IAAI,GAAG,KAAK,CAAA;YAChB,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,iBAAiB,EAAE,CAAC;YACrC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAA;YACvB,SAAQ;QACV,CAAC;aAAM,IAAI,GAAG,KAAK,oBAAoB,EAAE,CAAC;YACxC,GAAG,CAAC,YAAY,GAAG,KAAK,CAAA;YACxB,SAAQ;QACV,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAC1C,GAAG,CAAC,GAAG,GAAG,GAAG,CAAA;YACb,SAAQ;QACV,CAAC;aAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;YACnD,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;YACpB,SAAQ;QACV,CAAC;aAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;YACnD,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;YACpB,SAAQ;QACV,CAAC;aAAM,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;YAC/C,GAAG,CAAC,OAAO,GAAG,GAAG,CAAA;YACjB,SAAQ;QACV,CAAC;aAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;YAC3C,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,QAAQ;oBACX,IAAI,GAAG,MAAM,CAAA;oBACb,SAAQ;gBACV,KAAK,QAAQ,CAAC;gBACd,KAAK,QAAQ,CAAC;gBACd,KAAK,OAAO,CAAC;gBACb,KAAK,SAAS;oBACZ,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;oBAClB,SAAQ;gBACV,KAAK,aAAa;oBAChB,IAAI,GAAG,MAAM,CAAC,UAAU,CAAA;oBACxB,SAAQ;gBACV;oBACE,OAAO,CAAC,KAAK,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAA;oBAC/C,eAAe,EAAE,CAAA;oBACjB,OAAO,CAAC,CAAA;YACZ,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,KAAK,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAA;YACvC,eAAe,EAAE,CAAA;YACjB,OAAO,CAAC,CAAA;QACV,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IAED,IAAI,GAAG,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;QAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9C,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC9B,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAA;gBACtE,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAA;gBACjE,OAAO,CAAC,CAAA;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClB,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAA;QACtD,eAAe,EAAE,CAAA;QACjB,OAAO,CAAC,CAAA;IACV,CAAC;IAED,IAAI,IAAI,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1D,OAAO,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAA;QAChE,eAAe,EAAE,CAAA;QACjB,OAAO,CAAC,CAAA;IACV,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,MAAM,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;IAC3C,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACxB,CAAC;IAED,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;AAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;AAEtB,eAAe,IAAI,CAAA;AAEnB,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,GAAG,EAAE,CAAC;IAC/C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAClC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAChB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1B,EAAE,CAAC,EAAE;QACH,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACjB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC,CACF,CAAA;AACH,CAAC", "sourcesContent": ["#!/usr/bin/env node\nimport type { RimrafAsyncOptions } from './index.js'\nimport { rimraf } from './index.js'\n\nimport { loadPackageJson } from 'package-json-from-dist'\n\nconst { version } = loadPackageJson(import.meta.url, '../package.json')\n\nconst runHelpForUsage = () =>\n  console.error('run `rimraf --help` for usage information')\n\nexport const help = `rimraf version ${version}\n\nUsage: rimraf <path> [<path> ...]\nDeletes all files and folders at \"path\", recursively.\n\nOptions:\n  --                   Treat all subsequent arguments as paths\n  -h --help            Display this usage info\n  --version            Display version\n  --preserve-root      Do not remove '/' recursively (default)\n  --no-preserve-root   Do not treat '/' specially\n  -G --no-glob         Treat arguments as literal paths, not globs (default)\n  -g --glob            Treat arguments as glob patterns\n  -v --verbose         Be verbose when deleting files, showing them as\n                       they are removed. Not compatible with --impl=native\n  -V --no-verbose      Be silent when deleting files, showing nothing as\n                       they are removed (default)\n  -i --interactive     Ask for confirmation before deleting anything\n                       Not compatible with --impl=native\n  -I --no-interactive  Do not ask for confirmation before deleting\n\n  --impl=<type>        Specify the implementation to use:\n                       rimraf: choose the best option (default)\n                       native: the built-in implementation in Node.js\n                       manual: the platform-specific JS implementation\n                       posix: the Posix JS implementation\n                       windows: the Windows JS implementation (falls back to\n                                move-remove on ENOTEMPTY)\n                       move-remove: a slow reliable Windows fallback\n\nImplementation-specific options:\n  --tmp=<path>        Temp file folder for 'move-remove' implementation\n  --max-retries=<n>   maxRetries for 'native' and 'windows' implementations\n  --retry-delay=<n>   retryDelay for 'native' implementation, default 100\n  --backoff=<n>       Exponential backoff factor for retries (default: 1.2)\n`\n\nimport { parse, relative, resolve } from 'path'\nconst cwd = process.cwd()\n\nimport { Dirent, Stats } from 'fs'\nimport { createInterface, Interface } from 'readline'\n\nconst prompt = async (rl: Interface, q: string) =>\n  new Promise<string>(res => rl.question(q, res))\n\nconst interactiveRimraf = async (\n  impl: (path: string | string[], opt?: RimrafAsyncOptions) => Promise<boolean>,\n  paths: string[],\n  opt: RimrafAsyncOptions,\n) => {\n  const existingFilter = opt.filter || (() => true)\n  let allRemaining = false\n  let noneRemaining = false\n  const queue: (() => Promise<boolean>)[] = []\n  let processing = false\n  const processQueue = async () => {\n    if (processing) return\n    processing = true\n    let next: (() => Promise<boolean>) | undefined\n    while ((next = queue.shift())) {\n      await next()\n    }\n    processing = false\n  }\n  const oneAtATime =\n    (fn: (s: string, e: Dirent | Stats) => Promise<boolean>) =>\n    async (s: string, e: Dirent | Stats): Promise<boolean> => {\n      const p = new Promise<boolean>(res => {\n        queue.push(async () => {\n          const result = await fn(s, e)\n          res(result)\n          return result\n        })\n      })\n      processQueue()\n      return p\n    }\n  const rl = createInterface({\n    input: process.stdin,\n    output: process.stdout,\n  })\n  opt.filter = oneAtATime(\n    async (path: string, ent: Dirent | Stats): Promise<boolean> => {\n      if (noneRemaining) {\n        return false\n      }\n      while (!allRemaining) {\n        const a = (\n          await prompt(rl, `rm? ${relative(cwd, path)}\\n[(Yes)/No/All/Quit] > `)\n        ).trim()\n        if (/^n/i.test(a)) {\n          return false\n        } else if (/^a/i.test(a)) {\n          allRemaining = true\n          break\n        } else if (/^q/i.test(a)) {\n          noneRemaining = true\n          return false\n        } else if (a === '' || /^y/i.test(a)) {\n          break\n        } else {\n          continue\n        }\n      }\n      return existingFilter(path, ent)\n    },\n  )\n  await impl(paths, opt)\n  rl.close()\n}\n\nconst main = async (...args: string[]) => {\n  const verboseFilter = (s: string) => {\n    console.log(relative(cwd, s))\n    return true\n  }\n\n  if (process.env.__RIMRAF_TESTING_BIN_FAIL__ === '1') {\n    throw new Error('simulated rimraf failure')\n  }\n\n  const opt: RimrafAsyncOptions = {}\n  const paths: string[] = []\n  let dashdash = false\n  let impl: (\n    path: string | string[],\n    opt?: RimrafAsyncOptions,\n  ) => Promise<boolean> = rimraf\n\n  let interactive = false\n\n  for (const arg of args) {\n    if (dashdash) {\n      paths.push(arg)\n      continue\n    }\n    if (arg === '--') {\n      dashdash = true\n      continue\n    } else if (arg === '-rf' || arg === '-fr') {\n      // this never did anything, but people put it there I guess\n      continue\n    } else if (arg === '-h' || arg === '--help') {\n      console.log(help)\n      return 0\n    } else if (arg === '--version') {\n      console.log(version)\n      return 0\n    } else if (arg === '--interactive' || arg === '-i') {\n      interactive = true\n      continue\n    } else if (arg === '--no-interactive' || arg === '-I') {\n      interactive = false\n      continue\n    } else if (arg === '--verbose' || arg === '-v') {\n      opt.filter = verboseFilter\n      continue\n    } else if (arg === '--no-verbose' || arg === '-V') {\n      opt.filter = undefined\n      continue\n    } else if (arg === '-g' || arg === '--glob') {\n      opt.glob = true\n      continue\n    } else if (arg === '-G' || arg === '--no-glob') {\n      opt.glob = false\n      continue\n    } else if (arg === '--preserve-root') {\n      opt.preserveRoot = true\n      continue\n    } else if (arg === '--no-preserve-root') {\n      opt.preserveRoot = false\n      continue\n    } else if (/^--tmp=/.test(arg)) {\n      const val = arg.substring('--tmp='.length)\n      opt.tmp = val\n      continue\n    } else if (/^--max-retries=/.test(arg)) {\n      const val = +arg.substring('--max-retries='.length)\n      opt.maxRetries = val\n      continue\n    } else if (/^--retry-delay=/.test(arg)) {\n      const val = +arg.substring('--retry-delay='.length)\n      opt.retryDelay = val\n      continue\n    } else if (/^--backoff=/.test(arg)) {\n      const val = +arg.substring('--backoff='.length)\n      opt.backoff = val\n      continue\n    } else if (/^--impl=/.test(arg)) {\n      const val = arg.substring('--impl='.length)\n      switch (val) {\n        case 'rimraf':\n          impl = rimraf\n          continue\n        case 'native':\n        case 'manual':\n        case 'posix':\n        case 'windows':\n          impl = rimraf[val]\n          continue\n        case 'move-remove':\n          impl = rimraf.moveRemove\n          continue\n        default:\n          console.error(`unknown implementation: ${val}`)\n          runHelpForUsage()\n          return 1\n      }\n    } else if (/^-/.test(arg)) {\n      console.error(`unknown option: ${arg}`)\n      runHelpForUsage()\n      return 1\n    } else {\n      paths.push(arg)\n    }\n  }\n\n  if (opt.preserveRoot !== false) {\n    for (const path of paths.map(p => resolve(p))) {\n      if (path === parse(path).root) {\n        console.error(`rimraf: it is dangerous to operate recursively on '/'`)\n        console.error('use --no-preserve-root to override this failsafe')\n        return 1\n      }\n    }\n  }\n\n  if (!paths.length) {\n    console.error('rimraf: must provide a path to remove')\n    runHelpForUsage()\n    return 1\n  }\n\n  if (impl === rimraf.native && (interactive || opt.filter)) {\n    console.error('native implementation does not support -v or -i')\n    runHelpForUsage()\n    return 1\n  }\n\n  if (interactive) {\n    await interactiveRimraf(impl, paths, opt)\n  } else {\n    await impl(paths, opt)\n  }\n\n  return 0\n}\nmain.help = help\nmain.version = version\n\nexport default main\n\nif (process.env.__TESTING_RIMRAF_BIN__ !== '1') {\n  const args = process.argv.slice(2)\n  main(...args).then(\n    code => process.exit(code),\n    er => {\n      console.error(er)\n      process.exit(1)\n    },\n  )\n}\n"]}