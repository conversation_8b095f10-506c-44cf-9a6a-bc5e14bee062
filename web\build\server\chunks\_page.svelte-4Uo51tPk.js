import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, O as escape_html, J as attr_class, P as stringify } from './index3-CqUPEnZw.js';
import { g as goto } from './client-dNyMPa8V.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { S as SEO } from './SEO-UItXytUy.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { U as UniversalDocumentViewer } from './UniversalDocumentViewer-Cv4LqmRL.js';
import './false-CRHihH2U.js';
import './index2-Cut0V_vU.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './dialog-overlay-CspOQRJq.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './dialog-description2-rfr-pd9k.js';
import './skeleton-C-NLefl9.js';
import './zoom-out-vip0ZIci.js';
import './Icon-A4vzmk-O.js';
import './rotate-cw-CWqzplUz.js';
import './external-link-ZBG7aazC.js';
import './download-CLn66Ope.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let document = data.document || null;
  let loading = !document;
  let saving = false;
  let documentName = document ? document.label : "";
  let showDeleteDialog = false;
  async function saveDocument() {
    if (!document) return;
    saving = true;
    try {
      const response = await fetch(`/api/documents/${document.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ label: documentName })
      });
      if (response.ok) {
        const updatedDocument = await response.json();
        toast.success("Document updated", {
          description: "Your document has been updated successfully."
        });
        document.label = updatedDocument.label || documentName;
        document.updatedAt = updatedDocument.updatedAt || (/* @__PURE__ */ new Date()).toISOString();
        if (updatedDocument.source) {
          document.source = updatedDocument.source;
        }
        documentName = document.label;
      } else {
        toast.error("Error updating document", {
          description: "Could not update the document. Please try again."
        });
      }
    } catch (error) {
      console.error("Error updating document:", error);
      toast.error("Error updating document", {
        description: "Could not update the document. Please try again."
      });
    } finally {
      saving = false;
    }
  }
  async function parseResume() {
    if (!document) return;
    try {
      toast.loading("Sending resume for parsing...", { id: "parse-resume" });
      const response = await fetch(`/api/documents/${document.id}/parse`, { method: "POST" });
      if (response.ok) {
        toast.success("Resume parsing started", {
          id: "parse-resume",
          description: "Your resume is being analyzed. This may take a moment."
        });
        document = { ...document, isParsed: false, parsedAt: null };
      } else {
        let errorMessage = "Failed to start resume parsing";
        try {
          const errorData = await response.json();
          console.error("Parse API error details:", errorData);
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          console.error("Could not parse error response:", e);
        }
        console.error(errorMessage);
        toast.error("Resume parsing failed", { id: "parse-resume", description: errorMessage });
      }
    } catch (error) {
      console.error("Error sending resume to parsing queue:", error);
      toast.error("Resume parsing failed", {
        id: "parse-resume",
        description: "An unexpected error occurred. Please try again."
      });
    }
  }
  function confirmDelete() {
    if (!document) return;
    fetch(`/api/documents/${document.id}`, { method: "DELETE" }).then((res) => {
      if (res.ok) {
        toast.success("Document deleted", {
          description: `"${document?.label}" has been removed from your documents.`
        });
        goto();
      } else {
        toast.error("Delete failed", {
          description: "There was a problem deleting your document. Please try again."
        });
      }
      showDeleteDialog = false;
    }).catch((err) => {
      console.error("Delete error:", err);
      toast.error("Delete failed", {
        description: "There was a problem deleting your document. Please try again."
      });
      showDeleteDialog = false;
    });
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Edit Document | Auto Apply",
      description: "Edit your document details"
    });
    $$payload2.out += `<!----> <div class="container mx-auto p-6"><div class="mb-6 flex items-center justify-between"><div><h1 class="text-3xl font-bold">Edit Document</h1> <p class="text-gray-500">Update your document details</p></div> <div class="flex gap-2">`;
    Button($$payload2, {
      variant: "outline",
      onclick: () => goto(),
      children: ($$payload3) => {
        $$payload3.out += `<!---->Cancel`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      onclick: () => showDeleteDialog = true,
      variant: "destructive",
      children: ($$payload3) => {
        $$payload3.out += `<!---->Delete`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> `;
    if (loading) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex h-64 items-center justify-center"><p class="text-lg text-gray-500">Loading document...</p></div>`;
    } else if (document) {
      $$payload2.out += "<!--[1-->";
      $$payload2.out += `<div class="grid grid-cols-1 gap-6 lg:grid-cols-2"><div class="order-2 lg:order-1"><div style="height: 600px;">`;
      UniversalDocumentViewer($$payload2, { document });
      $$payload2.out += `<!----></div> `;
      if (document.type === "resume" && document.resume) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="mt-4">`;
        Button($$payload2, {
          variant: "outline",
          onclick: () => goto(`/dashboard/documents/${document.id}/ats`),
          children: ($$payload3) => {
            $$payload3.out += `<span class="mr-2">ATS Optimization</span> <span class="inline-flex h-5 items-center justify-center rounded-full bg-blue-100 px-2 text-xs font-medium text-blue-800">AI</span>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--></div> <div class="order-1 lg:order-2">`;
      Card($$payload2, {
        children: ($$payload3) => {
          Card_header($$payload3, {
            children: ($$payload4) => {
              Card_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Document Details`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Update the information for your document`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-4"><div><label for="documentName" class="mb-2 block text-sm font-medium">Document Name</label> `;
              Input($$payload4, {
                id: "documentName",
                placeholder: "Enter document name",
                get value() {
                  return documentName;
                },
                set value($$value) {
                  documentName = $$value;
                  $$settled = false;
                }
              });
              $$payload4.out += `<!----></div> <div><span class="mb-2 block text-sm font-medium">Document Type</span> <p class="text-sm text-gray-500">${escape_html(document.type === "resume" ? "Resume" : document.type === "cover_letter" ? "Cover Letter" : document.type)}</p></div> <div><span class="mb-2 block text-sm font-medium">Created</span> <div class="flex flex-col"><span class="text-sm text-gray-500">${escape_html(new Date(document.createdAt).toLocaleDateString())}</span> <span class="text-xs text-gray-500">${escape_html(new Date(document.createdAt).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }))}</span></div></div> `;
              if (document.updatedAt) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div><span class="mb-2 block text-sm font-medium">Last Edited</span> <div class="flex flex-col"><span class="text-sm text-gray-500">${escape_html(new Date(document.updatedAt).toLocaleDateString())}</span> <span class="text-xs text-gray-500">${escape_html(new Date(document.updatedAt).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }))}</span></div></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--> <div><span class="mb-2 block text-sm font-medium">File Name</span> <p class="text-sm text-gray-500">${escape_html(document.fileName || "unknown.pdf")}</p></div> `;
              if (document.source) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div><span class="mb-2 block text-sm font-medium">Source</span> <span${attr_class(`inline-block rounded px-2 py-0.5 text-xs ${stringify(document.source === "generated" ? "bg-purple-100 text-purple-800" : document.source === "created" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800")}`)}>${escape_html(document.source === "generated" ? "Generated" : document.source === "created" ? "Created" : "Uploaded")}</span></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--> `;
              if (document.isDefault !== void 0) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div><span class="mb-2 block text-sm font-medium">Status</span> `;
                if (document.isDefault) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span class="inline-block rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800">Default</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  $$payload4.out += `<span class="inline-block rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-800">Regular</span>`;
                }
                $$payload4.out += `<!--]--></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--> `;
              if (document.type === "resume") {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div><span class="mb-2 block text-sm font-medium">Parse Status</span> <div class="flex items-center gap-2">`;
                if (document.isParsed) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span class="inline-block rounded bg-green-100 px-2 py-0.5 text-xs text-green-800">Parsed</span> `;
                  if (document.parsedAt) {
                    $$payload4.out += "<!--[-->";
                    $$payload4.out += `<span class="text-xs text-gray-500">on ${escape_html(new Date(document.parsedAt).toLocaleDateString())}</span>`;
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]-->`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  $$payload4.out += `<span class="inline-block rounded bg-yellow-100 px-2 py-0.5 text-xs text-yellow-800">Not Parsed</span> `;
                  Button($$payload4, {
                    size: "sm",
                    variant: "outline",
                    class: "ml-2",
                    onclick: parseResume,
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->Parse Now`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                }
                $$payload4.out += `<!--]--></div></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--></div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_footer($$payload3, {
            class: "flex justify-between",
            children: ($$payload4) => {
              Button($$payload4, {
                variant: "outline",
                onclick: () => goto(),
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Cancel`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Button($$payload4, {
                onclick: saveDocument,
                disabled: saving || documentName === document.label,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->${escape_html(saving ? "Saving..." : "Save Changes")}`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="flex h-64 items-center justify-center"><p class="text-lg text-gray-500">Document not found</p></div>`;
    }
    $$payload2.out += `<!--]--></div> `;
    Root($$payload2, {
      get open() {
        return showDeleteDialog;
      },
      set open($$value) {
        showDeleteDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Alert_dialog_content($$payload3, {
          children: ($$payload4) => {
            Alert_dialog_header($$payload4, {
              children: ($$payload5) => {
                Alert_dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Are you sure?`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Alert_dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->This will permanently delete "${escape_html(document?.label)}" from your documents. This action cannot be
        undone.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Alert_dialog_footer($$payload4, {
              children: ($$payload5) => {
                Alert_dialog_cancel($$payload5, {
                  onclick: () => showDeleteDialog = false,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Alert_dialog_action($$payload5, {
                  onclick: confirmDelete,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Delete`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-4Uo51tPk.js.map
