import { j as json } from './index-Ddp2AB5f.js';
import { v as verifySessionToken } from './auth-BPad-IlN.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { R as RedisConnection } from './redis-DxlM1ibh.js';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import '@prisma/client';
import 'ioredis';

async function POST({ request, cookies, locals }) {
  const token = cookies.get("auth_token");
  if (!token) return new Response("Unauthorized", { status: 401 });
  const user = await verifySessionToken(token);
  if (!user) return new Response("Unauthorized", { status: 401 });
  const { resumeId } = await request.json();
  if (!resumeId) return new Response("Missing fields", { status: 400 });
  const resume = await prisma.resume.findFirst({
    where: {
      id: resumeId,
      profile: {
        OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }]
      }
    }
  });
  if (!resume) return new Response("Resume not found", { status: 404 });
  const job = {
    resumeId: resume.id,
    userId: user.id,
    profileId: resume.profileId,
    fileUrl: resume.fileUrl
  };
  await RedisConnection.xadd("optimize-resume", "*", "job", JSON.stringify(job));
  return json({ success: true });
}

export { POST };
//# sourceMappingURL=_server.ts-r2kB-OsJ.js.map
