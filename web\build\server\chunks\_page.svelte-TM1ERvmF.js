import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, J as attr_class, O as escape_html, M as ensure_array_like, S as store_get, K as fallback, $ as attr_style, a0 as slot, T as unsubscribe_stores, ab as store_mutate, P as stringify, ah as await_block } from './index3-CqUPEnZw.js';
import { p as page } from './stores-DSLMNPqo.js';
import { r as readable, g as get } from './index2-Cut0V_vU.js';
import { h as html } from './html-FW6Ia4bL.js';
import { j as zodClient } from './zod-DfpldWlD.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import 'clsx';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { F as Form_field, C as Control, a as Form_field_errors } from './index15-D3NL0C7o.js';
import { R as Root$1, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { R as Root, T as Tabs_list, a as Tabs_content, b as Tabs_trigger } from './index9-3zbfQ0pE.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { A as Avatar, a as Avatar_image, b as Avatar_fallback } from './avatar-fallback-B2wWy5ce.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { U as Users } from './users-e7-Uhkka.js';
import { U as User_plus } from './user-plus-C_wAa5PC.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { F as Form_label } from './form-label-C5yTxnxS.js';
import { M as Mail } from './mail-Brqxil2x.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { o as objectType, e as enumType, s as stringType } from './types-D78SXuvm.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './index4-HpJcNJHQ.js';
import './index-server-CezSOnuG.js';
import './client-dNyMPa8V.js';
import './stringify-DWCARkQV.js';
import './index-DAbaXdpL.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';
import './label-Dt8gTF_8.js';

function SuperDebug($$payload, $$props) {
  push();
  var $$store_subs;
  let themeStyle, debugData;
  let styleInit = false;
  let data = $$props["data"];
  let display = fallback($$props["display"], true);
  let status = fallback($$props["status"], true);
  let label = fallback($$props["label"], "");
  let stringTruncate = fallback($$props["stringTruncate"], 120);
  let ref = fallback($$props["ref"], void 0);
  let promise = fallback($$props["promise"], false);
  let raw = fallback($$props["raw"], false);
  let functions = fallback($$props["functions"], false);
  let theme = fallback($$props["theme"], "default");
  let collapsible = fallback($$props["collapsible"], false);
  let collapsed = fallback($$props["collapsed"], false);
  function syntaxHighlight(json) {
    switch (typeof json) {
      case "function": {
        return `<span class="function">[function ${json.name ?? "unnamed"}]</span>`;
      }
      case "symbol": {
        return `<span class="symbol">${json.toString()}</span>`;
      }
    }
    const encodedString = JSON.stringify(
      json,
      function(key, value) {
        if (value === void 0) {
          return "#}#undefined";
        }
        if (typeof this === "object" && this[key] instanceof Date) {
          return "#}D#" + (isNaN(this[key]) ? "Invalid Date" : value);
        }
        if (typeof value === "number") {
          if (value == Number.POSITIVE_INFINITY) return "#}#Inf";
          if (value == Number.NEGATIVE_INFINITY) return "#}#-Inf";
          if (isNaN(value)) return "#}#NaN";
        }
        if (typeof value === "bigint") {
          return "#}BI#" + value;
        }
        if (typeof value === "function" && functions) {
          return `#}F#[function ${value.name}]`;
        }
        if (value instanceof Error) {
          return `#}E#${value.name}: ${value.message || value.cause || "(No error message)"}`;
        }
        if (value instanceof Set) {
          return Array.from(value);
        }
        if (value instanceof Map) {
          return Array.from(value.entries());
        }
        if (typeof this === "object" && typeof this[key] == "object" && this[key] && "toExponential" in this[key]) {
          return "#}DE#" + this[key].toString();
        }
        return value;
      },
      2
    ).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
    return encodedString.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)/g, function(match) {
      let cls = "number";
      if (/^"/.test(match)) {
        if (/:$/.test(match)) {
          cls = "key";
          match = match.slice(1, -2) + ":";
        } else {
          cls = "string";
          match = stringTruncate > 0 && match.length > stringTruncate ? match.slice(0, stringTruncate / 2) + `[..${match.length - stringTruncate}/${match.length}..]` + match.slice(-stringTruncate / 2) : match;
          if (match == '"#}#undefined"') {
            cls = "undefined";
            match = "undefined";
          } else if (match.startsWith('"#}D#')) {
            cls = "date";
            match = match.slice(5, -1);
          } else if (match == '"#}#NaN"') {
            cls = "nan";
            match = "NaN";
          } else if (match == '"#}#Inf"') {
            cls = "nan";
            match = "Infinity";
          } else if (match == '"#}#-Inf"') {
            cls = "nan";
            match = "-Infinity";
          } else if (match.startsWith('"#}BI#')) {
            cls = "bigint";
            match = match.slice(6, -1) + "n";
          } else if (match.startsWith('"#}F#')) {
            cls = "function";
            match = match.slice(5, -1);
          } else if (match.startsWith('"#}E#')) {
            cls = "error";
            match = match.slice(5, -1);
          } else if (match.startsWith('"#}DE#')) {
            cls = "number";
            match = match.slice(6, -1);
          }
        }
      } else if (/true|false/.test(match)) {
        cls = "boolean";
      } else if (/null/.test(match)) {
        cls = "null";
      }
      return '<span class="' + cls + '">' + match + "</span>";
    });
  }
  function assertPromise(data2, raw2, promise2) {
    if (raw2) {
      return false;
    }
    return promise2 || typeof data2 === "object" && data2 !== null && "then" in data2 && typeof data2["then"] === "function";
  }
  function assertStore(data2, raw2) {
    if (raw2) {
      return false;
    }
    return typeof data2 === "object" && data2 !== null && "subscribe" in data2 && typeof data2["subscribe"] === "function";
  }
  themeStyle = theme === "vscode" ? `
      --sd-vscode-bg-color: #1f1f1f;
      --sd-vscode-label-color: #cccccc;
      --sd-vscode-code-default: #8c8a89;
      --sd-vscode-code-key: #9cdcfe;
      --sd-vscode-code-string: #ce9171;
      --sd-vscode-code-number: #b5c180;
      --sd-vscode-code-boolean: #4a9cd6;
      --sd-vscode-code-null: #4a9cd6;
      --sd-vscode-code-undefined: #4a9cd6;
      --sd-vscode-code-nan: #4a9cd6;
      --sd-vscode-code-symbol: #4de0c5;
      --sd-vscode-sb-thumb-color: #35373a;
      --sd-vscode-sb-thumb-color-focus: #4b4d50;
    ` : void 0;
  debugData = assertStore(data, raw) ? data : readable(data);
  if (!styleInit) {
    $$payload.out += "<!--[-->";
    styleInit = true;
    $$payload.out += `<style>
		.super-debug--absolute {
			position: absolute;
		}

		.super-debug--top-0 {
			top: 0;
		}

		.super-debug--inset-x-0 {
			left: 0px;
			right: 0px;
		}

		.super-debug--hidden {
			height: 0;
			overflow: hidden;
		}

		.super-debug--hidden:not(.super-debug--with-label) {
			height: 1.5em;
		}

		.super-debug--rotated {
			transform: rotate(180deg);
		}

		.super-debug {
			--_sd-bg-color: var(--sd-bg-color, var(--sd-vscode-bg-color, rgb(30, 41, 59)));
			position: relative;
			background-color: var(--_sd-bg-color);
			border-radius: 0.5rem;
			overflow: hidden;
		}

		.super-debug--pre {
			overflow-x: auto;
		}

		.super-debug--collapse {
			display: block;
			width: 100%;
			color: rgba(255, 255, 255, 0.25);
			background-color: rgba(255, 255, 255, 0.15);
			padding: 5px 0;
			display: flex;
			justify-content: center;
			border-color: transparent;
			margin: 0;
			padding: 3px 0;
		}

		.super-debug--collapse:focus {
			color: #fafafa;
			background-color: rgba(255, 255, 255, 0.25);
		}

		.super-debug--collapse:is(:hover) {
			color: rgba(255, 255, 255, 0.35);
			background-color: rgba(255, 255, 255, 0.25);
		}

		.super-debug--status {
			display: flex;
			padding: 1em;
			padding-bottom: 0;
			justify-content: space-between;
			font-family:
				Inconsolata, Monaco, Consolas, 'Lucida Console', 'Courier New', Courier, monospace;
		}

		.super-debug--right-status {
			display: flex;
			gap: 0.55em;
		}

		.super-debug--copy {
			margin: 0;
			padding: 0;
			padding-top: 2px;
			background-color: transparent;
			border: 0;
			color: #666;
			cursor: pointer;
		}

		.super-debug--copy:hover {
			background-color: transparent;
			color: #666;
		}

		.super-debug--copy:focus {
			background-color: transparent;
			color: #666;
		}

		.super-debug--label {
			color: var(--sd-label-color, var(--sd-vscode-label-color, white));
		}

		.super-debug--promise-loading {
			color: var(--sd-promise-loading-color, var(--sd-vscode-promise-loading-color, #999));
		}

		.super-debug--promise-rejected {
			color: var(--sd-promise-rejected-color, var(--sd-vscode-promise-rejected-color, #ff475d));
		}

		.super-debug pre {
			color: var(--sd-code-default, var(--sd-vscode-code-default, #999));
			background-color: var(--_sd-bg-color);
			font-size: 1em;
			margin-bottom: 0;
			padding: 1em 0 1em 1em;
		}

		.super-debug--info {
			color: var(--sd-info, var(--sd-vscode-info, rgb(85, 85, 255)));
		}

		.super-debug--success {
			color: var(--sd-success, var(--sd-vscode-success, #2cd212));
		}

		.super-debug--redirect {
			color: var(--sd-redirect, var(--sd-vscode-redirect, #03cae5));
		}

		.super-debug--error {
			color: var(--sd-error, var(--sd-vscode-error, #ff475d));
		}

		.super-debug--code .key {
			color: var(--sd-code-key, var(--sd-vscode-code-key, #eab308));
		}

		.super-debug--code .string {
			color: var(--sd-code-string, var(--sd-vscode-code-string, #6ec687));
		}

		.super-debug--code .date {
			color: var(--sd-code-date, var(--sd-vscode-code-date, #f06962));
		}

		.super-debug--code .boolean {
			color: var(--sd-code-boolean, var(--sd-vscode-code-boolean, #79b8ff));
		}

		.super-debug--code .number {
			color: var(--sd-code-number, var(--sd-vscode-code-number, #af77e9));
		}

		.super-debug--code .bigint {
			color: var(--sd-code-bigint, var(--sd-vscode-code-bigint, #af77e9));
		}

		.super-debug--code .null {
			color: var(--sd-code-null, var(--sd-vscode-code-null, #238afe));
		}

		.super-debug--code .nan {
			color: var(--sd-code-nan, var(--sd-vscode-code-nan, #af77e9));
		}

		.super-debug--code .undefined {
			color: var(--sd-code-undefined, var(--sd-vscode-code-undefined, #238afe));
		}

		.super-debug--code .function {
			color: var(--sd-code-function, var(--sd-vscode-code-function, #f06962));
		}

		.super-debug--code .symbol {
			color: var(--sd-code-symbol, var(--sd-vscode-code-symbol, #4de0c5));
		}

		.super-debug--code .error {
			color: var(--sd-code-error, var(--sd-vscode-code-error, #ff475d));
		}

		.super-debug pre::-webkit-scrollbar {
			width: var(--sd-sb-width, var(--sd-vscode-sb-width, 1rem));
			height: var(--sd-sb-height, var(--sd-vscode-sb-height, 1rem));
		}

		.super-debug pre::-webkit-scrollbar-track {
			border-radius: 12px;
			background-color: var(
				--sd-sb-track-color,
				var(--sd-vscode-sb-track-color, hsl(0, 0%, 40%, 0.2))
			);
		}
		.super-debug:is(:focus-within, :hover) pre::-webkit-scrollbar-track {
			border-radius: 12px;
			background-color: var(
				--sd-sb-track-color-focus,
				var(--sd-vscode-sb-track-color-focus, hsl(0, 0%, 50%, 0.2))
			);
		}

		.super-debug pre::-webkit-scrollbar-thumb {
			border-radius: 12px;
			background-color: var(
				--sd-sb-thumb-color,
				var(--sd-vscode-sb-thumb-color, hsl(217, 50%, 50%, 0.5))
			);
		}
		.super-debug:is(:focus-within, :hover) pre::-webkit-scrollbar-thumb {
			border-radius: 12px;
			background-color: var(
				--sd-sb-thumb-color-focus,
				var(--sd-vscode-sb-thumb-color-focus, hsl(217, 50%, 50%))
			);
		}
	</style>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (display) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div${attr_class("super-debug", void 0, { "super-debug--collapsible": collapsible })}${attr_style(themeStyle)} dir="ltr"><div${attr_class(`super-debug--status ${stringify(label === "" ? "super-debug--absolute super-debug--inset-x-0 super-debug--top-0" : "")}`)}><div class="super-debug--label">${escape_html(label)}</div> <div class="super-debug--right-status"><button type="button" class="super-debug--copy">`;
    {
      $$payload.out += "<!--[-->";
      $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M7 9.667A2.667 2.667 0 0 1 9.667 7h8.666A2.667 2.667 0 0 1 21 9.667v8.666A2.667 2.667 0 0 1 18.333 21H9.667A2.667 2.667 0 0 1 7 18.333z"></path><path d="M4.012 16.737A2.005 2.005 0 0 1 3 15V5c0-1.1.9-2 2-2h10c.75 0 1.158.385 1.5 1"></path></g></svg>`;
    }
    $$payload.out += `<!--]--></button> `;
    if (status) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div${attr_class("", void 0, {
        "super-debug--info": store_get($$store_subs ??= {}, "$page", page).status < 200,
        "super-debug--success": store_get($$store_subs ??= {}, "$page", page).status >= 200 && store_get($$store_subs ??= {}, "$page", page).status < 300,
        "super-debug--redirect": store_get($$store_subs ??= {}, "$page", page).status >= 300 && store_get($$store_subs ??= {}, "$page", page).status < 400,
        "super-debug--error": store_get($$store_subs ??= {}, "$page", page).status >= 400
      })}>${escape_html(store_get($$store_subs ??= {}, "$page", page).status)}</div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div> <pre${attr_class("super-debug--pre", void 0, {
      "super-debug--with-label": label,
      "super-debug--hidden": collapsed
    })}><code class="super-debug--code"><!---->`;
    slot($$payload, $$props, "default", {}, () => {
      if (assertPromise(store_get($$store_subs ??= {}, "$debugData", debugData), raw, promise)) {
        $$payload.out += "<!--[-->";
        await_block(
          $$payload,
          store_get($$store_subs ??= {}, "$debugData", debugData),
          () => {
            $$payload.out += `<div class="super-debug--promise-loading">Loading data...</div>`;
          },
          (result) => {
            $$payload.out += `${html(syntaxHighlight(assertStore(result, raw) ? get(result) : result))}`;
          }
        );
        $$payload.out += `<!--]-->`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `${html(syntaxHighlight(store_get($$store_subs ??= {}, "$debugData", debugData)))}`;
      }
      $$payload.out += `<!--]-->`;
    });
    $$payload.out += `<!----></code></pre> `;
    if (collapsible) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button type="button" class="super-debug--collapse" aria-label="Collapse"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"${attr_class("", void 0, { "super-debug--rotated": collapsed })}><path fill="currentColor" d="M4.08 11.92L12 4l7.92 7.92l-1.42 1.41l-5.5-5.5V22h-2V7.83l-5.5 5.5l-1.42-1.41M12 4h10V2H2v2h10Z"></path></svg></button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    data,
    display,
    status,
    label,
    stringTruncate,
    ref,
    promise,
    raw,
    functions,
    theme,
    collapsible,
    collapsed
  });
  pop();
}
function Custom_tabs_trigger($$payload, $$props) {
  push();
  let value = $$props["value"];
  let className = fallback($$props["className"], "");
  Tabs_trigger($$payload, {
    class: cn("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow", className),
    value,
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      slot($$payload2, $$props, "default", {}, null);
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  bind_props($$props, { value, className });
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let data = $$props["data"];
  const tabs = [
    { id: "teams", label: "My Teams", icon: Users },
    {
      id: "invite",
      label: "Invite Members",
      icon: User_plus
    },
    {
      id: "create",
      label: "Create Team",
      icon: Plus
    }
  ];
  let activeTab = "teams";
  let saveStatus = "saved";
  let statusTimeout;
  function updateStatus(status, duration = 3e3) {
    saveStatus = status;
    clearTimeout(statusTimeout);
    if (status !== "saved") {
      statusTimeout = setTimeout(
        () => {
          saveStatus = "saved";
        },
        duration
      );
    }
  }
  const inviteForm = superForm(data.inviteForm, {
    validators: zodClient(objectType({
      email: stringType().email("Invalid email address"),
      role: enumType(["member", "admin"]).default("member")
    })),
    dataType: "json",
    onSubmit: () => {
      updateStatus("saving");
    },
    onUpdated: ({ form }) => {
      if (form.valid) {
        updateStatus("saved");
        toast.success("Invitation sent successfully");
      }
    },
    onError: () => {
      updateStatus("error");
      toast.error("Failed to send invitation");
    }
  });
  const {
    form: inviteData,
    enhance: inviteEnhance,
    submitting: inviteSubmitting
  } = inviteForm;
  const teamForm = superForm(data.teamForm, {
    validators: zodClient(objectType({
      name: stringType().min(1, "Team name is required")
    })),
    dataType: "json",
    onSubmit: () => {
      updateStatus("saving");
    },
    onUpdated: ({ form }) => {
      if (form.valid) {
        updateStatus("saved");
        toast.success("Team created successfully");
      }
    },
    onError: () => {
      updateStatus("error");
      toast.error("Failed to create team");
    }
  });
  const {
    form: teamData,
    enhance: teamEnhance,
    submitting: teamSubmitting
  } = teamForm;
  async function removeTeamMember(teamId, memberId, memberName) {
    if (!confirm(`Are you sure you want to remove ${memberName} from the team?`)) {
      return;
    }
    updateStatus("saving");
    const formData = new FormData();
    formData.append("teamId", teamId);
    formData.append("memberId", memberId);
    try {
      const response = await fetch("?/removeTeamMember", { method: "POST", body: formData });
      const result = await response.json();
      if (result.success) {
        updateStatus("saved");
        toast.success(`${memberName} has been removed from the team`);
      } else {
        updateStatus("error");
        toast.error(result.error || "Failed to remove team member");
      }
    } catch (error) {
      updateStatus("error");
      toast.error("An error occurred while removing the team member");
    }
  }
  let showDebug = false;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Team Settings - Hirli",
      description: "Manage your teams, invite team members, and create new teams for collaboration on job applications and resume profiles.",
      keywords: "team management, team collaboration, invite members, create team, job application teams",
      url: "https://hirli.com/dashboard/settings/team"
    });
    $$payload2.out += `<!----> <div class="space-y-6"><div class="border-border flex flex-col justify-between border-b p-6"><div class="flex items-center justify-between"><div><h2 class="text-lg font-semibold">Team Settings</h2> <p class="text-muted-foreground text-sm">Manage your teams, invite team members, and create new teams.</p></div> <div class="flex items-center gap-3"><div class="flex items-center gap-2"><div${attr_class(`h-2 w-2 rounded-full ${stringify(saveStatus === "saving" ? "animate-pulse bg-orange-500" : saveStatus === "error" ? "bg-red-500" : "bg-green-500")}`)}></div> <span class="text-muted-foreground text-xs">`;
    if (saveStatus === "saving") {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `Saving...`;
    } else if (saveStatus === "error") {
      $$payload2.out += "<!--[1-->";
      $$payload2.out += `Error saving`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `Changes saved`;
    }
    $$payload2.out += `<!--]--></span></div> `;
    Button($$payload2, {
      type: "button",
      variant: "outline",
      size: "sm",
      class: "hidden md:flex",
      onclick: () => showDebug = !showDebug,
      children: ($$payload3) => {
        $$payload3.out += `<!---->${escape_html(showDebug ? "Hide" : "Show")} Debug`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></div> <div class="grid grid-cols-1 gap-6 md:grid-cols-[1fr_300px]"><div>`;
    Root($$payload2, {
      value: activeTab,
      onValueChange: (value) => activeTab = value,
      children: ($$payload3) => {
        const each_array_1 = ensure_array_like(tabs);
        Tabs_list($$payload3, {
          class: "w-full",
          children: ($$payload4) => {
            const each_array = ensure_array_like(tabs);
            $$payload4.out += `<!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let tab = each_array[$$index];
              Custom_tabs_trigger($$payload4, {
                value: tab.id,
                children: ($$payload5) => {
                  $$payload5.out += `<div class="flex items-center gap-2"><!---->`;
                  tab.icon?.($$payload5, { class: "h-4 w-4" });
                  $$payload5.out += `<!----> <span>${escape_html(tab.label)}</span></div>`;
                },
                $$slots: { default: true }
              });
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!--[-->`;
        for (let $$index_3 = 0, $$length = each_array_1.length; $$index_3 < $$length; $$index_3++) {
          let tab = each_array_1[$$index_3];
          Tabs_content($$payload3, {
            value: tab.id,
            class: "mt-6",
            children: ($$payload4) => {
              if (tab.id === "teams") {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div class="space-y-6">`;
                if (data.teams && data.teams.length > 0) {
                  $$payload4.out += "<!--[-->";
                  const each_array_2 = ensure_array_like(data.teams);
                  $$payload4.out += `<!--[-->`;
                  for (let $$index_2 = 0, $$length2 = each_array_2.length; $$index_2 < $$length2; $$index_2++) {
                    let team = each_array_2[$$index_2];
                    Card($$payload4, {
                      children: ($$payload5) => {
                        Card_header($$payload5, {
                          class: "p-6",
                          children: ($$payload6) => {
                            $$payload6.out += `<div class="flex items-center justify-between"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">`;
                            Users($$payload6, { class: "text-primary h-5 w-5" });
                            $$payload6.out += `<!----></div> <div>`;
                            Card_title($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(team.name)}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----> `;
                            Card_description($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(team.members?.length || 0)} member${escape_html(team.members?.length !== 1 ? "s" : "")}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----></div></div> <div>`;
                            if (team.ownerId === data.user.id) {
                              $$payload6.out += "<!--[-->";
                              $$payload6.out += `<span class="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">Owner</span>`;
                            } else {
                              $$payload6.out += "<!--[!-->";
                              $$payload6.out += `<span class="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">Member</span>`;
                            }
                            $$payload6.out += `<!--]--></div></div>`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload5.out += `<!----> `;
                        Card_content($$payload5, {
                          class: "p-6 pt-0",
                          children: ($$payload6) => {
                            $$payload6.out += `<div class="space-y-4"><h4 class="text-sm font-medium">Team Members</h4> <div class="divide-y">`;
                            if (team.members && team.members.length > 0) {
                              $$payload6.out += "<!--[-->";
                              const each_array_3 = ensure_array_like(team.members);
                              $$payload6.out += `<!--[-->`;
                              for (let $$index_1 = 0, $$length3 = each_array_3.length; $$index_1 < $$length3; $$index_1++) {
                                let member = each_array_3[$$index_1];
                                $$payload6.out += `<div class="flex items-center justify-between py-3"><div class="flex items-center gap-3">`;
                                Avatar($$payload6, {
                                  class: "h-8 w-8",
                                  children: ($$payload7) => {
                                    if (member.user?.image) {
                                      $$payload7.out += "<!--[-->";
                                      Avatar_image($$payload7, {
                                        src: member.user.image,
                                        alt: member.user.name || "User"
                                      });
                                    } else {
                                      $$payload7.out += "<!--[!-->";
                                    }
                                    $$payload7.out += `<!--]--> `;
                                    Avatar_fallback($$payload7, {
                                      class: "rounded-full border bg-neutral-200",
                                      children: ($$payload8) => {
                                        $$payload8.out += `<!---->${escape_html(member.user?.name?.charAt(0) || "U")}`;
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload7.out += `<!---->`;
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload6.out += `<!----> <div><p class="text-sm font-medium">${escape_html(member.user?.name || "Unknown User")}</p> <p class="text-muted-foreground text-xs">${escape_html(member.user?.email)}</p></div></div> <div class="flex items-center gap-2">`;
                                if (member.user?.id === team.ownerId) {
                                  $$payload6.out += "<!--[-->";
                                  $$payload6.out += `<span class="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">Owner</span>`;
                                } else if (team.ownerId === data.user.id) {
                                  $$payload6.out += "<!--[1-->";
                                  Button($$payload6, {
                                    variant: "ghost",
                                    size: "sm",
                                    class: "text-red-500 hover:bg-red-50 hover:text-red-600",
                                    onclick: () => removeTeamMember(team.id, member.id, member.user?.name || "this user"),
                                    children: ($$payload7) => {
                                      Trash_2($$payload7, { class: "h-4 w-4" });
                                    },
                                    $$slots: { default: true }
                                  });
                                } else {
                                  $$payload6.out += "<!--[!-->";
                                }
                                $$payload6.out += `<!--]--></div></div>`;
                              }
                              $$payload6.out += `<!--]-->`;
                            } else {
                              $$payload6.out += "<!--[!-->";
                              $$payload6.out += `<p class="text-muted-foreground py-3 text-sm">No team members found.</p>`;
                            }
                            $$payload6.out += `<!--]--></div></div>`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload5.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                  }
                  $$payload4.out += `<!--]-->`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  Card($$payload4, {
                    children: ($$payload5) => {
                      Card_content($$payload5, {
                        class: "p-6 text-center",
                        children: ($$payload6) => {
                          $$payload6.out += `<div class="mb-4 flex justify-center"><div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">`;
                          Users($$payload6, { class: "text-primary h-6 w-6" });
                          $$payload6.out += `<!----></div></div> <h3 class="mb-2 text-lg font-medium">No Teams Found</h3> <p class="text-muted-foreground">You are not a member of any teams yet.</p> <div class="mt-6">`;
                          Button($$payload6, {
                            onclick: () => activeTab = "create",
                            children: ($$payload7) => {
                              Plus($$payload7, { class: "mr-2 h-4 w-4" });
                              $$payload7.out += `<!----> Create a Team`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----></div>`;
                        },
                        $$slots: { default: true }
                      });
                    },
                    $$slots: { default: true }
                  });
                }
                $$payload4.out += `<!--]--></div>`;
              } else if (tab.id === "invite") {
                $$payload4.out += "<!--[1-->";
                Card($$payload4, {
                  children: ($$payload5) => {
                    Card_header($$payload5, {
                      class: "p-6",
                      children: ($$payload6) => {
                        Card_title($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Invite Team Members`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Card_description($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Send invitations to join your team.`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Card_content($$payload5, {
                      class: "p-6 pt-0",
                      children: ($$payload6) => {
                        $$payload6.out += `<form method="POST" action="?/invite" class="space-y-6">`;
                        Form_field($$payload6, {
                          form: inviteForm,
                          name: "email",
                          children: ($$payload7) => {
                            Control($$payload7, {
                              children: ($$payload8) => {
                                Form_label($$payload8, {
                                  children: ($$payload9) => {
                                    $$payload9.out += `<!---->Email Address`;
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out += `<!----> <div class="flex items-center gap-2"><div class="relative flex-1">`;
                                Mail($$payload8, {
                                  class: "text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
                                });
                                $$payload8.out += `<!----> `;
                                Input($$payload8, {
                                  type: "email",
                                  placeholder: "<EMAIL>",
                                  class: "pl-10",
                                  get value() {
                                    return store_get($$store_subs ??= {}, "$inviteData", inviteData).email;
                                  },
                                  set value($$value) {
                                    store_mutate($$store_subs ??= {}, "$inviteData", inviteData, store_get($$store_subs ??= {}, "$inviteData", inviteData).email = $$value);
                                    $$settled = false;
                                  }
                                });
                                $$payload8.out += `<!----></div> `;
                                Form_field($$payload8, {
                                  form: inviteForm,
                                  name: "role",
                                  children: ($$payload9) => {
                                    Control($$payload9, {
                                      children: ($$payload10) => {
                                        $$payload10.out += `<div>`;
                                        Root$1($$payload10, {
                                          type: "single",
                                          value: store_get($$store_subs ??= {}, "$inviteData", inviteData).role || "member",
                                          onValueChange: (value) => inviteData.update((f) => ({ ...f, role: value })),
                                          children: ($$payload11) => {
                                            Select_trigger($$payload11, {
                                              class: "w-32",
                                              children: ($$payload12) => {
                                                Select_value($$payload12, { placeholder: "Role" });
                                              },
                                              $$slots: { default: true }
                                            });
                                            $$payload11.out += `<!----> `;
                                            Select_content($$payload11, {
                                              class: "max-h-60",
                                              children: ($$payload12) => {
                                                Select_item($$payload12, {
                                                  value: "member",
                                                  children: ($$payload13) => {
                                                    $$payload13.out += `<!---->Member`;
                                                  },
                                                  $$slots: { default: true }
                                                });
                                                $$payload12.out += `<!----> `;
                                                Select_item($$payload12, {
                                                  value: "admin",
                                                  children: ($$payload13) => {
                                                    $$payload13.out += `<!---->Admin`;
                                                  },
                                                  $$slots: { default: true }
                                                });
                                                $$payload12.out += `<!---->`;
                                              },
                                              $$slots: { default: true }
                                            });
                                            $$payload11.out += `<!---->`;
                                          },
                                          $$slots: { default: true }
                                        });
                                        $$payload10.out += `<!----></div>`;
                                      },
                                      $$slots: { default: true }
                                    });
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out += `<!----></div>`;
                              }
                            });
                            $$payload7.out += `<!----> `;
                            Form_field_errors($$payload7, {});
                            $$payload7.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Button($$payload6, {
                          type: "submit",
                          disabled: store_get($$store_subs ??= {}, "$inviteSubmitting", inviteSubmitting),
                          class: "w-full",
                          children: ($$payload7) => {
                            User_plus($$payload7, { class: "mr-2 h-4 w-4" });
                            $$payload7.out += `<!----> ${escape_html(store_get($$store_subs ??= {}, "$inviteSubmitting", inviteSubmitting) ? "Sending Invitation..." : "Send Invitation")}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></form>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
              } else if (tab.id === "create") {
                $$payload4.out += "<!--[2-->";
                Card($$payload4, {
                  children: ($$payload5) => {
                    Card_header($$payload5, {
                      class: "p-6",
                      children: ($$payload6) => {
                        Card_title($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Create a New Team`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Card_description($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Set up a new team to collaborate with others.`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Card_content($$payload5, {
                      class: "p-6 pt-0",
                      children: ($$payload6) => {
                        $$payload6.out += `<form method="POST" action="?/createTeam" class="space-y-6">`;
                        Form_field($$payload6, {
                          form: teamForm,
                          name: "name",
                          children: ($$payload7) => {
                            Control($$payload7, {
                              children: ($$payload8) => {
                                Form_label($$payload8, {
                                  children: ($$payload9) => {
                                    $$payload9.out += `<!---->Team Name`;
                                  },
                                  $$slots: { default: true }
                                });
                                $$payload8.out += `<!----> `;
                                Input($$payload8, {
                                  type: "text",
                                  placeholder: "My Awesome Team",
                                  get value() {
                                    return store_get($$store_subs ??= {}, "$teamData", teamData).name;
                                  },
                                  set value($$value) {
                                    store_mutate($$store_subs ??= {}, "$teamData", teamData, store_get($$store_subs ??= {}, "$teamData", teamData).name = $$value);
                                    $$settled = false;
                                  }
                                });
                                $$payload8.out += `<!---->`;
                              }
                            });
                            $$payload7.out += `<!----> `;
                            Form_field_errors($$payload7, {});
                            $$payload7.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Button($$payload6, {
                          type: "submit",
                          disabled: store_get($$store_subs ??= {}, "$teamSubmitting", teamSubmitting),
                          class: "w-full",
                          children: ($$payload7) => {
                            Plus($$payload7, { class: "mr-2 h-4 w-4" });
                            $$payload7.out += `<!----> ${escape_html(store_get($$store_subs ??= {}, "$teamSubmitting", teamSubmitting) ? "Creating Team..." : "Create Team")}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></form>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        }
        $$payload3.out += `<!--]-->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> `;
    if (showDebug) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="bg-muted rounded-lg border p-4"><h3 class="mb-2 text-sm font-medium">Form Debug</h3> <div class="space-y-4"><div><h4 class="text-xs font-medium">Invite Form</h4> `;
      SuperDebug($$payload2, {
        data: store_get($$store_subs ??= {}, "$inviteData", inviteData)
      });
      $$payload2.out += `<!----></div> <div><h4 class="text-xs font-medium">Team Form</h4> `;
      SuperDebug($$payload2, {
        data: store_get($$store_subs ??= {}, "$teamData", teamData)
      });
      $$payload2.out += `<!----></div></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-TM1ERvmF.js.map
