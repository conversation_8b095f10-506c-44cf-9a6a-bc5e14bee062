{"version": 3, "file": "80-DWebBXFX.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/jobs/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/80.js"], "sourcesContent": ["import { v as verifySessionToken } from \"../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../chunks/prisma.js\";\nconst load = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  const user = token ? await verifySessionToken(token) : null;\n  const collection = url.searchParams.get(\"collection\");\n  const title = url.searchParams.get(\"title\") || \"\";\n  const locations = url.searchParams.get(\"locations\")?.split(\",\").filter(Boolean) || [];\n  const location = url.searchParams.get(\"location\") || \"\";\n  const locationType = url.searchParams.get(\"locationType\")?.split(\",\").filter(Boolean) || [];\n  const experience = url.searchParams.get(\"experience\")?.split(\",\").filter(Boolean) || [];\n  let salary = url.searchParams.get(\"salary\") || \"\";\n  const state = url.searchParams.get(\"state\") || \"\";\n  const country = url.searchParams.get(\"country\") || \"US\";\n  const datePosted = url.searchParams.get(\"datePosted\") || \"\";\n  const easyApply = url.searchParams.get(\"easyApply\") === \"true\";\n  const companies = url.searchParams.get(\"companies\")?.split(\",\").filter(Boolean) || [];\n  const jobId = url.searchParams.get(\"jobId\") || \"\";\n  const validSalaryValues = [\n    \"0-50000\",\n    \"50000-75000\",\n    \"75000-100000\",\n    \"100000-150000\",\n    \"150000+\",\n    \"\"\n  ];\n  if (!validSalaryValues.includes(salary)) {\n    salary = \"\";\n  }\n  let collectionDetails = null;\n  if (collection) {\n    try {\n      collectionDetails = await prisma.job_collections.findUnique({\n        where: { slug: collection }\n      });\n    } catch (error) {\n      console.error(\"Error fetching collection details:\", error);\n    }\n  }\n  let selectedJob = null;\n  if (jobId) {\n    try {\n      selectedJob = await prisma.job_listing.findUnique({\n        where: { id: jobId }\n      });\n    } catch (error) {\n      console.error(\"Error fetching job details:\", error);\n    }\n  }\n  let totalJobCount = 0;\n  try {\n    const filter = { isActive: true };\n    if (title) {\n      filter.title = { contains: title, mode: \"insensitive\" };\n    }\n    if (location) {\n      filter.location = { contains: location, mode: \"insensitive\" };\n    }\n    if (state) {\n      filter.stateId = state;\n    }\n    totalJobCount = await prisma.job_listing.count({ where: filter });\n  } catch (error) {\n    console.error(\"Error counting jobs:\", error);\n  }\n  return {\n    user,\n    collection: collectionDetails,\n    searchParams: {\n      title,\n      locations,\n      location,\n      locationType,\n      experience,\n      salary,\n      state,\n      country,\n      datePosted,\n      easyApply,\n      companies\n    },\n    selectedJob,\n    totalJobCount\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/jobs/_page.server.ts.js';\n\nexport const index = 80;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/jobs/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/jobs/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/80.BMVU8KyI.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/DV_57wcZ.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/Ci8yIwIB.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/B6TiSgAN.js\",\"_app/immutable/chunks/Dmwghw4a.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/DW5gea7N.js\",\"_app/immutable/chunks/B5tu6DNS.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/CfcZq63z.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/9r-6KH_O.js\",\"_app/immutable/chunks/CTn0v-X8.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/D9yI7a4E.js\",\"_app/immutable/chunks/eW6QhNR3.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/CVVv9lPb.js\",\"_app/immutable/chunks/KVutzy_p.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/VYoCKyli.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/CIPPbbaT.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/6BxQgNmX.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/BAawoUIy.js\",\"_app/immutable/chunks/BM9SsHQg.js\",\"_app/immutable/chunks/FAbXdqfL.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/ByUTvV5u.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/index.CV-KWLNP.css\",\"_app/immutable/assets/scroll-area.bHHIbcsu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACzC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC7D,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC;AACvD,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AACvF,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;AACzD,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AAC7F,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AACzF,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI;AACzD,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AAC7D,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,MAAM;AAChE,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AACvF,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,iBAAiB,GAAG;AAC5B,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC3C,IAAI,MAAM,GAAG,EAAE;AACf;AACA,EAAE,IAAI,iBAAiB,GAAG,IAAI;AAC9B,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,IAAI;AACR,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;AAClE,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU;AACjC,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAChE;AACA;AACA,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,IAAI;AACR,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACxD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK;AAC1B,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD;AACA;AACA,EAAE,IAAI,aAAa,GAAG,CAAC;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE;AACrC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,CAAC,KAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE;AAC7D;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,MAAM,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE;AACnE;AACA,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,CAAC,OAAO,GAAG,KAAK;AAC5B;AACA,IAAI,aAAa,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACrE,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD;AACA,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,UAAU,EAAE,iBAAiB;AACjC,IAAI,YAAY,EAAE;AAClB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM;AACN,KAAK;AACL,IAAI,WAAW;AACf,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;AClFW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAuC,CAAC,EAAE;AAErG,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACriG,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,0CAA0C,CAAC,gDAAgD;AACxJ,MAAC,KAAK,GAAG;;;;"}