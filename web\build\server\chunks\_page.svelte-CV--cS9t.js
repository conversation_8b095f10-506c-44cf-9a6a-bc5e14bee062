import 'clsx';
import { p as push, V as copy_payload, W as assign_payload, q as pop, O as escape_html, M as ensure_array_like, Q as bind_props } from './index3-CqUPEnZw.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import 'date-fns';
import './index-DwwMqnhu.js';
import { T as Trending_up } from './trending-up-BKR_Sbhj.js';
import { U as Users } from './users-e7-Uhkka.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Badge } from './badge-C9pSznab.js';
import { S as Separator } from './separator-5ooeI4XN.js';
import { G as Gift } from './gift-Ca32zso7.js';
import { S as Share_2 } from './share-2-ihgFYKw2.js';
import { C as Copy } from './copy-CgwvUJL5.js';
import { M as Mail } from './mail-Brqxil2x.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import 'svelte/store';
import './Icon-A4vzmk-O.js';
import './index2-Cut0V_vU.js';
import './index-DjwFQdT_.js';

function ReferralChart($$payload, $$props) {
  push();
  let { referralData } = $$props;
  $$payload.out += `<div class="border-border flex items-end justify-between border-b p-4"><div class="flex flex-col"><h4 class="text-md font-normal">Referral Analytics</h4> <p class="text-muted-foreground text-sm">Track your referral performance over time</p></div> <div class="flex items-center gap-2">`;
  Trending_up($$payload, { class: "text-primary h-5 w-5" });
  $$payload.out += `<!----> <span class="text-sm font-medium">Growth Trend</span></div></div> <div class="border-border grid grid-cols-1 gap-4 divide-x border-b sm:grid-cols-3"><div class="p-4"><div class="flex items-center gap-2">`;
  Users($$payload, { class: "text-primary h-4 w-4" });
  $$payload.out += `<!----> <span class="text-sm font-medium">Total Referrals</span></div> <div class="mt-2"><div class="text-2xl font-bold">${escape_html(referralData?.referralCount || 0)}</div> <div class="text-muted-foreground text-xs">All time</div></div></div> <div class="p-4"><div class="flex items-center gap-2">`;
  Calendar($$payload, { class: "text-success h-4 w-4" });
  $$payload.out += `<!----> <span class="text-sm font-medium">This Month</span></div> <div class="mt-2"><div class="text-2xl font-bold">${escape_html("...")}</div> <div class="text-muted-foreground text-xs">New referrals</div></div></div> <div class="p-4"><div class="flex items-center gap-2">`;
  Trending_up($$payload, { class: "text-warning h-4 w-4" });
  $$payload.out += `<!----> <span class="text-sm font-medium">Success Rate</span></div> <div class="mt-2"><div class="text-2xl font-bold">${escape_html("...")}%</div> <div class="text-muted-foreground text-xs">Conversion rate</div></div></div></div> <div class="h-64 p-4"><div class="mb-4 flex items-center justify-between"><h4 class="font-medium">Monthly Referrals</h4> <div class="flex items-center gap-4 text-xs"><div class="flex items-center gap-1"><div class="h-3 w-3 rounded-full" style="background-color: var(--chart-1);"></div> <span>Monthly</span></div> <div class="flex items-center gap-1"><div class="h-3 w-3 rounded-full" style="background-color: var(--chart-2);"></div> <span>Cumulative</span></div></div></div> `;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex h-48 items-center justify-center"><div class="text-muted-foreground text-sm">Loading chart data...</div></div>`;
  }
  $$payload.out += `<!--]--></div> <div class="bg-secondary m-4 space-y-4 rounded-md p-4"><div class="flex flex-col"><h4 class="flex gap-2 text-sm">`;
  Trending_up($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> Referral Code History</h4> <p class="text-muted-foreground text-xs">Track all your referral codes and their performance over time.</p></div> <div>`;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-muted-foreground text-center text-sm">Loading referral code history...</div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  pop();
}
function ReferralOverviewTab($$payload, $$props) {
  push();
  let { referralData } = $$props;
  let copying = false;
  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;
    copying = true;
    try {
      await navigator.clipboard.writeText(referralData.referralLink);
      toast.success("Referral link copied to clipboard!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy referral link");
    } finally {
      copying = false;
    }
  };
  const shareReferralLink = async () => {
    if (!referralData?.referralLink) return;
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Join Hirli with my referral link",
          text: "Sign up for Hirli using my referral link and get started with job automation!",
          url: referralData.referralLink
        });
      } catch (error) {
        console.error("Error sharing:", error);
        copyReferralLink();
      }
    } else {
      copyReferralLink();
    }
  };
  $$payload.out += `<div class="space-y-4"><div class="border-border grid gap-4 divide-x border-b md:grid-cols-3"><div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Total Referrals</h3> `;
  Users($$payload, { class: "text-muted-foreground h-4 w-4" });
  $$payload.out += `<!----></div> <div><div class="text-2xl font-bold">${escape_html(referralData.referralCount || 0)}</div> <p class="text-muted-foreground text-xs">People you've referred</p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Rewards Earned</h3> `;
  Gift($$payload, { class: "text-muted-foreground h-4 w-4" });
  $$payload.out += `<!----></div> <div><div class="text-2xl font-bold">$0</div> <p class="text-muted-foreground text-xs">Coming soon</p></div></div> <div class="p-4"><div class="flex flex-row items-center justify-between space-y-0 pb-2"><h3 class="text-sm font-medium">Your Code</h3> `;
  Share_2($$payload, { class: "text-muted-foreground h-4 w-4" });
  $$payload.out += `<!----></div> <div><div class="font-mono text-2xl font-bold">${escape_html(referralData.referralCode)}</div> <p class="text-muted-foreground text-xs">Your unique referral code</p></div></div></div> <div class="flex flex-col gap-8 px-4"><div class="grid grid-cols-2 gap-4"><div class="flex flex-col gap-4"><div class="flex flex-col gap-1"><h2 class="text-lg font-medium">Invite friends &amp; earn rewards</h2> <p class="text-muted-foreground text-sm">Refer a friend and earn $10 credit for every paying user. They'll get 50% off
            their first month on Starter or Pro plans. <strong>Start sharing today!</strong></p></div> <div class="w-2/3"><h3 class="mb-3 font-medium">Share your link</h3> <div class="space-y-4"><div class="flex gap-2">`;
  Input($$payload, {
    value: referralData.referralLink,
    readonly: true,
    class: "font-mono"
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    size: "sm",
    onclick: copyReferralLink,
    disabled: copying,
    children: ($$payload2) => {
      Copy($$payload2, { class: "h-4 w-4" });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    size: "sm",
    onclick: shareReferralLink,
    children: ($$payload2) => {
      Share_2($$payload2, { class: "h-4 w-4" });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></div></div></div> `;
  if (referralData.referrals && referralData.referrals.length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    Card($$payload, {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Card_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Recent Referrals`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->People who signed up using your referral code.`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Card_content($$payload2, {
          children: ($$payload3) => {
            const each_array = ensure_array_like(referralData.referrals.slice(0, 5));
            $$payload3.out += `<div class="space-y-3"><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let referral = each_array[$$index];
              $$payload3.out += `<div class="flex items-center justify-between"><div><p class="font-medium">${escape_html(referral.referred?.name || referral.referred?.email || "Unknown User")}</p> <p class="text-muted-foreground text-sm">Joined ${escape_html(new Date(referral.createdAt).toLocaleDateString())}</p></div> `;
              Badge($$payload3, {
                variant: referral.status === "completed" ? "default" : "secondary",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(referral.status || "pending")}`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div> `;
              if (referral !== referralData.referrals[referralData.referrals.length - 1]) {
                $$payload3.out += "<!--[-->";
                Separator($$payload3, {});
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]-->`;
            }
            $$payload3.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
function ReferralShareTab($$payload, $$props) {
  push();
  let { referralData } = $$props;
  let copying = false;
  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;
    copying = true;
    try {
      await navigator.clipboard.writeText(referralData.referralLink);
      toast.success("Referral link copied to clipboard!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy referral link");
    } finally {
      copying = false;
    }
  };
  const shareReferralLink = async () => {
    if (!referralData?.referralLink) return;
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Join Hirli with my referral link",
          text: "Sign up for Hirli using my referral link and get started with job automation!",
          url: referralData.referralLink
        });
      } catch (error) {
        console.error("Error sharing:", error);
        copyReferralLink();
      }
    } else {
      copyReferralLink();
    }
  };
  const generateShareMessage = () => {
    if (!referralData?.referralLink) return "";
    return `🚀 Join me on Hirli and automate your job search! Use my referral link to get started: ${referralData.referralLink}`;
  };
  const shareViaEmail = () => {
    const subject = encodeURIComponent("Join Hirli - Automate Your Job Search");
    const body = encodeURIComponent(generateShareMessage());
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };
  const shareViaSocial = (platform) => {
    const message = encodeURIComponent(generateShareMessage());
    const url = encodeURIComponent(referralData.referralLink);
    let shareUrl = "";
    switch (platform) {
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?text=${message}`;
        break;
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case "linkedin":
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
        break;
    }
    if (shareUrl) {
      window.open(shareUrl, "_blank", "width=600,height=400");
    }
  };
  $$payload.out += `<div class="border-border flex flex-col gap-0 border-b p-4"><h4 class="text-md font-normal">Share Your Referral Link</h4> <p class="text-muted-foreground text-sm">Choose how you want to share your referral link and start earning rewards.</p></div> <div class="space-y-6 p-4"><div class="bg-muted/50 rounded-lg border p-4"><h4 class="mb-2 font-medium">Share Message Preview:</h4> <p class="text-muted-foreground text-sm">${escape_html(generateShareMessage())}</p></div> <div class="space-y-4"><h4 class="font-medium">Your Referral Link</h4> <div class="flex gap-2">`;
  Input($$payload, {
    value: referralData.referralLink,
    readonly: true,
    class: "font-mono"
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    onclick: copyReferralLink,
    disabled: copying,
    children: ($$payload2) => {
      Copy($$payload2, { class: "mr-2 h-4 w-4" });
      $$payload2.out += `<!----> ${escape_html(copying ? "Copied!" : "Copy")}`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="space-y-4"><h4 class="font-medium">Share Options</h4> <div class="grid grid-cols-2 gap-3 sm:grid-cols-3">`;
  Button($$payload, {
    variant: "outline",
    class: "flex h-auto flex-col gap-2 py-4",
    onclick: () => shareViaSocial("twitter"),
    children: ($$payload2) => {
      Share_2($$payload2, { class: "h-5 w-5" });
      $$payload2.out += `<!----> <span class="text-xs">Twitter</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "flex h-auto flex-col gap-2 py-4",
    onclick: () => shareViaSocial("facebook"),
    children: ($$payload2) => {
      Share_2($$payload2, { class: "h-5 w-5" });
      $$payload2.out += `<!----> <span class="text-xs">Facebook</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "flex h-auto flex-col gap-2 py-4",
    onclick: () => shareViaSocial("linkedin"),
    children: ($$payload2) => {
      Share_2($$payload2, { class: "h-5 w-5" });
      $$payload2.out += `<!----> <span class="text-xs">LinkedIn</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "flex h-auto flex-col gap-2 py-4",
    onclick: shareViaEmail,
    children: ($$payload2) => {
      Mail($$payload2, { class: "h-5 w-5" });
      $$payload2.out += `<!----> <span class="text-xs">Email</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "flex h-auto flex-col gap-2 py-4",
    onclick: shareReferralLink,
    children: ($$payload2) => {
      Share_2($$payload2, { class: "h-5 w-5" });
      $$payload2.out += `<!----> <span class="text-xs">Native Share</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="rounded-lg border bg-blue-50 p-4 dark:bg-blue-950/20"><h4 class="mb-2 flex items-center gap-2 font-medium">`;
  Trending_up($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> Tips for Better Results</h4> <ul class="text-muted-foreground space-y-1 text-sm"><li>• Share with people actively looking for jobs</li> <li>• Explain how Hirli can help automate their job search</li> <li>• Follow up to help them get started</li> <li>• Share in relevant professional groups</li></ul></div></div>`;
  pop();
}
function ReferralSettingsTab($$payload, $$props) {
  push();
  let { referralData = void 0 } = $$props;
  let customCode = "";
  let updating = false;
  const generateNewCode = async () => {
    updating = true;
    try {
      const response = await fetch("/api/referrals", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "regenerate" })
      });
      if (response.ok) {
        const data = await response.json();
        referralData.referralCode = data.referralCode;
        referralData.referralLink = data.referralLink;
        toast.success("New referral code generated!");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to generate new code");
      }
    } catch (error) {
      console.error("Error generating new code:", error);
      toast.error("Failed to generate new code");
    } finally {
      updating = false;
    }
  };
  const setCustomCode = async () => {
    if (!customCode.trim()) {
      toast.error("Please enter a custom code");
      return;
    }
    updating = true;
    try {
      const response = await fetch("/api/referrals", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "create",
          customCode: customCode.trim()
        })
      });
      if (response.ok) {
        const data = await response.json();
        referralData.referralCode = data.referralCode;
        referralData.referralLink = data.referralLink;
        customCode = "";
        toast.success("Custom referral code set!");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to set custom code");
      }
    } catch (error) {
      console.error("Error setting custom code:", error);
      toast.error("Failed to set custom code");
    } finally {
      updating = false;
    }
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="border-border flex flex-col gap-0 border-b p-4"><h4 class="text-md font-normal">Customize Your Referral Code</h4> <p class="text-muted-foreground text-sm">Create a custom referral code that's easy to remember and share.</p></div> <div class="space-y-4 p-4"><div class="space-y-2"><div class="flex gap-2">`;
    Input($$payload2, {
      placeholder: "Enter custom code (4-12 characters)",
      class: "font-mono",
      get value() {
        return customCode;
      },
      set value($$value) {
        customCode = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      onclick: setCustomCode,
      disabled: updating || !customCode.trim(),
      children: ($$payload3) => {
        $$payload3.out += `<!---->Set Code`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "outline",
      onclick: generateNewCode,
      disabled: updating,
      children: ($$payload3) => {
        Refresh_cw($$payload3, { class: "mr-2 h-4 w-4" });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <p class="text-muted-foreground text-xs">Custom codes must be 4-12 characters long and contain only letters and numbers.</p></div> <div><h3>Current Referral Information</h3></div> <div class="space-y-4"><div class="grid grid-cols-2 gap-4"><div><div class="text-sm font-medium">Referral Code</div> <p class="font-mono text-lg">${escape_html(referralData.referralCode)}</p></div> <div><div class="text-sm font-medium">Total Referrals</div> <p class="text-lg font-semibold">${escape_html(referralData.referralCount || 0)}</p></div></div> <div><div class="text-sm font-medium">Full Referral URL</div> `;
    Input($$payload2, {
      value: referralData.referralLink,
      readonly: true,
      class: "font-mono text-xs"
    });
    $$payload2.out += `<!----></div></div> `;
    if (referralData.referredBy) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div><div><h3>You Were Referred By</h3></div> <div><div class="flex items-center gap-2"><p class="font-medium">${escape_html(referralData.referredBy.name || referralData.referredBy.email)}</p> `;
      Badge($$payload2, {
        variant: "outline",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Referrer`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div></div></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { referralData });
  pop();
}
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let referralData = data.referralData;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Referral Program | Hirli",
      description: "Share Hirli with friends and earn rewards for successful referrals.",
      keywords: "referral program, share, earn, rewards, referrals, Hirli",
      url: "https://hirli.com/dashboard/settings/referrals"
    });
    $$payload2.out += `<!----> <div class="flex h-full flex-col"><div class="flex flex-col justify-between p-6"><h2 class="text-lg font-semibold">Referral Program</h2> <p class="text-muted-foreground">Share Hirli with friends and earn rewards for successful referrals.</p></div> <div class="flex-1">`;
    if (referralData) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<!---->`;
      Root($$payload2, {
        value: "overview",
        class: "w-full",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Tabs_list($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Tabs_trigger($$payload4, {
                value: "overview",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Overview`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_trigger($$payload4, {
                value: "analytics",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Analytics`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_trigger($$payload4, {
                value: "share",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Share &amp; Earn`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_trigger($$payload4, {
                value: "settings",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Settings`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_content($$payload3, {
            value: "overview",
            children: ($$payload4) => {
              ReferralOverviewTab($$payload4, { referralData });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_content($$payload3, {
            value: "analytics",
            children: ($$payload4) => {
              ReferralChart($$payload4, { referralData });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_content($$payload3, {
            value: "share",
            children: ($$payload4) => {
              ReferralShareTab($$payload4, { referralData });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_content($$payload3, {
            value: "settings",
            children: ($$payload4) => {
              ReferralSettingsTab($$payload4, {
                get referralData() {
                  return referralData;
                },
                set referralData($$value) {
                  referralData = $$value;
                  $$settled = false;
                }
              });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="flex items-center justify-center py-8"><div class="text-muted-foreground">Failed to load referral data</div></div>`;
    }
    $$payload2.out += `<!--]--></div></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CV--cS9t.js.map
