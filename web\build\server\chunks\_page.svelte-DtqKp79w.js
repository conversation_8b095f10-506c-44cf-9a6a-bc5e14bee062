import { p as push, q as pop, M as ensure_array_like, O as escape_html } from './index3-CqUPEnZw.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { S as SEO } from './SEO-UItXytUy.js';
import _page$2 from './_page.svelte-CtA_VDdn.js';
import _page$3 from './_page.svelte-DM7wLP_d.js';
import _page$1 from './_page.svelte-VjBpIetU.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import 'clsx';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './card-D-TLkt4h.js';
import './card-content-CrxB5iaZ.js';
import './card-description-CMuO6f9m.js';
import './card-header-BSbSWnCH.js';
import './card-title-DNJv4RN2.js';
import './button-CrucCo1G.js';
import './index-DjwFQdT_.js';
import './input-DF0gPqYN.js';
import './index7-BURUpWjT.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import './index2-Cut0V_vU.js';
import './triangle-alert-DOwM8mYc.js';
import './plus-e8i_Czzl.js';
import './dialog-description-CxPAHL_4.js';
import './dialog-description2-rfr-pd9k.js';
import './index12-H6t3LX3-.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './hidden-input-1eDzjGOB.js';
import './textarea-DnpYDER1.js';
import './select-value-nUrqCsCq.js';
import './index16-Dse3U8B8.js';
import './separator-5ooeI4XN.js';
import './chevron-right-C2rn-JeO.js';
import './table-row-CyhLzMgE.js';
import './index14-C2WSwUih.js';
import './select-group-Cxqg41Dj.js';
import './refresh-cw-Dvfix_NJ.js';
import './download-CLn66Ope.js';
import './mail-Brqxil2x.js';
import './user-x-BlXWgQj5.js';
import './chevron-left-Q7JcxjQ3.js';
import './chevron-right2-CeLbJ90J.js';

function _page($$payload, $$props) {
  push();
  let activeTab = "analytics";
  const tabs = [
    {
      id: "analytics",
      label: "Analytics",
      component: _page$1
    },
    {
      id: "audiences",
      label: "Audiences",
      component: _page$2
    },
    {
      id: "broadcast",
      label: "Broadcast",
      component: _page$3
    }
  ];
  SEO($$payload, { title: "Email Management - Hirli" });
  $$payload.out += `<!----> <div class="border-border flex items-center justify-between border-b px-4 py-2"><h2 class="text-lg font-semibold">Email Settings</h2></div> <!---->`;
  Root($$payload, {
    value: activeTab,
    onValueChange: (value) => {
      activeTab = value;
    },
    children: ($$payload2) => {
      const each_array_1 = ensure_array_like(tabs);
      $$payload2.out += `<div class="border-border border-b p-0"><!---->`;
      Tabs_list($$payload2, {
        class: "flex flex-row divide-x",
        children: ($$payload3) => {
          const each_array = ensure_array_like(tabs);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let tab = each_array[$$index];
            $$payload3.out += `<!---->`;
            Tabs_trigger($$payload3, {
              value: tab.id,
              class: "no-border flex items-center rounded-none p-2",
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(tab.label)}`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!--[-->`;
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        let tab = each_array_1[$$index_1];
        $$payload2.out += `<!---->`;
        Tabs_content($$payload2, {
          value: tab.id,
          class: "space-y-4",
          children: ($$payload3) => {
            if (tab.component) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<!---->`;
              tab.component($$payload3, {});
              $$payload3.out += `<!---->`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DtqKp79w.js.map
