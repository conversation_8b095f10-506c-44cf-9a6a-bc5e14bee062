/* eslint-disable no-nested-ternary */

import {Layer<PERSON>rovider, ThemeProvider} from '@sanity/ui'
import {type RootTheme} from '@sanity/ui/theme'
import {type ReactNode} from 'react'
import {ColorSchemeSetValueContext, ColorSchemeValueContext} from 'sanity/_singletons'

import {defaultTheme, type StudioTheme} from '../theme'
import {useActiveWorkspace} from './activeWorkspaceMatcher'

interface StudioThemeProviderProps {
  children: ReactNode
}

// Temporary fix for fonts generated by Themer, once Theme<PERSON> is updated to use sanity v2 this can be removed
// It won't work for locally imported themes from themer, as they won't be updated with a new api call.
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const isThemerTheme = (theme: StudioTheme): boolean => theme.__themer === true

function getThemeValues(theme: StudioTheme): RootTheme {
  return {
    ...defaultTheme,
    v2: theme.v2,
    fonts: isThemerTheme(theme) ? defaultTheme.fonts : (theme.fonts ?? defaultTheme.fonts),
    color: theme.color ?? defaultTheme.color,
  }
}

/** @internal */
export function StudioThemeProvider({children}: StudioThemeProviderProps) {
  const theme = useActiveWorkspace().activeWorkspace.theme

  if (theme.__legacy) {
    const scheme = theme.__dark ? 'dark' : 'light'
    return (
      <ColorSchemeSetValueContext.Provider value={false}>
        <ColorSchemeValueContext.Provider value={scheme}>
          <ThemeProvider scheme={scheme} theme={getThemeValues(theme)}>
            <LayerProvider>{children}</LayerProvider>
          </ThemeProvider>
        </ColorSchemeValueContext.Provider>
      </ColorSchemeSetValueContext.Provider>
    )
  }

  return (
    <ThemeProvider theme={getThemeValues(theme)}>
      <LayerProvider>{children}</LayerProvider>
    </ThemeProvider>
  )
}
