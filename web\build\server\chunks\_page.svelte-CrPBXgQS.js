import { p as push, V as copy_payload, W as assign_payload, q as pop, P as stringify, O as escape_html } from './index3-CqUPEnZw.js';
import { o as onDestroy } from './index-server-CezSOnuG.js';
import { g as goto } from './client-dNyMPa8V.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { S as SEO } from './SEO-UItXytUy.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { R as ResumeTabs, a as Resume, E as EditDesignPanel, b as ResumeForm } from './EditDesignPanel-Dil18Jqx.js';
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { S as Square_pen } from './square-pen-DCE_ltl5.js';
import { S as Save } from './save-Cfytkt-w.js';
import { E as Ellipsis } from './ellipsis-C7bKlkmn.js';
import { D as Dropdown_menu_item } from './dropdown-menu-item-DwivDmnZ.js';
import { D as Dropdown_menu_separator } from './dropdown-menu-separator-B5VQzuNH.js';
import { C as Check } from './check-WP_4Msti.js';
import './false-CRHihH2U.js';
import './index2-Cut0V_vU.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './buildResume-ByCXwpI3.js';
import './types-D78SXuvm.js';
import './rotate-ccw-CmotOMz1.js';
import './Icon-A4vzmk-O.js';
import './settings-STaOxCkl.js';
import './sparkles-E4-thk3U.js';
import './textarea-DnpYDER1.js';
import './zod-DfpldWlD.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './superForm-CVYoTAIb.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './stringify-DWCARkQV.js';
import './index7-BURUpWjT.js';
import './scroll-lock-BkBz2nVp.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './chevron-down-xGjWLrZH.js';
import './dialog-description-CxPAHL_4.js';
import './dialog-description2-rfr-pd9k.js';
import './store-Dgwm3sxJ.js';
import './html-FW6Ia4bL.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './label-Dt8gTF_8.js';
import './separator-5ooeI4XN.js';
import './index15-D3NL0C7o.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './use-grace-area.svelte-CrXiOQDy.js';

function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  console.log("Builder page data:", data);
  if (!data.resumeData) {
    console.error("Resume data is missing");
    goto();
  }
  let saving = false;
  let lastSaved = null;
  let isEditingTitle = false;
  let resumeTitle = data.resumeData.document.label || "Untitled Resume";
  let hasUnsavedChanges = false;
  JSON.stringify(data.form.resume.data || {});
  JSON.stringify(data.form.design.data || {});
  onDestroy(() => {
  });
  async function saveResume() {
    saving = true;
    try {
      const formData = data.form.resume.data;
      let headerName = formData.header?.name || "";
      let headerEmail = formData.header?.email || "";
      let headerPhone = formData.header?.phone || "";
      if (typeof document !== "undefined") {
        try {
          const nameInput = document.getElementById("name-input");
          const emailInput = document.getElementById("email-input");
          const phoneInput = document.getElementById("phone-input");
          if (nameInput && nameInput.value) {
            headerName = nameInput.value;
          }
          if (emailInput && emailInput.value) {
            headerEmail = emailInput.value;
          }
          if (phoneInput && phoneInput.value) {
            headerPhone = phoneInput.value;
          }
        } catch (error) {
          console.error("Error getting values from DOM:", error);
        }
      }
      const parsedData = {
        header: {
          name: headerName,
          email: headerEmail,
          phone: headerPhone
        },
        summary: { content: formData.summary?.content || "" },
        experience: formData.experience || [],
        education: formData.education || [],
        skills: formData.skills || [],
        projects: formData.projects || [],
        certifications: formData.certifications || []
      };
      const response = await fetch(`/api/resume/${data.resumeData.id}/data`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ parsedData })
      });
      if (response.ok) {
        const result = await response.json();
        lastSaved = /* @__PURE__ */ new Date();
        if (result.data) {
          data.form.resume.data = result.data;
          if (data.form.resume.data && data.form.resume.data.header) {
            data.form.resume.data.header.name = parsedData.header.name;
            data.form.resume.data.header.email = parsedData.header.email;
            data.form.resume.data.header.phone = parsedData.header.phone;
          }
          console.log("Form data updated after save:", data.form.resume.data);
        }
        hasUnsavedChanges = false;
        toast.success("Resume saved", {
          description: "Your resume has been saved successfully."
        });
      } else {
        toast.error("Error saving resume", {
          description: "Could not save your resume. Please try again."
        });
      }
    } catch (error) {
      console.error("Error saving resume:", error);
      toast.error("Error saving resume", {
        description: "Could not save your resume. Please try again."
      });
    } finally {
      saving = false;
    }
  }
  function previewResume() {
    saveResume().then(() => {
      if (typeof window !== "undefined") {
        window.open(`/api/resume/${data.resumeData.id}/preview`, "_blank");
      }
    });
  }
  function downloadResume() {
    saveResume().then(() => {
      if (typeof window !== "undefined") {
        window.open(`/api/resume/${data.resumeData.id}/download`, "_blank");
      }
    });
  }
  async function saveResumeTitle() {
    try {
      const response = await fetch(`/api/documents/${data.resumeData.document.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ label: resumeTitle })
      });
      if (response.ok) {
        data.resumeData.document.label = resumeTitle;
        isEditingTitle = false;
        toast.success("Resume title updated");
      } else {
        toast.error("Failed to update resume title");
      }
    } catch (error) {
      console.error("Error updating resume title:", error);
      toast.error("Failed to update resume title");
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: `${stringify(resumeTitle)} | Auto Apply`,
      description: "Build your professional resume"
    });
    $$payload2.out += `<!----> <main><div class="flex items-center justify-between border border-l border-r border-t border-neutral-500 px-6 py-4"><div class="flex items-center gap-2">`;
    if (isEditingTitle) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex items-center gap-2">`;
      Input($$payload2, {
        type: "text",
        class: "h-10 text-xl font-bold",
        autofocus: true,
        get value() {
          return resumeTitle;
        },
        set value($$value) {
          resumeTitle = $$value;
          $$settled = false;
        }
      });
      $$payload2.out += `<!----> `;
      Button($$payload2, {
        variant: "ghost",
        size: "icon",
        onclick: saveResumeTitle,
        children: ($$payload3) => {
          Check($$payload3, { class: "h-5 w-5" });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="flex items-center gap-2"><h1 class="text-2xl font-bold">${escape_html(data.resumeData.document.label || "Resume Builder")}</h1> `;
      Button($$payload2, {
        variant: "ghost",
        size: "icon",
        onclick: () => isEditingTitle = true,
        children: ($$payload3) => {
          Square_pen($$payload3, { class: "h-4 w-4" });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    }
    $$payload2.out += `<!--]--></div> <div class="flex items-center gap-2">`;
    if (lastSaved) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<span class="text-sm text-gray-500">Last saved: ${escape_html(lastSaved.toLocaleTimeString())}</span>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    Button($$payload2, {
      variant: "default",
      onclick: saveResume,
      disabled: saving,
      children: ($$payload3) => {
        Save($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> ${escape_html(saving ? "Saving..." : "Save Resume")}`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Root($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dropdown_menu_trigger($$payload3, {
          children: ($$payload4) => {
            Button($$payload4, {
              variant: "ghost",
              class: "relative rounded-md border",
              children: ($$payload5) => {
                Ellipsis($$payload5, { class: "mr-2 h-4 w-4" });
                $$payload5.out += `<!----> Actions`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Dropdown_menu_content($$payload3, {
          sideOffset: 15,
          align: "end",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dropdown_menu_item($$payload4, {
              onclick: () => goto(),
              children: ($$payload5) => {
                $$payload5.out += `<!---->Documents`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Dropdown_menu_item($$payload4, {
              onclick: previewResume,
              children: ($$payload5) => {
                $$payload5.out += `<!---->Preview`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Dropdown_menu_separator($$payload4, {});
            $$payload4.out += `<!----> <!---->`;
            Dropdown_menu_item($$payload4, {
              onclick: downloadResume,
              children: ($$payload5) => {
                $$payload5.out += `<!---->Download`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <div class="grid h-[calc(100vh-8.4rem)] grid-cols-1 md:grid-cols-[2.5fr_3fr]"><div class="border border-b-0 border-l-0 border-t-0 border-zinc-900 text-white">`;
    ResumeTabs($$payload2, {
      $$slots: {
        content: ($$payload3) => {
          $$payload3.out += `<div slot="content">`;
          ResumeForm($$payload3, { data: data.form.resume });
          $$payload3.out += `<!----></div>`;
        },
        design: ($$payload3) => {
          $$payload3.out += `<div slot="design">`;
          EditDesignPanel($$payload3, { data: data.form.design });
          $$payload3.out += `<!----></div>`;
        }
      }
    });
    $$payload2.out += `<!----></div> <div class="flex items-center justify-center overflow-y-auto bg-neutral-950 p-4">`;
    Resume($$payload2, {
      formData: data.form.resume,
      designData: data.form.design
    });
    $$payload2.out += `<!----></div></div></main>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CrPBXgQS.js.map
