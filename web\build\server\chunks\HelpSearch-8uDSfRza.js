import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, J as attr_class, M as ensure_array_like, N as attr, O as escape_html, P as stringify } from './index3-CqUPEnZw.js';
import { g as goto } from './client-dNyMPa8V.js';
import { S as Search_input } from './search-input-CbGkN9s9.js';
import 'clsx';
import { R as Root, P as Popover_trigger, a as Popover_content } from './index14-C2WSwUih.js';
import { S as Scroll_area } from './scroll-area-Dn69zlyp.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';

function HelpSearch($$payload, $$props) {
  push();
  let { className = "", searchQuery = "" } = $$props;
  let searchResults = [];
  let isOpen = false;
  function handleSearch() {
    if (searchQuery.trim()) {
      goto(`/help/search?q=${encodeURIComponent(searchQuery.trim())}`);
      isOpen = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div${attr_class(`help-search-container ${stringify(className)}`)}><!---->`;
    Root($$payload2, {
      get open() {
        return isOpen;
      },
      set open($$value) {
        isOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Popover_trigger($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<div class="w-full">`;
            Search_input($$payload4, {
              placeholder: "Search help articles...",
              onSearch: handleSearch,
              className: "w-full",
              get value() {
                return searchQuery;
              },
              set value($$value) {
                searchQuery = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Popover_content($$payload3, {
          class: "w-[var(--radix-popover-trigger-width)] p-0",
          align: "start",
          sideOffset: 5,
          children: ($$payload4) => {
            if (searchResults.length > 0) {
              $$payload4.out += "<!--[1-->";
              $$payload4.out += `<!---->`;
              Scroll_area($$payload4, {
                class: "h-[300px]",
                children: ($$payload5) => {
                  const each_array = ensure_array_like(searchResults);
                  $$payload5.out += `<div class="p-2"><div class="space-y-1"><!--[-->`;
                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                    let result = each_array[$$index];
                    $$payload5.out += `<a${attr("href", `/help/${stringify(result.slug)}`)} class="hover:bg-accent flex items-center justify-between rounded-md p-2"><div><div class="font-medium">${escape_html(result.title)}</div> `;
                    if (result.excerpt) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="text-muted-foreground line-clamp-1 text-sm">${escape_html(result.excerpt)}</div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div> `;
                    Arrow_right($$payload5, { class: "text-muted-foreground h-4 w-4" });
                    $$payload5.out += `<!----></a>`;
                  }
                  $$payload5.out += `<!--]--></div> `;
                  {
                    $$payload5.out += "<!--[!-->";
                  }
                  $$payload5.out += `<!--]--></div>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            } else if (searchQuery.length >= 2) {
              $$payload4.out += "<!--[2-->";
              $$payload4.out += `<div class="text-muted-foreground p-4 text-center text-sm">No results found for "${escape_html(searchQuery)}"</div>`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `<div class="text-muted-foreground p-4 text-center text-sm">Type at least 2 characters to search</div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { searchQuery });
  pop();
}

export { HelpSearch as H };
//# sourceMappingURL=HelpSearch-8uDSfRza.js.map
