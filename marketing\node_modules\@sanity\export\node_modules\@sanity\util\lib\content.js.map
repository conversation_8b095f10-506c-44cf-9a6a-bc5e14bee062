{"version": 3, "file": "content.js", "sources": ["../src/content/hasOwn.ts", "../src/content/isDeepEmpty.ts", "../src/content/isShallowEmptyObject.ts", "../src/content/randomKey.ts", "../src/content/resolveJSType.ts", "../src/content/resolveTypeName.ts"], "sourcesContent": ["export default Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty)\n", "import hasOwn from './hasOwn'\n\nfunction isDeepEmptyObject(value: {[key: string]: any}): boolean {\n  for (const key in value) {\n    if (key === '_type' || key === '_key') {\n      continue\n    }\n    if (hasOwn(value, key) && !isDeepEmpty(value[key])) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction isDeepEmptyArray(value: unknown[]): boolean {\n  for (let i = 0; i < value.length; i++) {\n    if (!isDeepEmpty(value[i])) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * Looks at the value and determines if it is deeply empty while not considering _type and _key attributes on objects.\n * A value will be considered deeply empty if it is:\n *  - undefined or null\n *  - an object where all property values are deeply empty\n *  - an array where all items are deeply empty\n * @param value - the value to check for deep emptiness\n */\nexport function isDeepEmpty(value: unknown): boolean {\n  if (value === undefined || value === null) {\n    return true\n  }\n  const type = typeof value\n\n  if (Array.isArray(value)) {\n    return isDeepEmptyArray(value)\n  }\n  if (type === 'object') {\n    return isDeepEmptyObject(value)\n  }\n  return false\n}\n\n/**\n * @deprecated Use `isDeepEmpty` instead\n * todo: remove in v4\n */\nexport const isEmptyArray = isDeepEmptyArray\n\n/**\n * @deprecated Use `isDeepEmpty` instead\n * todo: remove in v4\n */\nexport const isEmpty = isDeepEmpty\n\n/**\n * @deprecated Use `isDeepEmpty` instead\n * todo: remove in v4\n */\nexport const isEmptyObject = isDeepEmptyObject\n", "import hasOwn from './hasOwn'\n\nexport function isShallowEmptyObject(value: {[key: string]: unknown}): boolean {\n  for (const key in value) {\n    if (key === '_type' || key === '_key') {\n      continue\n    }\n    if (hasOwn(value, key) && value[key] !== undefined) {\n      return false\n    }\n  }\n  return true\n}\n", "import getRandomValues from 'get-random-values-esm'\n\nconst getByteHexTable = (() => {\n  let table: any[]\n  return () => {\n    if (table) {\n      return table\n    }\n\n    table = []\n    for (let i = 0; i < 256; ++i) {\n      table[i] = (i + 0x100).toString(16).slice(1)\n    }\n    return table\n  }\n})()\n\n// WHATWG crypto RNG - https://w3c.github.io/webcrypto/Overview.html\nfunction whatwgRNG(length = 16) {\n  const rnds8 = new Uint8Array(length)\n  getRandomValues(rnds8)\n  return rnds8\n}\n\nexport function randomKey(length?: number): string {\n  const table = getByteHexTable()\n  return whatwgRNG(length)\n    .reduce((str, n) => str + table[n], '')\n    .slice(0, length)\n}\n", "const toString = Object.prototype.toString\n// Copied from https://github.com/ForbesLindesay/type-of, but inlined to have fine grained control\n\nexport function resolveJSType(val: unknown) {\n  switch (toString.call(val)) {\n    case '[object Function]':\n      return 'function'\n    case '[object Date]':\n      return 'date'\n    case '[object RegExp]':\n      return 'regexp'\n    case '[object Arguments]':\n      return 'arguments'\n    case '[object Array]':\n      return 'array'\n    case '[object String]':\n      return 'string'\n    default:\n  }\n\n  if (typeof val == 'object' && val && typeof (val as any).length == 'number') {\n    try {\n      if (typeof (val as any).callee == 'function') {\n        // eslint-disable-line max-depth\n        return 'arguments'\n      }\n    } catch (ex) {\n      if (ex instanceof TypeError) {\n        // eslint-disable-line max-depth\n        return 'arguments'\n      }\n    }\n  }\n\n  if (val === null) {\n    return 'null'\n  }\n\n  if (val === undefined) {\n    return 'undefined'\n  }\n\n  if (val && (val as any).nodeType === 1) {\n    return 'element'\n  }\n\n  if (val === Object(val)) {\n    return 'object'\n  }\n\n  return typeof val\n}\n", "import {resolveJSType} from './resolveJSType'\n\nexport function resolveTypeName(value: unknown): string {\n  const jsType = resolveJSType(value)\n  if (jsType !== 'object') {\n    return jsType\n  }\n\n  const obj = value as Record<string, unknown> & {_type?: string}\n  return ('_type' in obj && obj._type) || jsType\n}\n"], "names": ["getRandomValues"], "mappings": ";;;;;;uFAAA,SAAe,OAAO,UAAU,eAAe,KAAK,KAAK,OAAO,UAAU,cAAc;ACExF,SAAS,kBAAkB,OAAsC;AAC/D,aAAW,OAAO;AAChB,QAAI,EAAQ,QAAA,WAAW,QAAQ,WAG3B,OAAO,OAAO,GAAG,KAAK,CAAC,YAAY,MAAM,GAAG,CAAC;AACxC,aAAA;AAGJ,SAAA;AACT;AAEA,SAAS,iBAAiB,OAA2B;AACnD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,QAAI,CAAC,YAAY,MAAM,CAAC,CAAC;AAChB,aAAA;AAGJ,SAAA;AACT;AAUO,SAAS,YAAY,OAAyB;AACnD,MAA2B,SAAU;AAC5B,WAAA;AAET,QAAM,OAAO,OAAO;AAEhB,SAAA,MAAM,QAAQ,KAAK,IACd,iBAAiB,KAAK,IAE3B,SAAS,WACJ,kBAAkB,KAAK,IAEzB;AACT;AAMO,MAAM,eAAe,kBAMf,UAAU,aAMV,gBAAgB;AC5DtB,SAAS,qBAAqB,OAA0C;AAC7E,aAAW,OAAO;AACZ,QAAA,EAAA,QAAQ,WAAW,QAAQ,WAG3B,OAAO,OAAO,GAAG,KAAK,MAAM,GAAG,MAAM;AAChC,aAAA;AAGJ,SAAA;AACT;ACVA,MAAM,kBAAyB,uBAAA;AACzB,MAAA;AACJ,SAAO,MAAM;AACP,QAAA;AACK,aAAA;AAGT,YAAQ,CAAC;AACT,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACnB,YAAA,CAAC,KAAK,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC;AAEtC,WAAA;AAAA,EACT;AACF,GAAG;AAGH,SAAS,UAAU,SAAS,IAAI;AACxB,QAAA,QAAQ,IAAI,WAAW,MAAM;AACnC,SAAAA,yBAAA,QAAgB,KAAK,GACd;AACT;AAEO,SAAS,UAAU,QAAyB;AACjD,QAAM,QAAQ,gBAAgB;AAC9B,SAAO,UAAU,MAAM,EACpB,OAAO,CAAC,KAAK,MAAM,MAAM,MAAM,CAAC,GAAG,EAAE,EACrC,MAAM,GAAG,MAAM;AACpB;AC7BA,MAAM,WAAW,OAAO,UAAU;AAG3B,SAAS,cAAc,KAAc;AAClC,UAAA,SAAS,KAAK,GAAG,GAAG;AAAA,IAC1B,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,EACT;AAGF,MAAI,OAAO,OAAO,YAAY,OAAO,OAAQ,IAAY,UAAU;AAC7D,QAAA;AACE,UAAA,OAAQ,IAAY,UAAU;AAEzB,eAAA;AAAA,aAEF,IAAI;AACX,UAAI,cAAc;AAET,eAAA;AAAA,IAAA;AAKb,SAAI,QAAQ,OACH,SAGL,QAAQ,SACH,cAGL,OAAQ,IAAY,aAAa,IAC5B,YAGL,QAAQ,OAAO,GAAG,IACb,WAGF,OAAO;AAChB;ACjDO,SAAS,gBAAgB,OAAwB;AAChD,QAAA,SAAS,cAAc,KAAK;AAClC,MAAI,WAAW;AACN,WAAA;AAGT,QAAM,MAAM;AACJ,SAAA,WAAW,OAAO,IAAI,SAAU;AAC1C;;;;;;;;"}