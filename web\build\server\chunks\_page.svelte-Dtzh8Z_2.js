import { p as push, S as store_get, M as ensure_array_like, O as escape_html, T as unsubscribe_stores, q as pop } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { g as goto } from './client-dNyMPa8V.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { g as getStores } from './stores-DSLMNPqo.js';
import { U as User } from './user-DpDpidvb.js';
import { B as Bell } from './bell-C9_YgkSj.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { S as Sparkles } from './sparkles-E4-thk3U.js';
import { C as Credit_card } from './credit-card-8KNeZIt3.js';
import { A as Activity } from './activity-BF3HC42u.js';
import { B as Bell_ring } from './bell-ring-BPNzdg8B.js';
import { S as Share_2 } from './share-2-ihgFYKw2.js';
import { U as Users } from './users-e7-Uhkka.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let userData, hasTeamAccess, settingCards;
  const { page } = getStores();
  const baseSettingCards = [
    {
      title: "Profile",
      description: "Manage your personal information",
      content: "Update your name, email, profile picture and other personal details.",
      icon: User,
      href: "/dashboard/settings/profile"
    },
    {
      title: "Account",
      description: "Manage your account preferences",
      content: "Update your notification preferences, language settings, and accessibility options.",
      icon: Bell,
      href: "/dashboard/settings/account"
    },
    {
      title: "Security",
      description: "Manage your security settings",
      content: "Update your password, enable two-factor authentication, and manage your sessions.",
      icon: Shield,
      href: "/dashboard/settings/security"
    },
    {
      title: "AI Coach",
      description: "Practice for your interviews",
      content: "Use AI to practice for your interviews with personalized feedback and suggestions.",
      icon: Sparkles,
      href: "/dashboard/settings/interview-coach"
    },
    {
      title: "Billing",
      description: "Manage your subscription and payments",
      content: "View your current subscription, payment methods, and billing history.",
      icon: Credit_card,
      href: "/dashboard/settings/billing"
    },
    {
      title: "Usage",
      description: "Monitor your feature usage",
      content: "Track your feature usage and subscription limits across the platform.",
      icon: Activity,
      href: "/dashboard/settings/usage"
    },
    {
      title: "Notifications",
      description: "Manage your notification preferences",
      content: "Control how and when you receive notifications across email, browser, and more.",
      icon: Bell_ring,
      href: "/dashboard/settings/notifications"
    },
    {
      title: "Referrals",
      description: "Share and earn rewards",
      content: "Invite friends to join Hirli and earn rewards for successful referrals.",
      icon: Share_2,
      href: "/dashboard/settings/referrals"
    }
  ];
  const teamCard = {
    title: "Team",
    description: "Manage your team members",
    content: "Invite team members, manage permissions, and organize your team.",
    icon: Users,
    href: "/dashboard/settings/team"
  };
  userData = store_get($$store_subs ??= {}, "$page", page).data.user;
  hasTeamAccess = userData?.teamId || userData?.hasTeamFeature || false;
  settingCards = hasTeamAccess ? [...baseSettingCards, teamCard] : baseSettingCards;
  const each_array = ensure_array_like(settingCards);
  SEO($$payload, {
    title: "Account Settings | Hirli",
    description: "Manage your Hirli account settings, including profile information, security preferences, and notification settings.",
    keywords: "account settings, profile settings, security settings, notification preferences, account management"
  });
  $$payload.out += `<!----> <div class="flex h-full flex-col"><div class="border-border flex flex-col justify-between border-b p-6"><h2 class="text-lg font-semibold">General Settings</h2> <p class="text-muted-foreground">Manage your account settings and preferences.</p></div> <div class="divide-y"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let { title, description, content, icon, href } = each_array[$$index];
    $$payload.out += `<div class="hover:bg-muted/50 flex items-center justify-between p-6 transition-colors"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full"><!---->`;
    icon?.($$payload, { class: "text-primary h-5 w-5" });
    $$payload.out += `<!----></div> <div class="space-y-1"><h3 class="font-medium">${escape_html(title)}</h3> <p class="text-muted-foreground text-sm">${escape_html(description)}</p></div></div> <div class="flex items-center gap-2">`;
    Button($$payload, {
      variant: "outline",
      onclick: () => goto(),
      children: ($$payload2) => {
        $$payload2.out += `<!---->Manage`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    Chevron_right($$payload, { class: "text-muted-foreground h-4 w-4" });
    $$payload.out += `<!----></div></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Dtzh8Z_2.js.map
