import { p as push, Q as bind_props, q as pop, M as ensure_array_like, O as escape_html, S as store_get, T as unsubscribe_stores } from './index3-CqUPEnZw.js';
import { g as goto } from './client-dNyMPa8V.js';
import 'clsx';
import './zod-DfpldWlD.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root$1, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { B as Button } from './button-CrucCo1G.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { R as Root$2, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { M as Mail } from './mail-Brqxil2x.js';
import { B as Bell } from './bell-C9_YgkSj.js';
import { M as Megaphone } from './megaphone-CN3i-hyO.js';
import { M as Monitor } from './monitor-_9b3qg6F.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './index2-Cut0V_vU.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './index-server-CezSOnuG.js';
import './stringify-DWCARkQV.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './scroll-lock-BkBz2nVp.js';
import './dialog-description2-rfr-pd9k.js';
import './index-DjwFQdT_.js';
import './hidden-input-1eDzjGOB.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './Icon-A4vzmk-O.js';

function EmailNotifications($$payload, $$props) {
  push();
  var $$store_subs;
  let formData = $$props["formData"];
  const { form } = formData;
  function triggerFormChange() {
    const formElement = document.getElementById("notification-form");
    if (formElement) {
      formElement.dispatchEvent(new Event("change", { bubbles: true }));
    }
  }
  $$payload.out += `<div class="border-border border-b px-6 py-4"><div class="flex items-center justify-between"><div><h4 class="text-md font-normal">Email Notifications</h4> <p class="text-muted-foreground text-sm">Configure how you receive email notifications.</p></div> `;
  Button($$payload, {
    variant: "outline",
    onclick: () => goto(),
    children: ($$payload2) => {
      $$payload2.out += `<!---->Account Settings`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="grid grid-cols-2 items-center justify-between gap-4 p-4"><div class="space-y-0.5"><div class="font-medium">Email Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications via email</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).emailNotifications),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, emailNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----> `;
  if (Boolean(store_get($$store_subs ??= {}, "$form", form).emailNotifications)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="space-y-2"><div class="font-medium">Email Digest Frequency</div> `;
    Root$2($$payload, {
      type: "single",
      value: store_get($$store_subs ??= {}, "$form", form).emailDigest || "daily",
      onValueChange: (value) => {
        form.update((f) => ({ ...f, emailDigest: value }));
        triggerFormChange();
      },
      children: ($$payload2) => {
        Select_trigger($$payload2, {
          class: "w-full",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$form", form).emailDigest === "daily" ? "Daily" : store_get($$store_subs ??= {}, "$form", form).emailDigest === "weekly" ? "Weekly" : store_get($$store_subs ??= {}, "$form", form).emailDigest === "never" ? "Never" : "Select frequency")}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Select_content($$payload2, {
          class: "max-h-60",
          children: ($$payload3) => {
            Select_item($$payload3, {
              value: "daily",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Daily`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "weekly",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Weekly`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "never",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Never`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> <div class="text-muted-foreground text-sm">How often you want to receive email digests summarizing your notifications</div></div> <div class="space-y-2"><div class="font-medium">Email Format</div> `;
    Root$2($$payload, {
      type: "single",
      value: store_get($$store_subs ??= {}, "$form", form).emailFormat || "html",
      onValueChange: (value) => {
        form.update((f) => ({ ...f, emailFormat: value }));
        triggerFormChange();
      },
      children: ($$payload2) => {
        Select_trigger($$payload2, {
          class: "w-full",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$form", form).emailFormat === "html" ? "HTML (Rich formatting)" : store_get($$store_subs ??= {}, "$form", form).emailFormat === "text" ? "Plain Text" : "Select format")}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Select_content($$payload2, {
          class: "max-h-60",
          children: ($$payload3) => {
            Select_item($$payload3, {
              value: "html",
              children: ($$payload4) => {
                $$payload4.out += `<!---->HTML (Rich formatting)`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "text",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Plain Text`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> <div class="text-muted-foreground text-sm">Choose how you want your emails to be formatted</div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { formData });
  pop();
}
function JobNotifications($$payload, $$props) {
  push();
  var $$store_subs;
  let formData = $$props["formData"];
  const { form } = formData;
  function triggerFormChange() {
    const formElement = document.getElementById("notification-form");
    if (formElement) {
      formElement.dispatchEvent(new Event("change", { bubbles: true }));
    }
  }
  $$payload.out += `<div class="border-border flex flex-col border-b px-6 py-4"><h4 class="text-md font-normal">Job Alert Notifications</h4> <p class="text-muted-foreground text-sm">Configure notifications for job-related activities.</p></div> <div class="grid grid-cols-2 gap-4 p-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Job Match Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications when new job matches are found</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).jobMatchNotifications),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, jobMatchNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> `;
  if (Boolean(store_get($$store_subs ??= {}, "$form", form).jobMatchNotifications)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="border-muted mb-6 mt-4 border-l-2 pl-6"><div class="space-y-2"><div class="font-medium">Notification Frequency</div> `;
    Root$2($$payload, {
      type: "single",
      value: store_get($$store_subs ??= {}, "$form", form).jobMatchFrequency || "daily",
      onValueChange: (value) => {
        form.update((f) => ({ ...f, jobMatchFrequency: value }));
        triggerFormChange();
      },
      children: ($$payload2) => {
        Select_trigger($$payload2, {
          class: "w-full",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$form", form).jobMatchFrequency === "realtime" ? "Real-time" : store_get($$store_subs ??= {}, "$form", form).jobMatchFrequency === "daily" ? "Daily Digest" : store_get($$store_subs ??= {}, "$form", form).jobMatchFrequency === "weekly" ? "Weekly Digest" : "Select frequency")}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Select_content($$payload2, {
          class: "max-h-60",
          children: ($$payload3) => {
            Select_item($$payload3, {
              value: "realtime",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Real-time`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "daily",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Daily Digest`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "weekly",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Weekly Digest`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> <div class="text-muted-foreground text-sm">How often you want to receive job match notifications</div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Application Status Updates</div> <div class="text-muted-foreground text-sm">Receive notifications when your application status changes</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).applicationStatusNotifications),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, applicationStatusNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">New Jobs Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications when new jobs matching your criteria are posted</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).newJobsNotifications),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, newJobsNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> `;
  if (Boolean(store_get($$store_subs ??= {}, "$form", form).newJobsNotifications)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="border-muted mb-6 mt-4 border-l-2 pl-6"><div class="space-y-2"><div class="font-medium">Notification Frequency</div> `;
    Root$2($$payload, {
      type: "single",
      value: store_get($$store_subs ??= {}, "$form", form).newJobsFrequency || "daily",
      onValueChange: (value) => {
        form.update((f) => ({ ...f, newJobsFrequency: value }));
        triggerFormChange();
      },
      children: ($$payload2) => {
        Select_trigger($$payload2, {
          class: "w-full",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$form", form).newJobsFrequency === "realtime" ? "Real-time" : store_get($$store_subs ??= {}, "$form", form).newJobsFrequency === "daily" ? "Daily Digest" : store_get($$store_subs ??= {}, "$form", form).newJobsFrequency === "weekly" ? "Weekly Digest" : "Select frequency")}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Select_content($$payload2, {
          class: "max-h-60",
          children: ($$payload3) => {
            Select_item($$payload3, {
              value: "realtime",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Real-time`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "daily",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Daily Digest`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "weekly",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Weekly Digest`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> <div class="text-muted-foreground text-sm">How often you want to receive new job notifications</div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Interview Reminders</div> <div class="text-muted-foreground text-sm">Receive reminders about upcoming interviews and follow-ups</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).interviewReminders),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, interviewReminders: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Saved Jobs Updates</div> <div class="text-muted-foreground text-sm">Receive updates about jobs you've saved (closing dates, changes, etc.)</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).savedJobsUpdates),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, savedJobsUpdates: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Automation Notifications</div> <div class="text-muted-foreground text-sm">Receive notifications about automation runs, status updates, and results</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).automationNotifications),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, automationNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> `;
  if (Boolean(store_get($$store_subs ??= {}, "$form", form).automationNotifications)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="border-muted mb-6 mt-4 border-l-2 pl-6"><div class="space-y-2"><div class="font-medium">Notification Frequency</div> `;
    Root$2($$payload, {
      type: "single",
      value: store_get($$store_subs ??= {}, "$form", form).automationFrequency || "realtime",
      onValueChange: (value) => {
        form.update((f) => ({ ...f, automationFrequency: value }));
        triggerFormChange();
      },
      children: ($$payload2) => {
        Select_trigger($$payload2, {
          class: "w-full",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$form", form).automationFrequency === "realtime" ? "Real-time" : store_get($$store_subs ??= {}, "$form", form).automationFrequency === "daily" ? "Daily Digest" : store_get($$store_subs ??= {}, "$form", form).automationFrequency === "weekly" ? "Weekly Digest" : "Select frequency")}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Select_content($$payload2, {
          class: "max-h-60",
          children: ($$payload3) => {
            Select_item($$payload3, {
              value: "realtime",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Real-time`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "daily",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Daily Digest`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Select_item($$payload3, {
              value: "weekly",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Weekly Digest`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> <div class="text-muted-foreground text-sm">How often you want to receive automation notifications</div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="mt-6 border-t pt-6"><div class="mb-4 font-medium">Notification Channels</div> <div class="space-y-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Email Notifications</div> <div class="text-muted-foreground text-sm">Receive job alerts via email</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).jobEmailNotifications ?? true),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, jobEmailNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Browser Notifications</div> <div class="text-muted-foreground text-sm">Receive job alerts in your browser</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).jobBrowserNotifications ?? true),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, jobBrowserNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div></div></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { formData });
  pop();
}
function MarketingNotifications($$payload, $$props) {
  push();
  var $$store_subs;
  let formData = $$props["formData"];
  const { form } = formData;
  function triggerFormChange() {
    const formElement = document.getElementById("notification-form");
    if (formElement) {
      formElement.dispatchEvent(new Event("change", { bubbles: true }));
    }
  }
  $$payload.out += `<div class="border-border flex flex-col border-b px-6 py-4"><h4 class="text-md font-normal">Marketing Communications</h4> <p class="text-muted-foreground text-sm">Configure marketing and promotional communications.</p></div> <div class="grid grid-cols-2 gap-4 p-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Marketing Emails</div> <div class="text-muted-foreground text-sm">Receive marketing and promotional emails</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).marketingEmails),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, marketingEmails: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Product Updates</div> <div class="text-muted-foreground text-sm">Receive notifications about new features and product updates</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).productUpdates),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, productUpdates: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Newsletter Subscription</div> <div class="text-muted-foreground text-sm">Receive our monthly newsletter with job search tips and industry insights</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).newsletterSubscription),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, newsletterSubscription: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Event Invitations</div> <div class="text-muted-foreground text-sm">Receive invitations to webinars, career fairs, and other events</div></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).eventInvitations),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, eventInvitations: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { formData });
  pop();
}
function PlatformNotifications($$payload, $$props) {
  push();
  var $$store_subs;
  let formData = $$props["formData"];
  const { form } = formData;
  let isLoading = false;
  let pushStatus = {
    supported: false,
    permission: "default",
    hasSubscription: false,
    serviceWorkerRegistered: false
  };
  function triggerFormChange() {
    const formElement = document.getElementById("notification-form");
    if (formElement) {
      formElement.dispatchEvent(new Event("change", { bubbles: true }));
    }
  }
  async function handlePushNotificationToggle(checked) {
    console.log("🔄 Push notification toggle clicked:", checked);
    if (isLoading) {
      console.log("⏳ Already loading, ignoring click");
      return;
    }
    isLoading = true;
    try {
      if (checked) {
        console.log("🔔 Enabling push notifications...");
        const result = await requestPushNotificationPermission();
        console.log("🔔 Permission request result:", result);
        if (result.success) {
          console.log("✅ Permission granted, updating form...");
          form.update((f) => ({ ...f, pushNotifications: true }));
          triggerFormChange();
          toast.success("Push notifications enabled successfully!");
          pushStatus = await getPushNotificationStatus();
        } else {
          console.log("❌ Permission failed, keeping switch off");
          form.update((f) => ({ ...f, pushNotifications: false }));
          toast.error(result.error || "Failed to enable push notifications");
        }
      } else {
        console.log("🔕 Disabling push notifications...");
        const result = await unregisterPushNotifications();
        if (result.success) {
          form.update((f) => ({ ...f, pushNotifications: false }));
          triggerFormChange();
          toast.success("Push notifications disabled successfully");
          pushStatus = await getPushNotificationStatus();
        } else {
          form.update((f) => ({ ...f, pushNotifications: true }));
          toast.error(result.error || "Failed to disable push notifications");
        }
      }
    } catch (error) {
      console.error("❌ Error handling push notification toggle:", error);
      toast.error("An unexpected error occurred");
      form.update((f) => ({ ...f, pushNotifications: !checked }));
    } finally {
      isLoading = false;
    }
  }
  async function testPermissionRequest() {
    try {
      const permission = await testRequestPermission();
      toast.success(`Permission result: ${permission}`);
      pushStatus = await getPushNotificationStatus();
    } catch (error) {
      console.error("Error testing permission request:", error);
      toast.error("Error testing permission request");
    }
  }
  async function forcePermissionRequest() {
    try {
      const permission = await forceRequestPermission();
      toast.success(`Permission result: ${permission}`);
      pushStatus = await getPushNotificationStatus();
    } catch (error) {
      console.error("Error forcing permission request:", error);
      toast.error("Error requesting permission");
    }
  }
  async function resetPushSettings() {
    try {
      const result = await resetPushNotifications();
      if (result.success) {
        toast.success("Push notifications reset successfully! You can now enable them again.");
        form.update((f) => ({ ...f, pushNotifications: false }));
        pushStatus = await getPushNotificationStatus();
        triggerFormChange();
      } else {
        toast.error(result.error || "Failed to reset push notifications");
      }
    } catch (error) {
      console.error("Error resetting push notifications:", error);
      toast.error("Error resetting push notifications");
    }
  }
  async function testPushNotification() {
    try {
      const response = await fetch("/api/push/test", {
        method: "POST",
        headers: { "Content-Type": "application/json" }
      });
      const result = await response.json();
      if (result.success) {
        toast.success("Test push notification sent! Check your browser notifications.");
      } else {
        toast.error(result.message || "Failed to send test notification");
      }
    } catch (error) {
      console.error("Error testing push notification:", error);
      toast.error("Error testing push notification. Please try again.");
    }
  }
  $$payload.out += `<div class="border-border flex flex-col border-b px-6 py-4"><h4 class="text-md font-normal">Platform Notifications</h4> <p class="text-muted-foreground text-sm">Configure how you receive notifications on the platform.</p></div> <div class="grid grid-cols-2 gap-4 p-4"><div class="space-y-2"><div class="flex items-center justify-between"><div class="font-medium">Browser Notifications</div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).browserNotifications),
    onCheckedChange: (checked) => {
      form.update((f) => ({ ...f, browserNotifications: checked }));
      triggerFormChange();
    }
  });
  $$payload.out += `<!----></div> <div class="text-muted-foreground text-sm">Receive notifications in your browser when you're on the site</div></div> <div class="space-y-2"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Push Notifications</div> <div class="text-muted-foreground text-sm">Receive push notifications for important updates</div> <div class="space-y-1 text-xs"><div class="flex items-center gap-2"><span class="font-medium">Status:</span> `;
  if (!pushStatus.supported) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="text-destructive">❌ Not supported in this browser</span>`;
  } else if (pushStatus.permission === "granted") {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<span class="text-green-600">✅ Permission granted</span>`;
  } else if (pushStatus.permission === "denied") {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<span class="text-destructive">❌ Permission blocked</span>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="text-yellow-600">⚠️ Permission not requested</span>`;
  }
  $$payload.out += `<!--]--></div> `;
  if (pushStatus.supported) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-2"><span class="font-medium">Subscription:</span> `;
    if (pushStatus.hasSubscription) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="text-green-600">✅ Active</span>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<span class="text-muted-foreground">❌ None</span>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  if (pushStatus.permission === "denied") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-destructive bg-destructive/10 rounded p-2 text-xs"><strong>Notifications are blocked.</strong> To enable: <br/>1. Click the lock icon (🔒) in your address bar <br/>2. Change "Notifications" to "Allow" <br/>3. Refresh the page and try again</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  Switch($$payload, {
    checked: Boolean(store_get($$store_subs ??= {}, "$form", form).pushNotifications) && pushStatus.permission === "granted" && pushStatus.hasSubscription,
    disabled: isLoading || !pushStatus.supported || pushStatus.permission === "denied",
    onCheckedChange: handlePushNotificationToggle
  });
  $$payload.out += `<!----></div> `;
  if (Boolean(store_get($$store_subs ??= {}, "$form", form).pushNotifications) && pushStatus.hasSubscription) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex justify-start gap-2">`;
    Button($$payload, {
      type: "button",
      variant: "outline",
      size: "sm",
      onclick: testPushNotification,
      disabled: isLoading,
      children: ($$payload2) => {
        $$payload2.out += `<!---->Test Push Notification`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="flex flex-wrap gap-2">`;
  Button($$payload, {
    type: "button",
    variant: "secondary",
    size: "sm",
    onclick: testPermissionRequest,
    disabled: isLoading,
    children: ($$payload2) => {
      $$payload2.out += `<!---->🔧 Test Permission`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    type: "button",
    variant: "secondary",
    size: "sm",
    onclick: forcePermissionRequest,
    disabled: isLoading,
    children: ($$payload2) => {
      $$payload2.out += `<!---->🔔 Force Permission Dialog`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  if (pushStatus.hasSubscription || pushStatus.permission === "granted") {
    $$payload.out += "<!--[-->";
    Button($$payload, {
      type: "button",
      variant: "destructive",
      size: "sm",
      onclick: resetPushSettings,
      disabled: isLoading,
      children: ($$payload2) => {
        $$payload2.out += `<!---->🔄 Reset &amp; Clear All`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  if (isLoading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-muted-foreground text-xs">Processing push notification settings...</div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { formData });
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const tabs = [
    { id: "email", label: "Email", icon: Mail },
    { id: "jobs", label: "Job Alerts", icon: Bell },
    {
      id: "marketing",
      label: "Marketing",
      icon: Megaphone
    },
    {
      id: "platform",
      label: "Platform",
      icon: Monitor
    }
  ];
  let activeTab = "email";
  let statusTimeout = null;
  let showResetConfirmation = false;
  const form = superForm(data.form, {
    dataType: "json",
    validationMethod: "auto",
    taintedMessage: false,
    // Disable the browser's "unsaved changes" warning
    onUpdated: ({ form: form2 }) => {
      if (form2.valid) {
        updateStatus("saved");
        toast.success("Notification settings auto-saved successfully");
      }
    },
    onError: () => {
      updateStatus("error");
      toast.error("Failed to update notification settings");
    }
  });
  const {
    form: formData,
    enhance,
    submitting,
    delayed
  } = form;
  function updateStatus(status, duration = 3e3) {
    clearTimeout(statusTimeout);
    if (status === "error") {
      statusTimeout = setTimeout(
        () => {
        },
        duration
      );
    }
  }
  function resetToDefaults() {
    const defaultSettings = {
      emailNotifications: true,
      emailDigest: "daily",
      emailFormat: "html",
      jobMatchNotifications: true,
      jobMatchFrequency: "daily",
      applicationStatusNotifications: true,
      newJobsNotifications: true,
      newJobsFrequency: "daily",
      interviewReminders: true,
      savedJobsUpdates: true,
      jobEmailNotifications: true,
      jobBrowserNotifications: true,
      jobMobileNotifications: false,
      marketingEmails: true,
      productUpdates: true,
      newsletterSubscription: false,
      eventInvitations: false,
      browserNotifications: true,
      desktopNotifications: false,
      mobileNotifications: false,
      pushNotifications: true
    };
    formData.update(() => defaultSettings);
    toast.success("Notification settings reset to defaults");
    showResetConfirmation = false;
    const formElement = document.getElementById("notification-form");
    if (formElement) {
      formElement.dispatchEvent(new Event("change", { bubbles: true }));
    }
  }
  SEO($$payload, {
    title: "Notification Settings - Hirli",
    description: "Manage your notification preferences for emails, job alerts, marketing communications, and platform notifications.",
    keywords: "notification settings, email preferences, job alerts, marketing preferences, push notifications",
    url: "https://hirli.com/dashboard/settings/notifications"
  });
  $$payload.out += `<!----> <div class="flex flex-col justify-between p-6"><div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between"><div><div class="flex items-center gap-2"><h2 class="text-lg font-semibold">Notification Settings</h2></div> <p class="text-muted-foreground text-sm">Manage your notification preferences for emails, job alerts, marketing communications, and
        platform notifications.</p></div></div></div> <div class="grid grid-cols-1 gap-6"><div><form id="notification-form" method="POST" class="space-y-8">`;
  Root($$payload, {
    value: activeTab,
    onValueChange: (value) => activeTab = value,
    children: ($$payload2) => {
      const each_array_1 = ensure_array_like(tabs);
      Tabs_list($$payload2, {
        class: "w-full",
        children: ($$payload3) => {
          const each_array = ensure_array_like(tabs);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let tab = each_array[$$index];
            Tabs_trigger($$payload3, {
              value: tab.id,
              class: "flex-1",
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex items-center gap-2"><!---->`;
                tab.icon?.($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> <span>${escape_html(tab.label)}</span></div>`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!--[-->`;
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        let tab = each_array_1[$$index_1];
        Tabs_content($$payload2, {
          value: tab.id,
          children: ($$payload3) => {
            if (tab.id === "email") {
              $$payload3.out += "<!--[-->";
              EmailNotifications($$payload3, { formData: form });
            } else if (tab.id === "jobs") {
              $$payload3.out += "<!--[1-->";
              JobNotifications($$payload3, { formData: form });
            } else if (tab.id === "marketing") {
              $$payload3.out += "<!--[2-->";
              MarketingNotifications($$payload3, { formData: form });
            } else if (tab.id === "platform") {
              $$payload3.out += "<!--[3-->";
              PlatformNotifications($$payload3, { formData: form });
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <button id="submit-button" type="submit" class="hidden" aria-label="Save settings"></button></form></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  Root$1($$payload, {
    open: showResetConfirmation,
    onOpenChange: (open) => showResetConfirmation = open,
    children: ($$payload2) => {
      Alert_dialog_content($$payload2, {
        children: ($$payload3) => {
          Alert_dialog_header($$payload3, {
            children: ($$payload4) => {
              Alert_dialog_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Reset Notification Settings`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Alert_dialog_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->This will reset all notification settings to their default values. This action cannot be
        undone.`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Alert_dialog_footer($$payload3, {
            children: ($$payload4) => {
              Alert_dialog_cancel($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Cancel`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Alert_dialog_action($$payload4, {
                onclick: resetToDefaults,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Reset`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DJFapMQQ.js.map
