import { p as push, O as escape_html, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { g as goto } from './client-dNyMPa8V.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let activeTab = "details";
  $$payload.out += `<h1 class="mb-4 text-2xl font-semibold">${escape_html(data.resume.name)}</h1> <div class="text-muted-foreground mb-6 text-sm">Dashboard > Resume > ${escape_html(data.resume.name)}</div> `;
  Root($$payload, {
    value: activeTab,
    onValueChange: (value) => activeTab = value,
    class: "w-full",
    children: ($$payload2) => {
      Tabs_list($$payload2, {
        children: ($$payload3) => {
          Tabs_trigger($$payload3, {
            value: "details",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Resume Details`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Tabs_trigger($$payload3, {
            value: "parsed",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Parsed Data`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Tabs_trigger($$payload3, {
            value: "view",
            children: ($$payload4) => {
              $$payload4.out += `<!---->View Document`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Tabs_content($$payload2, {
        value: "details",
        class: "mt-4",
        children: ($$payload3) => {
          Card($$payload3, {
            class: "mb-6",
            children: ($$payload4) => {
              Card_header($$payload4, {
                children: ($$payload5) => {
                  Card_title($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Resume Details`;
                    },
                    $$slots: { default: true }
                  });
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Card_content($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<p class="text-muted-foreground mb-1 text-sm">Uploaded: ${escape_html(new Date(data.resume.createdAt).toLocaleDateString())}</p> <p class="mb-1 text-sm">Profile: ${escape_html(data.resume.profile.name)}</p> <p class="mb-4 text-sm">Filename: ${escape_html(data.resume.fileName)}</p> <div class="flex gap-2">`;
                  Button($$payload5, {
                    onclick: () => goto(`/dashboard/resume/${data.resume.id}/optimize`),
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Optimize Resume`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Button($$payload5, {
                    variant: "outline",
                    onclick: () => activeTab = "parsed",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->View Parsed Data`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Button($$payload5, {
                    variant: "outline",
                    onclick: () => activeTab = "view",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->View Document`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></div>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Tabs_content($$payload2, {
        value: "view",
        class: "mt-4",
        children: ($$payload3) => {
          Card($$payload3, {
            children: ($$payload4) => {
              Card_header($$payload4, {
                children: ($$payload5) => {
                  Card_title($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Document Viewer`;
                    },
                    $$slots: { default: true }
                  });
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Card_content($$payload4, {
                children: ($$payload5) => {
                  {
                    $$payload5.out += "<!--[!-->";
                    $$payload5.out += `<p class="text-muted-foreground">Document not available for viewing</p>`;
                  }
                  $$payload5.out += `<!--]-->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DG6yyfpU.js.map
