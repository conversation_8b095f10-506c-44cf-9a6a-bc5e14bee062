import { p as push, P as stringify, q as pop, X as sanitize_props, R as spread_props, a0 as slot, O as escape_html, N as attr, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Badge } from './badge-C9pSznab.js';
import { formatDistanceToNow } from 'date-fns';
import { I as Icon } from './Icon-A4vzmk-O.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { C as Credit_card } from './credit-card-8KNeZIt3.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';

function Book_open($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "M12 7v14" }],
    [
      "path",
      {
        "d": "M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "book-open" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function HelpArticleCard($$payload, $$props) {
  push();
  let { article, className = "" } = $$props;
  let updatedDate = formatDistanceToNow(new Date(article.updatedAt), { addSuffix: true });
  $$payload.out += `<!---->`;
  Card($$payload, {
    class: `h-full overflow-hidden ${stringify(className)}`,
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-center gap-2">`;
          Badge($$payload3, {
            variant: "outline",
            class: "flex items-center gap-1",
            children: ($$payload4) => {
              if (article.category.icon === "BookOpen") {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<!---->`;
                Book_open($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!---->`;
              } else if (article.category.icon === "FileText") {
                $$payload4.out += "<!--[1-->";
                $$payload4.out += `<!---->`;
                File_text($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!---->`;
              } else if (article.category.icon === "CreditCard") {
                $$payload4.out += "<!--[2-->";
                $$payload4.out += `<!---->`;
                Credit_card($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!---->`;
              } else if (article.category.icon === "Shield") {
                $$payload4.out += "<!--[3-->";
                $$payload4.out += `<!---->`;
                Shield($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!---->`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `<!---->`;
                File_text($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!---->`;
              }
              $$payload4.out += `<!--]--> <span>${escape_html(article.category.name)}</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <span class="text-muted-foreground text-xs">Updated ${escape_html(updatedDate)}</span></div> <!---->`;
          Card_title($$payload3, {
            class: "mt-2",
            children: ($$payload4) => {
              $$payload4.out += `<a${attr("href", `/help/${stringify(article.slug)}`)} class="hover:text-primary hover:underline">${escape_html(article.title)}</a>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          if (article.excerpt) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<!---->`;
            Card_description($$payload3, {
              class: "line-clamp-2",
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(article.excerpt)}`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_footer($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like((article.tags || []).slice(0, 3));
          $$payload3.out += `<div class="flex flex-wrap gap-2"><!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let tag = each_array[$$index];
            Badge($$payload3, {
              variant: "secondary",
              class: "text-xs",
              children: ($$payload4) => {
                $$payload4.out += `<!---->${escape_html(tag.name)}`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]--> `;
          if ((article.tags || []).length > 3) {
            $$payload3.out += "<!--[-->";
            Badge($$payload3, {
              variant: "secondary",
              class: "text-xs",
              children: ($$payload4) => {
                $$payload4.out += `<!---->+${escape_html((article.tags || []).length - 3)} more`;
              },
              $$slots: { default: true }
            });
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}

export { Book_open as B, HelpArticleCard as H };
//# sourceMappingURL=HelpArticleCard-CzuyIqVY.js.map
