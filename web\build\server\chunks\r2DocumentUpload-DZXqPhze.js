import { CopyObjectCommand, S3Client, DeleteObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';

const logger = {
  info: (message, ...args) => console.log(`[INFO] ${message}`, ...args),
  error: (message, ...args) => console.error(`[ERROR] ${message}`, ...args),
  warn: (message, ...args) => console.warn(`[WARN] ${message}`, ...args)
};
const R2_CONFIG = {
  endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto",
  // R2 uses 'auto' as region
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID ?? "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY ?? ""
  }
};
const r2Client = new S3Client(R2_CONFIG);
const BUCKET_NAMES = {
  [
    "company"
    /* COMPANY */
  ]: "hirli-company-logos",
  [
    "resumes"
    /* RESUMES */
  ]: "hirli-resume-files",
  [
    "user"
    /* USER */
  ]: "hirli-user-images",
  [
    "jobs"
    /* JOBS */
  ]: "hirli-job-assets",
  [
    "analytics"
    /* ANALYTICS */
  ]: "hirli-analytics-data"
};
function getBucketName(bucketType) {
  return BUCKET_NAMES[bucketType];
}
function generateFileKey(fileType, originalName, identifier) {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split(".").pop() || "";
  const baseName = originalName.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, "-").toLowerCase();
  if (fileType === "resumes" && identifier) {
    return `profile-${identifier}-resume-${timestamp}.${extension}`;
  }
  return `${baseName}-${timestamp}-${randomSuffix}.${extension}`;
}
function getPublicUrl(fileKey) {
  const customDomain = process.env.R2_CUSTOM_DOMAIN;
  if (customDomain) {
    return `https://${customDomain}/${fileKey}`;
  }
  const accountId = process.env.R2_ACCOUNT_ID;
  return `https://pub-${accountId}.r2.dev/${fileKey}`;
}
async function uploadFile(buffer, originalName, contentType, fileType, identifier) {
  try {
    let bucketType;
    switch (fileType) {
      case "resumes":
        bucketType = "resumes";
        break;
      case "companyLogos":
        bucketType = "company";
        break;
      case "userDocuments":
      default:
        bucketType = "user";
        break;
    }
    const bucketName = getBucketName(bucketType);
    const fileKey = generateFileKey(fileType, originalName, identifier);
    logger.info(`📤 Uploading to bucket: ${bucketName} with key: ${fileKey}`);
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: contentType,
      Metadata: {
        originalName,
        uploadedAt: (/* @__PURE__ */ new Date()).toISOString(),
        fileType,
        bucketType,
        ...identifier && { identifier }
      }
    });
    await r2Client.send(uploadCommand);
    const publicUrl = getPublicUrl(fileKey);
    logger.info(
      `✅ File uploaded successfully to ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );
    return {
      success: true,
      fileKey,
      publicUrl,
      fileSize: buffer.length,
      contentType,
      bucketName
    };
  } catch (error) {
    logger.error(`❌ Failed to upload file ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown upload error"
    };
  }
}
const DOCUMENT_TYPE_TO_R2_TYPE = {
  resume: "resumes",
  "cover-letter": "userDocuments",
  portfolio: "userDocuments",
  transcript: "userDocuments",
  certificate: "userDocuments",
  default: "userDocuments"
};
async function uploadDocumentToR2(file, documentType = "default", identifier) {
  try {
    logger.info(`📤 Uploading document to R2: ${file.name} (type: ${documentType})`);
    if (!file || file.size === 0) {
      throw new Error("No file provided or file is empty");
    }
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      throw new Error(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);
    }
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ];
    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        `File type ${file.type} is not supported. Please upload a PDF or Word document.`
      );
    }
    const buffer = Buffer.from(await file.arrayBuffer());
    const r2FileType = DOCUMENT_TYPE_TO_R2_TYPE[documentType] || DOCUMENT_TYPE_TO_R2_TYPE.default;
    const uploadResult = await uploadFile(buffer, file.name, file.type, r2FileType, identifier);
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || "Upload failed");
    }
    logger.info(`✅ Document uploaded successfully to R2: ${uploadResult.publicUrl}`);
    return {
      success: true,
      originalFileName: file.name,
      filename: uploadResult.fileKey?.split("/").pop() || file.name,
      filePath: uploadResult.fileKey,
      // R2 file key
      publicPath: uploadResult.publicUrl,
      // R2 public URL
      fileSize: uploadResult.fileSize,
      contentType: uploadResult.contentType
    };
  } catch (error) {
    logger.error(`❌ Failed to upload document to R2:`, error);
    return {
      success: false,
      originalFileName: file.name,
      filename: "",
      filePath: "",
      publicPath: "",
      fileSize: 0,
      contentType: file.type,
      error: error instanceof Error ? error.message : "Unknown upload error"
    };
  }
}
async function updateResumeFileKey(oldFileKey, resumeId) {
  try {
    logger.info(`🔄 Updating resume file key: ${oldFileKey} -> resume-${resumeId}`);
    const bucketName = getBucketName(
      "resumes"
      /* RESUMES */
    );
    const extension = oldFileKey.split(".").pop() || "pdf";
    const newFileKey = `profile-resume-${resumeId}.${extension}`;
    const copyCommand = new CopyObjectCommand({
      Bucket: bucketName,
      Key: newFileKey,
      CopySource: `${bucketName}/${oldFileKey}`,
      MetadataDirective: "COPY"
    });
    await r2Client.send(copyCommand);
    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: oldFileKey
    });
    await r2Client.send(deleteCommand);
    const newPublicUrl = getPublicUrl(newFileKey);
    logger.info(`✅ Resume file key updated successfully: ${newFileKey}`);
    return {
      success: true,
      newFileKey,
      newPublicUrl
    };
  } catch (error) {
    logger.error(`❌ Failed to update resume file key:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

export { uploadDocumentToR2 as default, getPublicUrl, updateResumeFileKey, uploadDocumentToR2, uploadFile };
//# sourceMappingURL=r2DocumentUpload-DZXqPhze.js.map
