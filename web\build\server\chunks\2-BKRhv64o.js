import { r as redirect } from './index-Ddp2AB5f.js';

const load = async ({ locals, url }) => {
  if (locals.user) {
    throw redirect(302, "/dashboard");
  }
  return {
    user: locals.user,
    currentPath: url.pathname
  };
};

var _layout_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 2;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-BP1undag.js')).default;
const server_id = "src/routes/auth/+layout.server.ts";
const imports = ["_app/immutable/nodes/2.BOZJxHPx.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/ByUTvV5u.js","_app/immutable/chunks/D85ENLd-.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=2-BKRhv64o.js.map
