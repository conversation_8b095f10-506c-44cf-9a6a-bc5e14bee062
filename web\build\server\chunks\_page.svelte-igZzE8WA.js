import { p as push, Q as bind_props, q as pop, M as ensure_array_like, P as stringify, O as escape_html, K as fallback, N as attr, J as attr_class } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { B as Button } from './button-CrucCo1G.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { S as Skeleton } from './skeleton-C-NLefl9.js';
import { B as Bot } from './bot-Be-vDUqI.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { A as Arrow_up_right, C as Chart_column_increasing, W as Workflow, M as Monitor_check } from './workflow-D3SVl6eZ.js';
import { P as Play } from './play-DKNYqs4c.js';
import { S as Search } from './search-B0oHlTPS.js';
import { S as Send } from './send-BkGWvFu2.js';
import { Z as Zap } from './zap-XN6hrXDp.js';
import { T as Target } from './target-VMK77SRs.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { C as Chart_no_axes_column_increasing } from './chart-no-axes-column-increasing-DwzmK_0v.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { G as Globe } from './globe-B6sBOhFF.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import { A as Award } from './award-DpjdNTEA.js';
import { S as Star } from './star-DbHO3Z_L.js';
import { C as Carousel, a as Carousel_content, b as Carousel_item } from './carousel-item-D-QWeSJJ.js';
import 'clsx';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { S as Sparkles } from './sparkles-E4-thk3U.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

const defaultOptions = {
  active: true,
  breakpoints: {},
  delay: 4000,
  jump: false,
  playOnInit: true,
  stopOnFocusIn: true,
  stopOnInteraction: true,
  stopOnMouseEnter: false,
  stopOnLastSnap: false,
  rootNode: null
};

function normalizeDelay(emblaApi, delay) {
  const scrollSnaps = emblaApi.scrollSnapList();
  if (typeof delay === 'number') {
    return scrollSnaps.map(() => delay);
  }
  return delay(scrollSnaps, emblaApi);
}
function getAutoplayRootNode(emblaApi, rootNode) {
  const emblaRootNode = emblaApi.rootNode();
  return rootNode && rootNode(emblaRootNode) || emblaRootNode;
}

function Autoplay(userOptions = {}) {
  let options;
  let emblaApi;
  let destroyed;
  let delay;
  let timerStartTime = null;
  let timerId = 0;
  let autoplayActive = false;
  let mouseIsOver = false;
  let playOnDocumentVisible = false;
  let jump = false;
  function init(emblaApiInstance, optionsHandler) {
    emblaApi = emblaApiInstance;
    const {
      mergeOptions,
      optionsAtMedia
    } = optionsHandler;
    const optionsBase = mergeOptions(defaultOptions, Autoplay.globalOptions);
    const allOptions = mergeOptions(optionsBase, userOptions);
    options = optionsAtMedia(allOptions);
    if (emblaApi.scrollSnapList().length <= 1) return;
    jump = options.jump;
    destroyed = false;
    delay = normalizeDelay(emblaApi, options.delay);
    const {
      eventStore,
      ownerDocument
    } = emblaApi.internalEngine();
    const isDraggable = !!emblaApi.internalEngine().options.watchDrag;
    const root = getAutoplayRootNode(emblaApi, options.rootNode);
    eventStore.add(ownerDocument, 'visibilitychange', visibilityChange);
    if (isDraggable) {
      emblaApi.on('pointerDown', pointerDown);
    }
    if (isDraggable && !options.stopOnInteraction) {
      emblaApi.on('pointerUp', pointerUp);
    }
    if (options.stopOnMouseEnter) {
      eventStore.add(root, 'mouseenter', mouseEnter);
    }
    if (options.stopOnMouseEnter && !options.stopOnInteraction) {
      eventStore.add(root, 'mouseleave', mouseLeave);
    }
    if (options.stopOnFocusIn) {
      emblaApi.on('slideFocusStart', stopAutoplay);
    }
    if (options.stopOnFocusIn && !options.stopOnInteraction) {
      eventStore.add(emblaApi.containerNode(), 'focusout', startAutoplay);
    }
    if (options.playOnInit) startAutoplay();
  }
  function destroy() {
    emblaApi.off('pointerDown', pointerDown).off('pointerUp', pointerUp).off('slideFocusStart', stopAutoplay);
    stopAutoplay();
    destroyed = true;
    autoplayActive = false;
  }
  function setTimer() {
    const {
      ownerWindow
    } = emblaApi.internalEngine();
    ownerWindow.clearTimeout(timerId);
    timerId = ownerWindow.setTimeout(next, delay[emblaApi.selectedScrollSnap()]);
    timerStartTime = new Date().getTime();
    emblaApi.emit('autoplay:timerset');
  }
  function clearTimer() {
    const {
      ownerWindow
    } = emblaApi.internalEngine();
    ownerWindow.clearTimeout(timerId);
    timerId = 0;
    timerStartTime = null;
    emblaApi.emit('autoplay:timerstopped');
  }
  function startAutoplay() {
    if (destroyed) return;
    if (documentIsHidden()) {
      playOnDocumentVisible = true;
      return;
    }
    if (!autoplayActive) emblaApi.emit('autoplay:play');
    setTimer();
    autoplayActive = true;
  }
  function stopAutoplay() {
    if (destroyed) return;
    if (autoplayActive) emblaApi.emit('autoplay:stop');
    clearTimer();
    autoplayActive = false;
  }
  function visibilityChange() {
    if (documentIsHidden()) {
      playOnDocumentVisible = autoplayActive;
      return stopAutoplay();
    }
    if (playOnDocumentVisible) startAutoplay();
  }
  function documentIsHidden() {
    const {
      ownerDocument
    } = emblaApi.internalEngine();
    return ownerDocument.visibilityState === 'hidden';
  }
  function pointerDown() {
    if (!mouseIsOver) stopAutoplay();
  }
  function pointerUp() {
    if (!mouseIsOver) startAutoplay();
  }
  function mouseEnter() {
    mouseIsOver = true;
    stopAutoplay();
  }
  function mouseLeave() {
    mouseIsOver = false;
    startAutoplay();
  }
  function play(jumpOverride) {
    if (typeof jumpOverride !== 'undefined') jump = jumpOverride;
    startAutoplay();
  }
  function stop() {
    if (autoplayActive) stopAutoplay();
  }
  function reset() {
    if (autoplayActive) startAutoplay();
  }
  function isPlaying() {
    return autoplayActive;
  }
  function next() {
    const {
      index
    } = emblaApi.internalEngine();
    const nextIndex = index.clone().add(1).get();
    const lastIndex = emblaApi.scrollSnapList().length - 1;
    const kill = options.stopOnLastSnap && nextIndex === lastIndex;
    if (emblaApi.canScrollNext()) {
      emblaApi.scrollNext(jump);
    } else {
      emblaApi.scrollTo(0, jump);
    }
    emblaApi.emit('autoplay:select');
    if (kill) return stopAutoplay();
    startAutoplay();
  }
  function timeUntilNext() {
    if (!timerStartTime) return null;
    const currentDelay = delay[emblaApi.selectedScrollSnap()];
    const timePastSinceStart = new Date().getTime() - timerStartTime;
    return currentDelay - timePastSinceStart;
  }
  const self = {
    name: 'autoplay',
    options: userOptions,
    init,
    destroy,
    play,
    stop,
    reset,
    isPlaying,
    timeUntilNext
  };
  return self;
}
Autoplay.globalOptions = undefined;

function HeroSection($$payload, $$props) {
  push();
  let streamingJobs = [];
  let isLoading = true;
  function getCompanyLogo(logoUrl) {
    if (!logoUrl) return null;
    const filename = logoUrl.split("/").pop();
    if (filename && filename !== "data:,") {
      const workerUrl = "https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev";
      return `${workerUrl}/logos/${filename}`;
    }
    return null;
  }
  async function fetchJobs() {
    try {
      console.log("Fetching jobs from API...");
      const response = await fetch("/api/jobs?limit=200&random=true");
      console.log("Response status:", response.status);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      console.log("API response data:", data);
      console.log("Jobs count:", data.jobs?.length || 0);
      if (data.jobs?.length > 0) {
        console.log("First job sample:", data.jobs[0]);
        console.log("Available fields:", Object.keys(data.jobs[0]));
        console.log("Description type:", typeof data.jobs[0].description);
        console.log("Description content:", data.jobs[0].description);
      }
      return data.jobs || [];
    } catch (error) {
      console.error("Failed to fetch jobs:", error);
      return [];
    }
  }
  const statusStages = [
    {
      status: "scanning",
      color: "blue",
      text: "Scanning",
      icon: Search
    },
    {
      status: "matching",
      color: "yellow",
      text: "Matching",
      icon: Workflow
    },
    {
      status: "applying",
      color: "orange",
      text: "Applying",
      icon: Send
    },
    {
      status: "applied",
      color: "green",
      text: "Applied",
      icon: Monitor_check
    }
  ];
  function getJobStatus(jobAge) {
    if (jobAge < 5) return statusStages[0];
    if (jobAge < 10) return statusStages[1];
    if (jobAge < 15) return statusStages[2];
    return statusStages[3];
  }
  function transformJobForDisplay(job, index) {
    const company = job?.company || "Unknown Company";
    const companyInitial = company.charAt(0).toUpperCase();
    const logoColors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-red-500",
      "bg-yellow-500",
      "bg-indigo-500",
      "bg-pink-500",
      "bg-teal-500"
    ];
    const logoColor = logoColors[company.length % logoColors.length];
    const rawLogoUrl = job?.companyRelation?.logoUrl || null;
    const companyName = job?.companyRelation?.name || company;
    const companyLogo = getCompanyLogo(rawLogoUrl);
    let industry = job?.industry || job?.industryTags?.[0];
    if (!industry) {
      const titleLower = (job?.title || "").toLowerCase();
      const companyLower = (job?.company || "").toLowerCase();
      if (/(health|medical|nurse|doctor|therapist|clinical)/.test(titleLower) || /(health|medical|hospital)/.test(companyLower)) {
        industry = "Healthcare";
      } else if (/(finance|bank|accounting|banker)/.test(titleLower) || /(bank|financial|wells fargo)/.test(companyLower)) {
        industry = "Finance";
      } else if (/(market|brand|social media)/.test(titleLower)) {
        industry = "Marketing";
      } else if (/(sales|account manager|business development)/.test(titleLower)) {
        industry = "Sales";
      } else if (/(design|ui|ux|graphic)/.test(titleLower)) {
        industry = "Design";
      } else if (/(engineer|developer|programmer|software|scientist|biostatistician)/.test(titleLower)) {
        industry = "Technology";
      } else if (/(teacher|education|instructor|professor|assistant)/.test(titleLower) || /(university|school)/.test(companyLower)) {
        industry = "Education";
      } else if (/(operations|logistics|supply chain|coordinator|clerk)/.test(titleLower)) {
        industry = "Operations";
      } else if (/(consultant|advisor)/.test(titleLower)) {
        industry = "Consulting";
      } else if (/(hr|human resources|benefits)/.test(titleLower)) {
        industry = "Human Resources";
      } else if (/(behavioral|behavior|therapist)/.test(titleLower)) {
        industry = "Healthcare";
      } else {
        industry = "Other";
      }
    }
    let seniority = job?.experienceLevel || job?.seniorityLevel;
    if (!seniority) {
      const titleLower = (job?.title || "").toLowerCase();
      if (/(senior|sr\.|lead|principal|staff)/.test(titleLower)) {
        seniority = "Senior Level";
      } else if (/(junior|jr\.|entry|assistant|aide|technician)/.test(titleLower)) {
        seniority = "Entry Level";
      } else if (/(director|head of|vp|vice president|manager)/.test(titleLower)) {
        seniority = "Management";
      } else if (/(coordinator|specialist|analyst)/.test(titleLower)) {
        seniority = "Mid-Level";
      } else {
        seniority = "Mid-Level";
      }
    }
    const benefits = Array.isArray(job?.benefits) ? job.benefits.slice(0, 3) : [];
    return {
      id: job?.id || Math.random().toString(),
      uniqueKey: `${job?.id || "job"}_${index}_${Date.now()}_${Math.random().toString(36).slice(2, 9)}`,
      company: companyName,
      // Use the company name from relation if available
      role: job?.title || "Software Engineer",
      location: job?.location || "Remote",
      salary: job?.salary || "Competitive",
      companyInitial,
      logoColor,
      companyLogo,
      // Add the R2 logo URL
      companyRelation: job?.companyRelation,
      // Preserve the original company relation data
      matchScore: Math.floor(Math.random() * 30) + 70,
      // random 70–99%
      industry,
      seniority,
      benefits,
      description: job?.description && typeof job.description === "string" ? job.description.slice(0, 100) + "..." : "",
      age: Math.floor(Math.random() * 20)
    };
  }
  if (typeof window !== "undefined") {
    (async function initializeStream() {
      console.log("Starting initialization...");
      isLoading = true;
      const jobs = await fetchJobs();
      console.log("Fetched jobs:", jobs.length);
      if (jobs.length > 0) {
        const initialCount = Math.min(40, jobs.length);
        const baseSlice = jobs.slice(0, initialCount);
        console.log("Base slice:", baseSlice.length);
        const firstBatch = baseSlice.map((job, i) => transformJobForDisplay(job, i));
        const secondBatch = baseSlice.map((job, i) => transformJobForDisplay(job, i + initialCount));
        streamingJobs = [...firstBatch, ...secondBatch];
        console.log("Final streaming jobs:", streamingJobs.length);
        isLoading = false;
      } else {
        console.warn("No jobs found in database");
        isLoading = false;
      }
    })();
    setInterval(
      () => {
        if (!streamingJobs.length) return;
        for (let i = 0; i < streamingJobs.length; i++) {
          if (Math.random() < 0.2) {
            streamingJobs[i].age = Math.floor(Math.random() * 20);
          }
        }
        streamingJobs = streamingJobs;
      },
      1500
    );
  }
  $$payload.out += `<section class="-mt-17 -z-50 svelte-14lt850"><div class="grid lg:grid-cols-[2fr_4fr] svelte-14lt850"><div class="relative flex flex-col justify-center overflow-hidden svelte-14lt850"><div class="mt-17 relative z-10 max-w-md space-y-8 p-10 svelte-14lt850"><div class="space-y-6 svelte-14lt850"><div class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 svelte-14lt850">`;
  Bot($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> <span class="svelte-14lt850">AI-Powered Automation</span></div> <h1 class="font-bold leading-tight text-gray-900 sm:text-2xl md:text-4xl lg:text-5xl xl:text-6xl svelte-14lt850">Apply to <span class="relative text-blue-600 svelte-14lt850">Hundreds <div class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60 svelte-14lt850"></div></span> of Jobs Automatically</h1> <p class="text-lg text-gray-600 svelte-14lt850">Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.</p></div> <div class="space-y-2 text-sm svelte-14lt850"><div class="flex items-center gap-3 svelte-14lt850"><div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100 svelte-14lt850">`;
  Circle_check_big($$payload, { class: "h-3 w-3 text-green-600" });
  $$payload.out += `<!----></div> <span class="text-gray-700 svelte-14lt850">100+ applications in minutes</span></div> <div class="flex items-center gap-3 svelte-14lt850"><div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 svelte-14lt850">`;
  Circle_check_big($$payload, { class: "h-3 w-3 text-blue-600" });
  $$payload.out += `<!----></div> <span class="text-gray-700 svelte-14lt850">AI-powered resume matching</span></div> <div class="flex items-center gap-3 svelte-14lt850"><div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100 svelte-14lt850">`;
  Circle_check_big($$payload, { class: "h-3 w-3 text-purple-600" });
  $$payload.out += `<!----></div> <span class="text-gray-700 svelte-14lt850">Real-time tracking &amp; analytics</span></div></div> <div class="space-y-4 svelte-14lt850"><div class="flex flex-col gap-3 sm:flex-row svelte-14lt850">`;
  Button($$payload, {
    href: "/auth/sign-up",
    class: "group bg-gradient-to-r from-blue-600 to-indigo-600 font-semibold text-white hover:from-blue-700 hover:to-indigo-700",
    children: ($$payload2) => {
      $$payload2.out += `<span class="flex items-center gap-2 svelte-14lt850">Get Started `;
      Arrow_up_right($$payload2, {
        class: "h-4 w-4 transition-transform group-hover:translate-x-0.5"
      });
      $$payload2.out += `<!----></span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "ghost",
    class: "group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900",
    children: ($$payload2) => {
      Play($$payload2, { class: "h-4 w-4 group-hover:text-blue-600" });
      $$payload2.out += `<!----> <span class="svelte-14lt850">Watch Demo</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="flex items-center gap-3 text-xs text-gray-500 svelte-14lt850"><div class="flex items-center gap-2 svelte-14lt850"><div class="h-1.5 w-1.5 rounded-full bg-green-500 svelte-14lt850"></div> <span class="svelte-14lt850">No credit card required</span></div> <span class="svelte-14lt850">Setup in minutes</span></div></div></div></div> <div class="bg-accent border-border relative h-[900px] overflow-hidden border-l opacity-80 svelte-14lt850"><div class="from-accent via-accent/90 pointer-events-none absolute left-0 right-0 top-0 z-10 h-24 bg-gradient-to-b to-transparent svelte-14lt850"></div> <div class="from-accent via-accent/70 pointer-events-none absolute bottom-0 left-0 right-0 z-10 h-16 bg-gradient-to-t to-transparent svelte-14lt850"></div> <div class="relative flex h-full flex-col justify-center svelte-14lt850"><div class="h-full overflow-hidden p-4 svelte-14lt850">`;
  if (isLoading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="columns-1 gap-4 space-y-4 sm:columns-2 lg:columns-3 xl:columns-4 svelte-14lt850">`;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-40 w-full" });
    $$payload.out += `<!----></div>`;
  } else if (streamingJobs.length === 0) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="flex h-full items-center justify-center svelte-14lt850"><div class="text-center svelte-14lt850"><p class="text-gray-600 svelte-14lt850">No jobs found in database</p></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(streamingJobs);
    $$payload.out += `<div class="animate-scroll-up columns-1 gap-3 sm:columns-2 lg:columns-3 xl:columns-4 svelte-14lt850"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
      let job = each_array[$$index_1];
      const currentStatus = getJobStatus(job.age || 0);
      const Icon = currentStatus.icon;
      $$payload.out += `<!---->`;
      Card($$payload, {
        class: "mb-2 break-inside-avoid gap-0 p-0 transition-all duration-500 ease-in-out",
        style: `animation-delay: ${stringify(job.age * 100)}ms`,
        children: ($$payload2) => {
          $$payload2.out += `<!---->`;
          Card_header($$payload2, {
            class: "border-border border-b !p-2",
            children: ($$payload3) => {
              $$payload3.out += `<div class="flex items-start gap-3 svelte-14lt850"><div class="h-8 w-8 overflow-hidden rounded-lg svelte-14lt850">`;
              if (getCompanyLogo(job?.companyRelation?.logoUrl)) {
                $$payload3.out += "<!--[-->";
                $$payload3.out += `<img${attr("src", getCompanyLogo(job?.companyRelation?.logoUrl))}${attr("alt", `${stringify(job.company)} logo`)} class="h-full w-full bg-white object-contain p-0.5 svelte-14lt850" loading="lazy" onerror="this.__e=event"/> <div${attr_class(`hidden h-full w-full items-center justify-center ${stringify(job.logoColor)} text-xs font-bold text-white`, "svelte-14lt850")}>${escape_html(job.companyInitial)}</div>`;
              } else {
                $$payload3.out += "<!--[!-->";
                $$payload3.out += `<div${attr_class(`flex h-full w-full items-center justify-center ${stringify(job.logoColor)} text-xs font-bold text-white`, "svelte-14lt850")}>${escape_html(job.companyInitial)}</div>`;
              }
              $$payload3.out += `<!--]--></div> <div class="min-w-0 flex-1 svelte-14lt850"><div class="text-sm font-semibold text-gray-900 svelte-14lt850">${escape_html(job.role)}</div> <div class="text-xs text-gray-600 svelte-14lt850">${escape_html(job.company)}</div> <div class="mt-1 flex flex-wrap gap-1 text-xs svelte-14lt850"><span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700 svelte-14lt850">${escape_html(job.industry)}</span> <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700 svelte-14lt850">${escape_html(job.seniority)}</span></div> <div class="mt-1 text-xs text-gray-500 svelte-14lt850"><div class="svelte-14lt850">${escape_html(job.location)} • ${escape_html(job.salary)}</div></div></div></div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> <!---->`;
          Card_content($$payload2, {
            class: "!p-2",
            children: ($$payload3) => {
              if (job.description) {
                $$payload3.out += "<!--[-->";
                $$payload3.out += `<div class="mt-2 text-xs text-gray-600 svelte-14lt850">${escape_html(job.description)}</div>`;
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]--> `;
              if (job.benefits && job.benefits.length > 0) {
                $$payload3.out += "<!--[-->";
                const each_array_1 = ensure_array_like(job.benefits);
                $$payload3.out += `<div class="mt-2 flex flex-wrap gap-1 svelte-14lt850"><!--[-->`;
                for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                  let benefit = each_array_1[$$index];
                  $$payload3.out += `<span class="rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-700 svelte-14lt850">${escape_html(benefit)}</span>`;
                }
                $$payload3.out += `<!--]--></div>`;
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> <!---->`;
          Card_footer($$payload2, {
            class: "border-border flex items-center justify-between border-t !p-2",
            children: ($$payload3) => {
              $$payload3.out += `<div class="flex items-center gap-1.5 svelte-14lt850"><div${attr_class(`flex h-4 w-4 items-center justify-center rounded-full bg-${stringify(currentStatus.color)}-100`, "svelte-14lt850")}><!---->`;
              Icon($$payload3, {
                class: `h-3 w-3 text-${stringify(currentStatus.color)}-600`
              });
              $$payload3.out += `<!----></div> <span class="text-xs font-medium text-gray-700 svelte-14lt850">${escape_html(currentStatus.text)}</span></div> <div${attr_class(`rounded-full bg-${stringify(currentStatus.color)}-50 px-2 py-0.5 text-xs font-bold text-${stringify(currentStatus.color)}-700`, "svelte-14lt850")}>${escape_html(job.matchScore)}% match</div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload.out += `<!---->`;
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div></div></div></div></section>`;
  pop();
}
function FeatureCard($$payload, $$props) {
  let icon = $$props["icon"];
  let title = $$props["title"];
  let description = $$props["description"];
  $$payload.out += `<div class="p-22 bg-background text-foreground group shadow-md transition-all duration-300 hover:shadow-lg"><div class="bg-primary/10 group-hover:bg-primary/20 mb-4 flex h-12 w-12 items-center justify-center rounded-lg transition-all duration-300"><!---->`;
  icon?.($$payload, {
    class: "text-primary h-6 w-6 transition-all duration-300 group-hover:scale-110"
  });
  $$payload.out += `<!----></div> <h3 class="font-normal! mb-4 text-3xl">${escape_html(title)}</h3> <p class="text-md text-muted-foreground">${escape_html(description)}</p></div>`;
  bind_props($$props, { icon, title, description });
}
function FeaturesSection($$payload) {
  const features = [
    {
      icon: Zap,
      title: "One-Click Apply",
      description: "Apply to hundreds of jobs across multiple platforms with a single click."
    },
    {
      icon: Target,
      title: "Smart Matching",
      description: "Our AI matches your resume to job requirements for higher success rates."
    },
    {
      icon: Clock,
      title: "Save Hours Daily",
      description: "Automate repetitive application tasks and focus on preparing for interviews."
    },
    {
      icon: Chart_no_axes_column_increasing,
      title: "Application Analytics",
      description: "Track your application performance and optimize your job search strategy."
    },
    {
      icon: Shield,
      title: "Resume Optimization",
      description: "Get suggestions to improve your resume for specific job postings."
    },
    {
      icon: Globe,
      title: "Multi-Platform Support",
      description: "Works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more."
    },
    {
      icon: Message_square,
      title: "AI Interview Coach",
      description: "Practice with realistic mock interviews tailored to your industry with instant feedback."
    },
    {
      icon: Award,
      title: "Career Advancement",
      description: "Get personalized recommendations for skills to develop for your dream roles."
    }
  ];
  const each_array = ensure_array_like(features);
  $$payload.out += `<section id="features" class="p-12"><div class="grid grid-cols-1 divide-x divide-y border md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    FeatureCard($$payload, {
      icon: feature.icon,
      title: feature.title,
      description: feature.description
    });
  }
  $$payload.out += `<!--]--></div></section>`;
}
function TestimonialCard($$payload, $$props) {
  let name = $$props["name"];
  let role = $$props["role"];
  let testimonial = $$props["testimonial"];
  let rating = $$props["rating"];
  let image = $$props["image"];
  const each_array = ensure_array_like(Array(5));
  $$payload.out += `<div class="border-border bg-card text-card-foreground flex h-full flex-col rounded-lg border p-6 shadow-md"><div class="mb-4 flex items-center space-x-4"><div class="h-12 w-12 overflow-hidden rounded-full"><img${attr("src", image)}${attr("alt", name)} class="h-full w-full object-cover"/></div> <div><h4 class="font-semibold">${escape_html(name)}</h4> <p class="text-muted-foreground text-sm">${escape_html(role)}</p></div></div> <div class="mb-4 flex"><!--[-->`;
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    each_array[i];
    Star($$payload, {
      size: 16,
      class: i < rating ? "fill-warning text-warning" : "text-muted"
    });
  }
  $$payload.out += `<!--]--></div> <p class="text-muted-foreground flex-grow italic">${escape_html(testimonial)}</p></div>`;
  bind_props($$props, { name, role, testimonial, rating, image });
}
function TestimonialsSection($$payload, $$props) {
  push();
  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Software Engineer",
      testimonial: "Auto Apply helped me submit over 200 applications in just two weeks. I landed 15 interviews and 3 job offers!",
      rating: 5,
      image: "https://randomuser.me/api/portraits/women/12.jpg"
    },
    {
      name: "Michael Chen",
      role: "Marketing Specialist",
      testimonial: "The automation is incredible. What used to take me hours now takes minutes. Plus, their resume optimization suggestions helped me improve my response rate.",
      rating: 5,
      image: "https://randomuser.me/api/portraits/men/32.jpg"
    },
    {
      name: "Emily Rodriguez",
      role: "Data Analyst",
      testimonial: "I was skeptical at first, but after using Auto Apply for a month, I received more interview calls than I had in the previous six months of job hunting.",
      rating: 4,
      image: "https://randomuser.me/api/portraits/women/65.jpg"
    },
    {
      name: "David Wilson",
      role: "Product Manager",
      testimonial: "The time I saved using Auto Apply allowed me to focus on networking and preparing for interviews, which ultimately helped me land my dream job.",
      rating: 5,
      image: "https://randomuser.me/api/portraits/men/75.jpg"
    },
    {
      name: "Lisa Thompson",
      role: "UX Designer",
      testimonial: "The platform made job hunting so much less stressful. The analytics feature showed me which applications were most effective.",
      rating: 4,
      image: "https://randomuser.me/api/portraits/women/33.jpg"
    },
    {
      name: "James Morris",
      role: "Financial Analyst",
      testimonial: "Auto Apply's custom cover letter generation is phenomenal. Each letter feels personal and tailored to the job. This service is worth every penny.",
      rating: 5,
      image: "https://randomuser.me/api/portraits/men/54.jpg"
    }
  ];
  const column1 = testimonials.slice(0, Math.ceil(testimonials.length / 3));
  const column2 = testimonials.slice(Math.ceil(testimonials.length / 3), Math.ceil(testimonials.length * 2 / 3));
  const column3 = testimonials.slice(Math.ceil(testimonials.length * 2 / 3));
  const plugin1 = Autoplay({ delay: 3e3, stopOnInteraction: true });
  const plugin2 = Autoplay({ delay: 4500, stopOnInteraction: true });
  const plugin3 = Autoplay({ delay: 3800, stopOnInteraction: true });
  const options1 = { align: "start", loop: true, axis: "y" };
  const options2 = { align: "start", loop: true, axis: "y" };
  const options3 = { align: "start", loop: true, axis: "y" };
  $$payload.out += `<section class="py-16"><div class="container mx-auto px-4"><h2 class="text-foreground mb-4 text-center text-4xl font-bold">What Our Users Say</h2> <p class="text-muted-foreground mx-auto mb-12 max-w-3xl text-center text-lg">Thousands of job seekers have found their dream jobs faster with Auto Apply.</p> <div class="mt-12 grid grid-cols-1 gap-8 md:grid-cols-3"><div class="h-[600px] overflow-hidden">`;
  Carousel($$payload, {
    plugins: [plugin1],
    opts: options1,
    class: "h-full",
    children: ($$payload2) => {
      Carousel_content($$payload2, {
        class: "h-full",
        children: ($$payload3) => {
          const each_array = ensure_array_like(column1);
          $$payload3.out += `<!--[-->`;
          for (let i = 0, $$length = each_array.length; i < $$length; i++) {
            let testimonial = each_array[i];
            Carousel_item($$payload3, {
              class: "h-auto pb-4 pt-4",
              children: ($$payload4) => {
                TestimonialCard($$payload4, {
                  name: testimonial.name,
                  role: testimonial.role,
                  testimonial: testimonial.testimonial,
                  rating: testimonial.rating,
                  image: testimonial.image
                });
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="h-[600px] overflow-hidden">`;
  Carousel($$payload, {
    plugins: [plugin2],
    opts: options2,
    class: "h-full",
    children: ($$payload2) => {
      Carousel_content($$payload2, {
        class: "h-full",
        children: ($$payload3) => {
          const each_array_1 = ensure_array_like(column2);
          $$payload3.out += `<!--[-->`;
          for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
            let testimonial = each_array_1[i];
            Carousel_item($$payload3, {
              class: "h-auto pb-4 pt-4",
              children: ($$payload4) => {
                TestimonialCard($$payload4, {
                  name: testimonial.name,
                  role: testimonial.role,
                  testimonial: testimonial.testimonial,
                  rating: testimonial.rating,
                  image: testimonial.image
                });
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="h-[600px] overflow-hidden">`;
  Carousel($$payload, {
    plugins: [plugin3],
    opts: options3,
    class: "h-full",
    children: ($$payload2) => {
      Carousel_content($$payload2, {
        class: "h-full",
        children: ($$payload3) => {
          const each_array_2 = ensure_array_like(column3);
          $$payload3.out += `<!--[-->`;
          for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
            let testimonial = each_array_2[i];
            Carousel_item($$payload3, {
              class: "h-auto pb-4 pt-4",
              children: ($$payload4) => {
                TestimonialCard($$payload4, {
                  name: testimonial.name,
                  role: testimonial.role,
                  testimonial: testimonial.testimonial,
                  rating: testimonial.rating,
                  image: testimonial.image
                });
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></section>`;
  pop();
}
function CompanySection($$payload, $$props) {
  push();
  $$payload.out += `<section class="bg-muted py-16"><div class="container mx-auto px-4"><div class="mb-12 text-center"><h2 class="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">Trusted by Leading Companies</h2> <p class="text-muted-foreground mt-4 text-lg">Join thousands of professionals working at innovative companies worldwide</p></div> `;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex flex-col gap-4"><div class="flex grid grid-cols-5 items-center justify-center gap-4">`;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----></div> <div class="flex grid grid-cols-5 items-center justify-center gap-4">`;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----></div> <div class="flex grid grid-cols-5 items-center justify-center gap-4">`;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "bg-primary/10 h-30 w-full" });
    $$payload.out += `<!----></div></div>`;
  }
  $$payload.out += `<!--]--></div></section>`;
  pop();
}
function ServicesSection($$payload) {
  const features = {
    // Automated Apply features
    automatedApply: {
      title: "Effortless Job Applications",
      description: "Submit applications to hundreds of positions with our streamlined one-click system.",
      secondary: [
        {
          icon: Clock,
          title: "Reclaim Your Time",
          description: "Save hours daily by automating repetitive tasks and focus on interview preparation."
        },
        {
          icon: Chart_column_increasing,
          title: "Performance Insights",
          description: "Gain valuable analytics to optimize your application strategy and improve results."
        },
        {
          icon: Shield,
          title: "Resume Enhancement",
          description: "Receive tailored suggestions to strengthen your resume for specific opportunities."
        },
        {
          icon: Globe,
          title: "Universal Platform Support",
          description: "Seamlessly works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more."
        }
      ]
    },
    // Job Tracker features
    jobTracker: {
      title: "Comprehensive Application Tracking",
      description: "Monitor all your job applications in one intuitive, centralized dashboard.",
      secondary: [
        {
          icon: Circle_check_big,
          title: "Real-time Status Updates",
          description: "Track your progress through each stage of the hiring process with clarity."
        },
        {
          icon: Clock,
          title: "Interview Management",
          description: "Organize and prepare for upcoming interviews with smart scheduling and reminders."
        },
        {
          icon: Chart_column_increasing,
          title: "Strategic Analytics",
          description: "Visualize your job search journey with detailed metrics and actionable insights."
        },
        {
          icon: Shield,
          title: "Enterprise-grade Security",
          description: "Rest assured your career data is protected with advanced encryption and privacy controls."
        }
      ]
    },
    // Resume Builder features
    resumeBuilder: {
      title: "Professional Resume Creator",
      description: "Craft standout resumes that capture attention with our intuitive builder.",
      secondary: [
        {
          icon: Target,
          title: "ATS-Friendly Formatting",
          description: "Ensure your resume successfully navigates through automated screening systems."
        },
        {
          icon: Award,
          title: "Strategic Skills Showcase",
          description: "Automatically highlight relevant qualifications based on target job descriptions."
        },
        {
          icon: Globe,
          title: "Versatile Export Options",
          description: "Download your polished resume in PDF, DOCX, or plain text formats as needed."
        },
        {
          icon: Shield,
          title: "Multiple Resume Versions",
          description: "Create and manage specialized resumes tailored for different career opportunities."
        }
      ]
    },
    // Co-Pilot features
    coPilot: {
      title: "AI Career Co-Pilot",
      description: "Navigate your career journey with AI-powered guidance every step of the way.",
      secondary: [
        {
          icon: Message_square,
          title: "AI Interview Coach",
          description: "Practice with realistic mock interviews tailored to your industry with instant feedback."
        },
        {
          icon: Sparkles,
          title: "Personalized Insights",
          description: "Receive custom career advice based on your skills, experience, and goals."
        },
        {
          icon: Target,
          title: "Job Match Analysis",
          description: "Get AI-powered compatibility scores for job listings based on your profile."
        },
        {
          icon: Shield,
          title: "Career Strategy Planning",
          description: "Develop a strategic roadmap to achieve your long-term professional objectives."
        }
      ]
    }
  };
  const each_array = ensure_array_like(features.automatedApply.secondary);
  const each_array_1 = ensure_array_like(features.jobTracker.secondary);
  const each_array_2 = ensure_array_like(features.resumeBuilder.secondary);
  const each_array_3 = ensure_array_like(features.coPilot.secondary);
  $$payload.out += `<section id="services" class="border-border border"><div class="flex flex-col"><div class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.automatedApply.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.automatedApply.description)}</p> <a href="/auto-apply" class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">Learn More `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div> <div class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t"></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    $$payload.out += `<div class="p-22 border-border bg-card text-card-foreground hover:bg-card/80 dark:hover:bg-card/90 hover:border-primary/20 group rounded-none border shadow-md transition-all duration-300 hover:shadow-lg"><div class="bg-primary/10 group-hover:bg-primary/20 mb-4 flex h-12 w-12 items-center justify-center rounded-lg transition-all duration-300"><!---->`;
    feature.icon?.($$payload, {
      class: "text-primary h-6 w-6 transition-all duration-300 group-hover:scale-110"
    });
    $$payload.out += `<!----></div> <h3 class="font-normal! mb-4 text-3xl">${escape_html(feature.title)}</h3> <p class="text-md text-muted-foreground">${escape_html(feature.description)}</p></div>`;
  }
  $$payload.out += `<!--]--></div></div> <div class="border-border md:grid-cols-16 flex flex-col border [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 border-border col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l"></div> <div class="p-15 text-foreground col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.jobTracker.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.jobTracker.description)}</p> <a href="/job-tracker" class="bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/70 group flex w-48 flex-row items-center justify-between rounded-md px-6 py-3 transition-all duration-200">Learn More `;
  Arrow_right($$payload, {
    class: "ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1"
  });
  $$payload.out += `<!----></a></div></div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let feature = each_array_1[$$index_1];
    FeatureCard($$payload, {
      icon: feature.icon,
      title: feature.title,
      description: feature.description
    });
  }
  $$payload.out += `<!--]--></div> <div class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.resumeBuilder.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.resumeBuilder.description)}</p> <a href="/resume-builder" class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">Learn More `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div> <div class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t"></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let feature = each_array_2[$$index_2];
    FeatureCard($$payload, {
      icon: feature.icon,
      title: feature.title,
      description: feature.description
    });
  }
  $$payload.out += `<!--]--></div> <div class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="border-border bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l"></div> <div class="p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.coPilot.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.coPilot.description)}</p> <a href="/co-pilot" class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">Learn More `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
    let feature = each_array_3[$$index_3];
    FeatureCard($$payload, {
      icon: feature.icon,
      title: feature.title,
      description: feature.description
    });
  }
  $$payload.out += `<!--]--></div></section>`;
}
function JobCollections($$payload, $$props) {
  push();
  let isAuthenticated = fallback($$props["isAuthenticated"], false);
  $$payload.out += `<section class="text-foreground px-6 py-16"><div class="grid-cols-0 grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-8"><div class="group col-span-2 row-span-4 flex flex-col justify-center gap-8 p-6 text-left transition-all md:flex-col"><div class="flex flex-col gap-2"><h2 class="text-3xl font-bold">Explore Job Collections</h2> <p class="text-muted-foreground max-w-2xs text-lg">Discover curated job collections for popular career paths</p></div> <a${attr("href", isAuthenticated ? "/dashboard/jobs" : "/jobs")} class="border-border text-foreground hover:bg-primary hover:text-primary-foreground inline-flex w-[200px] items-center rounded-md border px-6 py-3 text-base font-medium shadow-sm transition-colors">View All Jobs `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div> `;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex h-[400px] items-center justify-center"><div class="h-8 w-8 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>`;
  }
  $$payload.out += `<!--]--></div></section>`;
  bind_props($$props, { isAuthenticated });
  pop();
}
function CTASection($$payload) {
  $$payload.out += `<section class="py-16"><div class="container mx-auto px-4"><div class="bg-primary text-primary-foreground rounded-2xl p-12 text-center"><h2 class="mb-6 text-3xl font-bold md:text-4xl">Ready to Supercharge Your Job Search?</h2> <p class="mx-auto mb-8 max-w-2xl text-xl opacity-90">Join thousands of job seekers who have landed their dream jobs faster with Auto Apply.</p> <div class="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"><button class="bg-background text-foreground hover:bg-muted rounded-md px-8 py-6 text-lg transition-colors">Get Started Free</button> <button class="border-primary-foreground hover:bg-primary-foreground/20 group flex items-center rounded-md border px-8 py-6 text-lg transition-colors">See How It Works `;
  Arrow_right($$payload, {
    class: "ml-2 h-5 w-5 transition-transform group-hover:translate-x-1"
  });
  $$payload.out += `<!----></button></div> <p class="text-primary-foreground/80 mt-6 text-sm">No credit card required. 7-day free trial on all plans.</p></div></div></section>`;
}
function _page($$payload, $$props) {
  push();
  let userData;
  let data = $$props["data"];
  userData = data.user;
  SEO($$payload, {
    title: "Hirli - Automate Your Job Applications",
    description: "Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress.",
    keywords: "job application, automation, resume, job search, AI, career",
    url: "https://hirli.com",
    image: "/assets/og-image.jpg"
  });
  $$payload.out += `<!----> `;
  HeroSection($$payload);
  $$payload.out += `<!----> `;
  CompanySection($$payload);
  $$payload.out += `<!----> `;
  FeaturesSection($$payload);
  $$payload.out += `<!----> `;
  ServicesSection($$payload);
  $$payload.out += `<!----> `;
  TestimonialsSection($$payload);
  $$payload.out += `<!----> `;
  JobCollections($$payload, { isAuthenticated: !!userData });
  $$payload.out += `<!----> `;
  CTASection($$payload);
  $$payload.out += `<!---->`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-igZzE8WA.js.map
