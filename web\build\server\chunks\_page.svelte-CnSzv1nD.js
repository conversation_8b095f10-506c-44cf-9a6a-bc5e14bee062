import { p as push, O as escape_html, q as pop, M as ensure_array_like, Q as bind_props, K as fallback, a0 as slot } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { B as Button } from './button-CrucCo1G.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { P as Progress } from './progress-DR0SfStT.js';
import { P as Provider, R as Root$1, T as Tooltip_trigger, a as Tooltip_content } from './index8-9uwikfBL.js';
import { A as Alert, a as Alert_title, b as Alert_description } from './alert-title-gIeEAof-.js';
import { c as createFeatureAccess } from './index13-DOBlGKWb.js';
import { o as openPricingModal } from './pricing-D13CEnfk.js';
import { T as Terminal } from './terminal-DlUSFYdt.js';
import { I as Info } from './info-Ce09B-Yv.js';
import { U as Upload } from './upload-C2KwXIf1.js';
import { c as getFeaturesByCategory, F as FEATURES } from './dynamic-registry-Cmy1Wm2Q.js';
import { F as FeatureCategory } from './features-SWeUHekJ.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './scroll-lock-BkBz2nVp.js';
import './use-grace-area.svelte-CrXiOQDy.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './index2-Cut0V_vU.js';
import './Icon-A4vzmk-O.js';
import './index4-HpJcNJHQ.js';

function FeatureGuard($$payload, $$props) {
  push();
  let featureAccess, canAccess, blockReason;
  let userData = $$props["userData"];
  let featureId = $$props["featureId"];
  let limitId = fallback($$props["limitId"], void 0);
  let showUpgradePrompt = fallback($$props["showUpgradePrompt"], true);
  function handleUpgrade() {
    openPricingModal({
      section: "pro",
      currentPlanId: userData.role || userData.subscription?.planId || "free"
    });
  }
  featureAccess = createFeatureAccess(userData);
  canAccess = limitId ? featureAccess.canPerformAction(featureId, limitId) : featureAccess.hasAccess(featureId);
  blockReason = limitId ? featureAccess.getBlockReason(featureId, limitId) : featureAccess.getBlockReason(featureId);
  if (canAccess) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", {}, null);
    $$payload.out += `<!---->`;
  } else if (showUpgradePrompt) {
    $$payload.out += "<!--[1-->";
    Alert($$payload, {
      children: ($$payload2) => {
        Terminal($$payload2, { class: "size-4" });
        $$payload2.out += `<!----> `;
        Alert_title($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->Access Restricted`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Alert_description($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(blockReason)} `;
            Button($$payload3, {
              variant: "link",
              class: "h-auto p-0",
              onclick: handleUpgrade,
              children: ($$payload4) => {
                $$payload4.out += `<!---->Upgrade your plan`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, {
    userData,
    featureId,
    limitId,
    showUpgradePrompt
  });
  pop();
}
function ResumeAnalyzerWithGuard($$payload, $$props) {
  push();
  let featureAccess, currentUsage, limitValue, isUnlimited, remainingScans, usagePercentage;
  let userData = $$props["userData"];
  function handleResumeUpload() {
    console.log("Uploading resume...");
  }
  featureAccess = createFeatureAccess(userData);
  currentUsage = userData.usage?.resume_scanner_resume_scans_per_month || 0;
  limitValue = featureAccess.getNumericLimitValue("resume_scanner", "resume_scans_per_month", 10);
  isUnlimited = limitValue === Infinity;
  remainingScans = isUnlimited ? Infinity : Math.max(0, limitValue - currentUsage);
  usagePercentage = isUnlimited ? 0 : Math.min(currentUsage / limitValue * 100, 100);
  Provider($$payload, {
    children: ($$payload2) => {
      FeatureGuard($$payload2, {
        userData,
        featureId: "resume_scanner",
        limitId: "resume_scans_per_month",
        children: ($$payload3) => {
          $$payload3.out += `<div class="space-y-4"><div class="flex items-center justify-between"><h2 class="text-2xl font-bold">Resume Analyzer</h2> `;
          if (!isUnlimited) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="flex items-center gap-2"><span class="text-muted-foreground text-sm">${escape_html(currentUsage)} / ${escape_html(limitValue)} scans used</span> `;
            Root$1($$payload3, {
              children: ($$payload4) => {
                Tooltip_trigger($$payload4, {
                  asChild: true,
                  children: ($$payload5) => {
                    Info($$payload5, { class: "text-muted-foreground h-4 w-4" });
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Tooltip_content($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<p class="text-sm">Resets on the 1st of each month</p>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></div> <p class="text-muted-foreground">Upload your resume to analyze it against job descriptions.</p> `;
          if (!isUnlimited) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="w-full">`;
            Progress($$payload3, { value: usagePercentage, class: "h-2" });
            $$payload3.out += `<!----></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> <div class="flex items-center justify-between"><div>`;
          Button($$payload3, {
            onclick: handleResumeUpload,
            children: ($$payload4) => {
              Upload($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Upload Resume`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> `;
          if (isUnlimited) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="text-muted-foreground text-sm">Unlimited scans available</div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<div class="text-muted-foreground text-sm">${escape_html(remainingScans)}
            ${escape_html(remainingScans === 1 ? "scan" : "scans")} remaining</div>`;
          }
          $$payload3.out += `<!--]--></div></div>`;
        },
        $$slots: { default: true }
      });
    }
  });
  bind_props($$props, { userData });
  pop();
}
function _page($$payload, $$props) {
  push();
  const userData = {
    id: "123",
    role: "casual",
    // Change this to test different plans: 'free', 'casual', 'active', 'power', etc.
    usage: {
      // Mock usage data
      resume_scanner_resume_scans_per_month: 25,
      resume_builder_resume_versions: 5,
      job_save_saved_jobs: 50,
      application_tracker_applications_per_month: 15
    }
  };
  const featureAccess = createFeatureAccess(userData);
  getFeaturesByCategory(FeatureCategory.Resume);
  const currentPlan = {
    id: userData.role,
    name: userData.role.charAt(0).toUpperCase() + userData.role.slice(1)
  };
  SEO($$payload, { title: "Features Demo" });
  $$payload.out += `<!----> <div class="container py-10"><h1 class="mb-6 text-3xl font-bold">Features Demo</h1> <p class="text-muted-foreground mb-8">This page demonstrates the feature access control system. Current plan: <strong>${escape_html(currentPlan.name)}</strong></p> <div class="grid gap-6 md:grid-cols-2">`;
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        class: "p-6",
        children: ($$payload3) => {
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Resume Analyzer Demo`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->This component is protected by the FeatureGuard component.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        class: "p-6 pt-0",
        children: ($$payload3) => {
          ResumeAnalyzerWithGuard($$payload3, { userData });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        class: "p-6",
        children: ($$payload3) => {
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Cover Letter Generator Demo`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->This component is protected by the FeatureGuard component.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        class: "p-6 pt-0",
        children: ($$payload3) => {
          FeatureGuard($$payload3, {
            userData,
            featureId: "cover_letter_generator",
            limitId: "cover_letters_per_month",
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-4"><p class="text-muted-foreground">Generate personalized cover letters for your job applications.</p> `;
              Button($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Generate Cover Letter`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <h2 class="mb-6 mt-12 text-2xl font-bold">Available Features</h2> `;
  Root($$payload, {
    value: "all",
    children: ($$payload2) => {
      Tabs_list($$payload2, {
        class: "w-full",
        children: ($$payload3) => {
          Tabs_trigger($$payload3, {
            value: "all",
            children: ($$payload4) => {
              $$payload4.out += `<!---->All Features`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Tabs_trigger($$payload3, {
            value: "resume",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Resume Features`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Tabs_trigger($$payload3, {
            value: "job_search",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Job Search Features`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Tabs_trigger($$payload3, {
            value: "applications",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Application Features`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Tabs_content($$payload2, {
        value: "all",
        class: "mt-6",
        children: ($$payload3) => {
          const each_array = ensure_array_like(FEATURES);
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-3"><!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let feature = each_array[$$index];
            Card($$payload3, {
              class: featureAccess.hasAccess(feature.id) ? "border-green-200" : "opacity-60",
              children: ($$payload4) => {
                Card_header($$payload4, {
                  class: "p-4",
                  children: ($$payload5) => {
                    Card_title($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.name)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.description)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Card_footer($$payload4, {
                  class: "flex items-center justify-between p-4 pt-0",
                  children: ($$payload5) => {
                    $$payload5.out += `<span class="text-muted-foreground text-xs">${escape_html(feature.category)}</span> `;
                    if (featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Tabs_content($$payload2, {
        value: "resume",
        class: "mt-6",
        children: ($$payload3) => {
          const each_array_1 = ensure_array_like(getFeaturesByCategory(FeatureCategory.Resume));
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-3"><!--[-->`;
          for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
            let feature = each_array_1[$$index_2];
            Card($$payload3, {
              class: featureAccess.hasAccess(feature.id) ? "border-green-200" : "opacity-60",
              children: ($$payload4) => {
                Card_header($$payload4, {
                  class: "p-4",
                  children: ($$payload5) => {
                    Card_title($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.name)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.description)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Card_footer($$payload4, {
                  class: "flex items-center justify-between p-4 pt-0",
                  children: ($$payload5) => {
                    if (feature.limits && feature.limits.length > 0 && featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      const each_array_2 = ensure_array_like(feature.limits);
                      $$payload5.out += `<span class="text-muted-foreground text-xs"><!--[-->`;
                      for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                        let limit = each_array_2[$$index_1];
                        $$payload5.out += `<div>${escape_html(limit.name)}: ${escape_html(featureAccess.getLimitValue(feature.id, limit.id) || "N/A")}</div>`;
                      }
                      $$payload5.out += `<!--]--></span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="text-muted-foreground text-xs">No limits</span>`;
                    }
                    $$payload5.out += `<!--]--> `;
                    if (featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Tabs_content($$payload2, {
        value: "job_search",
        class: "mt-6",
        children: ($$payload3) => {
          const each_array_3 = ensure_array_like(getFeaturesByCategory(FeatureCategory.JobSearch));
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-3"><!--[-->`;
          for (let $$index_4 = 0, $$length = each_array_3.length; $$index_4 < $$length; $$index_4++) {
            let feature = each_array_3[$$index_4];
            Card($$payload3, {
              class: featureAccess.hasAccess(feature.id) ? "border-green-200" : "opacity-60",
              children: ($$payload4) => {
                Card_header($$payload4, {
                  class: "p-4",
                  children: ($$payload5) => {
                    Card_title($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.name)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.description)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Card_footer($$payload4, {
                  class: "flex items-center justify-between p-4 pt-0",
                  children: ($$payload5) => {
                    if (feature.limits && feature.limits.length > 0 && featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      const each_array_4 = ensure_array_like(feature.limits);
                      $$payload5.out += `<span class="text-muted-foreground text-xs"><!--[-->`;
                      for (let $$index_3 = 0, $$length2 = each_array_4.length; $$index_3 < $$length2; $$index_3++) {
                        let limit = each_array_4[$$index_3];
                        $$payload5.out += `<div>${escape_html(limit.name)}: ${escape_html(featureAccess.getLimitValue(feature.id, limit.id) || "N/A")}</div>`;
                      }
                      $$payload5.out += `<!--]--></span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="text-muted-foreground text-xs">No limits</span>`;
                    }
                    $$payload5.out += `<!--]--> `;
                    if (featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Tabs_content($$payload2, {
        value: "applications",
        class: "mt-6",
        children: ($$payload3) => {
          const each_array_5 = ensure_array_like(getFeaturesByCategory(FeatureCategory.Applications));
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-3"><!--[-->`;
          for (let $$index_6 = 0, $$length = each_array_5.length; $$index_6 < $$length; $$index_6++) {
            let feature = each_array_5[$$index_6];
            Card($$payload3, {
              class: featureAccess.hasAccess(feature.id) ? "border-green-200" : "opacity-60",
              children: ($$payload4) => {
                Card_header($$payload4, {
                  class: "p-4",
                  children: ($$payload5) => {
                    Card_title($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.name)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(feature.description)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Card_footer($$payload4, {
                  class: "flex items-center justify-between p-4 pt-0",
                  children: ($$payload5) => {
                    if (feature.limits && feature.limits.length > 0 && featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      const each_array_6 = ensure_array_like(feature.limits);
                      $$payload5.out += `<span class="text-muted-foreground text-xs"><!--[-->`;
                      for (let $$index_5 = 0, $$length2 = each_array_6.length; $$index_5 < $$length2; $$index_5++) {
                        let limit = each_array_6[$$index_5];
                        $$payload5.out += `<div>${escape_html(limit.name)}: ${escape_html(featureAccess.getLimitValue(feature.id, limit.id) || "N/A")}</div>`;
                      }
                      $$payload5.out += `<!--]--></span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="text-muted-foreground text-xs">No limits</span>`;
                    }
                    $$payload5.out += `<!--]--> `;
                    if (featureAccess.hasAccess(feature.id)) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<span class="rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-600">Available</span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">Unavailable</span>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CnSzv1nD.js.map
