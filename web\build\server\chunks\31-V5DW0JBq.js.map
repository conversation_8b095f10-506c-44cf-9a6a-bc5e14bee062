{"version": 3, "file": "31-V5DW0JBq.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/builder/_id_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/31.js"], "sourcesContent": ["import { r as redirect } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { s as superValidate } from \"../../../../../chunks/superValidate.js\";\nimport \"ts-deepmerge\";\nimport \"memoize-weak\";\nimport { z as zod } from \"../../../../../chunks/zod.js\";\nimport { a as designFormSchema, d as designDefaultValues, r as resumeFormSchema } from \"../../../../../chunks/buildResume.js\";\nconst load = async ({ params, cookies, locals }) => {\n  const token = cookies.get(\"auth_token\");\n  const user = token && verifySessionToken(token);\n  if (!user) throw redirect(302, \"/auth/sign-in\");\n  const id = params.id;\n  console.log(\"Looking up with ID:\", id);\n  let resume = await prisma.resume.findUnique({\n    where: { id },\n    include: {\n      document: {\n        include: {\n          profile: true\n        }\n      }\n    }\n  });\n  if (!resume) {\n    console.log(\"Resume not found by ID, trying to find by document ID\");\n    const document = await prisma.document.findUnique({\n      where: { id },\n      include: {\n        profile: true\n      }\n    });\n    if (document) {\n      console.log(\"Document found, looking for associated resume\");\n      resume = await prisma.resume.findUnique({\n        where: { documentId: document.id },\n        include: {\n          document: {\n            include: {\n              profile: true\n            }\n          }\n        }\n      });\n    }\n  }\n  console.log(\"Resume found:\", resume ? \"Yes\" : \"No\");\n  if (!resume) {\n    console.log(\"No resume found for ID:\", id);\n    throw redirect(302, \"/dashboard/documents\");\n  }\n  console.log(\"Resume document userId:\", resume.document.userId);\n  console.log(\"Current user id:\", user.id);\n  const design = await superValidate(designDefaultValues, zod(designFormSchema));\n  let initialResumeData = resume.parsedData || {};\n  console.log(\"Initial resume data:\", JSON.stringify(initialResumeData));\n  const defaultData = {\n    header: {\n      name: \"\",\n      email: \"\",\n      phone: \"\"\n    },\n    summary: {\n      content: \"\"\n    },\n    experience: [],\n    education: [],\n    skills: [],\n    projects: [],\n    certifications: []\n  };\n  let needsUpdate = false;\n  if (!initialResumeData.header) {\n    console.log(\"Resume data missing header, using default\");\n    initialResumeData.header = defaultData.header;\n    needsUpdate = true;\n  }\n  if (!initialResumeData.summary) {\n    console.log(\"Resume data missing summary, using default\");\n    initialResumeData.summary = defaultData.summary;\n    needsUpdate = true;\n  }\n  if (!initialResumeData.experience) {\n    console.log(\"Resume data missing experience, using default\");\n    initialResumeData.experience = defaultData.experience;\n    needsUpdate = true;\n  }\n  if (!initialResumeData.education) {\n    console.log(\"Resume data missing education, using default\");\n    initialResumeData.education = defaultData.education;\n    needsUpdate = true;\n  }\n  if (!initialResumeData.skills) {\n    console.log(\"Resume data missing skills, using default\");\n    initialResumeData.skills = defaultData.skills;\n    needsUpdate = true;\n  }\n  if (!initialResumeData.projects) {\n    console.log(\"Resume data missing projects, using default\");\n    initialResumeData.projects = defaultData.projects;\n    needsUpdate = true;\n  }\n  if (!initialResumeData.certifications) {\n    console.log(\"Resume data missing certifications, using default\");\n    initialResumeData.certifications = defaultData.certifications;\n    needsUpdate = true;\n  }\n  if (needsUpdate) {\n    console.log(\"Updating resume with default structure\");\n    await prisma.resume.update({\n      where: { id: resume.id },\n      data: {\n        parsedData: initialResumeData\n      }\n    });\n  }\n  const resumeForm = await superValidate(initialResumeData, zod(resumeFormSchema));\n  return {\n    form: {\n      resume: resumeForm,\n      design\n    },\n    resumeData: resume,\n    user\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/builder/_id_/_page.server.ts.js';\n\nexport const index = 31;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/builder/_id_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/builder/[id]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/31.DKwtFJp0.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/Dx8hstp8.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/BAWahpTV.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/DDUgF6Ik.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/CPe_16wQ.js\",\"_app/immutable/chunks/FAbXdqfL.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/BuYRPDDz.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/C3y1xd2Y.js\",\"_app/immutable/chunks/BoNCRmBc.js\",\"_app/immutable/chunks/DRGimm5x.js\",\"_app/immutable/chunks/CrpvsheG.js\",\"_app/immutable/chunks/lZwfPN85.js\",\"_app/immutable/chunks/VNuMAkuB.js\",\"_app/immutable/chunks/CTX2GAsz.js\",\"_app/immutable/chunks/Dd0-sqM0.js\",\"_app/immutable/chunks/CrHU05dq.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/7AwcL9ec.js\",\"_app/immutable/chunks/6UJoWgvL.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/Dc4vaUpe.js\",\"_app/immutable/chunks/DKxpkEzC.js\",\"_app/immutable/chunks/CWmzcjye.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/0ykhD7u6.js\",\"_app/immutable/chunks/FeejBSkx.js\",\"_app/immutable/chunks/C8B1VUaq.js\",\"_app/immutable/chunks/WD4kvFhR.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/hrXlVaSN.js\",\"_app/immutable/chunks/BHzYYMdu.js\",\"_app/immutable/chunks/8b74MdfD.js\",\"_app/immutable/chunks/Z6UAQTuv.js\",\"_app/immutable/chunks/Dz4exfp3.js\",\"_app/immutable/chunks/DumgozFE.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/scroll-area.bHHIbcsu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAQA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACpD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC;AACjD,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACjD,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE;AACtB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,EAAE,CAAC;AACxC,EAAE,IAAI,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC9C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE;AACjB,IAAI,OAAO,EAAE;AACb,MAAM,QAAQ,EAAE;AAChB,QAAQ,OAAO,EAAE;AACjB,UAAU,OAAO,EAAE;AACnB;AACA;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC;AACxE,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC;AAClE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC9C,QAAQ,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC1C,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE;AACpB,YAAY,OAAO,EAAE;AACrB,cAAc,OAAO,EAAE;AACvB;AACA;AACA;AACA,OAAO,CAAC;AACR;AACA;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;AACrD,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,EAAE,CAAC;AAC9C,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,sBAAsB,CAAC;AAC/C;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAChE,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC;AAC1C,EAAE,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,mBAAmB,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAChF,EAAE,IAAI,iBAAiB,GAAG,MAAM,CAAC,UAAU,IAAI,EAAE;AACjD,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AACxE,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,MAAM,EAAE;AACZ,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,KAAK,EAAE;AACb,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE;AACf,KAAK;AACL,IAAI,UAAU,EAAE,EAAE;AAClB,IAAI,SAAS,EAAE,EAAE;AACjB,IAAI,MAAM,EAAE,EAAE;AACd,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,cAAc,EAAE;AACpB,GAAG;AACH,EAAE,IAAI,WAAW,GAAG,KAAK;AACzB,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AACjC,IAAI,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AAC5D,IAAI,iBAAiB,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM;AACjD,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;AAClC,IAAI,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;AAC7D,IAAI,iBAAiB,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;AACnD,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;AACrC,IAAI,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC;AAChE,IAAI,iBAAiB,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU;AACzD,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;AACpC,IAAI,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC;AAC/D,IAAI,iBAAiB,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS;AACvD,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AACjC,IAAI,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AAC5D,IAAI,iBAAiB,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM;AACjD,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;AACnC,IAAI,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC;AAC9D,IAAI,iBAAiB,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ;AACrD,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE;AACzC,IAAI,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC;AACpE,IAAI,iBAAiB,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc;AACjE,IAAI,WAAW,GAAG,IAAI;AACtB;AACA,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;AACzD,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK,CAAC;AACN;AACA,EAAE,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,iBAAiB,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAClF,EAAE,OAAO;AACT,IAAI,IAAI,EAAE;AACV,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM;AACN,KAAK;AACL,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;AC3HW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAyD,CAAC,EAAE;AAEvH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACztG,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,gDAAgD;AAC7G,MAAC,KAAK,GAAG;;;;"}