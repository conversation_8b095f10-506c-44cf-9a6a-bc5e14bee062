import { p as push, q as pop, O as escape_html, J as attr_class, P as stringify, M as ensure_array_like, N as attr, $ as attr_style, aa as maybe_selected } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { B as Button } from './button-CrucCo1G.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Chart_container, B as BarChart, a as Chart_tooltip } from './chart-tooltip-RMuhks-A.js';
import 'date-fns';
import './index-DwwMqnhu.js';
import 'clsx';
import { T as Trending_up } from './trending-up-BKR_Sbhj.js';
import { T as Trending_down, M as Minus } from './trending-down-CJ3RECgj.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { P as Progress } from './progress-DR0SfStT.js';
import { B as Badge } from './badge-C9pSznab.js';
import { A as Activity } from './activity-BF3HC42u.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { F as FeatureCategory } from './features-SWeUHekJ.js';
import { g as goto } from './client-dNyMPa8V.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { C as Chevron_down } from './chevron-down-xGjWLrZH.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './html-FW6Ia4bL.js';
import './clone-BRGVxGEr.js';
import 'svelte/store';
import './Icon-A4vzmk-O.js';

function UsageHistoryChart($$payload, $$props) {
  push();
  const {
    featureId,
    limitId,
    title = "Usage History",
    description = "Track your usage over time",
    periodsToShow = 6,
    initialPeriodType = "monthly"
  } = $$props;
  let periodType = initialPeriodType;
  let loading = true;
  let error = null;
  let usageData = [];
  const chartConfig = {
    used: { label: "Usage", color: "var(--chart-1)" },
    limit: { label: "Limit", color: "var(--chart-5)" }
  };
  const chartData = () => {
    return usageData.map((item) => ({
      period: formatDate(item.date, periodType),
      used: item.used,
      limit: item.limit || 0
    }));
  };
  let trendData = () => {
    if (usageData.length < 2) return null;
    const current = usageData[usageData.length - 1]?.used || 0;
    const previous = usageData[usageData.length - 2]?.used || 0;
    if (previous === 0) return null;
    const change = current - previous;
    const percentChange = Math.round(change / previous * 100);
    return {
      change,
      percentChange,
      direction: change > 0 ? "up" : change < 0 ? "down" : "stable"
    };
  };
  function formatDate(date, type) {
    if (type === "daily") {
      return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
    } else if (type === "weekly") {
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      return `${weekStart.toLocaleDateString("en-US", { month: "short", day: "numeric" })} - ${weekEnd.toLocaleDateString("en-US", { month: "short", day: "numeric" })}`;
    } else {
      return date.toLocaleDateString("en-US", { month: "short", year: "numeric" });
    }
  }
  function generatePeriods(type, count) {
    const result = [];
    const now = /* @__PURE__ */ new Date();
    for (let i = 0; i < count; i++) {
      const date = /* @__PURE__ */ new Date();
      if (type === "daily") {
        date.setDate(now.getDate() - i);
        const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
        result.unshift({ date, period });
      } else if (type === "weekly") {
        date.setDate(now.getDate() - i * 7);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const period = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getDate() + weekStart.getDay()) / 7)}`;
        result.unshift({ date: weekStart, period });
      } else {
        date.setMonth(now.getMonth() - i);
        const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
        result.unshift({ date, period });
      }
    }
    return result;
  }
  async function fetchUsageData() {
    try {
      loading = true;
      error = null;
      if (!featureId || !limitId) {
        usageData = [];
        loading = false;
        return;
      }
      const periodsList = generatePeriods(periodType, periodsToShow);
      const response = await fetch(`/api/feature-usage?featureId=${featureId}&limitId=${limitId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch usage data");
      }
      const data = await response.json();
      usageData = periodsList.map(({ date, period }) => {
        const usage = data.find((d) => d.period === period);
        return {
          date,
          period,
          used: usage ? usage.used : 0,
          limit: usage ? usage.limit : null
        };
      });
    } catch (err) {
      console.error("Error fetching usage data:", err);
      error = err.message;
    } finally {
      loading = false;
    }
  }
  $$payload.out += `<!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-center justify-between"><div><div class="flex items-center gap-3"><div><!---->`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(title)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(description)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> `;
          if (trendData()) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="flex items-center gap-1 text-sm">`;
            if (trendData().direction === "up") {
              $$payload3.out += "<!--[-->";
              Trending_up($$payload3, { class: "h-4 w-4 text-green-500" });
              $$payload3.out += `<!----> <span class="font-medium text-green-600">+${escape_html(trendData().percentChange)}%</span>`;
            } else if (trendData().direction === "down") {
              $$payload3.out += "<!--[1-->";
              Trending_down($$payload3, { class: "h-4 w-4 text-red-500" });
              $$payload3.out += `<!----> <span class="font-medium text-red-600">${escape_html(trendData().percentChange)}%</span>`;
            } else {
              $$payload3.out += "<!--[!-->";
              Minus($$payload3, { class: "h-4 w-4 text-gray-500" });
              $$payload3.out += `<!----> <span class="font-medium text-gray-600">No change</span>`;
            }
            $$payload3.out += `<!--]--></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></div></div> <div class="flex items-center space-x-2"><select class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2">`;
          $$payload3.select_value = periodType;
          $$payload3.out += `<option value="daily"${maybe_selected($$payload3, "daily")}>Daily</option><option value="weekly"${maybe_selected($$payload3, "weekly")}>Weekly</option><option value="monthly"${maybe_selected($$payload3, "monthly")}>Monthly</option>`;
          $$payload3.select_value = void 0;
          $$payload3.out += `</select> `;
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: fetchUsageData,
            disabled: loading,
            children: ($$payload4) => {
              Loader_circle($$payload4, {
                class: `h-4 w-4 ${loading ? "animate-spin" : ""}`
              });
              $$payload4.out += `<!----> <span class="sr-only">Refresh</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          if (loading) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="flex justify-center py-8">`;
            Loader_circle($$payload3, { class: "text-primary h-8 w-8 animate-spin" });
            $$payload3.out += `<!----></div>`;
          } else if (error) {
            $$payload3.out += "<!--[1-->";
            $$payload3.out += `<div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm"><p>Error loading usage data: ${escape_html(error)}</p></div>`;
          } else if (usageData.length === 0) {
            $$payload3.out += "<!--[2-->";
            $$payload3.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available.</p></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<!---->`;
            Chart_container($$payload3, {
              config: chartConfig,
              class: "h-60 w-full",
              children: ($$payload4) => {
                {
                  let tooltip = function($$payload5) {
                    $$payload5.out += `<!---->`;
                    Chart_tooltip($$payload5, {});
                    $$payload5.out += `<!---->`;
                  };
                  BarChart($$payload4, {
                    data: chartData(),
                    x: "period",
                    axis: "x",
                    legend: true,
                    series: [
                      {
                        key: "used",
                        label: chartConfig.used.label,
                        color: chartConfig.used.color
                      }
                    ],
                    props: {
                      xAxis: { format: (d) => d.slice(0, 6) }
                    },
                    tooltip,
                    $$slots: { tooltip: true }
                  });
                }
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <div class="text-muted-foreground mt-4 flex items-center justify-between text-sm"><div class="flex items-center gap-4"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded" style="background-color: var(--chart-1);"></div> <span>Usage</span></div> `;
            if (usageData.some((d) => d.limit !== null && d.limit > 0)) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="flex items-center gap-2"><div class="h-0.5 w-4 opacity-70" style="background-color: var(--chart-5); border-top: 2px dashed;"></div> <span>Limit</span></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--></div> <div>Total: ${escape_html(usageData.reduce((sum, d) => sum + d.used, 0))} uses</div></div>`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function UsageOverview($$payload, $$props) {
  push();
  const { usageTrends, featuresData = [] } = $$props;
  let stats = () => {
    const totalUsage = featuresData.reduce(
      (sum, feature) => {
        return sum + (feature.usage?.reduce((featureSum, usage) => featureSum + (usage.used || 0), 0) || 0);
      },
      0
    );
    const featuresWithUsage = featuresData.filter((feature) => feature.usage?.some((usage) => usage.used > 0)).length;
    const featuresNearLimit = featuresData.filter((feature) => feature.usage?.some((usage) => usage.limit && typeof usage.limit === "number" && usage.percentUsed >= 80)).length;
    const featuresAtLimit = featuresData.filter((feature) => feature.usage?.some((usage) => usage.limit && typeof usage.limit === "number" && usage.used >= usage.limit)).length;
    return {
      totalUsage,
      featuresWithUsage,
      featuresNearLimit,
      featuresAtLimit,
      totalFeatures: featuresData.length
    };
  };
  function getTrendDisplay(direction, percent) {
    if (direction === "up") {
      return {
        icon: Trending_up,
        color: "text-green-600",
        bgColor: "bg-green-50",
        text: `+${percent}%`
      };
    } else if (direction === "down") {
      return {
        icon: Trending_down,
        color: "text-red-600",
        bgColor: "bg-red-50",
        text: `${percent}%`
      };
    } else {
      return {
        icon: Minus,
        color: "text-gray-600",
        bgColor: "bg-gray-50",
        text: "No change"
      };
    }
  }
  const trendDisplay = () => usageTrends ? getTrendDisplay(usageTrends.trendDirection, usageTrends.trendPercent) : null;
  $$payload.out += `<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4"><!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        class: "p-6",
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">Total Usage</p> <p class="text-2xl font-bold">${escape_html(stats.totalUsage)}</p> <p class="text-muted-foreground mt-1 text-xs">This month</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50">`;
          Activity($$payload3, { class: "h-6 w-6 text-blue-600" });
          $$payload3.out += `<!----></div></div> `;
          if (trendDisplay) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="mt-4 flex items-center gap-2"><div${attr_class(`flex items-center gap-1 rounded-full px-2 py-1 ${stringify(trendDisplay.bgColor)}`)}>`;
            if (trendDisplay.icon) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<!---->`;
              trendDisplay.icon?.($$payload3, {
                class: `h-3 w-3 ${stringify(trendDisplay.color)}`
              });
              $$payload3.out += `<!---->`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--> <span${attr_class(`text-xs font-medium ${stringify(trendDisplay.color)}`)}>${escape_html(trendDisplay.text)}</span></div> <span class="text-muted-foreground text-xs">vs last month</span></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        class: "p-6",
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">Active Features</p> <p class="text-2xl font-bold">${escape_html(stats.featuresWithUsage)}</p> <p class="text-muted-foreground mt-1 text-xs">of ${escape_html(stats.totalFeatures)} available</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-green-50">`;
          Circle_check_big($$payload3, { class: "h-6 w-6 text-green-600" });
          $$payload3.out += `<!----></div></div> <div class="mt-4">`;
          Progress($$payload3, {
            value: stats.featuresWithUsage / stats.totalFeatures * 100,
            class: "h-2"
          });
          $$payload3.out += `<!----></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        class: "p-6",
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">Near Limits</p> <p class="text-2xl font-bold">${escape_html(stats().featuresNearLimit)}</p> <p class="text-muted-foreground mt-1 text-xs">features at 80%+</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-50">`;
          Clock($$payload3, { class: "h-6 w-6 text-yellow-600" });
          $$payload3.out += `<!----></div></div> `;
          if (stats().featuresNearLimit > 0) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="mt-4">`;
            Badge($$payload3, {
              variant: "outline",
              class: "border-yellow-200 text-yellow-700",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Action needed`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        class: "p-6",
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex items-center justify-between"><div><p class="text-muted-foreground text-sm font-medium">At Limits</p> <p class="text-2xl font-bold">${escape_html(stats().featuresAtLimit)}</p> <p class="text-muted-foreground mt-1 text-xs">features maxed out</p></div> <div class="flex h-12 w-12 items-center justify-center rounded-full bg-red-50">`;
          Triangle_alert($$payload3, { class: "h-6 w-6 text-red-600" });
          $$payload3.out += `<!----></div></div> `;
          if (stats().featuresAtLimit > 0) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="mt-4">`;
            Badge($$payload3, {
              variant: "destructive",
              class: "text-xs",
              children: ($$payload4) => {
                $$payload4.out += `<!---->Upgrade needed`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> `;
  if (usageTrends) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    Card($$payload, {
      class: "mt-6",
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Card_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Monthly Comparison`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Compare your usage with the previous month`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Card_content($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<div class="grid gap-4 md:grid-cols-2"><div class="space-y-2"><p class="text-muted-foreground text-sm font-medium">Current Month</p> <p class="text-3xl font-bold">${escape_html(usageTrends.currentMonthTotal)}</p> <p class="text-muted-foreground text-sm">Total uses</p></div> <div class="space-y-2"><p class="text-muted-foreground text-sm font-medium">Previous Month</p> <p class="text-3xl font-bold">${escape_html(usageTrends.previousMonthTotal)}</p> <p class="text-muted-foreground text-sm">Total uses</p></div></div> `;
            if (trendDisplay) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div${attr_class(`mt-6 rounded-lg p-4 ${stringify(trendDisplay()?.bgColor)}`)}><div class="flex items-center gap-3"><!---->`;
              trendDisplay()?.icon?.($$payload3, {
                class: `h-5 w-5 ${stringify(trendDisplay()?.color)}`
              });
              $$payload3.out += `<!----> <div><p${attr_class(`font-medium ${stringify(trendDisplay()?.color)}`)}>${escape_html(usageTrends.trendDirection === "up" ? "Usage increased" : usageTrends.trendDirection === "down" ? "Usage decreased" : "Usage remained stable")}</p> <p${attr_class(`text-sm ${stringify(trendDisplay()?.color)} opacity-80`)}>${escape_html(trendDisplay()?.text)} compared to last month</p></div></div></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function UsageDistributionChart($$payload, $$props) {
  push();
  const {
    featuresData = [],
    title = "Usage Distribution",
    description = "See how your usage is distributed across features"
  } = $$props;
  const chartData = () => {
    const data = featuresData.map((feature) => {
      const totalUsage2 = feature.usage?.reduce((sum, usage) => sum + (usage.used || 0), 0) || 0;
      return {
        name: feature.name,
        usage: totalUsage2,
        category: feature.category,
        color: getCategoryColor(feature.category)
      };
    }).filter((item) => item.usage > 0).sort((a, b) => b.usage - a.usage);
    const total = data.reduce((sum, item) => sum + item.usage, 0);
    return data.map((item) => ({
      ...item,
      percentage: total > 0 ? Math.round(item.usage / total * 100) : 0
    }));
  };
  function getCategoryColor(category) {
    const colors = {
      core: "#3b82f6",
      resume: "#10b981",
      job_search: "#f59e0b",
      applications: "#8b5cf6",
      analytics: "#ef4444",
      team: "#06b6d4",
      integration: "#84cc16",
      communication: "#f97316",
      automation: "#ec4899",
      security: "#6b7280",
      customization: "#14b8a6",
      advanced: "#7c3aed"
    };
    return colors[category] || "#6b7280";
  }
  const size = 200;
  const strokeWidth = 20;
  const radius = (size - strokeWidth) / 2;
  const arcs = () => {
    let cumulativePercentage = 0;
    return chartData().map((item) => {
      const startAngle = cumulativePercentage / 100 * 360 - 90;
      const endAngle = (cumulativePercentage + item.percentage) / 100 * 360 - 90;
      cumulativePercentage += item.percentage;
      const startAngleRad = startAngle * Math.PI / 180;
      const endAngleRad = endAngle * Math.PI / 180;
      const x1 = size / 2 + radius * Math.cos(startAngleRad);
      const y1 = size / 2 + radius * Math.sin(startAngleRad);
      const x2 = size / 2 + radius * Math.cos(endAngleRad);
      const y2 = size / 2 + radius * Math.sin(endAngleRad);
      const largeArcFlag = item.percentage > 50 ? 1 : 0;
      const pathData = [
        `M ${size / 2} ${size / 2}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        "Z"
      ].join(" ");
      return { ...item, pathData, startAngle, endAngle };
    });
  };
  const totalUsage = () => chartData().reduce((sum, item) => sum + item.usage, 0);
  $$payload.out += `<!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(title)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(description)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          if (chartData.length === 0) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available to display.</p></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            const each_array = ensure_array_like(arcs());
            const each_array_1 = ensure_array_like(chartData());
            $$payload3.out += `<div class="flex flex-col items-center gap-8 lg:flex-row"><div class="relative"><svg${attr("width", size)}${attr("height", size)} class="-rotate-90 transform"><circle${attr("cx", size / 2)}${attr("cy", size / 2)}${attr("r", radius)} fill="none" stroke="currentColor"${attr("stroke-width", strokeWidth)} stroke-opacity="0.1"></circle><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let arc = each_array[$$index];
              $$payload3.out += `<path${attr("d", arc.pathData)}${attr("fill", arc.color)} opacity="0.8" class="transition-opacity hover:opacity-100"><title>${escape_html(arc.name)}: ${escape_html(arc.usage)} uses (${escape_html(arc.percentage)}%)</title></path>`;
            }
            $$payload3.out += `<!--]--></svg> <div class="absolute inset-0 flex flex-col items-center justify-center"><div class="text-2xl font-bold">${escape_html(totalUsage)}</div> <div class="text-muted-foreground text-sm">Total Uses</div></div></div> <div class="flex-1 space-y-3"><!--[-->`;
            for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
              let item = each_array_1[$$index_1];
              $$payload3.out += `<div class="flex items-center justify-between rounded-lg border p-3"><div class="flex items-center gap-3"><div class="h-4 w-4 rounded-full"${attr_style(`background-color: ${stringify(item.color)}`)}></div> <div><p class="font-medium">${escape_html(item.name)}</p> <p class="text-muted-foreground text-sm capitalize">${escape_html(item.category.replace("_", " "))}</p></div></div> <div class="text-right"><p class="font-semibold">${escape_html(item.usage)}</p> `;
              Badge($$payload3, {
                variant: "outline",
                class: "text-xs",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(item.percentage)}%`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div></div>`;
            }
            $$payload3.out += `<!--]--></div></div> <div class="mt-6 grid grid-cols-2 gap-4 md:grid-cols-4"><div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold">${escape_html(chartData.length)}</p> <p class="text-muted-foreground text-sm">Active Features</p></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold">${escape_html(totalUsage)}</p> <p class="text-muted-foreground text-sm">Total Uses</p></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold">${escape_html(chartData().length > 0 ? Math.round(totalUsage() / chartData().length) : 0)}</p> <p class="text-muted-foreground text-sm">Avg per Feature</p></div> <div class="bg-muted/50 rounded-lg p-3 text-center"><p class="text-lg font-bold">${escape_html(chartData.length > 0 ? chartData[0].name.slice(0, 8) + "..." : "N/A")}</p> <p class="text-muted-foreground text-sm">Most Used</p></div></div>`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function _page($$payload, $$props) {
  push();
  const { $$slots, $$events, ...props } = $$props;
  let user = props?.data?.user || {};
  let initialFeatureData = props?.data?.featureUsage || [];
  let usageTrends = props?.data?.usageTrends || {
    currentMonthTotal: 0,
    previousMonthTotal: 0,
    trendPercent: 0,
    trendDirection: "stable"
  };
  let usageData = [];
  let featuresData = initialFeatureData;
  let categorizedFeatures = {};
  let usageSummary = null;
  let isLoading = true;
  let error = null;
  let selectedFeatureId = "";
  let selectedLimitId = "";
  let timeRange = "30d";
  let selectedFeature = featuresData.find((f) => f.id === selectedFeatureId);
  function exportUsageData() {
    try {
      const csvData = featuresData.map((feature) => {
        const usage = feature.usage || [];
        return usage.map((u) => ({
          Feature: feature.name,
          Category: feature.category,
          Limit: u.limitName,
          Used: u.used,
          Limit_Value: u.limit,
          Remaining: u.remaining,
          Percentage: u.percentUsed,
          Period: u.period
        }));
      }).flat();
      if (csvData.length === 0) {
        alert("No usage data to export");
        return;
      }
      const headers = Object.keys(csvData[0]);
      const csvContent = [
        headers.join(","),
        ...csvData.map((row) => headers.map((header) => `"${row[header] || ""}"`).join(","))
      ].join("\n");
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `usage-data-${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error2) {
      console.error("Error exporting data:", error2);
      alert("Failed to export data");
    }
  }
  loadUsageData();
  async function loadUsageData() {
    isLoading = true;
    error = null;
    try {
      if (!user.id) {
        throw new Error("User ID is required to fetch usage data");
      }
      if (props?.data?.featureUsage) {
        featuresData = props.data.featureUsage;
        console.log("Using server-side feature usage data:", featuresData);
        const totalFeatures = featuresData.length;
        const featuresUsed = featuresData.filter((f) => f.usage && f.usage.length > 0 && f.usage.some((u) => u.used > 0 && !u.placeholder)).length;
        const featuresWithLimits = featuresData.filter((f) => f.usage && f.usage.length > 0 && f.usage.some((u) => u.limit !== "unlimited")).length;
        const featuresAtLimit = featuresData.filter((f) => f.usage && f.usage.length > 0 && f.usage.some((u) => !u.placeholder && u.limit !== "unlimited" && typeof u.limit === "number" && u.used >= u.limit)).length;
        const topFeatures = featuresData.filter((f) => f.usage && f.usage.length > 0 && f.usage.some((u) => !u.placeholder && u.used > 0)).map((f) => {
          const highestUsage = f.usage.filter((u) => !u.placeholder).reduce(
            (highest, current) => {
              if (current.percentUsed === void 0) return highest;
              if (!highest || highest.percentUsed === void 0) return current;
              return current.percentUsed > highest.percentUsed ? current : highest;
            },
            null
          ) || f.usage[0];
          return {
            featureId: f.id,
            featureName: f.name,
            used: highestUsage.used,
            limit: highestUsage.limit,
            percentUsed: highestUsage.percentUsed
          };
        }).filter((f) => f.percentUsed !== void 0).sort((a, b) => (b.percentUsed || 0) - (a.percentUsed || 0)).slice(0, 5);
        usageSummary = {
          totalFeatures,
          featuresUsed,
          featuresWithLimits,
          featuresAtLimit,
          topFeatures
        };
        console.log("Calculated usage summary:", usageSummary);
      } else {
        console.warn("No feature usage data available from server");
        featuresData = [];
        usageSummary = {
          totalFeatures: 0,
          featuresUsed: 0,
          featuresWithLimits: 0,
          featuresAtLimit: 0,
          topFeatures: []
        };
      }
      categorizedFeatures = featuresData.reduce(
        (acc, feature) => {
          const category = feature.category;
          if (!acc[category]) {
            acc[category] = [];
          }
          acc[category].push(feature);
          return acc;
        },
        {}
      );
      usageData = featuresData.flatMap((feature) => feature.usage?.map((usage) => ({
        id: `${feature.id}-${usage.limitId}`,
        featureId: feature.id,
        featureName: feature.name,
        limitId: usage.limitId,
        limitName: usage.limitName,
        used: usage.used,
        limit: usage.limit,
        remaining: usage.remaining,
        percentUsed: usage.percentUsed,
        period: usage.period
      })) || []);
      if (!selectedFeatureId && featuresData.length > 0) {
        const featureWithUsage = featuresData.find((f) => f.usage && f.usage.length > 0);
        if (featureWithUsage) {
          selectedFeatureId = featureWithUsage.id;
          if (featureWithUsage.usage && featureWithUsage.usage.length > 0) {
            selectedLimitId = featureWithUsage.usage[0].limitId;
          }
        } else if (featuresData.length > 0) {
          selectedFeatureId = featuresData[0].id;
        }
      }
      if (!error) {
        if (featuresData.length === 0) {
          error = "No feature usage data found. This could be because you haven't used any features yet, or because the feature usage tracking system is still being set up.";
        } else if (featuresData.every((f) => !f.usage || f.usage.length === 0)) {
          error = "Features are available but no usage data has been recorded yet. Start using the application to see your usage statistics.";
        } else if (featuresData.every((f) => f.usage && f.usage.every((u) => u.placeholder === true))) {
          console.info("All features have placeholder usage data. No actual usage has been recorded yet.");
        }
      }
    } catch (err) {
      console.error("Error loading feature usage:", err);
      error = err.message || "Failed to load feature usage";
      featuresData = [];
      usageSummary = null;
      categorizedFeatures = {};
      usageData = [];
    } finally {
      isLoading = false;
    }
  }
  let featuresWithUsage = [];
  let featuresByCategory = () => {
    const result = {};
    const categoryNames = {
      [FeatureCategory.Core]: "Core Features",
      [FeatureCategory.JobSearch]: "Job Search",
      [FeatureCategory.Resume]: "Resume",
      [FeatureCategory.Applications]: "Applications",
      [FeatureCategory.Analytics]: "Analytics",
      [FeatureCategory.Team]: "Team",
      [FeatureCategory.Integration]: "Integration"
    };
    Object.entries(categorizedFeatures).forEach(([category, features]) => {
      const featuresWithUsage2 = features.filter((f) => f.usage && f.usage.length > 0);
      if (featuresWithUsage2.length > 0) {
        result[category] = {
          name: categoryNames[category] || category,
          features: featuresWithUsage2.sort((a, b) => a.name.localeCompare(b.name))
        };
      }
    });
    return result;
  };
  SEO($$payload, {
    title: "Feature Usage | Hirli",
    description: "Track your feature usage and subscription limits",
    keywords: "feature usage, subscription, limits, tracking",
    url: "https://hirli.com/dashboard/settings/usage"
  });
  $$payload.out += `<!----> <div class="border-border flex flex-col justify-between border-b p-6"><h2 class="text-lg font-semibold">Feature Usage</h2> <p class="text-muted-foreground text-foreground/80">Track your feature usage and subscription limits.</p></div> <div class="space-y-6">`;
  UsageOverview($$payload, { usageTrends, featuresData });
  $$payload.out += `<!----> `;
  UsageDistributionChart($$payload, { featuresData });
  $$payload.out += `<!----> <!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="mb-4 flex items-center justify-between"><div><h3 class="text-lg font-medium">Feature Usage Details</h3> <p class="text-muted-foreground text-sm">Track your usage across all features</p></div> <div class="flex gap-2">`;
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: exportUsageData,
            disabled: isLoading,
            children: ($$payload4) => {
              Calendar($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Export Data`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: loadUsageData,
            disabled: isLoading,
            children: ($$payload4) => {
              if (isLoading) {
                $$payload4.out += "<!--[-->";
                Loader_circle($$payload4, { class: "mr-2 h-4 w-4 animate-spin" });
                $$payload4.out += `<!----> Refreshing...`;
              } else {
                $$payload4.out += "<!--[!-->";
                Refresh_cw($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Refresh`;
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div></div> `;
          if (isLoading && !usageSummary) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="flex justify-center py-8">`;
            Loader_circle($$payload3, { class: "text-primary h-8 w-8 animate-spin" });
            $$payload3.out += `<!----></div>`;
          } else if (usageSummary) {
            $$payload3.out += "<!--[1-->";
            const each_array = ensure_array_like(Array(6));
            $$payload3.out += `<div class="grid gap-4 md:grid-cols-3"><div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Features Used</p> <h4 class="mt-1 text-2xl font-bold">${escape_html(usageSummary.featuresUsed)}</h4> <p class="text-muted-foreground mt-1 text-xs">of ${escape_html(usageSummary.totalFeatures)} total features</p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Features at Limit</p> <h4 class="mt-1 text-2xl font-bold">${escape_html(usageSummary.featuresAtLimit)}</h4> <p class="text-muted-foreground mt-1 text-xs">${escape_html(usageSummary.featuresAtLimit > 0 ? "Consider upgrading your plan" : "You have room to grow")}</p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Usage Efficiency</p> <h4 class="mt-1 text-2xl font-bold">${escape_html(usageSummary && usageSummary.totalFeatures > 0 ? Math.round(usageSummary.featuresUsed / usageSummary.totalFeatures * 100) + "%" : "0%")}</h4> <div class="mt-1"><div class="bg-muted h-1.5 w-full overflow-hidden rounded-full"><div class="bg-primary h-full"${attr_style(`width: ${usageSummary && usageSummary.totalFeatures > 0 ? Math.round(usageSummary.featuresUsed / usageSummary.totalFeatures * 100) : 0}%`)}></div></div> <p class="text-muted-foreground mt-1 text-xs">${escape_html(usageSummary && usageSummary.featuresUsed > 0 ? `Using ${usageSummary.featuresUsed} of ${usageSummary.totalFeatures} available features` : "No features used yet")}</p></div></div></div> <div class="mt-4 grid gap-4 md:grid-cols-3"><div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Usage Trend</p> <h4 class="mt-1 flex items-center text-2xl font-bold">${escape_html(props.data.usageTrends?.trendPercent || 0)}% `;
            if (props.data.usageTrends?.trendDirection === "up") {
              $$payload3.out += "<!--[-->";
              Trending_up($$payload3, { class: "ml-2 h-5 w-5 text-green-500" });
            } else if (props.data.usageTrends?.trendDirection === "down") {
              $$payload3.out += "<!--[1-->";
              Trending_down($$payload3, { class: "ml-2 h-5 w-5 text-red-500" });
            } else {
              $$payload3.out += "<!--[!-->";
              Minus($$payload3, { class: "ml-2 h-5 w-5 text-gray-500" });
            }
            $$payload3.out += `<!--]--></h4> <div class="mt-3 flex h-2 w-full items-center gap-1"><!--[-->`;
            for (let i = 0, $$length = each_array.length; i < $$length; i++) {
              each_array[i];
              const isActive = i < Math.min(5, Math.abs(props.data.usageTrends?.trendPercent || 0) / 20);
              const color = props.data.usageTrends?.trendDirection === "up" ? "bg-green-500" : props.data.usageTrends?.trendDirection === "down" ? "bg-red-500" : "bg-gray-300";
              $$payload3.out += `<div${attr_class(`h-full flex-1 rounded-sm ${isActive ? color : "bg-gray-200"}`)}></div>`;
            }
            $$payload3.out += `<!--]--></div> <p class="text-muted-foreground mt-2 text-xs">`;
            if (props.data.usageTrends?.trendDirection === "up") {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `Increased usage compared to last month`;
            } else if (props.data.usageTrends?.trendDirection === "down") {
              $$payload3.out += "<!--[1-->";
              $$payload3.out += `Decreased usage compared to last month`;
            } else {
              $$payload3.out += "<!--[!-->";
              $$payload3.out += `Usage stable compared to last month`;
            }
            $$payload3.out += `<!--]--></p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Usage Comparison</p> <h4 class="mt-1 text-2xl font-bold">`;
            if (usageSummary.featuresUsed > 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `${escape_html(usageSummary.featuresUsed > usageSummary.totalFeatures / 2 ? "Above Average" : "Below Average")}`;
            } else {
              $$payload3.out += "<!--[!-->";
              $$payload3.out += `No Data`;
            }
            $$payload3.out += `<!--]--></h4> `;
            if (usageSummary.featuresUsed > 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="mt-3 flex h-2 w-full items-center">`;
              if (usageSummary.totalFeatures > 0) {
                $$payload3.out += "<!--[-->";
                const position = Math.min(100, Math.max(0, usageSummary.featuresUsed / usageSummary.totalFeatures * 100));
                $$payload3.out += `<div class="relative h-full w-full rounded-sm bg-gray-200"><div class="absolute bottom-0 left-1/2 top-0 w-0.5 -translate-x-1/2 transform bg-gray-400"></div> <div class="absolute bottom-0 top-0 h-2 w-2 -translate-x-1/2 -translate-y-1/4 transform rounded-full bg-blue-500"${attr_style(`left: ${position}%`)}></div></div>`;
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]--></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--> <p class="text-muted-foreground mt-2 text-xs">`;
            if (usageSummary.featuresUsed > 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `${escape_html(usageSummary.featuresUsed > usageSummary.totalFeatures / 2 ? "You use more features than average" : "You use fewer features than average")}`;
            } else {
              $$payload3.out += "<!--[!-->";
              $$payload3.out += `Start using features to see comparison`;
            }
            $$payload3.out += `<!--]--></p></div> <div class="rounded-md border p-4"><p class="text-muted-foreground text-sm">Most Valuable Feature</p> <h4 class="mt-1 text-2xl font-bold">${escape_html(usageSummary.topFeatures && usageSummary.topFeatures.length > 0 ? usageSummary.topFeatures[0].featureName : "None")}</h4> `;
            if (usageSummary.topFeatures && usageSummary.topFeatures.length > 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="mt-3 flex h-2 w-full items-center"><div class="bg-primary h-full rounded-sm"${attr_style(`width: ${Math.min(100, usageSummary.topFeatures[0].percentUsed || 0)}%`)}></div></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--> <p class="text-muted-foreground mt-2 text-xs">${escape_html(usageSummary.topFeatures && usageSummary.topFeatures.length > 0 ? `Used ${usageSummary.topFeatures[0].used} times this month` : "No feature usage recorded yet")}</p></div></div> `;
            if (usageSummary.featuresAtLimit > 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="bg-warning/10 border-warning/20 mt-4 rounded-md border p-4 text-sm"><div class="flex items-start gap-3"><div class="text-warning mt-0.5">`;
              Triangle_alert($$payload3, { size: 16 });
              $$payload3.out += `<!----></div> <div><p class="text-warning font-medium">You've reached usage limits on ${escape_html(usageSummary.featuresAtLimit)} feature${escape_html(usageSummary.featuresAtLimit > 1 ? "s" : "")}.</p> <p class="text-muted-foreground mt-1">You've reached the maximum usage for some features.</p> <div class="mt-3">`;
              Button($$payload3, {
                variant: "outline",
                size: "sm",
                class: "text-warning border-warning/20 hover:bg-warning/10",
                onclick: () => goto(),
                children: ($$payload4) => {
                  $$payload4.out += `<!---->View Account Settings`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div></div></div></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]-->`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Detailed Usage`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->View detailed usage for each feature`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Root($$payload3, {
            value: "features",
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Tabs_list($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Tabs_trigger($$payload5, {
                    value: "features",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->By Feature`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Tabs_trigger($$payload5, {
                    value: "history",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Usage History`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_content($$payload4, {
                value: "features",
                class: "pt-4",
                children: ($$payload5) => {
                  if (isLoading) {
                    $$payload5.out += "<!--[-->";
                    $$payload5.out += `<div class="flex justify-center py-8">`;
                    Loader_circle($$payload5, { class: "text-primary h-8 w-8 animate-spin" });
                    $$payload5.out += `<!----></div>`;
                  } else if (error) {
                    $$payload5.out += "<!--[1-->";
                    $$payload5.out += `<div class="rounded-md border p-10 text-center"><div class="bg-muted mx-auto mb-4 flex h-6 w-6 items-center justify-center rounded-full">`;
                    Triangle_alert($$payload5, { class: "text-muted-foreground h-6 w-6" });
                    $$payload5.out += `<!----></div> <h3 class="mb-2 text-lg font-medium">No Usage Data Available</h3> <p class="text-muted-foreground mx-auto max-w-md">${escape_html(error)}</p></div>`;
                  } else if (featuresWithUsage.length === 0) {
                    $$payload5.out += "<!--[2-->";
                    $$payload5.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No feature usage data available yet.</p> <p class="text-muted-foreground mt-2 text-sm">Start using features to see your usage statistics.</p> `;
                    if (featuresData.length > 0) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="mt-4">`;
                      Button($$payload5, {
                        variant: "outline",
                        size: "sm",
                        onclick: () => {
                          featuresWithUsage = featuresData;
                        },
                        children: ($$payload6) => {
                          $$payload6.out += `<!---->Show All Available Features`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out += `<!----></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div>`;
                  } else {
                    $$payload5.out += "<!--[!-->";
                    const each_array_1 = ensure_array_like(Object.entries(featuresByCategory));
                    $$payload5.out += `<div class="grid gap-6 md:grid-cols-2"><div class="space-y-6"><h3 class="text-lg font-medium">Features by Category</h3> <!--[-->`;
                    for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
                      let [_categoryId, categoryData] = each_array_1[$$index_2];
                      const each_array_2 = ensure_array_like(categoryData.features);
                      $$payload5.out += `<div class="space-y-2"><h4 class="text-muted-foreground text-sm font-medium uppercase">${escape_html(categoryData.name)}</h4> <div class="space-y-2"><!--[-->`;
                      for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                        let feature = each_array_2[$$index_1];
                        $$payload5.out += `<button${attr_class(`w-full rounded-md border p-3 text-left transition-colors ${selectedFeatureId === feature.id ? "border-primary bg-primary/5" : "hover:bg-muted/50"}`)}><div class="flex items-center gap-3"><div class="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full"><span class="text-primary text-sm">${escape_html(feature.icon || feature.name[0])}</span></div> <div class="flex-1"><h4 class="font-medium">${escape_html(feature.name)}</h4> <div class="flex items-center justify-between"><p class="text-muted-foreground text-sm">${escape_html(feature.description)}</p> `;
                        if (feature.usage && feature.usage.length > 0 && feature.usage[0].percentUsed !== null) {
                          $$payload5.out += "<!--[-->";
                          $$payload5.out += `<span class="bg-primary/10 text-primary ml-2 rounded-full px-2 py-0.5 text-xs font-medium">${escape_html(Math.round(feature.usage[0].percentUsed))}%</span>`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                        }
                        $$payload5.out += `<!--]--></div></div></div></button>`;
                      }
                      $$payload5.out += `<!--]--></div></div>`;
                    }
                    $$payload5.out += `<!--]--></div> <div class="space-y-4"><h3 class="text-lg font-medium">${escape_html(selectedFeature ? selectedFeature.name : "Select a Feature")}</h3> `;
                    if (!selectedFeature) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">Select a feature to view its usage details.</p></div>`;
                    } else if (!selectedFeature.usage || selectedFeature.usage.length === 0) {
                      $$payload5.out += "<!--[1-->";
                      $$payload5.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">No usage data available for this feature.</p></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      const each_array_3 = ensure_array_like(selectedFeature.usage);
                      $$payload5.out += `<div class="space-y-4"><!--[-->`;
                      for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                        let usage = each_array_3[$$index_3];
                        $$payload5.out += `<div class="rounded-md border p-4"><div class="mb-2 flex items-center justify-between"><h4 class="font-medium">${escape_html(usage.limitName)}</h4> <div class="text-right"><span class="font-medium">${escape_html(usage.used)} / ${escape_html(usage.limit === "unlimited" ? "Unlimited" : usage.limit)}</span> `;
                        if (usage.placeholder) {
                          $$payload5.out += "<!--[-->";
                          $$payload5.out += `<p class="text-muted-foreground text-xs">No usage yet</p>`;
                        } else if (usage.percentUsed !== null && usage.percentUsed !== void 0) {
                          $$payload5.out += "<!--[1-->";
                          $$payload5.out += `<p class="text-muted-foreground text-xs">${escape_html(Math.round(usage.percentUsed))}% used</p>`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                        }
                        $$payload5.out += `<!--]--></div></div> <p class="text-muted-foreground mb-3 text-sm">${escape_html(usage.description || "")}</p> `;
                        if (!usage.placeholder && usage.percentUsed !== null && usage.percentUsed !== void 0) {
                          $$payload5.out += "<!--[-->";
                          $$payload5.out += `<div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div${attr_class(`h-full ${usage.percentUsed >= 90 ? "bg-destructive" : usage.percentUsed >= 75 ? "bg-warning" : "bg-primary"}`)}${attr_style(`width: ${Math.min(100, usage.percentUsed)}%`)}></div></div>`;
                        } else if (usage.placeholder) {
                          $$payload5.out += "<!--[1-->";
                          $$payload5.out += `<div class="bg-muted h-2 w-full overflow-hidden rounded-full"><div class="bg-primary/30 h-full" style="width: 0%"></div></div>`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                        }
                        $$payload5.out += `<!--]--> `;
                        if (usage.period) {
                          $$payload5.out += "<!--[-->";
                          $$payload5.out += `<div class="text-muted-foreground mt-3 text-xs">`;
                          if (usage.period.includes("-")) {
                            $$payload5.out += "<!--[-->";
                            $$payload5.out += `Period: ${escape_html((/* @__PURE__ */ new Date(usage.period + "-01")).toLocaleDateString(void 0, { year: "numeric", month: "long" }))}`;
                          } else {
                            $$payload5.out += "<!--[!-->";
                            $$payload5.out += `Period: ${escape_html(usage.period)}`;
                          }
                          $$payload5.out += `<!--]--></div>`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                        }
                        $$payload5.out += `<!--]--> <div class="mt-4 flex flex-wrap gap-2">`;
                        Button($$payload5, {
                          variant: "outline",
                          size: "sm",
                          onclick: () => {
                            selectedLimitId = usage.limitId;
                          },
                          children: ($$payload6) => {
                            $$payload6.out += `<!---->View History`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload5.out += `<!----> `;
                        if (!usage.placeholder && usage.percentUsed >= 75 && usage.limit !== "unlimited") {
                          $$payload5.out += "<!--[-->";
                          Button($$payload5, {
                            variant: usage.percentUsed >= 90 ? "destructive" : "outline",
                            size: "sm",
                            class: usage.percentUsed >= 75 && usage.percentUsed < 90 ? "text-warning border-warning/20 hover:bg-warning/10" : "",
                            onclick: () => goto(),
                            children: ($$payload6) => {
                              $$payload6.out += `<!---->${escape_html(usage.percentUsed >= 90 ? "View Limits" : "Check Usage")}`;
                            },
                            $$slots: { default: true }
                          });
                        } else {
                          $$payload5.out += "<!--[!-->";
                        }
                        $$payload5.out += `<!--]--></div></div>`;
                      }
                      $$payload5.out += `<!--]--></div>`;
                    }
                    $$payload5.out += `<!--]--></div></div>`;
                  }
                  $$payload5.out += `<!--]-->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_content($$payload4, {
                value: "history",
                class: "pt-4",
                children: ($$payload5) => {
                  $$payload5.out += `<div class="space-y-6">`;
                  if (selectedFeatureId && selectedLimitId) {
                    $$payload5.out += "<!--[-->";
                    const each_array_4 = ensure_array_like(featuresData.filter((f) => f.usage && f.usage.length > 0));
                    $$payload5.out += `<div class="mb-6"><div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between"><div><h3 class="text-lg font-medium">Usage History</h3> <p class="text-muted-foreground text-sm">Track your usage over time for ${escape_html(selectedFeature?.name || "selected feature")}</p></div> <div class="flex flex-wrap items-center gap-2"><select class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2">`;
                    $$payload5.select_value = selectedFeatureId;
                    $$payload5.out += `<option value=""${maybe_selected($$payload5, "")} disabled>Select Feature</option><!--[-->`;
                    for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
                      let feature = each_array_4[$$index_4];
                      $$payload5.out += `<option${attr("value", feature.id)}${maybe_selected($$payload5, feature.id)}>${escape_html(feature.name)}</option>`;
                    }
                    $$payload5.out += `<!--]-->`;
                    $$payload5.select_value = void 0;
                    $$payload5.out += `</select> `;
                    if (selectedFeature && selectedFeature.usage && selectedFeature.usage.length > 0) {
                      $$payload5.out += "<!--[-->";
                      const each_array_5 = ensure_array_like(selectedFeature.usage);
                      $$payload5.out += `<select class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2">`;
                      $$payload5.select_value = selectedLimitId;
                      $$payload5.out += `<option value=""${maybe_selected($$payload5, "")} disabled>Select Limit</option><!--[-->`;
                      for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
                        let usage = each_array_5[$$index_5];
                        $$payload5.out += `<option${attr("value", usage.limitId)}${maybe_selected($$payload5, usage.limitId)}>${escape_html(usage.limitName)}</option>`;
                      }
                      $$payload5.out += `<!--]-->`;
                      $$payload5.select_value = void 0;
                      $$payload5.out += `</select>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> <div class="ml-auto flex items-center gap-2"><div class="relative">`;
                    Button($$payload5, {
                      variant: "outline",
                      size: "sm",
                      class: "flex h-8 items-center gap-1",
                      children: ($$payload6) => {
                        Calendar($$payload6, { class: "h-4 w-4" });
                        $$payload6.out += `<!----> <span>Date Range</span> `;
                        Chevron_down($$payload6, { class: "h-4 w-4" });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div> <div class="flex items-center gap-1 overflow-hidden rounded-md border">`;
                    Button($$payload5, {
                      variant: timeRange === "30d" ? "default" : "ghost",
                      size: "sm",
                      class: "h-8 rounded-none",
                      onclick: () => timeRange = "30d",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->30d`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Button($$payload5, {
                      variant: timeRange === "90d" ? "default" : "ghost",
                      size: "sm",
                      class: "h-8 rounded-none",
                      onclick: () => timeRange = "90d",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->90d`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Button($$payload5, {
                      variant: timeRange === "1y" ? "default" : "ghost",
                      size: "sm",
                      class: "h-8 rounded-none",
                      onclick: () => timeRange = "1y",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->1y`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div></div></div></div></div> `;
                    UsageHistoryChart($$payload5, {
                      featureId: selectedFeatureId,
                      limitId: selectedLimitId
                    });
                    $$payload5.out += `<!----> <div class="mt-8 rounded-md border p-4"><h4 class="mb-2 text-sm font-medium">Usage Tips</h4> <ul class="text-muted-foreground space-y-2 text-sm"><li class="flex items-start gap-2">`;
                    Circle_check_big($$payload5, { size: 16, class: "mt-0.5" });
                    $$payload5.out += `<!----> <span>Usage is tracked on a monthly basis and resets at the beginning of each month.</span></li> <li class="flex items-start gap-2">`;
                    Circle_check_big($$payload5, { size: 16, class: "mt-0.5" });
                    $$payload5.out += `<!----> <span>Usage limits are based on your account type and settings.</span></li> <li class="flex items-start gap-2">`;
                    Circle_check_big($$payload5, { size: 16, class: "mt-0.5" });
                    $$payload5.out += `<!----> <span>Contact support if you need temporary limit increases for special projects.</span></li></ul></div>`;
                  } else {
                    $$payload5.out += "<!--[!-->";
                    $$payload5.out += `<div class="rounded-md border border-dashed p-8 text-center"><p class="text-muted-foreground">Select a feature and limit to view usage history.</p></div>`;
                  }
                  $$payload5.out += `<!--]--></div>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DtGwnLWw.js.map
