{"version": 3, "file": "88-B7xVDyBF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/pricing/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/88.js"], "sourcesContent": ["import \"../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../chunks/auth.js\";\nimport \"../../../chunks/index.js\";\nimport { i as initializePlansInDatabase, g as getPlansFromDatabase } from \"../../../chunks/plan-sync.js\";\nconst load = async ({ cookies, locals, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (token) {\n    const user = await verifySessionToken(token);\n    if (user) {\n      locals.user = user;\n    }\n  }\n  if (!locals.user) {\n    locals.user = null;\n  }\n  const preselectedPlanId = url.searchParams.get(\"plan\");\n  const preselectedBillingCycle = url.searchParams.get(\"billing\") === \"annual\" ? \"annual\" : \"monthly\";\n  const preselectedSection = url.searchParams.get(\"section\") === \"teams\" ? \"teams\" : \"pro\";\n  await initializePlansInDatabase();\n  let plans;\n  try {\n    plans = await getPlansFromDatabase();\n    console.log(\"Plans from database:\", plans);\n    console.log(\"Number of plans:\", plans.length);\n    console.log(\n      \"Individual plans:\",\n      plans.filter((p) => p.section === \"pro\").map((p) => p.id)\n    );\n    const freePlanExists = plans.some((p) => p.id === \"free\");\n    console.log(\"Free plan exists:\", freePlanExists);\n    if (!freePlanExists) {\n      console.log(\"Adding free plan manually\");\n      plans.push({\n        id: \"free\",\n        name: \"Free\",\n        description: \"Basic features for personal use\",\n        section: \"pro\",\n        monthlyPrice: 0,\n        annualPrice: 0,\n        popular: false,\n        features: []\n      });\n    }\n  } catch (error) {\n    console.error(\"Error loading plans:\", error);\n    plans = [\n      {\n        id: \"free\",\n        name: \"Free\",\n        description: \"Basic features for personal use\",\n        section: \"pro\",\n        monthlyPrice: 0,\n        annualPrice: 0,\n        features: []\n      }\n    ];\n  }\n  return {\n    user: locals.user,\n    plans,\n    preselectedPlanId,\n    preselectedBillingCycle,\n    preselectedSection\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/pricing/_page.server.ts.js';\n\nexport const index = 88;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/pricing/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/pricing/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/88.CGXEMuP3.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/0ykhD7u6.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/BuYRPDDz.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/D9yI7a4E.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/eZTU2AfY.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CWmzcjye.js\",\"_app/immutable/chunks/T7uRAIbG.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/sDlmbjaf.js\",\"_app/immutable/chunks/ByUTvV5u.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/CY_6SfHi.js\",\"_app/immutable/chunks/CodWuqwu.js\",\"_app/immutable/chunks/CQeqUgF6.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/88.B3t7FN5M.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA,MAAM,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AACjD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAChD,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI;AACxB;AACA;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI;AACtB;AACA,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;AACxD,EAAE,MAAM,uBAAuB,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACrG,EAAE,MAAM,kBAAkB,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,OAAO,GAAG,OAAO,GAAG,KAAK;AAC1F,EAAE,MAAM,yBAAyB,EAAE;AACnC,EAAE,IAAI,KAAK;AACX,EAAE,IAAI;AACN,IAAI,KAAK,GAAG,MAAM,oBAAoB,EAAE;AACxC,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,MAAM,CAAC;AACjD,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,mBAAmB;AACzB,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;AAC9D,KAAK;AACL,IAAI,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;AAC7D,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,cAAc,CAAC;AACpD,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAC9C,MAAM,KAAK,CAAC,IAAI,CAAC;AACjB,QAAQ,EAAE,EAAE,MAAM;AAClB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,WAAW,EAAE,iCAAiC;AACtD,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,WAAW,EAAE,CAAC;AACtB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,QAAQ,EAAE;AAClB,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD,IAAI,KAAK,GAAG;AACZ,MAAM;AACN,QAAQ,EAAE,EAAE,MAAM;AAClB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,WAAW,EAAE,iCAAiC;AACtD,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,WAAW,EAAE,CAAC;AACtB,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK;AACL;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI;AACrB,IAAI,KAAK;AACT,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;AC9DW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA0C,CAAC,EAAE;AAExG,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7nE,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,uCAAuC;AACpG,MAAC,KAAK,GAAG;;;;"}