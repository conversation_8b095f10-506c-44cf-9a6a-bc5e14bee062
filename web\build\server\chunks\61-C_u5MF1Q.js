import { r as redirect, f as fail } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { g as getUserFromToken } from './auth-BPad-IlN.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, s as stringType } from './types-D78SXuvm.js';
import '@prisma/client';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const generalSettingsSchema = objectType({
  siteName: stringType().min(1, "Site name is required"),
  siteDescription: stringType().optional(),
  contactEmail: stringType().email("Invalid email address"),
  timezone: stringType(),
  dateFormat: stringType(),
  timeFormat: stringType(),
  language: stringType()
});
const load = async ({ cookies, locals }) => {
  const tokenData = getUserFromToken(cookies);
  if (!tokenData || !tokenData.email) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { email: tokenData.email }
  });
  if (!userData) {
    throw redirect(302, "/auth/sign-in");
  }
  locals.user = userData;
  const preferences = userData.preferences ? userData.preferences : {};
  const form = await superValidate(
    {
      siteName: preferences.siteName || "Hirli",
      siteDescription: preferences.siteDescription || "Your job application automation assistant",
      contactEmail: preferences.contactEmail || userData.email,
      timezone: preferences.timezone || "UTC",
      dateFormat: preferences.dateFormat || "MM/DD/YYYY",
      timeFormat: preferences.timeFormat || "12h",
      language: preferences.language || "en"
    },
    zod(generalSettingsSchema)
  );
  return {
    user: userData,
    form
  };
};
const actions = {
  default: async ({ request, cookies, locals }) => {
    const tokenData = getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(generalSettingsSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    try {
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          preferences: {
            ...userData.preferences,
            siteName: form.data.siteName,
            siteDescription: form.data.siteDescription,
            contactEmail: form.data.contactEmail,
            timezone: form.data.timezone,
            dateFormat: form.data.dateFormat,
            timeFormat: form.data.timeFormat,
            language: form.data.language
          }
        }
      });
      return { form, success: true };
    } catch (error) {
      console.error("Error updating general settings:", error);
      return fail(500, { form, error: "Failed to update general settings" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 61;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DvoupwzW.js')).default;
const server_id = "src/routes/dashboard/settings/general/+page.server.ts";
const imports = ["_app/immutable/nodes/61.D3NzeD8O.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/FeejBSkx.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/D85ENLd-.js","_app/immutable/chunks/ByUTvV5u.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u-Lut8o2.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BoNCRmBc.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CXvW3J0s.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/ByFxH6T3.js","_app/immutable/chunks/D1zde6Ej.js","_app/immutable/chunks/rNI1Perp.js","_app/immutable/chunks/hA0h0kTo.js","_app/immutable/chunks/BSHZ37s_.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=61-C_u5MF1Q.js.map
