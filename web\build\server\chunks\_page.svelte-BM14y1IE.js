import 'clsx';
import { p as push, q as pop } from './index3-CqUPEnZw.js';
import './false-CRHihH2U.js';

function _page($$payload, $$props) {
  push();
  $$payload.out += `<div class="container mx-auto py-8"><div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div> <p class="ml-4">Redirecting to email settings...</p></div></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BM14y1IE.js.map
