import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { prisma } from './lib/prismaClient.js';
import dotenv from 'dotenv';

dotenv.config();

const R2_CONFIG = {
  endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto",
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID ?? "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY ?? "",
  },
};

const r2Client = new S3Client(R2_CONFIG);

async function migrateLogos() {
  console.log('🚀 Starting logo migration from old R2 account to worker account...');
  
  try {
    // Get all companies with R2 logo URLs from the old account
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: {
          contains: 'pub-46a6f782171b440493a823a520764a72.r2.dev'
        }
      },
      select: {
        id: true,
        name: true,
        logoUrl: true
      }
    });
    
    console.log(`📊 Found ${companies.length} companies with logos to migrate`);
    
    let migrated = 0;
    let failed = 0;
    
    for (const company of companies) {
      try {
        console.log(`\n[${migrated + failed + 1}/${companies.length}] Processing: ${company.name}`);
        
        if (!company.logoUrl) {
          console.log('   ⚠️ No logo URL found, skipping');
          continue;
        }
        
        // Extract filename
        const filename = company.logoUrl.split('/').pop();
        if (!filename) {
          console.log('   ⚠️ Could not extract filename, skipping');
          continue;
        }
        
        console.log(`   📥 Downloading: ${company.logoUrl}`);
        
        // Download the logo from the old account
        const response = await fetch(company.logoUrl);
        if (!response.ok) {
          throw new Error(`Failed to download: ${response.status} ${response.statusText}`);
        }
        
        const buffer = Buffer.from(await response.arrayBuffer());
        const contentType = response.headers.get('content-type') || 'image/webp';
        
        console.log(`   📤 Uploading to new account: ${filename} (${buffer.length} bytes)`);
        
        // Upload to the new account's company logos bucket
        const uploadCommand = new PutObjectCommand({
          Bucket: 'hirli-company-logos',
          Key: filename,
          Body: buffer,
          ContentType: contentType,
          Metadata: {
            originalName: filename,
            uploadedAt: new Date().toISOString(),
            fileType: 'companyLogos',
            bucketType: 'company',
            companyId: company.id,
            companyName: company.name,
            migratedFrom: 'pub-46a6f782171b440493a823a520764a72'
          },
        });
        
        await r2Client.send(uploadCommand);
        
        console.log(`   ✅ Successfully migrated logo for ${company.name}`);
        migrated++;
        
        // Add a small delay to avoid overwhelming the services
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`   ❌ Failed to migrate logo for ${company.name}:`, error.message);
        failed++;
      }
    }
    
    console.log(`\n🎉 Migration completed!`);
    console.log(`   ✅ Successfully migrated: ${migrated}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📊 Total processed: ${companies.length}`);
    
    if (migrated > 0) {
      console.log(`\n🔗 Test a migrated logo:`);
      console.log(`   https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/logos/comfort-keepers-logo-optimized.webp`);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateLogos();
