import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, P as stringify, N as attr, O as escape_html, M as ensure_array_like, J as attr_class, $ as attr_style, _ as clsx } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { B as Badge } from './badge-C9pSznab.js';
import { B as Button } from './button-CrucCo1G.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root$2, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root, d as Dialog_overlay, D as Dialog_content } from './index7-BURUpWjT.js';
import { R as Root$1, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { g as goto } from './client-dNyMPa8V.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { B as Bookmark_check, F as Flag, C as Clipboard_check } from './flag-CSTMD-YC.js';
import { B as Bookmark } from './bookmark-DazMkrfp.js';
import { S as Share_2 } from './share-2-ihgFYKw2.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import { S as Sparkles } from './sparkles-E4-thk3U.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { B as Building } from './building-8WHBOPYC.js';
import { D as Dollar_sign } from './dollar-sign-CXBwKToB.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { h as html } from './html-FW6Ia4bL.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './dialog-description2-rfr-pd9k.js';
import './index2-Cut0V_vU.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { job, matchScore, similarJobs } = data;
  const title = job?.title || "Job Details";
  let isJobSaved = data.isSaved || false;
  let isJobApplied = data.isApplied || false;
  const skillMatchData = data.skillMatchData || null;
  let showReportDialog = false;
  let reportReason = "";
  let showApplyDialog = false;
  function formatMatchScore(score) {
    if (score === null) return "N/A";
    return `${Math.round(score * 100)}%`;
  }
  function getScoreColorClass(score) {
    if (score === null) return "bg-gray-100 text-gray-800";
    if (score >= 0.8) return "bg-green-100 text-green-800";
    if (score >= 0.6) return "bg-blue-100 text-blue-800";
    if (score >= 0.4) return "bg-yellow-100 text-yellow-800";
    return "bg-gray-100 text-gray-800";
  }
  function getProgressColorClass(score) {
    if (score >= 0.8) return "bg-green-500";
    if (score >= 0.6) return "bg-blue-500";
    if (score >= 0.4) return "bg-yellow-500";
    return "bg-gray-500";
  }
  function applyToJob() {
    if (isJobApplied) {
      goto();
      return;
    }
    if (job.url) {
      window.open(job.url, "_blank", "noopener,noreferrer");
    }
    showApplyDialog = true;
  }
  async function confirmJobApplication() {
    try {
      const loadingToast = toast.loading("Adding to your tracker...");
      const response = await fetch("/api/applications/add", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          jobId: job.id,
          jobUrl: job.url,
          company: job.company,
          position: job.title,
          location: job.location
        })
      });
      const result = await response.json();
      toast.dismiss(loadingToast);
      if (!response.ok) {
        throw new Error(result.error || "Failed to add application");
      }
      isJobApplied = true;
      if (isJobSaved) {
        isJobSaved = false;
      }
      showApplyDialog = false;
      toast.success("Application tracked", {
        description: "Added to your application tracker"
      });
      setTimeout(
        () => {
          toast.message("View your application", {
            description: "Go to your application tracker to manage it",
            action: {
              label: "Go to Tracker",
              onClick: () => goto("/dashboard/tracker")
            }
          });
        },
        2e3
      );
    } catch (error) {
      console.error("Error adding application:", error);
      toast.error("Failed to add application", {
        description: error instanceof Error ? error.message : "Please try again later"
      });
    }
  }
  async function saveJob() {
    if (isJobApplied) {
      toast.info("This job is in your applications", {
        description: "Applied jobs are automatically tracked"
      });
      return;
    }
    try {
      const response = await fetch(`/api/jobs/${job.id}/save`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notes: "" })
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Failed to save job");
      }
      isJobSaved = !isJobSaved;
      toast.success(isJobSaved ? "Job saved" : "Job removed", {
        description: isJobSaved ? "Added to your saved jobs" : "Removed from your saved jobs"
      });
    } catch (error) {
      console.error("Error saving job:", error);
      toast.error("Failed to save job");
    }
  }
  function shareJob() {
    const tempInput = document.createElement("input");
    tempInput.value = window.location.href;
    document.body.appendChild(tempInput);
    try {
      if (typeof navigator !== "undefined" && navigator.share) {
        navigator.share({
          title: job?.title || "Job Listing",
          text: `Check out this job: ${job?.title || "Job Listing"} at ${job?.company || "Company"}`,
          url: window.location.href
        }).then(() => {
          toast.success("Job shared successfully");
        }).catch((error) => {
          console.error("Error sharing job:", error);
          fallbackCopyToClipboard(tempInput);
        });
      } else {
        fallbackCopyToClipboard(tempInput);
      }
    } catch (error) {
      console.error("Error in share function:", error);
      fallbackCopyToClipboard(tempInput);
    } finally {
      document.body.removeChild(tempInput);
    }
  }
  function fallbackCopyToClipboard(input) {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(window.location.href).then(() => {
          toast.success("Link copied to clipboard", { description: "You can now paste and share it" });
        }).catch((err) => {
          console.error("Clipboard API failed:", err);
          selectAndCopy(input);
        });
      } else {
        selectAndCopy(input);
      }
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
      selectAndCopy(input);
    }
  }
  function selectAndCopy(input) {
    try {
      input.select();
      input.setSelectionRange(0, 99999);
      let successful = false;
      try {
        successful = document.execCommand("copy");
      } catch (e) {
        console.error("execCommand error:", e);
      }
      if (successful) {
        toast.success("Link copied to clipboard", { description: "You can now paste and share it" });
      } else {
        throw new Error("Copy command failed");
      }
    } catch (err) {
      console.error("Selection copy failed:", err);
      toast.error("Could not copy link", {
        description: "Please copy the URL from the address bar"
      });
    }
  }
  function getTimeSincePosted(dateString) {
    if (!dateString) return "Recently";
    const postedDate = new Date(dateString);
    const now = /* @__PURE__ */ new Date();
    const diffTime = Math.abs(now.getTime() - postedDate.getTime());
    const diffDays = Math.floor(diffTime / (1e3 * 60 * 60 * 24));
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  }
  function goBack() {
    window.history.back();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: `${stringify(title)} | Hirli`,
      description: "View detailed information about this job opportunity and take action. Apply, save, or share this job with others.",
      keywords: `job details, job opportunity, job application, career search, job description, ${stringify(job?.title)}, ${stringify(job?.company)}`
    });
    $$payload2.out += `<!----> <div class="container mx-auto px-4 py-8">`;
    Button($$payload2, {
      variant: "outline",
      class: "mb-6",
      onclick: goBack,
      children: ($$payload3) => {
        Chevron_left($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Back to Matches`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <div class="grid grid-cols-1 gap-6 lg:grid-cols-3"><div class="lg:col-span-2">`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<div class="flex flex-wrap items-center justify-between gap-2 border-b p-4"><div class="flex flex-wrap gap-2">`;
        Button($$payload3, {
          variant: isJobSaved ? "default" : "outline",
          size: "sm",
          onclick: saveJob,
          disabled: isJobApplied,
          children: ($$payload4) => {
            if (isJobSaved) {
              $$payload4.out += "<!--[-->";
              Bookmark_check($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Saved`;
            } else {
              $$payload4.out += "<!--[!-->";
              Bookmark($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Save`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Button($$payload3, {
          variant: "outline",
          size: "sm",
          onclick: shareJob,
          children: ($$payload4) => {
            Share_2($$payload4, { class: "mr-2 h-4 w-4" });
            $$payload4.out += `<!----> Share`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Button($$payload3, {
          variant: "outline",
          size: "sm",
          onclick: () => showReportDialog = true,
          children: ($$payload4) => {
            Flag($$payload4, { class: "mr-2 h-4 w-4" });
            $$payload4.out += `<!----> Report`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        if (job.url) {
          $$payload3.out += "<!--[-->";
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            asChild: true,
            children: ($$payload4) => {
              $$payload4.out += `<a${attr("href", job.url)} target="_blank" rel="noopener noreferrer">`;
              External_link($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> View Original</a>`;
            },
            $$slots: { default: true }
          });
        } else {
          $$payload3.out += "<!--[!-->";
        }
        $$payload3.out += `<!--]--></div> `;
        Button($$payload3, {
          size: "sm",
          onclick: applyToJob,
          class: isJobApplied ? "bg-green-600 hover:bg-green-700" : "bg-primary hover:bg-primary/90",
          children: ($$payload4) => {
            if (isJobApplied) {
              $$payload4.out += "<!--[-->";
              Clipboard_check($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> View Application`;
            } else {
              $$payload4.out += "<!--[!-->";
              Sparkles($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> Apply Now`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> `;
        Card_header($$payload3, {
          class: "p-6 pb-3",
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-start justify-between"><div class="flex-1">`;
            if (matchScore !== null) {
              $$payload4.out += "<!--[-->";
              Badge($$payload4, {
                variant: "secondary",
                class: `${getScoreColorClass(matchScore)} mb-2`,
                children: ($$payload5) => {
                  Sparkles($$payload5, { class: "mr-1 h-3 w-3" });
                  $$payload5.out += `<!----> ${escape_html(formatMatchScore(matchScore))} Match`;
                },
                $$slots: { default: true }
              });
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            Card_title($$payload4, {
              class: "text-2xl font-bold",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(job.title)}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Card_description($$payload4, {
              class: "text-primary text-lg font-medium",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(job.company)}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="text-muted-foreground mt-2 flex items-center gap-2 text-sm">`;
            Map_pin($$payload4, { class: "h-4 w-4" });
            $$payload4.out += `<!----> <span>${escape_html(job.location || "Remote")}</span></div></div> <div class="ml-4 flex-shrink-0"><div class="bg-primary/10 text-primary flex h-16 w-16 items-center justify-center rounded-md">`;
            Building($$payload4, { class: "h-8 w-8" });
            $$payload4.out += `<!----></div></div></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Card_content($$payload3, {
          class: "p-6 pt-3",
          children: ($$payload4) => {
            $$payload4.out += `<div class="bg-muted/5 mb-6 rounded-lg border p-4"><h3 class="text-muted-foreground mb-3 text-sm font-medium">Key Job Details</h3> <div class="grid grid-cols-2 gap-x-6 gap-y-4 md:grid-cols-3">`;
            if (job.salary) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium">`;
              Dollar_sign($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Salary</span></div> <p class="mt-1 text-sm font-medium">${escape_html(job.salary)}</p></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium">`;
            Briefcase($$payload4, { class: "h-4 w-4" });
            $$payload4.out += `<!----> <span>Job Type</span></div> <div class="mt-1 flex items-center gap-2"><p class="text-sm font-medium">${escape_html(job.employmentType || "Full-time")}</p> `;
            if (job.remoteType === "remote") {
              $$payload4.out += "<!--[-->";
              Badge($$payload4, {
                variant: "outline",
                class: "text-xs",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Remote`;
                },
                $$slots: { default: true }
              });
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> <div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium">`;
            Map_pin($$payload4, { class: "h-4 w-4" });
            $$payload4.out += `<!----> <span>Location</span></div> <p class="mt-1 text-sm font-medium">${escape_html(job.location || "Remote")}</p></div> <div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium">`;
            Calendar($$payload4, { class: "h-4 w-4" });
            $$payload4.out += `<!----> <span>Posted</span></div> <p class="mt-1 text-sm font-medium">${escape_html(job.postedDate ? getTimeSincePosted(job.postedDate) : "Recently")}</p></div> `;
            if (job.experienceLevel) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium">`;
              Briefcase($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Experience</span></div> <p class="mt-1 text-sm font-medium">${escape_html(job.experienceLevel)}</p></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (job.company) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex flex-col items-start"><div class="text-primary flex items-center gap-1.5 text-sm font-medium">`;
              Building($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Company</span></div> <p class="mt-1 text-sm font-medium">${escape_html(job.company)}</p></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> <div class="mb-6 overflow-hidden rounded-lg border"><div class="bg-primary/10 p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="bg-primary/20 flex h-10 w-10 items-center justify-center rounded-full">`;
            Sparkles($$payload4, { class: "text-primary h-5 w-5" });
            $$payload4.out += `<!----></div> <div><h3 class="font-semibold">Apply with Hirli AI</h3> <p class="text-muted-foreground text-sm">Increase your chances of getting hired</p></div></div> `;
            Button($$payload4, {
              size: "sm",
              onclick: applyToJob,
              class: isJobApplied ? "bg-green-600 hover:bg-green-700" : "bg-primary hover:bg-primary/90",
              children: ($$payload5) => {
                if (isJobApplied) {
                  $$payload5.out += "<!--[-->";
                  Clipboard_check($$payload5, { class: "mr-1 h-3.5 w-3.5" });
                  $$payload5.out += `<!----> View Application`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  Sparkles($$payload5, { class: "mr-1 h-3.5 w-3.5" });
                  $$payload5.out += `<!----> Apply with AI`;
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div> <div class="bg-primary/5 p-4"><ul class="grid gap-2 text-sm md:grid-cols-2"><li class="flex items-start gap-2">`;
            Circle_check_big($$payload4, { class: "text-primary mt-0.5 h-4 w-4" });
            $$payload4.out += `<!----> <span>AI-optimized resume tailored for this job</span></li> <li class="flex items-start gap-2">`;
            Circle_check_big($$payload4, { class: "text-primary mt-0.5 h-4 w-4" });
            $$payload4.out += `<!----> <span>Keyword matching for ATS systems</span></li> <li class="flex items-start gap-2">`;
            Circle_check_big($$payload4, { class: "text-primary mt-0.5 h-4 w-4" });
            $$payload4.out += `<!----> <span>Highlight relevant skills and experience</span></li> <li class="flex items-start gap-2">`;
            Circle_check_big($$payload4, { class: "text-primary mt-0.5 h-4 w-4" });
            $$payload4.out += `<!----> <span>Professional formatting and layout</span></li></ul></div></div> `;
            Root$2($$payload4, {
              children: ($$payload5) => {
                Tabs_list($$payload5, {
                  class: "w-full border-b",
                  children: ($$payload6) => {
                    Tabs_trigger($$payload6, {
                      value: "description",
                      class: "flex-1",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Description`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Tabs_trigger($$payload6, {
                      value: "requirements",
                      class: "flex-1",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Requirements`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Tabs_trigger($$payload6, {
                      value: "benefits",
                      class: "flex-1",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Benefits`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Tabs_content($$payload5, {
                  value: "description",
                  class: "mt-4",
                  children: ($$payload6) => {
                    $$payload6.out += `<div class="prose max-w-none">${html(job.description || "No description available.")}</div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Tabs_content($$payload5, {
                  value: "requirements",
                  class: "mt-4",
                  children: ($$payload6) => {
                    if (job.requirements && job.requirements.length > 0) {
                      $$payload6.out += "<!--[-->";
                      const each_array = ensure_array_like(job.requirements);
                      $$payload6.out += `<ul class="list-disc space-y-2 pl-5"><!--[-->`;
                      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                        let requirement = each_array[$$index];
                        $$payload6.out += `<li>${escape_html(requirement)}</li>`;
                      }
                      $$payload6.out += `<!--]--></ul>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `<p>No specific requirements listed.</p>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Tabs_content($$payload5, {
                  value: "benefits",
                  class: "mt-4",
                  children: ($$payload6) => {
                    if (job.benefits && job.benefits.length > 0) {
                      $$payload6.out += "<!--[-->";
                      const each_array_1 = ensure_array_like(job.benefits);
                      $$payload6.out += `<ul class="list-disc space-y-2 pl-5"><!--[-->`;
                      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                        let benefit = each_array_1[$$index_1];
                        $$payload6.out += `<li>${escape_html(benefit)}</li>`;
                      }
                      $$payload6.out += `<!--]--></ul>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `<p>No benefits listed.</p>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Card_footer($$payload3, {
          class: "flex justify-center p-6",
          children: ($$payload4) => {
            Button($$payload4, {
              onclick: applyToJob,
              class: isJobApplied ? "bg-green-600 px-8 hover:bg-green-700" : "bg-primary hover:bg-primary/90 px-8",
              children: ($$payload5) => {
                if (isJobApplied) {
                  $$payload5.out += "<!--[-->";
                  Clipboard_check($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> View Application`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  Sparkles($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Apply Now`;
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    if (job.company) {
      $$payload2.out += "<!--[-->";
      Card($$payload2, {
        class: "mt-6",
        children: ($$payload3) => {
          Card_header($$payload3, {
            class: "p-6",
            children: ($$payload4) => {
              Card_title($$payload4, {
                class: "flex items-center gap-2",
                children: ($$payload5) => {
                  Building($$payload5, { class: "h-5 w-5" });
                  $$payload5.out += `<!----> About the company`;
                },
                $$slots: { default: true }
              });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_content($$payload3, {
            class: "p-6 pt-0",
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex items-center gap-4"><div class="bg-primary/10 text-primary flex h-16 w-16 items-center justify-center rounded-md">`;
              Building($$payload4, { class: "h-8 w-8" });
              $$payload4.out += `<!----></div> <div><h3 class="text-lg font-semibold">${escape_html(job.company)}</h3> <p class="text-muted-foreground text-sm">${escape_html(job.location || "Remote")}</p></div> `;
              Button($$payload4, {
                variant: "outline",
                size: "sm",
                class: "ml-auto",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Follow`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div> <p class="mt-4 text-sm">${escape_html(`${job.company} is hiring for the role of ${job.title}. Apply now to join their team.`)}</p>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="space-y-6">`;
    if (skillMatchData) {
      $$payload2.out += "<!--[-->";
      Card($$payload2, {
        class: "overflow-hidden",
        children: ($$payload3) => {
          Card_header($$payload3, {
            class: "bg-primary/5 p-4",
            children: ($$payload4) => {
              $$payload4.out += `<div class="flex items-center justify-between">`;
              Card_title($$payload4, {
                class: "flex items-center gap-2",
                children: ($$payload5) => {
                  Sparkles($$payload5, { class: "text-primary h-5 w-5" });
                  $$payload5.out += `<!----> AI Match Analysis`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Badge($$payload4, {
                class: `${getScoreColorClass(skillMatchData.overallMatch)} px-2.5 py-1 text-base`,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->${escape_html(formatMatchScore(skillMatchData.overallMatch))}`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div> `;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->How your profile matches this job`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_content($$payload3, {
            class: "p-4",
            children: ($$payload4) => {
              const each_array_2 = ensure_array_like(skillMatchData.matchedSkills);
              $$payload4.out += `<div class="mb-6 flex flex-col items-center"><div class="relative mb-2 h-32 w-32"><svg class="h-32 w-32 -rotate-90 transform" viewBox="0 0 100 100"><circle class="stroke-gray-200" cx="50" cy="50" r="45" fill="none" stroke-width="10"></circle><circle${attr_class(clsx(getProgressColorClass(skillMatchData.overallMatch)))} cx="50" cy="50" r="45" fill="none" stroke-width="10" stroke-dasharray="282.7"${attr("stroke-dashoffset", 282.7 - 282.7 * skillMatchData.overallMatch)}></circle></svg> <div class="absolute inset-0 flex items-center justify-center"><span class="text-2xl font-bold">${escape_html(formatMatchScore(skillMatchData.overallMatch))}</span></div></div> <p class="text-muted-foreground text-center text-sm">Overall Match Score</p></div> <div class="space-y-4"><div><div class="mb-1 flex items-center justify-between text-sm"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full bg-blue-500"></div> <span>Skills Match</span></div> <span class="font-medium">${escape_html(formatMatchScore(skillMatchData.skillsMatch))}</span></div> <div class="h-2 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-blue-500"${attr_style(`width: ${stringify(Math.min(skillMatchData.skillsMatch * 100, 100))}%`)}></div></div></div> <div><div class="mb-1 flex items-center justify-between text-sm"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full bg-green-500"></div> <span>Experience Match</span></div> <span class="font-medium">${escape_html(formatMatchScore(skillMatchData.experienceMatch))}</span></div> <div class="h-2 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-green-500"${attr_style(`width: ${stringify(Math.min(skillMatchData.experienceMatch * 100, 100))}%`)}></div></div></div> <div><div class="mb-1 flex items-center justify-between text-sm"><div class="flex items-center gap-2"><div class="h-3 w-3 rounded-full bg-purple-500"></div> <span>Education Match</span></div> <span class="font-medium">${escape_html(formatMatchScore(skillMatchData.educationMatch))}</span></div> <div class="h-2 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-purple-500"${attr_style(`width: ${stringify(Math.min(skillMatchData.educationMatch * 100, 100))}%`)}></div></div></div></div> <div class="mt-6 rounded-lg border bg-green-50/50 p-3"><h4 class="mb-2 flex items-center gap-1.5 text-sm font-medium text-green-700">`;
              Circle_check_big($$payload4, { class: "h-4 w-4 text-green-600" });
              $$payload4.out += `<!----> Your matching skills</h4> <div class="flex flex-wrap gap-2"><!--[-->`;
              for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                let skill = each_array_2[$$index_2];
                Badge($$payload4, {
                  variant: "outline",
                  class: "border-green-200 bg-green-100/50 text-green-800",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(skill.name)} <span class="ml-1 text-xs opacity-70">(${escape_html(skill.level)})</span>`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]--></div></div> `;
              if (skillMatchData.missingSkills && skillMatchData.missingSkills.length > 0) {
                $$payload4.out += "<!--[-->";
                const each_array_3 = ensure_array_like(skillMatchData.missingSkills);
                $$payload4.out += `<div class="mt-3 rounded-lg border bg-yellow-50/50 p-3"><h4 class="mb-2 flex items-center gap-1.5 text-sm font-medium text-yellow-700">`;
                Circle_x($$payload4, { class: "h-4 w-4 text-yellow-600" });
                $$payload4.out += `<!----> Skills to develop</h4> <div class="flex flex-wrap gap-2"><!--[-->`;
                for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                  let skill = each_array_3[$$index_3];
                  Badge($$payload4, {
                    variant: "outline",
                    class: "border-yellow-200 bg-yellow-100/50 text-yellow-800",
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(skill.name)} <span class="ml-1 text-xs opacity-70">(${escape_html(skill.importance)})</span>`;
                    },
                    $$slots: { default: true }
                  });
                }
                $$payload4.out += `<!--]--></div></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]--> `;
              Button($$payload4, {
                class: "mt-4 w-full",
                variant: "outline",
                children: ($$payload5) => {
                  Sparkles($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Optimize your resume for this job`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    if (similarJobs && similarJobs.length > 0) {
      $$payload2.out += "<!--[-->";
      Card($$payload2, {
        children: ($$payload3) => {
          Card_header($$payload3, {
            class: "bg-muted/5 p-4",
            children: ($$payload4) => {
              Card_title($$payload4, {
                class: "flex items-center gap-2",
                children: ($$payload5) => {
                  Briefcase($$payload5, { class: "h-5 w-5" });
                  $$payload5.out += `<!----> Similar Jobs`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Card_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Jobs that match your profile and interests`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_content($$payload3, {
            class: "p-4 pt-0",
            children: ($$payload4) => {
              const each_array_4 = ensure_array_like(similarJobs);
              $$payload4.out += `<div class="space-y-3"><!--[-->`;
              for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
                let similarJob = each_array_4[$$index_4];
                $$payload4.out += `<a${attr("href", `/dashboard/jobs/${stringify(similarJob.id)}`)} class="hover:border-primary/30 hover:bg-primary/5 group relative block rounded-lg border p-3 transition-colors"><div class="flex justify-between"><h4 class="group-hover:text-primary line-clamp-1 font-medium">${escape_html(similarJob.title)}</h4> `;
                if (similarJob.matchPercentage) {
                  $$payload4.out += "<!--[-->";
                  Badge($$payload4, {
                    variant: "outline",
                    class: getScoreColorClass(similarJob.matchPercentage / 100),
                    children: ($$payload5) => {
                      Sparkles($$payload5, { class: "mr-1 h-3 w-3" });
                      $$payload5.out += `<!----> ${escape_html(similarJob.matchPercentage)}% Match`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> <div class="flex items-center gap-2"><p class="line-clamp-1 text-sm text-gray-600">${escape_html(similarJob.company)}</p> `;
                if (similarJob.salary) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span class="text-xs text-gray-500">• ${escape_html(similarJob.salary)}</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> <div class="mt-2 flex items-center justify-between"><div class="flex items-center gap-1 text-xs text-gray-500">`;
                Map_pin($$payload4, { class: "h-3 w-3" });
                $$payload4.out += `<!----> <span>${escape_html(similarJob.location || "Remote")}</span></div> `;
                if (similarJob.postedDate) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span class="text-xs text-gray-500">${escape_html(getTimeSincePosted(similarJob.postedDate))}</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> <div class="group-hover:border-primary/30 absolute inset-0 rounded-lg border-2 border-transparent transition-colors"></div></a>`;
              }
              $$payload4.out += `<!--]--> `;
              Button($$payload4, {
                variant: "outline",
                class: "w-full",
                asChild: true,
                children: ($$payload5) => {
                  $$payload5.out += `<a href="/dashboard/jobs">`;
                  Sparkles($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Find more matching jobs</a>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div></div></div> `;
    Root($$payload2, {
      get open() {
        return showReportDialog;
      },
      set open($$value) {
        showReportDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-md",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Report Job`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Help us maintain quality job listings by reporting issues with this job.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <form class="grid gap-4 py-4"><div class="grid gap-2">`;
            Label($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Reason for reporting`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              class: "min-h-[120px]",
              get value() {
                return reportReason;
              },
              set value($$value) {
                reportReason = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (reportReason.trim().length === 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-muted-foreground text-xs">Please provide details to help our team review this listing.</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="text-muted-foreground text-xs"><p>Common reasons for reporting:</p> <ul class="mt-1 list-disc pl-5"><li>Job posting is a scam</li> <li>Misleading information</li> <li>Discriminatory content</li> <li>Duplicate listing</li></ul></div> `;
            Dialog_footer($$payload4, {
              class: "pt-2 sm:justify-between",
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: () => {
                    showReportDialog = false;
                    reportReason = "";
                  },
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "submit",
                  variant: "destructive",
                  disabled: !reportReason.trim(),
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Report Job`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Root$1($$payload2, {
      get open() {
        return showApplyDialog;
      },
      set open($$value) {
        showApplyDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Alert_dialog_content($$payload3, {
          children: ($$payload4) => {
            Alert_dialog_header($$payload4, {
              children: ($$payload5) => {
                Alert_dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Did you apply to this job?`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Alert_dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->We opened the job posting in a new tab. If you submitted an application, click "Yes" to add
        it to your tracker.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Alert_dialog_footer($$payload4, {
              children: ($$payload5) => {
                Alert_dialog_cancel($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->No, I didn't apply`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Alert_dialog_action($$payload5, {
                  onclick: confirmJobApplication,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Yes, I applied`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Fwc8Cl7x.js.map
