import { p as push, O as escape_html, q as pop } from './index3-CqUPEnZw.js';
import 'clsx';
import { B as Button } from './button-CrucCo1G.js';
import { S as Skeleton } from './skeleton-C-NLefl9.js';
import { Z as Zoom_out, a as Zoom_in } from './zoom-out-vip0ZIci.js';
import { R as Rotate_cw } from './rotate-cw-CWqzplUz.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import { D as Download } from './download-CLn66Ope.js';

function UniversalDocumentViewer($$payload, $$props) {
  push();
  const { document } = $$props;
  let loading = true;
  let scale = 1;
  let pdfDocument = null;
  function getDocumentType() {
    if (!document?.fileUrl) return "unknown";
    const url = document.fileUrl.toLowerCase();
    const fileName = document.fileName?.toLowerCase() || "";
    if (url.match(/\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff)$/) || fileName.match(/\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff)$/) || document.type?.includes("image")) {
      return "image";
    }
    if (url.match(/\.(pdf)$/) || fileName.match(/\.(pdf)$/) || document.type?.includes("pdf")) {
      return "pdf";
    }
    if (url.match(/\.(docx)$/) || fileName.match(/\.(docx)$/) || document.type?.includes("docx")) {
      return "docx";
    }
    if (url.match(/\.(doc)$/) || fileName.match(/\.(doc)$/) || document.type?.includes("doc")) {
      return "doc";
    }
    if (url.match(/\.(txt|md|rtf)$/) || fileName.match(/\.(txt|md|rtf)$/) || document.type?.includes("text")) {
      return "text";
    }
    if (url.match(/\.(xls|xlsx|ppt|pptx)$/) || fileName.match(/\.(xls|xlsx|ppt|pptx)$/) || document.type?.includes("office")) {
      return "office";
    }
    if (url.match(/\.(js|ts|html|css|json|xml|py|java|c|cpp|cs|go|rb|php)$/) || fileName.match(/\.(js|ts|html|css|json|xml|py|java|c|cpp|cs|go|rb|php)$/) || document.type?.includes("code")) {
      return "code";
    }
    return "generic";
  }
  function downloadDocument() {
    if (document?.fileUrl) {
      window.open(document.fileUrl, "_blank");
    }
  }
  function openInNewTab() {
    if (document?.fileUrl) {
      window.open(document.fileUrl, "_blank");
    }
  }
  function zoomIn() {
    scale += 0.2;
    if (getDocumentType() === "pdf" && pdfDocument) ;
  }
  function zoomOut() {
    if (scale <= 0.4) return;
    scale -= 0.2;
    if (getDocumentType() === "pdf" && pdfDocument) ;
  }
  function rotate() {
  }
  $$payload.out += `<div class="document-viewer flex h-full w-full flex-col overflow-hidden rounded-md border"><div class="flex flex-1 items-center justify-center overflow-auto bg-gray-100">`;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center p-4">`;
    Skeleton($$payload, { class: "h-[400px] w-[300px] rounded-md" });
    $$payload.out += `<!----> <p class="mt-4 text-sm text-gray-500">Loading document...</p></div>`;
  }
  $$payload.out += `<!--]--></div> <div class="flex items-center justify-between border-t bg-gray-50 p-2"><div class="flex items-center space-x-2"><span class="text-sm font-medium">${escape_html(document?.label || "Document")}</span> <span class="text-xs text-gray-500">${escape_html(document?.fileName || "")}</span> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="flex items-center space-x-2">`;
  if (getDocumentType() === "image" || getDocumentType() === "pdf") {
    $$payload.out += "<!--[-->";
    Button($$payload, {
      variant: "outline",
      size: "sm",
      onclick: zoomOut,
      disabled: loading,
      children: ($$payload2) => {
        Zoom_out($$payload2, { class: "h-4 w-4" });
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    Button($$payload, {
      variant: "outline",
      size: "sm",
      onclick: zoomIn,
      disabled: loading,
      children: ($$payload2) => {
        Zoom_in($$payload2, { class: "h-4 w-4" });
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (getDocumentType() === "image") {
    $$payload.out += "<!--[-->";
    Button($$payload, {
      variant: "outline",
      size: "sm",
      onclick: rotate,
      disabled: loading,
      children: ($$payload2) => {
        Rotate_cw($$payload2, { class: "h-4 w-4" });
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  Button($$payload, {
    variant: "outline",
    size: "sm",
    onclick: openInNewTab,
    disabled: loading,
    children: ($$payload2) => {
      External_link($$payload2, { class: "mr-1 h-4 w-4" });
      $$payload2.out += `<!----> Open`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    size: "sm",
    onclick: downloadDocument,
    disabled: loading,
    children: ($$payload2) => {
      Download($$payload2, { class: "mr-1 h-4 w-4" });
      $$payload2.out += `<!----> Download`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div>`;
  pop();
}

export { UniversalDocumentViewer as U };
//# sourceMappingURL=UniversalDocumentViewer-Cv4LqmRL.js.map
