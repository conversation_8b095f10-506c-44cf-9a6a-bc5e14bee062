import { p as prisma } from './prisma-Cit_HrSw.js';

async function searchUsers(query, options = {}) {
  try {
    if (!query || query.length < 2) return { hits: [] };
    const { limit = 10, teamId = null } = options;
    const where = {
      OR: [
        { name: { contains: query, mode: "insensitive" } },
        { email: { contains: query, mode: "insensitive" } }
      ]
    };
    if (teamId) {
      where.TeamMember = {
        some: {
          teamId
        }
      };
    }
    const users = await prisma.user.findMany({
      where,
      take: limit,
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
        createdAt: true,
        updatedAt: true
      }
    });
    return {
      hits: users.map((user) => ({
        objectID: user.id,
        id: user.id,
        ...user
      }))
    };
  } catch (error) {
    console.error("Error searching users:", error);
    return { hits: [] };
  }
}
async function searchJobs(query, options = {}) {
  try {
    if (!query || query.length < 2) return { hits: [] };
    const { limit = 10, location = null } = options;
    const where = {
      title: { contains: query, mode: "insensitive" }
    };
    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }
    const jobs = await prisma.job_listing.findMany({
      where,
      take: limit,
      orderBy: {
        postedDate: "desc"
      },
      select: {
        id: true,
        title: true,
        company: true,
        location: true,
        url: true,
        postedDate: true
      }
    });
    return {
      hits: jobs.map((job) => ({
        objectID: job.id,
        id: job.id,
        ...job
      }))
    };
  } catch (error) {
    console.error("Error searching jobs:", error);
    return { hits: [] };
  }
}
async function searchDocuments(query, options = {}) {
  try {
    if (!query || query.length < 2) return { hits: [] };
    const { limit = 10, userId = null } = options;
    const where = {
      OR: [
        { label: { contains: query, mode: "insensitive" } },
        { fileName: { contains: query, mode: "insensitive" } }
      ]
    };
    if (userId) {
      where.userId = userId;
    }
    const documents = await prisma.document.findMany({
      where,
      take: limit,
      orderBy: {
        updatedAt: "desc"
      },
      select: {
        id: true,
        label: true,
        type: true,
        fileName: true,
        fileUrl: true,
        createdAt: true,
        updatedAt: true
      }
    });
    return {
      hits: documents.map((doc) => ({
        objectID: doc.id,
        id: doc.id,
        title: doc.label,
        type: doc.type,
        ...doc
      }))
    };
  } catch (error) {
    console.error("Error searching documents:", error);
    return { hits: [] };
  }
}

export { searchJobs as a, searchDocuments as b, searchUsers as s };
//# sourceMappingURL=prisma-search-service-CcRkP_J1.js.map
