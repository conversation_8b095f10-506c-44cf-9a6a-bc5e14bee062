import { p as push, V as copy_payload, W as assign_payload, q as pop, P as stringify, O as escape_html, M as ensure_array_like, K as fallback, Q as bind_props, ab as store_mutate, S as store_get, T as unsubscribe_stores } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { B as Badge } from './badge-C9pSznab.js';
import { R as Root$4, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { R as Root$5, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { B as Bell } from './bell-C9_YgkSj.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { E as Ellipsis_vertical } from './ellipsis-vertical-D1_2-qGm.js';
import { D as Dropdown_menu_item } from './dropdown-menu-item-DwivDmnZ.js';
import { B as Bell_off, L as Loader } from './loader-CdDCbeER.js';
import { S as Square_pen } from './square-pen-DCE_ltl5.js';
import { D as Dropdown_menu_separator } from './dropdown-menu-separator-B5VQzuNH.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { R as Root$6, d as Dialog_overlay, D as Dialog_content } from './index7-BURUpWjT.js';
import { I as Input } from './input-DF0gPqYN.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { R as Root$3, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { w as writable, d as derived, g as get } from './index2-Cut0V_vU.js';
import { g as getDefaultExportFromCjs } from './_commonjsHelpers-BFTU3MAI.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { I as Info } from './info-Ce09B-Yv.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { g as goto } from './client-dNyMPa8V.js';
import { P as Provider, R as Root$2, T as Tooltip_trigger, a as Tooltip_content } from './index8-9uwikfBL.js';
import { c as createEventDispatcher } from './index-server-CezSOnuG.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { D as Dollar_sign } from './dollar-sign-CXBwKToB.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root$1, a as Sheet_content, b as Sheet_header, c as Sheet_title, d as Sheet_description } from './index10-F28UXWIO.js';
import { S as Skeleton } from './skeleton-C-NLefl9.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { B as Bookmark } from './bookmark-DazMkrfp.js';
import { S as Settings } from './settings-STaOxCkl.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { X } from './x-DwZgpWRG.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import { S as Sheet_footer } from './sheet-footer-B80ycEhL.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './scroll-lock-BkBz2nVp.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './use-grace-area.svelte-CrXiOQDy.js';
import './dialog-overlay-CspOQRJq.js';
import './dialog-description2-rfr-pd9k.js';
import './Icon-A4vzmk-O.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './hidden-input-1eDzjGOB.js';

var has = Object.prototype.hasOwnProperty;

function dequal(foo, bar) {
	var ctor, len;
	if (foo === bar) return true;

	if (foo && bar && (ctor=foo.constructor) === bar.constructor) {
		if (ctor === Date) return foo.getTime() === bar.getTime();
		if (ctor === RegExp) return foo.toString() === bar.toString();

		if (ctor === Array) {
			if ((len=foo.length) === bar.length) {
				while (len-- && dequal(foo[len], bar[len]));
			}
			return len === -1;
		}

		if (!ctor || typeof foo === 'object') {
			len = 0;
			for (ctor in foo) {
				if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;
				if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;
			}
			return Object.keys(bar).length === len;
		}
	}

	return foo !== foo && bar !== bar;
}

/**
 * Based on Kendo UI Core expression code <https://github.com/telerik/kendo-ui-core#license-information>
 */

var propertyExpr;
var hasRequiredPropertyExpr;

function requirePropertyExpr () {
	if (hasRequiredPropertyExpr) return propertyExpr;
	hasRequiredPropertyExpr = 1;

	function Cache(maxSize) {
	  this._maxSize = maxSize;
	  this.clear();
	}
	Cache.prototype.clear = function () {
	  this._size = 0;
	  this._values = Object.create(null);
	};
	Cache.prototype.get = function (key) {
	  return this._values[key]
	};
	Cache.prototype.set = function (key, value) {
	  this._size >= this._maxSize && this.clear();
	  if (!(key in this._values)) this._size++;

	  return (this._values[key] = value)
	};

	var SPLIT_REGEX = /[^.^\]^[]+|(?=\[\]|\.\.)/g,
	  DIGIT_REGEX = /^\d+$/,
	  LEAD_DIGIT_REGEX = /^\d/,
	  SPEC_CHAR_REGEX = /[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,
	  CLEAN_QUOTES_REGEX = /^\s*(['"]?)(.*?)(\1)\s*$/,
	  MAX_CACHE_SIZE = 512;

	var pathCache = new Cache(MAX_CACHE_SIZE),
	  setCache = new Cache(MAX_CACHE_SIZE),
	  getCache = new Cache(MAX_CACHE_SIZE);

	propertyExpr = {
	  Cache: Cache,

	  split: split,

	  normalizePath: normalizePath,

	  setter: function (path) {
	    var parts = normalizePath(path);

	    return (
	      setCache.get(path) ||
	      setCache.set(path, function setter(obj, value) {
	        var index = 0;
	        var len = parts.length;
	        var data = obj;

	        while (index < len - 1) {
	          var part = parts[index];
	          if (
	            part === '__proto__' ||
	            part === 'constructor' ||
	            part === 'prototype'
	          ) {
	            return obj
	          }

	          data = data[parts[index++]];
	        }
	        data[parts[index]] = value;
	      })
	    )
	  },

	  getter: function (path, safe) {
	    var parts = normalizePath(path);
	    return (
	      getCache.get(path) ||
	      getCache.set(path, function getter(data) {
	        var index = 0,
	          len = parts.length;
	        while (index < len) {
	          if (data != null || !safe) data = data[parts[index++]];
	          else return
	        }
	        return data
	      })
	    )
	  },

	  join: function (segments) {
	    return segments.reduce(function (path, part) {
	      return (
	        path +
	        (isQuoted(part) || DIGIT_REGEX.test(part)
	          ? '[' + part + ']'
	          : (path ? '.' : '') + part)
	      )
	    }, '')
	  },

	  forEach: function (path, cb, thisArg) {
	    forEach(Array.isArray(path) ? path : split(path), cb, thisArg);
	  },
	};

	function normalizePath(path) {
	  return (
	    pathCache.get(path) ||
	    pathCache.set(
	      path,
	      split(path).map(function (part) {
	        return part.replace(CLEAN_QUOTES_REGEX, '$2')
	      })
	    )
	  )
	}

	function split(path) {
	  return path.match(SPLIT_REGEX) || ['']
	}

	function forEach(parts, iter, thisArg) {
	  var len = parts.length,
	    part,
	    idx,
	    isArray,
	    isBracket;

	  for (idx = 0; idx < len; idx++) {
	    part = parts[idx];

	    if (part) {
	      if (shouldBeQuoted(part)) {
	        part = '"' + part + '"';
	      }

	      isBracket = isQuoted(part);
	      isArray = !isBracket && /^\d+$/.test(part);

	      iter.call(thisArg, part, isBracket, isArray, idx, parts);
	    }
	  }
	}

	function isQuoted(str) {
	  return (
	    typeof str === 'string' && str && ["'", '"'].indexOf(str.charAt(0)) !== -1
	  )
	}

	function hasLeadingNumber(part) {
	  return part.match(LEAD_DIGIT_REGEX) && !part.match(DIGIT_REGEX)
	}

	function hasSpecialChars(part) {
	  return SPEC_CHAR_REGEX.test(part)
	}

	function shouldBeQuoted(part) {
	  return !isQuoted(part) && (hasLeadingNumber(part) || hasSpecialChars(part))
	}
	return propertyExpr;
}

var propertyExprExports = requirePropertyExpr();

var tinyCase;
var hasRequiredTinyCase;

function requireTinyCase () {
	if (hasRequiredTinyCase) return tinyCase;
	hasRequiredTinyCase = 1;
	const reWords = /[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g;

	const words = (str) => str.match(reWords) || [];

	const upperFirst = (str) => str[0].toUpperCase() + str.slice(1);

	const join = (str, d) => words(str).join(d).toLowerCase();

	const camelCase = (str) =>
	  words(str).reduce(
	    (acc, next) =>
	      `${acc}${
	        !acc
	          ? next.toLowerCase()
	          : next[0].toUpperCase() + next.slice(1).toLowerCase()
	      }`,
	    '',
	  );

	const pascalCase = (str) => upperFirst(camelCase(str));

	const snakeCase = (str) => join(str, '_');

	const kebabCase = (str) => join(str, '-');

	const sentenceCase = (str) => upperFirst(join(str, ' '));

	const titleCase = (str) => words(str).map(upperFirst).join(' ');

	tinyCase = {
	  words,
	  upperFirst,
	  camelCase,
	  pascalCase,
	  snakeCase,
	  kebabCase,
	  sentenceCase,
	  titleCase,
	};
	return tinyCase;
}

var tinyCaseExports = requireTinyCase();

var toposort$1 = {exports: {}};

var hasRequiredToposort;

function requireToposort () {
	if (hasRequiredToposort) return toposort$1.exports;
	hasRequiredToposort = 1;
	/**
	 * Topological sorting function
	 *
	 * @param {Array} edges
	 * @returns {Array}
	 */

	toposort$1.exports = function(edges) {
	  return toposort(uniqueNodes(edges), edges)
	};

	toposort$1.exports.array = toposort;

	function toposort(nodes, edges) {
	  var cursor = nodes.length
	    , sorted = new Array(cursor)
	    , visited = {}
	    , i = cursor
	    // Better data structures make algorithm much faster.
	    , outgoingEdges = makeOutgoingEdges(edges)
	    , nodesHash = makeNodesHash(nodes);

	  // check for unknown nodes
	  edges.forEach(function(edge) {
	    if (!nodesHash.has(edge[0]) || !nodesHash.has(edge[1])) {
	      throw new Error('Unknown node. There is an unknown node in the supplied edges.')
	    }
	  });

	  while (i--) {
	    if (!visited[i]) visit(nodes[i], i, new Set());
	  }

	  return sorted

	  function visit(node, i, predecessors) {
	    if(predecessors.has(node)) {
	      var nodeRep;
	      try {
	        nodeRep = ", node was:" + JSON.stringify(node);
	      } catch(e) {
	        nodeRep = "";
	      }
	      throw new Error('Cyclic dependency' + nodeRep)
	    }

	    if (!nodesHash.has(node)) {
	      throw new Error('Found unknown node. Make sure to provided all involved nodes. Unknown node: '+JSON.stringify(node))
	    }

	    if (visited[i]) return;
	    visited[i] = true;

	    var outgoing = outgoingEdges.get(node) || new Set();
	    outgoing = Array.from(outgoing);

	    if (i = outgoing.length) {
	      predecessors.add(node);
	      do {
	        var child = outgoing[--i];
	        visit(child, nodesHash.get(child), predecessors);
	      } while (i)
	      predecessors.delete(node);
	    }

	    sorted[--cursor] = node;
	  }
	}

	function uniqueNodes(arr){
	  var res = new Set();
	  for (var i = 0, len = arr.length; i < len; i++) {
	    var edge = arr[i];
	    res.add(edge[0]);
	    res.add(edge[1]);
	  }
	  return Array.from(res)
	}

	function makeOutgoingEdges(arr){
	  var edges = new Map();
	  for (var i = 0, len = arr.length; i < len; i++) {
	    var edge = arr[i];
	    if (!edges.has(edge[0])) edges.set(edge[0], new Set());
	    if (!edges.has(edge[1])) edges.set(edge[1], new Set());
	    edges.get(edge[0]).add(edge[1]);
	  }
	  return edges
	}

	function makeNodesHash(arr){
	  var res = new Map();
	  for (var i = 0, len = arr.length; i < len; i++) {
	    res.set(arr[i], i);
	  }
	  return res
	}
	return toposort$1.exports;
}

var toposortExports = requireToposort();
var toposort = /*@__PURE__*/getDefaultExportFromCjs(toposortExports);

const toString = Object.prototype.toString;
const errorToString = Error.prototype.toString;
const regExpToString = RegExp.prototype.toString;
const symbolToString = typeof Symbol !== 'undefined' ? Symbol.prototype.toString : () => '';
const SYMBOL_REGEXP = /^Symbol\((.*)\)(.*)$/;
function printNumber(val) {
  if (val != +val) return 'NaN';
  const isNegativeZero = val === 0 && 1 / val < 0;
  return isNegativeZero ? '-0' : '' + val;
}
function printSimpleValue(val, quoteStrings = false) {
  if (val == null || val === true || val === false) return '' + val;
  const typeOf = typeof val;
  if (typeOf === 'number') return printNumber(val);
  if (typeOf === 'string') return quoteStrings ? `"${val}"` : val;
  if (typeOf === 'function') return '[Function ' + (val.name || 'anonymous') + ']';
  if (typeOf === 'symbol') return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');
  const tag = toString.call(val).slice(8, -1);
  if (tag === 'Date') return isNaN(val.getTime()) ? '' + val : val.toISOString(val);
  if (tag === 'Error' || val instanceof Error) return '[' + errorToString.call(val) + ']';
  if (tag === 'RegExp') return regExpToString.call(val);
  return null;
}
function printValue(value, quoteStrings) {
  let result = printSimpleValue(value, quoteStrings);
  if (result !== null) return result;
  return JSON.stringify(value, function (key, value) {
    let result = printSimpleValue(this[key], quoteStrings);
    if (result !== null) return result;
    return value;
  }, 2);
}

function toArray(value) {
  return value == null ? [] : [].concat(value);
}

let _Symbol$toStringTag, _Symbol$hasInstance, _Symbol$toStringTag2;
let strReg = /\$\{\s*(\w+)\s*\}/g;
_Symbol$toStringTag = Symbol.toStringTag;
class ValidationErrorNoStack {
  constructor(errorOrErrors, value, field, type) {
    this.name = void 0;
    this.message = void 0;
    this.value = void 0;
    this.path = void 0;
    this.type = void 0;
    this.params = void 0;
    this.errors = void 0;
    this.inner = void 0;
    this[_Symbol$toStringTag] = 'Error';
    this.name = 'ValidationError';
    this.value = value;
    this.path = field;
    this.type = type;
    this.errors = [];
    this.inner = [];
    toArray(errorOrErrors).forEach(err => {
      if (ValidationError.isError(err)) {
        this.errors.push(...err.errors);
        const innerErrors = err.inner.length ? err.inner : [err];
        this.inner.push(...innerErrors);
      } else {
        this.errors.push(err);
      }
    });
    this.message = this.errors.length > 1 ? `${this.errors.length} errors occurred` : this.errors[0];
  }
}
_Symbol$hasInstance = Symbol.hasInstance;
_Symbol$toStringTag2 = Symbol.toStringTag;
class ValidationError extends Error {
  static formatError(message, params) {
    // Attempt to make the path more friendly for error message interpolation.
    const path = params.label || params.path || 'this';
    // Store the original path under `originalPath` so it isn't lost to custom
    // message functions; e.g., ones provided in `setLocale()` calls.
    params = Object.assign({}, params, {
      path,
      originalPath: params.path
    });
    if (typeof message === 'string') return message.replace(strReg, (_, key) => printValue(params[key]));
    if (typeof message === 'function') return message(params);
    return message;
  }
  static isError(err) {
    return err && err.name === 'ValidationError';
  }
  constructor(errorOrErrors, value, field, type, disableStack) {
    const errorNoStack = new ValidationErrorNoStack(errorOrErrors, value, field, type);
    if (disableStack) {
      return errorNoStack;
    }
    super();
    this.value = void 0;
    this.path = void 0;
    this.type = void 0;
    this.params = void 0;
    this.errors = [];
    this.inner = [];
    this[_Symbol$toStringTag2] = 'Error';
    this.name = errorNoStack.name;
    this.message = errorNoStack.message;
    this.type = errorNoStack.type;
    this.value = errorNoStack.value;
    this.path = errorNoStack.path;
    this.errors = errorNoStack.errors;
    this.inner = errorNoStack.inner;
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ValidationError);
    }
  }
  static [_Symbol$hasInstance](inst) {
    return ValidationErrorNoStack[Symbol.hasInstance](inst) || super[Symbol.hasInstance](inst);
  }
}

let mixed = {
  default: '${path} is invalid',
  required: '${path} is a required field',
  defined: '${path} must be defined',
  notNull: '${path} cannot be null',
  oneOf: '${path} must be one of the following values: ${values}',
  notOneOf: '${path} must not be one of the following values: ${values}',
  notType: ({
    path,
    type,
    value,
    originalValue
  }) => {
    const castMsg = originalValue != null && originalValue !== value ? ` (cast from the value \`${printValue(originalValue, true)}\`).` : '.';
    return type !== 'mixed' ? `${path} must be a \`${type}\` type, ` + `but the final value was: \`${printValue(value, true)}\`` + castMsg : `${path} must match the configured type. ` + `The validated value was: \`${printValue(value, true)}\`` + castMsg;
  }
};
let string = {
  length: '${path} must be exactly ${length} characters',
  min: '${path} must be at least ${min} characters',
  max: '${path} must be at most ${max} characters',
  matches: '${path} must match the following: "${regex}"',
  email: '${path} must be a valid email',
  url: '${path} must be a valid URL',
  uuid: '${path} must be a valid UUID',
  datetime: '${path} must be a valid ISO date-time',
  datetime_precision: '${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits',
  datetime_offset: '${path} must be a valid ISO date-time with UTC "Z" timezone',
  trim: '${path} must be a trimmed string',
  lowercase: '${path} must be a lowercase string',
  uppercase: '${path} must be a upper case string'
};
let number = {
  min: '${path} must be greater than or equal to ${min}',
  max: '${path} must be less than or equal to ${max}',
  lessThan: '${path} must be less than ${less}',
  moreThan: '${path} must be greater than ${more}',
  positive: '${path} must be a positive number',
  negative: '${path} must be a negative number',
  integer: '${path} must be an integer'
};
let date = {
  min: '${path} field must be later than ${min}',
  max: '${path} field must be at earlier than ${max}'
};
let boolean = {
  isValue: '${path} field must be ${value}'
};
let object = {
  noUnknown: '${path} field has unspecified keys: ${unknown}',
  exact: '${path} object contains unknown properties: ${properties}'
};
let array = {
  min: '${path} field must have at least ${min} items',
  max: '${path} field must have less than or equal to ${max} items',
  length: '${path} must have ${length} items'
};
let tuple = {
  notType: params => {
    const {
      path,
      value,
      spec
    } = params;
    const typeLen = spec.types.length;
    if (Array.isArray(value)) {
      if (value.length < typeLen) return `${path} tuple value has too few items, expected a length of ${typeLen} but got ${value.length} for value: \`${printValue(value, true)}\``;
      if (value.length > typeLen) return `${path} tuple value has too many items, expected a length of ${typeLen} but got ${value.length} for value: \`${printValue(value, true)}\``;
    }
    return ValidationError.formatError(mixed.notType, params);
  }
};
Object.assign(Object.create(null), {
  mixed,
  string,
  number,
  date,
  object,
  array,
  boolean,
  tuple
});

const isSchema = obj => obj && obj.__isYupSchema__;

class Condition {
  static fromOptions(refs, config) {
    if (!config.then && !config.otherwise) throw new TypeError('either `then:` or `otherwise:` is required for `when()` conditions');
    let {
      is,
      then,
      otherwise
    } = config;
    let check = typeof is === 'function' ? is : (...values) => values.every(value => value === is);
    return new Condition(refs, (values, schema) => {
      var _branch;
      let branch = check(...values) ? then : otherwise;
      return (_branch = branch == null ? void 0 : branch(schema)) != null ? _branch : schema;
    });
  }
  constructor(refs, builder) {
    this.fn = void 0;
    this.refs = refs;
    this.refs = refs;
    this.fn = builder;
  }
  resolve(base, options) {
    let values = this.refs.map(ref =>
    // TODO: ? operator here?
    ref.getValue(options == null ? void 0 : options.value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context));
    let schema = this.fn(values, base, options);
    if (schema === undefined ||
    // @ts-ignore this can be base
    schema === base) {
      return base;
    }
    if (!isSchema(schema)) throw new TypeError('conditions must return a schema object');
    return schema.resolve(options);
  }
}

const prefixes = {
  context: '$',
  value: '.'
};
class Reference {
  constructor(key, options = {}) {
    this.key = void 0;
    this.isContext = void 0;
    this.isValue = void 0;
    this.isSibling = void 0;
    this.path = void 0;
    this.getter = void 0;
    this.map = void 0;
    if (typeof key !== 'string') throw new TypeError('ref must be a string, got: ' + key);
    this.key = key.trim();
    if (key === '') throw new TypeError('ref must be a non-empty string');
    this.isContext = this.key[0] === prefixes.context;
    this.isValue = this.key[0] === prefixes.value;
    this.isSibling = !this.isContext && !this.isValue;
    let prefix = this.isContext ? prefixes.context : this.isValue ? prefixes.value : '';
    this.path = this.key.slice(prefix.length);
    this.getter = this.path && propertyExprExports.getter(this.path, true);
    this.map = options.map;
  }
  getValue(value, parent, context) {
    let result = this.isContext ? context : this.isValue ? value : parent;
    if (this.getter) result = this.getter(result || {});
    if (this.map) result = this.map(result);
    return result;
  }

  /**
   *
   * @param {*} value
   * @param {Object} options
   * @param {Object=} options.context
   * @param {Object=} options.parent
   */
  cast(value, options) {
    return this.getValue(value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context);
  }
  resolve() {
    return this;
  }
  describe() {
    return {
      type: 'ref',
      key: this.key
    };
  }
  toString() {
    return `Ref(${this.key})`;
  }
  static isRef(value) {
    return value && value.__isYupRef;
  }
}

// @ts-ignore
Reference.prototype.__isYupRef = true;

const isAbsent = value => value == null;

function createValidation(config) {
  function validate({
    value,
    path = '',
    options,
    originalValue,
    schema
  }, panic, next) {
    const {
      name,
      test,
      params,
      message,
      skipAbsent
    } = config;
    let {
      parent,
      context,
      abortEarly = schema.spec.abortEarly,
      disableStackTrace = schema.spec.disableStackTrace
    } = options;
    function resolve(item) {
      return Reference.isRef(item) ? item.getValue(value, parent, context) : item;
    }
    function createError(overrides = {}) {
      const nextParams = Object.assign({
        value,
        originalValue,
        label: schema.spec.label,
        path: overrides.path || path,
        spec: schema.spec,
        disableStackTrace: overrides.disableStackTrace || disableStackTrace
      }, params, overrides.params);
      for (const key of Object.keys(nextParams)) nextParams[key] = resolve(nextParams[key]);
      const error = new ValidationError(ValidationError.formatError(overrides.message || message, nextParams), value, nextParams.path, overrides.type || name, nextParams.disableStackTrace);
      error.params = nextParams;
      return error;
    }
    const invalid = abortEarly ? panic : next;
    let ctx = {
      path,
      parent,
      type: name,
      from: options.from,
      createError,
      resolve,
      options,
      originalValue,
      schema
    };
    const handleResult = validOrError => {
      if (ValidationError.isError(validOrError)) invalid(validOrError);else if (!validOrError) invalid(createError());else next(null);
    };
    const handleError = err => {
      if (ValidationError.isError(err)) invalid(err);else panic(err);
    };
    const shouldSkip = skipAbsent && isAbsent(value);
    if (shouldSkip) {
      return handleResult(true);
    }
    let result;
    try {
      var _result;
      result = test.call(ctx, value, ctx);
      if (typeof ((_result = result) == null ? void 0 : _result.then) === 'function') {
        if (options.sync) {
          throw new Error(`Validation test of type: "${ctx.type}" returned a Promise during a synchronous validate. ` + `This test will finish after the validate call has returned`);
        }
        return Promise.resolve(result).then(handleResult, handleError);
      }
    } catch (err) {
      handleError(err);
      return;
    }
    handleResult(result);
  }
  validate.OPTIONS = config;
  return validate;
}

function getIn(schema, path, value, context = value) {
  let parent, lastPart, lastPartDebug;

  // root path: ''
  if (!path) return {
    parent,
    parentPath: path,
    schema
  };
  propertyExprExports.forEach(path, (_part, isBracket, isArray) => {
    let part = isBracket ? _part.slice(1, _part.length - 1) : _part;
    schema = schema.resolve({
      context,
      parent,
      value
    });
    let isTuple = schema.type === 'tuple';
    let idx = isArray ? parseInt(part, 10) : 0;
    if (schema.innerType || isTuple) {
      if (isTuple && !isArray) throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${lastPartDebug}" must contain an index to the tuple element, e.g. "${lastPartDebug}[0]"`);
      if (value && idx >= value.length) {
        throw new Error(`Yup.reach cannot resolve an array item at index: ${_part}, in the path: ${path}. ` + `because there is no value at that index. `);
      }
      parent = value;
      value = value && value[idx];
      schema = isTuple ? schema.spec.types[idx] : schema.innerType;
    }

    // sometimes the array index part of a path doesn't exist: "nested.arr.child"
    // in these cases the current part is the next schema and should be processed
    // in this iteration. For cases where the index signature is included this
    // check will fail and we'll handle the `child` part on the next iteration like normal
    if (!isArray) {
      if (!schema.fields || !schema.fields[part]) throw new Error(`The schema does not contain the path: ${path}. ` + `(failed at: ${lastPartDebug} which is a type: "${schema.type}")`);
      parent = value;
      value = value && value[part];
      schema = schema.fields[part];
    }
    lastPart = part;
    lastPartDebug = isBracket ? '[' + _part + ']' : '.' + _part;
  });
  return {
    schema,
    parent,
    parentPath: lastPart
  };
}

class ReferenceSet extends Set {
  describe() {
    const description = [];
    for (const item of this.values()) {
      description.push(Reference.isRef(item) ? item.describe() : item);
    }
    return description;
  }
  resolveAll(resolve) {
    let result = [];
    for (const item of this.values()) {
      result.push(resolve(item));
    }
    return result;
  }
  clone() {
    return new ReferenceSet(this.values());
  }
  merge(newItems, removeItems) {
    const next = this.clone();
    newItems.forEach(value => next.add(value));
    removeItems.forEach(value => next.delete(value));
    return next;
  }
}

// tweaked from https://github.com/Kelin2025/nanoclone/blob/0abeb7635bda9b68ef2277093f76dbe3bf3948e1/src/index.js
function clone(src, seen = new Map()) {
  if (isSchema(src) || !src || typeof src !== 'object') return src;
  if (seen.has(src)) return seen.get(src);
  let copy;
  if (src instanceof Date) {
    // Date
    copy = new Date(src.getTime());
    seen.set(src, copy);
  } else if (src instanceof RegExp) {
    // RegExp
    copy = new RegExp(src);
    seen.set(src, copy);
  } else if (Array.isArray(src)) {
    // Array
    copy = new Array(src.length);
    seen.set(src, copy);
    for (let i = 0; i < src.length; i++) copy[i] = clone(src[i], seen);
  } else if (src instanceof Map) {
    // Map
    copy = new Map();
    seen.set(src, copy);
    for (const [k, v] of src.entries()) copy.set(k, clone(v, seen));
  } else if (src instanceof Set) {
    // Set
    copy = new Set();
    seen.set(src, copy);
    for (const v of src) copy.add(clone(v, seen));
  } else if (src instanceof Object) {
    // Object
    copy = {};
    seen.set(src, copy);
    for (const [k, v] of Object.entries(src)) copy[k] = clone(v, seen);
  } else {
    throw Error(`Unable to clone ${src}`);
  }
  return copy;
}

// If `CustomSchemaMeta` isn't extended with any keys, we'll fall back to a
// loose Record definition allowing free form usage.
class Schema {
  constructor(options) {
    this.type = void 0;
    this.deps = [];
    this.tests = void 0;
    this.transforms = void 0;
    this.conditions = [];
    this._mutate = void 0;
    this.internalTests = {};
    this._whitelist = new ReferenceSet();
    this._blacklist = new ReferenceSet();
    this.exclusiveTests = Object.create(null);
    this._typeCheck = void 0;
    this.spec = void 0;
    this.tests = [];
    this.transforms = [];
    this.withMutation(() => {
      this.typeError(mixed.notType);
    });
    this.type = options.type;
    this._typeCheck = options.check;
    this.spec = Object.assign({
      strip: false,
      strict: false,
      abortEarly: true,
      recursive: true,
      disableStackTrace: false,
      nullable: false,
      optional: true,
      coerce: true
    }, options == null ? void 0 : options.spec);
    this.withMutation(s => {
      s.nonNullable();
    });
  }

  // TODO: remove
  get _type() {
    return this.type;
  }
  clone(spec) {
    if (this._mutate) {
      if (spec) Object.assign(this.spec, spec);
      return this;
    }

    // if the nested value is a schema we can skip cloning, since
    // they are already immutable
    const next = Object.create(Object.getPrototypeOf(this));

    // @ts-expect-error this is readonly
    next.type = this.type;
    next._typeCheck = this._typeCheck;
    next._whitelist = this._whitelist.clone();
    next._blacklist = this._blacklist.clone();
    next.internalTests = Object.assign({}, this.internalTests);
    next.exclusiveTests = Object.assign({}, this.exclusiveTests);

    // @ts-expect-error this is readonly
    next.deps = [...this.deps];
    next.conditions = [...this.conditions];
    next.tests = [...this.tests];
    next.transforms = [...this.transforms];
    next.spec = clone(Object.assign({}, this.spec, spec));
    return next;
  }
  label(label) {
    let next = this.clone();
    next.spec.label = label;
    return next;
  }
  meta(...args) {
    if (args.length === 0) return this.spec.meta;
    let next = this.clone();
    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);
    return next;
  }
  withMutation(fn) {
    let before = this._mutate;
    this._mutate = true;
    let result = fn(this);
    this._mutate = before;
    return result;
  }
  concat(schema) {
    if (!schema || schema === this) return this;
    if (schema.type !== this.type && this.type !== 'mixed') throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${schema.type}`);
    let base = this;
    let combined = schema.clone();
    const mergedSpec = Object.assign({}, base.spec, combined.spec);
    combined.spec = mergedSpec;
    combined.internalTests = Object.assign({}, base.internalTests, combined.internalTests);

    // manually merge the blacklist/whitelist (the other `schema` takes
    // precedence in case of conflicts)
    combined._whitelist = base._whitelist.merge(schema._whitelist, schema._blacklist);
    combined._blacklist = base._blacklist.merge(schema._blacklist, schema._whitelist);

    // start with the current tests
    combined.tests = base.tests;
    combined.exclusiveTests = base.exclusiveTests;

    // manually add the new tests to ensure
    // the deduping logic is consistent
    combined.withMutation(next => {
      schema.tests.forEach(fn => {
        next.test(fn.OPTIONS);
      });
    });
    combined.transforms = [...base.transforms, ...combined.transforms];
    return combined;
  }
  isType(v) {
    if (v == null) {
      if (this.spec.nullable && v === null) return true;
      if (this.spec.optional && v === undefined) return true;
      return false;
    }
    return this._typeCheck(v);
  }
  resolve(options) {
    let schema = this;
    if (schema.conditions.length) {
      let conditions = schema.conditions;
      schema = schema.clone();
      schema.conditions = [];
      schema = conditions.reduce((prevSchema, condition) => condition.resolve(prevSchema, options), schema);
      schema = schema.resolve(options);
    }
    return schema;
  }
  resolveOptions(options) {
    var _options$strict, _options$abortEarly, _options$recursive, _options$disableStack;
    return Object.assign({}, options, {
      from: options.from || [],
      strict: (_options$strict = options.strict) != null ? _options$strict : this.spec.strict,
      abortEarly: (_options$abortEarly = options.abortEarly) != null ? _options$abortEarly : this.spec.abortEarly,
      recursive: (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive,
      disableStackTrace: (_options$disableStack = options.disableStackTrace) != null ? _options$disableStack : this.spec.disableStackTrace
    });
  }

  /**
   * Run the configured transform pipeline over an input value.
   */

  cast(value, options = {}) {
    let resolvedSchema = this.resolve(Object.assign({
      value
    }, options));
    let allowOptionality = options.assert === 'ignore-optionality';
    let result = resolvedSchema._cast(value, options);
    if (options.assert !== false && !resolvedSchema.isType(result)) {
      if (allowOptionality && isAbsent(result)) {
        return result;
      }
      let formattedValue = printValue(value);
      let formattedResult = printValue(result);
      throw new TypeError(`The value of ${options.path || 'field'} could not be cast to a value ` + `that satisfies the schema type: "${resolvedSchema.type}". \n\n` + `attempted value: ${formattedValue} \n` + (formattedResult !== formattedValue ? `result of cast: ${formattedResult}` : ''));
    }
    return result;
  }
  _cast(rawValue, options) {
    let value = rawValue === undefined ? rawValue : this.transforms.reduce((prevValue, fn) => fn.call(this, prevValue, rawValue, this), rawValue);
    if (value === undefined) {
      value = this.getDefault(options);
    }
    return value;
  }
  _validate(_value, options = {}, panic, next) {
    let {
      path,
      originalValue = _value,
      strict = this.spec.strict
    } = options;
    let value = _value;
    if (!strict) {
      value = this._cast(value, Object.assign({
        assert: false
      }, options));
    }
    let initialTests = [];
    for (let test of Object.values(this.internalTests)) {
      if (test) initialTests.push(test);
    }
    this.runTests({
      path,
      value,
      originalValue,
      options,
      tests: initialTests
    }, panic, initialErrors => {
      // even if we aren't ending early we can't proceed further if the types aren't correct
      if (initialErrors.length) {
        return next(initialErrors, value);
      }
      this.runTests({
        path,
        value,
        originalValue,
        options,
        tests: this.tests
      }, panic, next);
    });
  }

  /**
   * Executes a set of validations, either schema, produced Tests or a nested
   * schema validate result.
   */
  runTests(runOptions, panic, next) {
    let fired = false;
    let {
      tests,
      value,
      originalValue,
      path,
      options
    } = runOptions;
    let panicOnce = arg => {
      if (fired) return;
      fired = true;
      panic(arg, value);
    };
    let nextOnce = arg => {
      if (fired) return;
      fired = true;
      next(arg, value);
    };
    let count = tests.length;
    let nestedErrors = [];
    if (!count) return nextOnce([]);
    let args = {
      value,
      originalValue,
      path,
      options,
      schema: this
    };
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      test(args, panicOnce, function finishTestRun(err) {
        if (err) {
          Array.isArray(err) ? nestedErrors.push(...err) : nestedErrors.push(err);
        }
        if (--count <= 0) {
          nextOnce(nestedErrors);
        }
      });
    }
  }
  asNestedTest({
    key,
    index,
    parent,
    parentPath,
    originalParent,
    options
  }) {
    const k = key != null ? key : index;
    if (k == null) {
      throw TypeError('Must include `key` or `index` for nested validations');
    }
    const isIndex = typeof k === 'number';
    let value = parent[k];
    const testOptions = Object.assign({}, options, {
      // Nested validations fields are always strict:
      //    1. parent isn't strict so the casting will also have cast inner values
      //    2. parent is strict in which case the nested values weren't cast either
      strict: true,
      parent,
      value,
      originalValue: originalParent[k],
      // FIXME: tests depend on `index` being passed around deeply,
      //   we should not let the options.key/index bleed through
      key: undefined,
      // index: undefined,
      [isIndex ? 'index' : 'key']: k,
      path: isIndex || k.includes('.') ? `${parentPath || ''}[${isIndex ? k : `"${k}"`}]` : (parentPath ? `${parentPath}.` : '') + key
    });
    return (_, panic, next) => this.resolve(testOptions)._validate(value, testOptions, panic, next);
  }
  validate(value, options) {
    var _options$disableStack2;
    let schema = this.resolve(Object.assign({}, options, {
      value
    }));
    let disableStackTrace = (_options$disableStack2 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack2 : schema.spec.disableStackTrace;
    return new Promise((resolve, reject) => schema._validate(value, options, (error, parsed) => {
      if (ValidationError.isError(error)) error.value = parsed;
      reject(error);
    }, (errors, validated) => {
      if (errors.length) reject(new ValidationError(errors, validated, undefined, undefined, disableStackTrace));else resolve(validated);
    }));
  }
  validateSync(value, options) {
    var _options$disableStack3;
    let schema = this.resolve(Object.assign({}, options, {
      value
    }));
    let result;
    let disableStackTrace = (_options$disableStack3 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack3 : schema.spec.disableStackTrace;
    schema._validate(value, Object.assign({}, options, {
      sync: true
    }), (error, parsed) => {
      if (ValidationError.isError(error)) error.value = parsed;
      throw error;
    }, (errors, validated) => {
      if (errors.length) throw new ValidationError(errors, value, undefined, undefined, disableStackTrace);
      result = validated;
    });
    return result;
  }
  isValid(value, options) {
    return this.validate(value, options).then(() => true, err => {
      if (ValidationError.isError(err)) return false;
      throw err;
    });
  }
  isValidSync(value, options) {
    try {
      this.validateSync(value, options);
      return true;
    } catch (err) {
      if (ValidationError.isError(err)) return false;
      throw err;
    }
  }
  _getDefault(options) {
    let defaultValue = this.spec.default;
    if (defaultValue == null) {
      return defaultValue;
    }
    return typeof defaultValue === 'function' ? defaultValue.call(this, options) : clone(defaultValue);
  }
  getDefault(options
  // If schema is defaulted we know it's at least not undefined
  ) {
    let schema = this.resolve(options || {});
    return schema._getDefault(options);
  }
  default(def) {
    if (arguments.length === 0) {
      return this._getDefault();
    }
    let next = this.clone({
      default: def
    });
    return next;
  }
  strict(isStrict = true) {
    return this.clone({
      strict: isStrict
    });
  }
  nullability(nullable, message) {
    const next = this.clone({
      nullable
    });
    next.internalTests.nullable = createValidation({
      message,
      name: 'nullable',
      test(value) {
        return value === null ? this.schema.spec.nullable : true;
      }
    });
    return next;
  }
  optionality(optional, message) {
    const next = this.clone({
      optional
    });
    next.internalTests.optionality = createValidation({
      message,
      name: 'optionality',
      test(value) {
        return value === undefined ? this.schema.spec.optional : true;
      }
    });
    return next;
  }
  optional() {
    return this.optionality(true);
  }
  defined(message = mixed.defined) {
    return this.optionality(false, message);
  }
  nullable() {
    return this.nullability(true);
  }
  nonNullable(message = mixed.notNull) {
    return this.nullability(false, message);
  }
  required(message = mixed.required) {
    return this.clone().withMutation(next => next.nonNullable(message).defined(message));
  }
  notRequired() {
    return this.clone().withMutation(next => next.nullable().optional());
  }
  transform(fn) {
    let next = this.clone();
    next.transforms.push(fn);
    return next;
  }

  /**
   * Adds a test function to the schema's queue of tests.
   * tests can be exclusive or non-exclusive.
   *
   * - exclusive tests, will replace any existing tests of the same name.
   * - non-exclusive: can be stacked
   *
   * If a non-exclusive test is added to a schema with an exclusive test of the same name
   * the exclusive test is removed and further tests of the same name will be stacked.
   *
   * If an exclusive test is added to a schema with non-exclusive tests of the same name
   * the previous tests are removed and further tests of the same name will replace each other.
   */

  test(...args) {
    let opts;
    if (args.length === 1) {
      if (typeof args[0] === 'function') {
        opts = {
          test: args[0]
        };
      } else {
        opts = args[0];
      }
    } else if (args.length === 2) {
      opts = {
        name: args[0],
        test: args[1]
      };
    } else {
      opts = {
        name: args[0],
        message: args[1],
        test: args[2]
      };
    }
    if (opts.message === undefined) opts.message = mixed.default;
    if (typeof opts.test !== 'function') throw new TypeError('`test` is a required parameters');
    let next = this.clone();
    let validate = createValidation(opts);
    let isExclusive = opts.exclusive || opts.name && next.exclusiveTests[opts.name] === true;
    if (opts.exclusive) {
      if (!opts.name) throw new TypeError('Exclusive tests must provide a unique `name` identifying the test');
    }
    if (opts.name) next.exclusiveTests[opts.name] = !!opts.exclusive;
    next.tests = next.tests.filter(fn => {
      if (fn.OPTIONS.name === opts.name) {
        if (isExclusive) return false;
        if (fn.OPTIONS.test === validate.OPTIONS.test) return false;
      }
      return true;
    });
    next.tests.push(validate);
    return next;
  }
  when(keys, options) {
    if (!Array.isArray(keys) && typeof keys !== 'string') {
      options = keys;
      keys = '.';
    }
    let next = this.clone();
    let deps = toArray(keys).map(key => new Reference(key));
    deps.forEach(dep => {
      // @ts-ignore readonly array
      if (dep.isSibling) next.deps.push(dep.key);
    });
    next.conditions.push(typeof options === 'function' ? new Condition(deps, options) : Condition.fromOptions(deps, options));
    return next;
  }
  typeError(message) {
    let next = this.clone();
    next.internalTests.typeError = createValidation({
      message,
      name: 'typeError',
      skipAbsent: true,
      test(value) {
        if (!this.schema._typeCheck(value)) return this.createError({
          params: {
            type: this.schema.type
          }
        });
        return true;
      }
    });
    return next;
  }
  oneOf(enums, message = mixed.oneOf) {
    let next = this.clone();
    enums.forEach(val => {
      next._whitelist.add(val);
      next._blacklist.delete(val);
    });
    next.internalTests.whiteList = createValidation({
      message,
      name: 'oneOf',
      skipAbsent: true,
      test(value) {
        let valids = this.schema._whitelist;
        let resolved = valids.resolveAll(this.resolve);
        return resolved.includes(value) ? true : this.createError({
          params: {
            values: Array.from(valids).join(', '),
            resolved
          }
        });
      }
    });
    return next;
  }
  notOneOf(enums, message = mixed.notOneOf) {
    let next = this.clone();
    enums.forEach(val => {
      next._blacklist.add(val);
      next._whitelist.delete(val);
    });
    next.internalTests.blacklist = createValidation({
      message,
      name: 'notOneOf',
      test(value) {
        let invalids = this.schema._blacklist;
        let resolved = invalids.resolveAll(this.resolve);
        if (resolved.includes(value)) return this.createError({
          params: {
            values: Array.from(invalids).join(', '),
            resolved
          }
        });
        return true;
      }
    });
    return next;
  }
  strip(strip = true) {
    let next = this.clone();
    next.spec.strip = strip;
    return next;
  }

  /**
   * Return a serialized description of the schema including validations, flags, types etc.
   *
   * @param options Provide any needed context for resolving runtime schema alterations (lazy, when conditions, etc).
   */
  describe(options) {
    const next = (options ? this.resolve(options) : this).clone();
    const {
      label,
      meta,
      optional,
      nullable
    } = next.spec;
    const description = {
      meta,
      label,
      optional,
      nullable,
      default: next.getDefault(options),
      type: next.type,
      oneOf: next._whitelist.describe(),
      notOneOf: next._blacklist.describe(),
      tests: next.tests.map(fn => ({
        name: fn.OPTIONS.name,
        params: fn.OPTIONS.params
      })).filter((n, idx, list) => list.findIndex(c => c.name === n.name) === idx)
    };
    return description;
  }
}
// @ts-expect-error
Schema.prototype.__isYupSchema__ = true;
for (const method of ['validate', 'validateSync']) Schema.prototype[`${method}At`] = function (path, value, options = {}) {
  const {
    parent,
    parentPath,
    schema
  } = getIn(this, path, value, options.context);
  return schema[method](parent && parent[parentPath], Object.assign({}, options, {
    parent,
    path
  }));
};
for (const alias of ['equals', 'is']) Schema.prototype[alias] = Schema.prototype.oneOf;
for (const alias of ['not', 'nope']) Schema.prototype[alias] = Schema.prototype.notOneOf;

function create$7() {
  return new BooleanSchema();
}
class BooleanSchema extends Schema {
  constructor() {
    super({
      type: 'boolean',
      check(v) {
        if (v instanceof Boolean) v = v.valueOf();
        return typeof v === 'boolean';
      }
    });
    this.withMutation(() => {
      this.transform((value, _raw, ctx) => {
        if (ctx.spec.coerce && !ctx.isType(value)) {
          if (/^(true|1)$/i.test(String(value))) return true;
          if (/^(false|0)$/i.test(String(value))) return false;
        }
        return value;
      });
    });
  }
  isTrue(message = boolean.isValue) {
    return this.test({
      message,
      name: 'is-value',
      exclusive: true,
      params: {
        value: 'true'
      },
      test(value) {
        return isAbsent(value) || value === true;
      }
    });
  }
  isFalse(message = boolean.isValue) {
    return this.test({
      message,
      name: 'is-value',
      exclusive: true,
      params: {
        value: 'false'
      },
      test(value) {
        return isAbsent(value) || value === false;
      }
    });
  }
  default(def) {
    return super.default(def);
  }
  defined(msg) {
    return super.defined(msg);
  }
  optional() {
    return super.optional();
  }
  required(msg) {
    return super.required(msg);
  }
  notRequired() {
    return super.notRequired();
  }
  nullable() {
    return super.nullable();
  }
  nonNullable(msg) {
    return super.nonNullable(msg);
  }
  strip(v) {
    return super.strip(v);
  }
}
create$7.prototype = BooleanSchema.prototype;

/**
 * This file is a modified version of the file from the following repository:
 * Date.parse with progressive enhancement for ISO 8601 <https://github.com/csnover/js-iso8601>
 * NON-CONFORMANT EDITION.
 * © 2011 Colin Snover <http://zetafleet.com>
 * Released under MIT license.
 */

// prettier-ignore
//                1 YYYY                2 MM        3 DD              4 HH     5 mm        6 ss           7 msec         8 Z 9 ±   10 tzHH    11 tzmm
const isoReg = /^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;
function parseIsoDate(date) {
  const struct = parseDateStruct(date);
  if (!struct) return Date.parse ? Date.parse(date) : Number.NaN;

  // timestamps without timezone identifiers should be considered local time
  if (struct.z === undefined && struct.plusMinus === undefined) {
    return new Date(struct.year, struct.month, struct.day, struct.hour, struct.minute, struct.second, struct.millisecond).valueOf();
  }
  let totalMinutesOffset = 0;
  if (struct.z !== 'Z' && struct.plusMinus !== undefined) {
    totalMinutesOffset = struct.hourOffset * 60 + struct.minuteOffset;
    if (struct.plusMinus === '+') totalMinutesOffset = 0 - totalMinutesOffset;
  }
  return Date.UTC(struct.year, struct.month, struct.day, struct.hour, struct.minute + totalMinutesOffset, struct.second, struct.millisecond);
}
function parseDateStruct(date) {
  var _regexResult$7$length, _regexResult$;
  const regexResult = isoReg.exec(date);
  if (!regexResult) return null;

  // use of toNumber() avoids NaN timestamps caused by “undefined”
  // values being passed to Date constructor
  return {
    year: toNumber(regexResult[1]),
    month: toNumber(regexResult[2], 1) - 1,
    day: toNumber(regexResult[3], 1),
    hour: toNumber(regexResult[4]),
    minute: toNumber(regexResult[5]),
    second: toNumber(regexResult[6]),
    millisecond: regexResult[7] ?
    // allow arbitrary sub-second precision beyond milliseconds
    toNumber(regexResult[7].substring(0, 3)) : 0,
    precision: (_regexResult$7$length = (_regexResult$ = regexResult[7]) == null ? void 0 : _regexResult$.length) != null ? _regexResult$7$length : undefined,
    z: regexResult[8] || undefined,
    plusMinus: regexResult[9] || undefined,
    hourOffset: toNumber(regexResult[10]),
    minuteOffset: toNumber(regexResult[11])
  };
}
function toNumber(str, defaultValue = 0) {
  return Number(str) || defaultValue;
}

// Taken from HTML spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address
let rEmail =
// eslint-disable-next-line
/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
let rUrl =
// eslint-disable-next-line
/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i;

// eslint-disable-next-line
let rUUID = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;
let yearMonthDay = '^\\d{4}-\\d{2}-\\d{2}';
let hourMinuteSecond = '\\d{2}:\\d{2}:\\d{2}';
let zOrOffset = '(([+-]\\d{2}(:?\\d{2})?)|Z)';
let rIsoDateTime = new RegExp(`${yearMonthDay}T${hourMinuteSecond}(\\.\\d+)?${zOrOffset}$`);
let isTrimmed = value => isAbsent(value) || value === value.trim();
let objStringTag = {}.toString();
function create$6() {
  return new StringSchema();
}
class StringSchema extends Schema {
  constructor() {
    super({
      type: 'string',
      check(value) {
        if (value instanceof String) value = value.valueOf();
        return typeof value === 'string';
      }
    });
    this.withMutation(() => {
      this.transform((value, _raw, ctx) => {
        if (!ctx.spec.coerce || ctx.isType(value)) return value;

        // don't ever convert arrays
        if (Array.isArray(value)) return value;
        const strValue = value != null && value.toString ? value.toString() : value;

        // no one wants plain objects converted to [Object object]
        if (strValue === objStringTag) return value;
        return strValue;
      });
    });
  }
  required(message) {
    return super.required(message).withMutation(schema => schema.test({
      message: message || mixed.required,
      name: 'required',
      skipAbsent: true,
      test: value => !!value.length
    }));
  }
  notRequired() {
    return super.notRequired().withMutation(schema => {
      schema.tests = schema.tests.filter(t => t.OPTIONS.name !== 'required');
      return schema;
    });
  }
  length(length, message = string.length) {
    return this.test({
      message,
      name: 'length',
      exclusive: true,
      params: {
        length
      },
      skipAbsent: true,
      test(value) {
        return value.length === this.resolve(length);
      }
    });
  }
  min(min, message = string.min) {
    return this.test({
      message,
      name: 'min',
      exclusive: true,
      params: {
        min
      },
      skipAbsent: true,
      test(value) {
        return value.length >= this.resolve(min);
      }
    });
  }
  max(max, message = string.max) {
    return this.test({
      name: 'max',
      exclusive: true,
      message,
      params: {
        max
      },
      skipAbsent: true,
      test(value) {
        return value.length <= this.resolve(max);
      }
    });
  }
  matches(regex, options) {
    let excludeEmptyString = false;
    let message;
    let name;
    if (options) {
      if (typeof options === 'object') {
        ({
          excludeEmptyString = false,
          message,
          name
        } = options);
      } else {
        message = options;
      }
    }
    return this.test({
      name: name || 'matches',
      message: message || string.matches,
      params: {
        regex
      },
      skipAbsent: true,
      test: value => value === '' && excludeEmptyString || value.search(regex) !== -1
    });
  }
  email(message = string.email) {
    return this.matches(rEmail, {
      name: 'email',
      message,
      excludeEmptyString: true
    });
  }
  url(message = string.url) {
    return this.matches(rUrl, {
      name: 'url',
      message,
      excludeEmptyString: true
    });
  }
  uuid(message = string.uuid) {
    return this.matches(rUUID, {
      name: 'uuid',
      message,
      excludeEmptyString: false
    });
  }
  datetime(options) {
    let message = '';
    let allowOffset;
    let precision;
    if (options) {
      if (typeof options === 'object') {
        ({
          message = '',
          allowOffset = false,
          precision = undefined
        } = options);
      } else {
        message = options;
      }
    }
    return this.matches(rIsoDateTime, {
      name: 'datetime',
      message: message || string.datetime,
      excludeEmptyString: true
    }).test({
      name: 'datetime_offset',
      message: message || string.datetime_offset,
      params: {
        allowOffset
      },
      skipAbsent: true,
      test: value => {
        if (!value || allowOffset) return true;
        const struct = parseDateStruct(value);
        if (!struct) return false;
        return !!struct.z;
      }
    }).test({
      name: 'datetime_precision',
      message: message || string.datetime_precision,
      params: {
        precision
      },
      skipAbsent: true,
      test: value => {
        if (!value || precision == undefined) return true;
        const struct = parseDateStruct(value);
        if (!struct) return false;
        return struct.precision === precision;
      }
    });
  }

  //-- transforms --
  ensure() {
    return this.default('').transform(val => val === null ? '' : val);
  }
  trim(message = string.trim) {
    return this.transform(val => val != null ? val.trim() : val).test({
      message,
      name: 'trim',
      test: isTrimmed
    });
  }
  lowercase(message = string.lowercase) {
    return this.transform(value => !isAbsent(value) ? value.toLowerCase() : value).test({
      message,
      name: 'string_case',
      exclusive: true,
      skipAbsent: true,
      test: value => isAbsent(value) || value === value.toLowerCase()
    });
  }
  uppercase(message = string.uppercase) {
    return this.transform(value => !isAbsent(value) ? value.toUpperCase() : value).test({
      message,
      name: 'string_case',
      exclusive: true,
      skipAbsent: true,
      test: value => isAbsent(value) || value === value.toUpperCase()
    });
  }
}
create$6.prototype = StringSchema.prototype;

//
// Number Interfaces
//

let invalidDate = new Date('');
let isDate = obj => Object.prototype.toString.call(obj) === '[object Date]';
class DateSchema extends Schema {
  constructor() {
    super({
      type: 'date',
      check(v) {
        return isDate(v) && !isNaN(v.getTime());
      }
    });
    this.withMutation(() => {
      this.transform((value, _raw, ctx) => {
        // null -> InvalidDate isn't useful; treat all nulls as null and let it fail on
        // nullability check vs TypeErrors
        if (!ctx.spec.coerce || ctx.isType(value) || value === null) return value;
        value = parseIsoDate(value);

        // 0 is a valid timestamp equivalent to 1970-01-01T00:00:00Z(unix epoch) or before.
        return !isNaN(value) ? new Date(value) : DateSchema.INVALID_DATE;
      });
    });
  }
  prepareParam(ref, name) {
    let param;
    if (!Reference.isRef(ref)) {
      let cast = this.cast(ref);
      if (!this._typeCheck(cast)) throw new TypeError(`\`${name}\` must be a Date or a value that can be \`cast()\` to a Date`);
      param = cast;
    } else {
      param = ref;
    }
    return param;
  }
  min(min, message = date.min) {
    let limit = this.prepareParam(min, 'min');
    return this.test({
      message,
      name: 'min',
      exclusive: true,
      params: {
        min
      },
      skipAbsent: true,
      test(value) {
        return value >= this.resolve(limit);
      }
    });
  }
  max(max, message = date.max) {
    let limit = this.prepareParam(max, 'max');
    return this.test({
      message,
      name: 'max',
      exclusive: true,
      params: {
        max
      },
      skipAbsent: true,
      test(value) {
        return value <= this.resolve(limit);
      }
    });
  }
}
DateSchema.INVALID_DATE = invalidDate;

// @ts-expect-error
function sortFields(fields, excludedEdges = []) {
  let edges = [];
  let nodes = new Set();
  let excludes = new Set(excludedEdges.map(([a, b]) => `${a}-${b}`));
  function addNode(depPath, key) {
    let node = propertyExprExports.split(depPath)[0];
    nodes.add(node);
    if (!excludes.has(`${key}-${node}`)) edges.push([key, node]);
  }
  for (const key of Object.keys(fields)) {
    let value = fields[key];
    nodes.add(key);
    if (Reference.isRef(value) && value.isSibling) addNode(value.path, key);else if (isSchema(value) && 'deps' in value) value.deps.forEach(path => addNode(path, key));
  }
  return toposort.array(Array.from(nodes), edges).reverse();
}

function findIndex(arr, err) {
  let idx = Infinity;
  arr.some((key, ii) => {
    var _err$path;
    if ((_err$path = err.path) != null && _err$path.includes(key)) {
      idx = ii;
      return true;
    }
  });
  return idx;
}
function sortByKeyOrder(keys) {
  return (a, b) => {
    return findIndex(keys, a) - findIndex(keys, b);
  };
}

const parseJson = (value, _, ctx) => {
  if (typeof value !== 'string') {
    return value;
  }
  let parsed = value;
  try {
    parsed = JSON.parse(value);
  } catch (err) {
    /* */
  }
  return ctx.isType(parsed) ? parsed : value;
};

// @ts-ignore
function deepPartial(schema) {
  if ('fields' in schema) {
    const partial = {};
    for (const [key, fieldSchema] of Object.entries(schema.fields)) {
      partial[key] = deepPartial(fieldSchema);
    }
    return schema.setFields(partial);
  }
  if (schema.type === 'array') {
    const nextArray = schema.optional();
    if (nextArray.innerType) nextArray.innerType = deepPartial(nextArray.innerType);
    return nextArray;
  }
  if (schema.type === 'tuple') {
    return schema.optional().clone({
      types: schema.spec.types.map(deepPartial)
    });
  }
  if ('optional' in schema) {
    return schema.optional();
  }
  return schema;
}
const deepHas = (obj, p) => {
  const path = [...propertyExprExports.normalizePath(p)];
  if (path.length === 1) return path[0] in obj;
  let last = path.pop();
  let parent = propertyExprExports.getter(propertyExprExports.join(path), true)(obj);
  return !!(parent && last in parent);
};
let isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';
function unknown(ctx, value) {
  let known = Object.keys(ctx.fields);
  return Object.keys(value).filter(key => known.indexOf(key) === -1);
}
const defaultSort = sortByKeyOrder([]);
function create$3(spec) {
  return new ObjectSchema(spec);
}
class ObjectSchema extends Schema {
  constructor(spec) {
    super({
      type: 'object',
      check(value) {
        return isObject(value) || typeof value === 'function';
      }
    });
    this.fields = Object.create(null);
    this._sortErrors = defaultSort;
    this._nodes = [];
    this._excludedEdges = [];
    this.withMutation(() => {
      if (spec) {
        this.shape(spec);
      }
    });
  }
  _cast(_value, options = {}) {
    var _options$stripUnknown;
    let value = super._cast(_value, options);

    //should ignore nulls here
    if (value === undefined) return this.getDefault(options);
    if (!this._typeCheck(value)) return value;
    let fields = this.fields;
    let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;
    let props = [].concat(this._nodes, Object.keys(value).filter(v => !this._nodes.includes(v)));
    let intermediateValue = {}; // is filled during the transform below
    let innerOptions = Object.assign({}, options, {
      parent: intermediateValue,
      __validating: options.__validating || false
    });
    let isChanged = false;
    for (const prop of props) {
      let field = fields[prop];
      let exists = (prop in value);
      if (field) {
        let fieldValue;
        let inputValue = value[prop];

        // safe to mutate since this is fired in sequence
        innerOptions.path = (options.path ? `${options.path}.` : '') + prop;
        field = field.resolve({
          value: inputValue,
          context: options.context,
          parent: intermediateValue
        });
        let fieldSpec = field instanceof Schema ? field.spec : undefined;
        let strict = fieldSpec == null ? void 0 : fieldSpec.strict;
        if (fieldSpec != null && fieldSpec.strip) {
          isChanged = isChanged || prop in value;
          continue;
        }
        fieldValue = !options.__validating || !strict ?
        // TODO: use _cast, this is double resolving
        field.cast(value[prop], innerOptions) : value[prop];
        if (fieldValue !== undefined) {
          intermediateValue[prop] = fieldValue;
        }
      } else if (exists && !strip) {
        intermediateValue[prop] = value[prop];
      }
      if (exists !== prop in intermediateValue || intermediateValue[prop] !== value[prop]) {
        isChanged = true;
      }
    }
    return isChanged ? intermediateValue : value;
  }
  _validate(_value, options = {}, panic, next) {
    let {
      from = [],
      originalValue = _value,
      recursive = this.spec.recursive
    } = options;
    options.from = [{
      schema: this,
      value: originalValue
    }, ...from];
    // this flag is needed for handling `strict` correctly in the context of
    // validation vs just casting. e.g strict() on a field is only used when validating
    options.__validating = true;
    options.originalValue = originalValue;
    super._validate(_value, options, panic, (objectErrors, value) => {
      if (!recursive || !isObject(value)) {
        next(objectErrors, value);
        return;
      }
      originalValue = originalValue || value;
      let tests = [];
      for (let key of this._nodes) {
        let field = this.fields[key];
        if (!field || Reference.isRef(field)) {
          continue;
        }
        tests.push(field.asNestedTest({
          options,
          key,
          parent: value,
          parentPath: options.path,
          originalParent: originalValue
        }));
      }
      this.runTests({
        tests,
        value,
        originalValue,
        options
      }, panic, fieldErrors => {
        next(fieldErrors.sort(this._sortErrors).concat(objectErrors), value);
      });
    });
  }
  clone(spec) {
    const next = super.clone(spec);
    next.fields = Object.assign({}, this.fields);
    next._nodes = this._nodes;
    next._excludedEdges = this._excludedEdges;
    next._sortErrors = this._sortErrors;
    return next;
  }
  concat(schema) {
    let next = super.concat(schema);
    let nextFields = next.fields;
    for (let [field, schemaOrRef] of Object.entries(this.fields)) {
      const target = nextFields[field];
      nextFields[field] = target === undefined ? schemaOrRef : target;
    }
    return next.withMutation(s =>
    // XXX: excludes here is wrong
    s.setFields(nextFields, [...this._excludedEdges, ...schema._excludedEdges]));
  }
  _getDefault(options) {
    if ('default' in this.spec) {
      return super._getDefault(options);
    }

    // if there is no default set invent one
    if (!this._nodes.length) {
      return undefined;
    }
    let dft = {};
    this._nodes.forEach(key => {
      var _innerOptions;
      const field = this.fields[key];
      let innerOptions = options;
      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {
        innerOptions = Object.assign({}, innerOptions, {
          parent: innerOptions.value,
          value: innerOptions.value[key]
        });
      }
      dft[key] = field && 'getDefault' in field ? field.getDefault(innerOptions) : undefined;
    });
    return dft;
  }
  setFields(shape, excludedEdges) {
    let next = this.clone();
    next.fields = shape;
    next._nodes = sortFields(shape, excludedEdges);
    next._sortErrors = sortByKeyOrder(Object.keys(shape));
    // XXX: this carries over edges which may not be what you want
    if (excludedEdges) next._excludedEdges = excludedEdges;
    return next;
  }
  shape(additions, excludes = []) {
    return this.clone().withMutation(next => {
      let edges = next._excludedEdges;
      if (excludes.length) {
        if (!Array.isArray(excludes[0])) excludes = [excludes];
        edges = [...next._excludedEdges, ...excludes];
      }

      // XXX: excludes here is wrong
      return next.setFields(Object.assign(next.fields, additions), edges);
    });
  }
  partial() {
    const partial = {};
    for (const [key, schema] of Object.entries(this.fields)) {
      partial[key] = 'optional' in schema && schema.optional instanceof Function ? schema.optional() : schema;
    }
    return this.setFields(partial);
  }
  deepPartial() {
    const next = deepPartial(this);
    return next;
  }
  pick(keys) {
    const picked = {};
    for (const key of keys) {
      if (this.fields[key]) picked[key] = this.fields[key];
    }
    return this.setFields(picked, this._excludedEdges.filter(([a, b]) => keys.includes(a) && keys.includes(b)));
  }
  omit(keys) {
    const remaining = [];
    for (const key of Object.keys(this.fields)) {
      if (keys.includes(key)) continue;
      remaining.push(key);
    }
    return this.pick(remaining);
  }
  from(from, to, alias) {
    let fromGetter = propertyExprExports.getter(from, true);
    return this.transform(obj => {
      if (!obj) return obj;
      let newObj = obj;
      if (deepHas(obj, from)) {
        newObj = Object.assign({}, obj);
        if (!alias) delete newObj[from];
        newObj[to] = fromGetter(obj);
      }
      return newObj;
    });
  }

  /** Parse an input JSON string to an object */
  json() {
    return this.transform(parseJson);
  }

  /**
   * Similar to `noUnknown` but only validates that an object is the right shape without stripping the unknown keys
   */
  exact(message) {
    return this.test({
      name: 'exact',
      exclusive: true,
      message: message || object.exact,
      test(value) {
        if (value == null) return true;
        const unknownKeys = unknown(this.schema, value);
        return unknownKeys.length === 0 || this.createError({
          params: {
            properties: unknownKeys.join(', ')
          }
        });
      }
    });
  }
  stripUnknown() {
    return this.clone({
      noUnknown: true
    });
  }
  noUnknown(noAllow = true, message = object.noUnknown) {
    if (typeof noAllow !== 'boolean') {
      message = noAllow;
      noAllow = true;
    }
    let next = this.test({
      name: 'noUnknown',
      exclusive: true,
      message: message,
      test(value) {
        if (value == null) return true;
        const unknownKeys = unknown(this.schema, value);
        return !noAllow || unknownKeys.length === 0 || this.createError({
          params: {
            unknown: unknownKeys.join(', ')
          }
        });
      }
    });
    next.spec.noUnknown = noAllow;
    return next;
  }
  unknown(allow = true, message = object.noUnknown) {
    return this.noUnknown(!allow, message);
  }
  transformKeys(fn) {
    return this.transform(obj => {
      if (!obj) return obj;
      const result = {};
      for (const key of Object.keys(obj)) result[fn(key)] = obj[key];
      return result;
    });
  }
  camelCase() {
    return this.transformKeys(tinyCaseExports.camelCase);
  }
  snakeCase() {
    return this.transformKeys(tinyCaseExports.snakeCase);
  }
  constantCase() {
    return this.transformKeys(key => tinyCaseExports.snakeCase(key).toUpperCase());
  }
  describe(options) {
    const next = (options ? this.resolve(options) : this).clone();
    const base = super.describe(options);
    base.fields = {};
    for (const [key, value] of Object.entries(next.fields)) {
      var _innerOptions2;
      let innerOptions = options;
      if ((_innerOptions2 = innerOptions) != null && _innerOptions2.value) {
        innerOptions = Object.assign({}, innerOptions, {
          parent: innerOptions.value,
          value: innerOptions.value[key]
        });
      }
      base.fields[key] = value.describe(innerOptions);
    }
    return base;
  }
}
create$3.prototype = ObjectSchema.prototype;

function AlertsList($$payload, $$props) {
  push();
  let alerts = fallback($$props["alerts"], () => [], true);
  let onCreateAlert = fallback($$props["onCreateAlert"], () => {
  });
  let showDeleteDialog = false;
  let currentAlert = null;
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };
  const getFrequencyText = (frequency) => {
    switch (frequency) {
      case "daily":
        return "Daily";
      case "weekly":
        return "Weekly";
      case "monthly":
        return "Monthly";
      default:
        return "As needed";
    }
  };
  const handleEditAlert = (alert) => {
    currentAlert = alert;
    toast.info("Edit functionality will be available soon");
  };
  const handleDeleteAlert = (alert) => {
    currentAlert = alert;
    showDeleteDialog = true;
  };
  const handleToggleAlert = async (alert) => {
    try {
      const response = await fetch("/api/job-alerts", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: alert.id, enabled: !alert.enabled })
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Failed to update job alert");
      }
      alerts = alerts.map((a) => a.id === alert.id ? { ...a, enabled: !a.enabled } : a);
      toast.success(`Alert ${alert.enabled ? "disabled" : "enabled"} successfully`);
    } catch (error) {
      console.error("Error updating job alert:", error);
      toast.error("Failed to update job alert");
    }
  };
  const handleConfirmDelete = async () => {
    if (!currentAlert) return;
    try {
      const response = await fetch("/api/job-alerts", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: currentAlert.id })
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Failed to delete job alert");
      }
      alerts = alerts.filter((a) => a.id !== currentAlert.id);
      showDeleteDialog = false;
      currentAlert = null;
      toast.success("Alert deleted successfully");
    } catch (error) {
      console.error("Error deleting job alert:", error);
      toast.error("Failed to delete job alert");
    }
  };
  const formatSearchParams = (params) => {
    if (!params) return "All jobs";
    const parts = [];
    if (params.keywords) parts.push(`Keywords: ${params.keywords}`);
    if (params.location) parts.push(`Location: ${params.location}`);
    if (params.jobType) {
      const jobTypeMap = {
        full_time: "Full-time",
        part_time: "Part-time",
        contract: "Contract",
        temporary: "Temporary",
        internship: "Internship"
      };
      parts.push(`Job Type: ${jobTypeMap[params.jobType] || params.jobType}`);
    }
    if (params.remote) parts.push("Remote Only");
    return parts.length > 0 ? parts.join(", ") : "All jobs";
  };
  const getStatusBadgeVariant = (enabled) => {
    return enabled ? "default" : "secondary";
  };
  const getMatchingJobsCount = (alert) => {
    return Math.floor(Math.random() * 20);
  };
  $$payload.out += `<div>`;
  if (alerts.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="rounded-lg border p-6 text-center"><div class="bg-foreground mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">`;
    Bell($$payload, { class: "text-background h-6 w-6" });
    $$payload.out += `<!----></div> <h3 class="mb-2 text-lg font-medium">No job alerts yet</h3> <p class="text-muted-foreground mx-auto mb-4 max-w-md">Create job alerts to get notified when new jobs matching your criteria are available. You'll
        receive email notifications based on your selected frequency.</p> `;
    Button($$payload, {
      class: "mt-6",
      variant: "default",
      onclick: onCreateAlert,
      children: ($$payload2) => {
        Plus($$payload2, { class: "mr-2 h-4 w-4" });
        $$payload2.out += `<!----> Create Your First Alert`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(alerts);
    $$payload.out += `<div class="space-y-4"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let alert = each_array[$$index];
      Card($$payload, {
        children: ($$payload2) => {
          Card_header($$payload2, {
            class: "p-4",
            children: ($$payload3) => {
              $$payload3.out += `<div class="flex items-start justify-between"><div>`;
              Card_title($$payload3, {
                class: "flex items-center gap-2",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(alert.name)} `;
                  Badge($$payload4, {
                    variant: getStatusBadgeVariant(alert.enabled),
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(alert.enabled ? "Active" : "Disabled")}`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----> `;
              Card_description($$payload3, {
                class: "mt-1",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(formatSearchParams(alert.searchParams))}`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----> <div class="mt-2">`;
              Badge($$payload3, {
                variant: "outline",
                class: "text-xs",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(getMatchingJobsCount())} matching jobs`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div></div> `;
              Root$4($$payload3, {
                children: ($$payload4) => {
                  Dropdown_menu_trigger($$payload4, {
                    children: ($$payload5) => {
                      Button($$payload5, {
                        variant: "ghost",
                        size: "icon",
                        class: "h-8 w-8",
                        children: ($$payload6) => {
                          Ellipsis_vertical($$payload6, { class: "h-4 w-4" });
                          $$payload6.out += `<!----> <span class="sr-only">Open menu</span>`;
                        },
                        $$slots: { default: true }
                      });
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!----> `;
                  Dropdown_menu_content($$payload4, {
                    align: "end",
                    children: ($$payload5) => {
                      Dropdown_menu_item($$payload5, {
                        onclick: () => handleToggleAlert(alert),
                        class: "flex items-center gap-2",
                        children: ($$payload6) => {
                          if (alert.enabled) {
                            $$payload6.out += "<!--[-->";
                            Bell_off($$payload6, { class: "h-4 w-4" });
                            $$payload6.out += `<!----> <span>Disable Alert</span>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                            Bell($$payload6, { class: "h-4 w-4" });
                            $$payload6.out += `<!----> <span>Enable Alert</span>`;
                          }
                          $$payload6.out += `<!--]-->`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out += `<!----> `;
                      Dropdown_menu_item($$payload5, {
                        onclick: () => handleEditAlert(alert),
                        class: "flex items-center gap-2",
                        children: ($$payload6) => {
                          Square_pen($$payload6, { class: "h-4 w-4" });
                          $$payload6.out += `<!----> <span>Edit Alert</span>`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out += `<!----> `;
                      Dropdown_menu_separator($$payload5, {});
                      $$payload5.out += `<!----> `;
                      Dropdown_menu_item($$payload5, {
                        onclick: () => handleDeleteAlert(alert),
                        class: "flex items-center gap-2 text-red-600",
                        children: ($$payload6) => {
                          Trash_2($$payload6, { class: "h-4 w-4" });
                          $$payload6.out += `<!----> <span>Delete Alert</span>`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload5.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> `;
          Card_content($$payload2, {
            class: "p-4 pt-0",
            children: ($$payload3) => {
              $$payload3.out += `<div class="flex flex-wrap gap-x-6 gap-y-2 text-sm text-gray-500"><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="M12 6v6l4 2"></path></svg> <span class="font-medium">Frequency:</span> ${escape_html(getFrequencyText(alert.frequency))}</div> <div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg> <span class="font-medium">Created:</span> ${escape_html(formatDate(alert.createdAt))}</div> `;
              if (alert.lastSentAt) {
                $$payload3.out += "<!--[-->";
                $$payload3.out += `<div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 2L11 13"></path><path d="M22 2l-7 20-4-9-9-4 20-7z"></path></svg> <span class="font-medium">Last sent:</span> ${escape_html(formatDate(alert.lastSentAt))}</div>`;
              } else {
                $$payload3.out += "<!--[!-->";
              }
              $$payload3.out += `<!--]--></div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> `;
          Card_footer($$payload2, {
            class: "p-4 pt-0",
            children: ($$payload3) => {
              $$payload3.out += `<div class="flex justify-end">`;
              Button($$payload3, {
                variant: "outline",
                size: "sm",
                class: "text-xs",
                onclick: () => window.location.href = "/dashboard/jobs",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->View Matching Jobs`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--> `;
  Root$5($$payload, {
    open: showDeleteDialog,
    children: ($$payload2) => {
      Alert_dialog_content($$payload2, {
        children: ($$payload3) => {
          Alert_dialog_header($$payload3, {
            children: ($$payload4) => {
              Alert_dialog_title($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Delete Job Alert`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Alert_dialog_description($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Are you sure you want to delete this job alert? This action cannot be undone.`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Alert_dialog_footer($$payload3, {
            children: ($$payload4) => {
              Alert_dialog_cancel($$payload4, {
                onclick: () => showDeleteDialog = false,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Cancel`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Alert_dialog_action($$payload4, {
                onclick: handleConfirmDelete,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Delete`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  bind_props($$props, { alerts, onCreateAlert });
  pop();
}
function subscribeOnce(observable) {
  return new Promise((resolve) => {
    observable.subscribe(resolve)();
  });
}
function update(object, path, value) {
  object.update((o) => {
    set(o, path, value);
    return o;
  });
}
function cloneDeep(object) {
  return JSON.parse(JSON.stringify(object));
}
function isNullish(value) {
  return value === void 0 || value === null;
}
function isEmpty(object) {
  return isNullish(object) || Object.keys(object).length <= 0;
}
function getValues(object) {
  let results = [];
  for (const [, value] of Object.entries(object)) {
    const values = typeof value === "object" ? getValues(value) : [value];
    results = [...results, ...values];
  }
  return results;
}
function getErrorsFromSchema(initialValues, schema, errors = {}) {
  for (const key in schema) {
    switch (true) {
      case (schema[key].type === "object" && !isEmpty(schema[key].fields)): {
        errors[key] = getErrorsFromSchema(
          initialValues[key],
          schema[key].fields,
          { ...errors[key] }
        );
        break;
      }
      case schema[key].type === "array": {
        const values = initialValues && initialValues[key] ? initialValues[key] : [];
        errors[key] = values.map((value) => {
          const innerError = getErrorsFromSchema(
            value,
            schema[key].innerType.fields,
            { ...errors[key] }
          );
          return Object.keys(innerError).length > 0 ? innerError : "";
        });
        break;
      }
      default: {
        errors[key] = "";
      }
    }
  }
  return errors;
}
const deepEqual = dequal;
function assignDeep(object, value) {
  if (Array.isArray(object)) {
    return object.map((o) => assignDeep(o, value));
  }
  const copy = {};
  for (const key in object) {
    copy[key] = typeof object[key] === "object" && !isNullish(object[key]) ? assignDeep(object[key], value) : value;
  }
  return copy;
}
function set(object, path, value) {
  if (new Object(object) !== object) return object;
  if (!Array.isArray(path)) {
    path = path.toString().match(/[^.[\]]+/g) || [];
  }
  const result = path.slice(0, -1).reduce(
    (accumulator, key, index) => new Object(accumulator[key]) === accumulator[key] ? accumulator[key] : accumulator[key] = Math.trunc(Math.abs(path[index + 1])) === +path[index + 1] ? [] : {},
    object
  );
  result[path[path.length - 1]] = value;
  return object;
}
const util = {
  assignDeep,
  cloneDeep,
  deepEqual,
  getErrorsFromSchema,
  getValues,
  isEmpty,
  isNullish,
  set,
  subscribeOnce,
  update
};
const NO_ERROR = "";
function isCheckbox(element) {
  return element.getAttribute && element.getAttribute("type") === "checkbox";
}
function isFileInput(element) {
  return element.getAttribute && element.getAttribute("type") === "file";
}
function resolveValue(element) {
  if (isFileInput(element)) {
    return element.files;
  } else if (isCheckbox(element)) {
    return element.checked;
  } else {
    return element.value;
  }
}
const createForm = (config) => {
  let initialValues = config.initialValues || {};
  const validationSchema = config.validationSchema;
  const validateFunction = config.validate;
  const onSubmit = config.onSubmit;
  const getInitial = {
    values: () => util.cloneDeep(initialValues),
    errors: () => validationSchema ? util.getErrorsFromSchema(initialValues, validationSchema.fields) : util.assignDeep(initialValues, NO_ERROR),
    touched: () => util.assignDeep(initialValues, false)
  };
  const form = writable(getInitial.values());
  const errors = writable(getInitial.errors());
  const touched = writable(getInitial.touched());
  const isSubmitting = writable(false);
  const isValidating = writable(false);
  const isValid = derived(errors, ($errors) => {
    const noErrors = util.getValues($errors).every((field) => field === NO_ERROR);
    return noErrors;
  });
  const modified = derived(form, ($form) => {
    const object = util.assignDeep($form, false);
    for (let key in $form) {
      object[key] = !util.deepEqual($form[key], initialValues[key]);
    }
    return object;
  });
  const isModified = derived(modified, ($modified) => {
    return util.getValues($modified).includes(true);
  });
  function validateField(field) {
    return util.subscribeOnce(form).then((values) => validateFieldValue(field, values[field]));
  }
  function validateFieldValue(field, value) {
    updateTouched(field, true);
    if (validationSchema) {
      isValidating.set(true);
      return validationSchema.validateAt(field, get(form)).then(() => util.update(errors, field, "")).catch((error) => util.update(errors, field, error.message)).finally(() => {
        isValidating.set(false);
      });
    }
    if (validateFunction) {
      isValidating.set(true);
      return Promise.resolve().then(() => validateFunction({ [field]: value })).then(
        (errs) => util.update(errors, field, !util.isNullish(errs) ? errs[field] : "")
      ).finally(() => {
        isValidating.set(false);
      });
    }
    return Promise.resolve();
  }
  function updateValidateField(field, value) {
    updateField(field, value);
    return validateFieldValue(field, value);
  }
  function handleChange(event) {
    const element = event.target;
    const field = element.name || element.id;
    const value = resolveValue(element);
    return updateValidateField(field, value);
  }
  function handleSubmit(event) {
    if (event && event.preventDefault) {
      event.preventDefault();
    }
    isSubmitting.set(true);
    return util.subscribeOnce(form).then((values) => {
      if (typeof validateFunction === "function") {
        isValidating.set(true);
        return Promise.resolve().then(() => validateFunction(values)).then((error) => {
          if (util.isNullish(error) || util.getValues(error).length === 0) {
            return clearErrorsAndSubmit(values);
          } else {
            errors.set(error);
            isSubmitting.set(false);
          }
        }).finally(() => isValidating.set(false));
      }
      if (validationSchema) {
        isValidating.set(true);
        return validationSchema.validate(values, { abortEarly: false }).then(() => clearErrorsAndSubmit(values)).catch((yupErrors) => {
          if (yupErrors && yupErrors.inner) {
            const updatedErrors = getInitial.errors();
            yupErrors.inner.map(
              (error) => util.set(updatedErrors, error.path, error.message)
            );
            errors.set(updatedErrors);
          }
          isSubmitting.set(false);
        }).finally(() => isValidating.set(false));
      }
      return clearErrorsAndSubmit(values);
    });
  }
  function handleReset() {
    form.set(getInitial.values());
    errors.set(getInitial.errors());
    touched.set(getInitial.touched());
  }
  function clearErrorsAndSubmit(values) {
    return Promise.resolve().then(() => errors.set(getInitial.errors())).then(() => onSubmit(values, form, errors)).finally(() => isSubmitting.set(false));
  }
  function updateField(field, value) {
    util.update(form, field, value);
  }
  function updateTouched(field, value) {
    util.update(touched, field, value);
  }
  function updateInitialValues(newValues) {
    initialValues = newValues;
    handleReset();
  }
  return {
    form,
    errors,
    touched,
    modified,
    isValid,
    isSubmitting,
    isValidating,
    isModified,
    handleChange,
    handleSubmit,
    handleReset,
    updateField,
    updateValidateField,
    updateTouched,
    validateField,
    updateInitialValues,
    state: derived(
      [
        form,
        errors,
        touched,
        modified,
        isValid,
        isValidating,
        isSubmitting,
        isModified
      ],
      ([
        $form,
        $errors,
        $touched,
        $modified,
        $isValid,
        $isValidating,
        $isSubmitting,
        $isModified
      ]) => ({
        form: $form,
        errors: $errors,
        touched: $touched,
        modified: $modified,
        isValid: $isValid,
        isSubmitting: $isSubmitting,
        isValidating: $isValidating,
        isModified: $isModified
      })
    )
  };
};
function CreateAlertDialog($$payload, $$props) {
  push();
  var $$store_subs;
  let onClose = $$props["onClose"];
  let onCreated = $$props["onCreated"];
  const userId = "";
  const schema = create$3().shape({
    name: create$6().required("Alert name is required"),
    keywords: create$6(),
    location: create$6(),
    jobType: create$6(),
    remote: create$7(),
    frequency: create$6().required("Frequency is required"),
    enabled: create$7()
  });
  const { form, errors, isSubmitting } = createForm({
    initialValues: {
      name: "",
      keywords: "",
      location: "",
      jobType: "",
      remote: false,
      frequency: "daily",
      enabled: true
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      try {
        const searchParams = {
          keywords: values.keywords || void 0,
          location: values.location || void 0,
          jobType: values.jobType || void 0,
          remote: values.remote || void 0
        };
        Object.keys(searchParams).forEach((key) => {
          if (searchParams[key] === void 0) {
            delete searchParams[key];
          }
        });
        const alertData = {
          name: values.name,
          searchParams,
          frequency: values.frequency,
          enabled: values.enabled
        };
        const response = await fetch("/api/job-alerts", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(alertData)
        });
        const result = await response.json();
        if (!response.ok) {
          throw new Error(result.error || "Failed to create job alert");
        }
        toast.success("Job alert created successfully");
        onCreated();
      } catch (error) {
        console.error("Error creating job alert:", error);
        toast.error("Failed to create job alert");
      }
    }
  });
  const jobTypeOptions = [
    { value: "", label: "Any" },
    { value: "full_time", label: "Full-time" },
    { value: "part_time", label: "Part-time" },
    { value: "contract", label: "Contract" },
    { value: "temporary", label: "Temporary" },
    { value: "internship", label: "Internship" }
  ];
  const frequencyOptions = [
    { value: "daily", label: "Daily" },
    { value: "weekly", label: "Weekly" },
    { value: "monthly", label: "Monthly" }
  ];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$6($$payload2, {
      open: true,
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-[500px]",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  class: "mb-3",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Create Job Alert`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Set up a new job alert to get notified when new jobs matching your criteria are available.
        You'll receive email notifications based on your selected frequency.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="bg-primary/80 text-background/80 mb-2 rounded-lg p-3 text-sm"><div class="flex items-start">`;
            Info($$payload4, { class: "mr-2 mt-0.5 h-4 w-4" });
            $$payload4.out += `<!----> <div><p class="font-medium">Tips for effective job alerts:</p> <ul class="ml-5 mt-1 list-disc"><li>Use specific keywords related to your skills</li> <li>Include location preferences or select "Remote"</li> <li>Choose a frequency that works best for your job search</li></ul></div></div></div> <form><div class="grid gap-4 py-4"><div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "name",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Alert Name`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "name",
              placeholder: "e.g., Software Developer Jobs",
              class: `col-span-2 ${stringify(store_get($$store_subs ??= {}, "$errors", errors).name ? "border-red-500" : "")}`,
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).name;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).name = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).name) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="mt-1 text-xs text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).name)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "keywords",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Keywords`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              class: "col-span-2",
              id: "keywords",
              placeholder: "e.g., javascript, react",
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).keywords;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).keywords = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "location",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Location`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              class: "col-span-2",
              id: "location",
              placeholder: "e.g., New York, Remote",
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).location;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).location = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "jobType",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Job Type`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Root$3($$payload4, {
              type: "single",
              name: "jobType",
              value: store_get($$store_subs ??= {}, "$form", form).jobType,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).jobType = value);
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "col-span-2 w-full px-3 py-2",
                  children: ($$payload6) => {
                    Select_value($$payload6, {
                      placeholder: jobTypeOptions.find((o) => o.value === store_get($$store_subs ??= {}, "$form", form).jobType)?.label || "Select job type"
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "max-h-60",
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(jobTypeOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let option = each_array[$$index];
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "remote",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Remote Only`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="items-right align-right col-span-2 flex items-center space-x-2">`;
            Switch($$payload4, {
              id: "remote",
              name: "remote",
              checked: store_get($$store_subs ??= {}, "$form", form).remote,
              onCheckedChange: (checked) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).remote = checked);
              }
            });
            $$payload4.out += `<!----> <span class="text-muted-foreground text-sm">Only show remote jobs</span></div></div> <div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "frequency",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Frequency`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="col-span-2">`;
            Root$3($$payload4, {
              type: "single",
              name: "frequency",
              value: store_get($$store_subs ??= {}, "$form", form).frequency,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).frequency = value);
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full px-3 py-2",
                  children: ($$payload6) => {
                    Select_value($$payload6, {
                      placeholder: frequencyOptions.find((o) => o.value === store_get($$store_subs ??= {}, "$form", form).frequency)?.label || "Select frequency"
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "max-h-60",
                  children: ($$payload6) => {
                    const each_array_1 = ensure_array_like(frequencyOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                      let option = each_array_1[$$index_1];
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).frequency) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="mt-1 text-xs text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).frequency)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> <div class="grid grid-cols-3 items-center gap-4">`;
            Label($$payload4, {
              for: "enabled",
              class: "text-left",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Enabled`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="col-span-2 flex items-center space-x-2">`;
            Switch($$payload4, {
              id: "enabled",
              name: "enabled",
              checked: store_get($$store_subs ??= {}, "$form", form).enabled,
              onCheckedChange: (checked) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).enabled = checked);
              }
            });
            $$payload4.out += `<!----> <span class="text-muted-foreground text-sm">Receive emails with new job matches</span></div></div></div> `;
            Dialog_footer($$payload4, {
              class: "mt-2 sm:justify-between",
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: onClose,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "submit",
                  disabled: store_get($$store_subs ??= {}, "$isSubmitting", isSubmitting) || !store_get($$store_subs ??= {}, "$form", form).name.trim() || !store_get($$store_subs ??= {}, "$form", form).frequency.trim() || !store_get($$store_subs ??= {}, "$form", form).keywords.trim() || !store_get($$store_subs ??= {}, "$form", form).location.trim() || !store_get($$store_subs ??= {}, "$form", form).jobType.trim(),
                  class: "flex items-center gap-2",
                  children: ($$payload6) => {
                    if (store_get($$store_subs ??= {}, "$isSubmitting", isSubmitting)) {
                      $$payload6.out += "<!--[-->";
                      Loader($$payload6, { class: "h-4 w-4 animate-spin" });
                      $$payload6.out += `<!----> <span>Creating...</span>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      Bell($$payload6, { class: "h-4 w-4" });
                      $$payload6.out += `<!----> <span>Create Alert</span>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { onClose, onCreated, userId });
  pop();
}
function SavedJobCard($$payload, $$props) {
  push();
  let job = $$props["job"];
  const dispatch = createEventDispatcher();
  function handleRemove() {
    dispatch("remove", { jobId: job.id });
  }
  function formatSalary(job2) {
    if (job2.salaryMin && job2.salaryMax) {
      return `$${job2.salaryMin.toLocaleString()} - $${job2.salaryMax.toLocaleString()}`;
    } else if (job2.salary) {
      return job2.salary;
    }
    return null;
  }
  Card($$payload, {
    class: "hover:border-primary/50 group relative transition-all hover:shadow-sm",
    children: ($$payload2) => {
      $$payload2.out += `<div class="absolute right-2 top-2 z-10">`;
      Provider($$payload2, {
        children: ($$payload3) => {
          Root$2($$payload3, {
            children: ($$payload4) => {
              Tooltip_trigger($$payload4, {
                children: ($$payload5) => {
                  Button($$payload5, {
                    size: "sm",
                    variant: "ghost",
                    class: "hover:bg-background/80 h-6 w-6 p-0 hover:text-red-500",
                    onclick: handleRemove,
                    children: ($$payload6) => {
                      Trash_2($$payload6, { class: "h-3 w-3" });
                    },
                    $$slots: { default: true }
                  });
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              Tooltip_content($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<p>Remove from saved jobs</p>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
        }
      });
      $$payload2.out += `<!----></div> `;
      Card_header($$payload2, {
        class: "border-border gap-1 border-b !p-4",
        children: ($$payload3) => {
          Card_title($$payload3, {
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Briefcase($$payload4, { class: "h-5 w-5" });
              $$payload4.out += `<!----> ${escape_html(job.title)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(job.company)}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        class: "flex flex-col gap-2 p-4",
        children: ($$payload3) => {
          $$payload3.out += `<div class="text-muted-foreground space-y-1 text-xs">`;
          if (job.location) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="flex items-center gap-1">`;
            Map_pin($$payload3, { class: "h-3 w-3" });
            $$payload3.out += `<!----> ${escape_html(job.location)}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (job.employmentType) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> ${escape_html(job.employmentType)}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (job.remoteType) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> ${escape_html(job.remoteType)}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (formatSalary(job)) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="flex items-center gap-1">`;
            Dollar_sign($$payload3, { class: "h-3 w-3" });
            $$payload3.out += `<!----> ${escape_html(formatSalary(job))}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (job.postedDate) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<p class="flex items-center gap-1">`;
            Calendar($$payload3, { class: "h-3 w-3" });
            $$payload3.out += `<!----> Posted ${escape_html(new Date(job.postedDate).toLocaleDateString())}</p>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></div> `;
          if (job.benefits && job.benefits.length > 0) {
            $$payload3.out += "<!--[-->";
            const each_array = ensure_array_like(job.benefits.slice(0, 3));
            $$payload3.out += `<div class="flex flex-wrap gap-1"><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let benefit = each_array[$$index];
              Badge($$payload3, {
                variant: "outline",
                class: "px-1.5 py-0.5 text-xs",
                children: ($$payload4) => {
                  $$payload4.out += `<!---->${escape_html(benefit)}`;
                },
                $$slots: { default: true }
              });
            }
            $$payload3.out += `<!--]--></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_footer($$payload2, {
        class: "border-t !p-2",
        children: ($$payload3) => {
          $$payload3.out += `<div class="flex w-full gap-2">`;
          Button($$payload3, {
            size: "sm",
            variant: "outline",
            class: "h-8 flex-1 text-xs",
            onclick: () => window.open(job.url, "_blank"),
            children: ($$payload4) => {
              External_link($$payload4, { class: "mr-1 h-3 w-3" });
              $$payload4.out += `<!----> View Job`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Button($$payload3, {
            size: "sm",
            class: "h-8 flex-1 text-xs",
            onclick: () => goto(`/dashboard/jobs/${job.id}`),
            children: ($$payload4) => {
              $$payload4.out += `<!---->Apply Now`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  bind_props($$props, { job });
  pop();
}
function EmptyState($$payload, $$props) {
  let title = $$props["title"];
  let description = $$props["description"];
  let actionText = fallback($$props["actionText"], void 0);
  let actionHref = fallback($$props["actionHref"], void 0);
  $$payload.out += `<div class="flex flex-col items-center justify-center py-12 text-center"><div class="mx-auto max-w-md"><h3 class="mb-2 text-xl font-semibold">${escape_html(title)}</h3> <p class="mb-6 text-gray-500">${escape_html(description)}</p> `;
  if (actionText && actionHref) {
    $$payload.out += "<!--[-->";
    Button($$payload, {
      href: actionHref,
      children: ($$payload2) => {
        $$payload2.out += `<!---->${escape_html(actionText)}`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { title, description, actionText, actionHref });
}
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let matchScoreFilter = "";
  let locationFilter = "";
  let sortOption = "match";
  let showFilterSheet = false;
  let pageSize = data.pagination.limit;
  let profileSwitching = false;
  let maxMatches = () => {
    return 20;
  };
  let activeFilterCount = () => {
    let count = 0;
    if (matchScoreFilter) count++;
    if (locationFilter) count++;
    if (sortOption !== "match") count++;
    return count;
  };
  let filteredMatches = () => {
    let matches = [...data.matches];
    if (matchScoreFilter) {
      const minScore = parseInt(matchScoreFilter) / 100;
      matches = matches.filter((match) => match.matchScore >= minScore);
    }
    if (locationFilter) {
      matches = matches.filter((match) => {
        const job = match.job_listing;
        if (locationFilter === "remote") return job.remoteType === "Remote";
        if (locationFilter === "hybrid") return job.remoteType === "Hybrid";
        if (locationFilter === "onsite") return job.remoteType === "On-site";
        return true;
      });
    }
    matches.sort((a, b) => {
      switch (sortOption) {
        case "newest":
          return new Date(b.job_listing.postedDate).getTime() - new Date(a.job_listing.postedDate).getTime();
        case "salary":
          const aSalary = a.job_listing.salaryMax || a.job_listing.salaryMin || 0;
          const bSalary = b.job_listing.salaryMax || b.job_listing.salaryMin || 0;
          return bSalary - aSalary;
        case "company":
          return a.job_listing.company.localeCompare(b.job_listing.company);
        case "match":
        default:
          return b.matchScore - a.matchScore;
      }
    });
    return matches;
  };
  let filteredSavedJobs = () => {
    if (!savedJobsFilter) return savedJobs;
    const filter = savedJobsFilter.toLowerCase();
    return savedJobs.filter((savedJob) => {
      const job = savedJob.job_listing;
      if (!job) {
        console.log("No job_listing found for savedJob:", savedJob);
        return false;
      }
      const titleMatch = job.title?.toLowerCase().includes(filter);
      const companyMatch = job.company?.toLowerCase().includes(filter);
      const locationMatch = job.location?.toLowerCase().includes(filter);
      return titleMatch || companyMatch || locationMatch;
    });
  };
  const VALID_TABS = ["matches", "saved", "alerts"];
  let activeTab = "matches";
  if (typeof window !== "undefined") {
    const url = new URL(window.location.href);
    const tabParam = url.searchParams.get("tab");
    if (tabParam && VALID_TABS.includes(tabParam)) {
      activeTab = tabParam;
    }
  }
  let savedJobs = data.savedJobs || [];
  let savedJobsError = null;
  let savedJobsLoading = false;
  let savedJobsFilter = "";
  let showCreateAlertDialog = false;
  function getPageTitle(tab) {
    switch (tab) {
      case "saved":
        return "Saved Jobs";
      case "alerts":
        return "Job Alerts";
      default:
        return "Job Matches";
    }
  }
  function getPageDescription(tab) {
    switch (tab) {
      case "saved":
        return "View and manage your saved job opportunities";
      case "alerts":
        return "Manage your job alerts and notifications";
      default:
        return "Jobs matched to your profile based on your resume";
    }
  }
  let pageTitle = getPageTitle(activeTab);
  let pageDescription = getPageDescription(activeTab);
  let savedJobsLoaded = false;
  if (typeof window !== "undefined") {
    profileSwitching = false;
  }
  async function handleSaveJob(jobId) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/save`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notes: "" })
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Failed to save job");
      }
      toast.success("Job saved!", { description: "Added to your saved jobs" });
      if (activeTab === "saved") {
        fetchSavedJobs();
      }
    } catch (error) {
      console.error("Error saving job:", error);
      toast.error("Failed to save job");
    }
  }
  async function handleDismissJob(jobId) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/dismiss`, {
        method: "POST",
        headers: { "Content-Type": "application/json" }
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Failed to dismiss job");
      }
      data.matches = data.matches.filter((match) => match.job_listing.id !== jobId);
      toast.success("Job dismissed", {
        description: "This job will no longer appear in your matches"
      });
    } catch (error) {
      console.error("Error dismissing job:", error);
      toast.error("Failed to dismiss job");
    }
  }
  function handleTabChange(value) {
    activeTab = value;
    if (value === "saved" && !savedJobsLoaded) {
      fetchSavedJobs();
    }
    updateUrlWithTab(value);
  }
  function handleCreateAlert() {
    showCreateAlertDialog = true;
  }
  function handleAlertCreated() {
    showCreateAlertDialog = false;
    if (typeof window !== "undefined") {
      window.location.reload();
    }
  }
  function handleAlertDialogClose() {
    showCreateAlertDialog = false;
  }
  async function fetchSavedJobs() {
    savedJobsError = null;
    savedJobsLoading = true;
    try {
      const response = await fetch("/api/saved-jobs");
      if (!response.ok) {
        throw new Error("Failed to fetch saved jobs");
      }
      const result = await response.json();
      savedJobs = result.savedJobs || [];
      savedJobsLoaded = true;
    } catch (error) {
      console.error("Error fetching saved jobs:", error);
      savedJobsError = error.message;
      savedJobsLoaded = false;
    } finally {
      savedJobsLoading = false;
    }
  }
  function updateUrlWithTab(tab) {
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      url.searchParams.set("tab", tab);
      window.history.replaceState({}, "", url.toString());
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: `${stringify(pageTitle)} | Hirli`,
      description: pageDescription,
      keywords: "job matches, saved jobs, job alerts, AI recommendations, career matches, job opportunities, skill matching, resume matching"
    });
    $$payload2.out += `<!----> <!---->`;
    Root($$payload2, {
      value: activeTab,
      onValueChange: handleTabChange,
      children: ($$payload3) => {
        $$payload3.out += `<div class="p-0"><!---->`;
        Tabs_list($$payload3, {
          class: "border-t-0",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Tabs_trigger($$payload4, {
              value: "matches",
              class: "flex-1 gap-2",
              children: ($$payload5) => {
                Circle_check_big($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Job Matches</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "saved",
              class: "flex-1 gap-2",
              children: ($$payload5) => {
                Bookmark($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Saved Jobs</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "alerts",
              class: "flex-1 gap-2",
              children: ($$payload5) => {
                Bell($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Job Alerts</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> <!---->`;
        Tabs_content($$payload3, {
          value: "matches",
          children: ($$payload4) => {
            $$payload4.out += `<div class="border-border flex items-center justify-between border-b p-2"><div class="flex items-center gap-4">`;
            Button($$payload4, {
              variant: "outline",
              onclick: () => showFilterSheet = true,
              class: "relative flex items-center gap-2",
              children: ($$payload5) => {
                Settings($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> Filters `;
                if (activeFilterCount() > 0) {
                  $$payload5.out += "<!--[-->";
                  Badge($$payload5, {
                    variant: "default",
                    class: "ml-1 h-5 w-5 rounded-full p-0 text-xs",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->${escape_html(activeFilterCount())}`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex items-center gap-2">`;
            Badge($$payload4, {
              variant: "secondary",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(filteredMatches().length)} matches found`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Provider($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Root$2($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Tooltip_trigger($$payload6, {
                      children: ($$payload7) => {
                        Info($$payload7, {
                          class: "text-muted-foreground h-4 w-4 cursor-help"
                        });
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Tooltip_content($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<p>Free plan: ${escape_html(maxMatches())} matches per day</p>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              }
            });
            $$payload4.out += `<!----></div></div> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: data.selectedProfileId,
              disabled: profileSwitching,
              onValueChange: (profileId) => {
                if (profileId && profileId !== data.selectedProfileId) {
                  profileSwitching = true;
                  goto();
                }
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  class: " px-3 py-2",
                  children: ($$payload6) => {
                    $$payload6.out += `<div class="flex items-center gap-2">`;
                    if (profileSwitching) {
                      $$payload6.out += "<!--[-->";
                      Refresh_cw($$payload6, { class: "h-4 w-4 animate-spin" });
                    } else {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--> <!---->`;
                    Select_value($$payload6, {
                      placeholder: data.profiles.find((profile) => profile.id === data.selectedProfileId)?.name || "Select Profile"
                    });
                    $$payload6.out += `<!----></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  class: "max-h-60",
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(data.profiles);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let profile = each_array[$$index];
                      $$payload6.out += `<!---->`;
                      Select_item($$payload6, {
                        value: profile.id,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(profile.name)}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="p-4">`;
            if (data.profiles.length === 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="rounded-lg border p-6">`;
              EmptyState($$payload4, {
                title: "No profiles found",
                description: "Create a profile to get job matches based on your resume.",
                actionText: "Create Profile",
                actionHref: "/dashboard/settings/profile"
              });
              $$payload4.out += `<!----></div>`;
            } else if (filteredMatches().length === 0) {
              $$payload4.out += "<!--[1-->";
              $$payload4.out += `<div class="rounded-lg border p-6">`;
              EmptyState($$payload4, {
                title: data.matches.length === 0 ? "No job matches available" : "No matches with current filters",
                description: data.matches.length === 0 ? "We are continuously adding new job opportunities. Check back soon or browse all available jobs." : "Try adjusting your filters to see more job matches. You have " + data.matches.length + " total matches available.",
                actionText: data.matches.length === 0 ? "Browse All Jobs" : "Clear Filters",
                actionHref: data.matches.length === 0 ? "/dashboard/jobs" : "#"
              });
              $$payload4.out += `<!----></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
              const each_array_1 = ensure_array_like(filteredMatches());
              $$payload4.out += `<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"><!--[-->`;
              for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
                let match = each_array_1[$$index_2];
                const job = match.job_listing;
                const matchScore = Math.round(match.matchScore * 100);
                const scoreColor = matchScore >= 90 ? "bg-green-100 text-green-800 border-green-200" : matchScore >= 80 ? "bg-blue-100 text-blue-800 border-blue-200" : matchScore >= 70 ? "bg-yellow-100 text-yellow-800 border-yellow-200" : "bg-gray-100 text-gray-800 border-gray-200";
                $$payload4.out += `<!---->`;
                Card($$payload4, {
                  class: "hover:border-primary/50 group relative gap-0 p-0 transition-all hover:shadow-sm",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="absolute right-2 top-2 z-10 flex gap-1" role="group" aria-label="Job actions"><!---->`;
                    Provider($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->`;
                        Root$2($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            Tooltip_trigger($$payload7, {
                              children: ($$payload8) => {
                                Button($$payload8, {
                                  size: "sm",
                                  variant: "ghost",
                                  class: "hover:bg-background/80 h-6 w-6 p-0",
                                  onclick: () => handleSaveJob(job.id),
                                  children: ($$payload9) => {
                                    Bookmark($$payload9, { class: "h-3 w-3" });
                                  },
                                  $$slots: { default: true }
                                });
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Tooltip_content($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<p>Save job</p>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      }
                    });
                    $$payload5.out += `<!----> <!---->`;
                    Provider($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->`;
                        Root$2($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            Tooltip_trigger($$payload7, {
                              children: ($$payload8) => {
                                Button($$payload8, {
                                  size: "sm",
                                  variant: "ghost",
                                  class: "hover:bg-background/80 h-6 w-6 p-0",
                                  onclick: () => handleDismissJob(job.id),
                                  children: ($$payload9) => {
                                    X($$payload9, { class: "h-3 w-3" });
                                  },
                                  $$slots: { default: true }
                                });
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Tooltip_content($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<p>Dismiss job</p>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      }
                    });
                    $$payload5.out += `<!----></div> <div class="absolute -left-2 -top-2 z-10"><!---->`;
                    Provider($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->`;
                        Root$2($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            Tooltip_trigger($$payload7, {
                              children: ($$payload8) => {
                                Badge($$payload8, {
                                  class: `border-background rounded-full border-2 px-2 py-1 text-xs font-bold ${stringify(scoreColor)}`,
                                  children: ($$payload9) => {
                                    $$payload9.out += `<!---->${escape_html(matchScore)}%`;
                                  },
                                  $$slots: { default: true }
                                });
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!----> <!---->`;
                            Tooltip_content($$payload7, {
                              class: "max-w-xs",
                              children: ($$payload8) => {
                                $$payload8.out += `<div class="space-y-1"><p class="font-medium">Why this matches:</p> <ul class="space-y-0.5 text-xs"><li>• Skills match: ${escape_html(Math.round(matchScore * 0.8))}%</li> <li>• Experience level: ${escape_html(job.experienceLevel || "Mid-level")}</li> <li>• Location preference: ${escape_html(job.remoteType || "On-site")}</li> `;
                                if (job.techStack && job.techStack.length > 0) {
                                  $$payload8.out += "<!--[-->";
                                  $$payload8.out += `<li>• Tech stack: ${escape_html(job.techStack.slice(0, 2).join(", "))}</li>`;
                                } else {
                                  $$payload8.out += "<!--[!-->";
                                }
                                $$payload8.out += `<!--]--></ul></div>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      }
                    });
                    $$payload5.out += `<!----></div> <!---->`;
                    Card_header($$payload5, {
                      class: "border-border gap-1 border-b !p-4",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->`;
                        Card_title($$payload6, {
                          class: "flex items-center gap-2",
                          children: ($$payload7) => {
                            Briefcase($$payload7, { class: "h-5 w-5" });
                            $$payload7.out += `<!----> ${escape_html(job.title)}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> <!---->`;
                        Card_description($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->${escape_html(job.company)}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> <!---->`;
                    Card_content($$payload5, {
                      class: "flex flex-col gap-2 p-4",
                      children: ($$payload6) => {
                        if (job.benefits && job.benefits.length > 0) {
                          $$payload6.out += "<!--[-->";
                          const each_array_2 = ensure_array_like(job.benefits.slice(0, 3));
                          $$payload6.out += `<div class="flex flex-wrap gap-1"><!--[-->`;
                          for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                            let benefit = each_array_2[$$index_1];
                            Badge($$payload6, {
                              variant: "outline",
                              class: "px-1.5 py-0.5 text-xs",
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(benefit)}`;
                              },
                              $$slots: { default: true }
                            });
                          }
                          $$payload6.out += `<!--]--></div>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> <div class="text-muted-foreground space-y-1 text-xs"><p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> ${escape_html(job.location)}</p> `;
                        if (job.salaryMin && job.salaryMax) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> $${escape_html(job.salaryMin?.toLocaleString())} - $${escape_html(job.salaryMax?.toLocaleString())}</p>`;
                        } else if (job.salary) {
                          $$payload6.out += "<!--[1-->";
                          $$payload6.out += `<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> ${escape_html(job.salary)}</p>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> `;
                        if (job.employmentType) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> ${escape_html(job.employmentType)}</p>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> `;
                        if (job.remoteType) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `<p class="flex items-center gap-1"><span class="inline-block h-1 w-1 rounded-full bg-current"></span> ${escape_html(job.remoteType)}</p>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> <!---->`;
                    Card_footer($$payload5, {
                      class: "border-t !p-2",
                      children: ($$payload6) => {
                        Button($$payload6, {
                          size: "sm",
                          class: "h-8 w-full text-xs",
                          onclick: () => goto(`/dashboard/jobs/${job.id}`),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Apply Now`;
                          },
                          $$slots: { default: true }
                        });
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              }
              $$payload4.out += `<!--]--></div> `;
              if (data.pagination.totalPages > 1) {
                $$payload4.out += "<!--[-->";
                const each_array_3 = ensure_array_like(Array.from(
                  {
                    length: Math.min(5, data.pagination.totalPages)
                  },
                  (_, i) => {
                    const startPage = Math.max(1, data.pagination.page - 2);
                    return startPage + i;
                  }
                ).filter((page) => page <= data.pagination.totalPages));
                $$payload4.out += `<div class="mt-8 flex items-center justify-between"><div class="flex items-center gap-4"><div class="text-muted-foreground text-sm">Showing ${escape_html((data.pagination.page - 1) * data.pagination.limit + 1)} to ${escape_html(Math.min(data.pagination.page * data.pagination.limit, data.pagination.totalCount))} of ${escape_html(data.pagination.totalCount)} results</div> <div class="flex items-center gap-2"><span class="text-muted-foreground text-sm">Show:</span> <!---->`;
                Root$3($$payload4, {
                  type: "single",
                  value: String(pageSize),
                  onValueChange: (value) => {
                    const newPageSize = parseInt(value || "20");
                    pageSize = newPageSize;
                    goto(`/dashboard/matches?profileId=${data.selectedProfileId}&page=1&limit=${newPageSize}`);
                  },
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_trigger($$payload5, {
                      class: "h-8 w-16",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->`;
                        Select_value($$payload6, { placeholder: String(pageSize) });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> <!---->`;
                    Select_content($$payload5, {
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->`;
                        Select_item($$payload6, {
                          value: "10",
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->10`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> <!---->`;
                        Select_item($$payload6, {
                          value: "20",
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->20`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> <!---->`;
                        Select_item($$payload6, {
                          value: "50",
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->50`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> <!---->`;
                        Select_item($$payload6, {
                          value: "100",
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->100`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div></div> <div class="flex items-center gap-2">`;
                Button($$payload4, {
                  variant: "outline",
                  size: "sm",
                  disabled: data.pagination.page === 1,
                  onclick: () => goto(`/dashboard/matches?profileId=${data.selectedProfileId}&page=1&limit=${pageSize}`),
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->First`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Button($$payload4, {
                  variant: "outline",
                  size: "sm",
                  disabled: data.pagination.page === 1,
                  onclick: () => goto(`/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.page - 1}&limit=${pageSize}`),
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Previous`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <div class="flex items-center gap-1"><!--[-->`;
                for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                  let page = each_array_3[$$index_3];
                  Button($$payload4, {
                    variant: page === data.pagination.page ? "default" : "outline",
                    size: "sm",
                    class: "h-8 w-8 p-0",
                    onclick: () => goto(`/dashboard/matches?profileId=${data.selectedProfileId}&page=${page}&limit=${pageSize}`),
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(page)}`;
                    },
                    $$slots: { default: true }
                  });
                }
                $$payload4.out += `<!--]--></div> `;
                Button($$payload4, {
                  variant: "outline",
                  size: "sm",
                  disabled: !data.pagination.hasMore,
                  onclick: () => goto(`/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.page + 1}&limit=${pageSize}`),
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Next`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Button($$payload4, {
                  variant: "outline",
                  size: "sm",
                  disabled: !data.pagination.hasMore,
                  onclick: () => goto(`/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.totalPages}&limit=${pageSize}`),
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Last`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            }
            $$payload4.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "saved",
          class: "p-4",
          children: ($$payload4) => {
            $$payload4.out += `<div class="mb-6 flex items-center justify-between"><div class="flex items-center gap-4"><h2 class="text-lg font-semibold">Your Saved Jobs</h2> `;
            Badge($$payload4, {
              variant: "secondary",
              class: "text-sm",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(savedJobs.length)} saved`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (savedJobsFilter) {
              $$payload4.out += "<!--[-->";
              Badge($$payload4, {
                variant: "outline",
                class: "text-sm",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->${escape_html(filteredSavedJobs().length)} filtered`;
                },
                $$slots: { default: true }
              });
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="flex gap-2">`;
            Button($$payload4, {
              variant: "outline",
              onclick: fetchSavedJobs,
              disabled: savedJobsLoading,
              class: "flex items-center gap-2",
              children: ($$payload5) => {
                Refresh_cw($$payload5, {
                  class: `h-4 w-4 ${stringify(savedJobsLoading ? "animate-spin" : "")}`
                });
                $$payload5.out += `<!----> <span>Refresh</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Button($$payload4, {
              variant: "outline",
              onclick: () => goto(),
              class: "flex items-center gap-2",
              children: ($$payload5) => {
                Chevron_right($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Browse Jobs</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div> `;
            if (savedJobs.length > 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="mb-4">`;
              Input($$payload4, {
                type: "text",
                placeholder: "Search saved jobs by title, company, or location...",
                class: "max-w-md",
                get value() {
                  return savedJobsFilter;
                },
                set value($$value) {
                  savedJobsFilter = $$value;
                  $$settled = false;
                }
              });
              $$payload4.out += `<!----></div> <div class="mb-4 rounded-lg bg-gray-50 p-3 text-xs"><p><strong>Debug Info:</strong></p> <p>Total saved jobs: ${escape_html(savedJobs.length)}</p> <p>Filtered results: ${escape_html(filteredSavedJobs().length)}</p> <p>Search term: "${escape_html(savedJobsFilter)}"</p> `;
              if (savedJobs.length > 0) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<details class="mt-2"><summary class="cursor-pointer font-medium">Sample job data</summary> <pre class="mt-2 overflow-auto text-xs">${escape_html(JSON.stringify(savedJobs[0], null, 2))}</pre></details>`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `<p class="text-red-600">No saved jobs found in data</p> `;
                if (data.matches.length > 0) {
                  $$payload4.out += "<!--[-->";
                  Button($$payload4, {
                    size: "sm",
                    class: "mt-2",
                    onclick: () => handleSaveJob(data.matches[0].job_listing.id),
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->Test: Save First Match`;
                    },
                    $$slots: { default: true }
                  });
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              }
              $$payload4.out += `<!--]--></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            if (savedJobsLoading) {
              $$payload4.out += "<!--[-->";
              const each_array_4 = ensure_array_like(Array(8));
              $$payload4.out += `<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"><!--[-->`;
              for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
                each_array_4[$$index_4];
                $$payload4.out += `<div class="rounded-lg border p-6"><div class="space-y-3">`;
                Skeleton($$payload4, { class: "h-4 w-3/4" });
                $$payload4.out += `<!----> `;
                Skeleton($$payload4, { class: "h-4 w-1/2" });
                $$payload4.out += `<!----> <div class="space-y-2">`;
                Skeleton($$payload4, { class: "h-3 w-full" });
                $$payload4.out += `<!----> `;
                Skeleton($$payload4, { class: "h-3 w-2/3" });
                $$payload4.out += `<!----></div> `;
                Skeleton($$payload4, { class: "h-8 w-full rounded" });
                $$payload4.out += `<!----></div></div>`;
              }
              $$payload4.out += `<!--]--></div>`;
            } else if (savedJobsError) {
              $$payload4.out += "<!--[1-->";
              $$payload4.out += `<div class="rounded-lg border border-red-200 bg-red-50 p-6"><div class="flex flex-col items-center space-y-2 text-center">`;
              Circle_alert($$payload4, { class: "h-6 w-6 text-red-500" });
              $$payload4.out += `<!----> <h3 class="text-lg font-medium text-red-800">Error loading saved jobs</h3> <p class="text-sm text-red-600">${escape_html(savedJobsError)}</p> `;
              Button($$payload4, {
                variant: "outline",
                class: "mt-4",
                onclick: fetchSavedJobs,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Try Again`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div></div>`;
            } else if (savedJobs.length > 0) {
              $$payload4.out += "<!--[2-->";
              if (filteredSavedJobs().length > 0) {
                $$payload4.out += "<!--[-->";
                const each_array_5 = ensure_array_like(filteredSavedJobs());
                $$payload4.out += `<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"><!--[-->`;
                for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
                  let savedJob = each_array_5[$$index_5];
                  const job = savedJob.job_listing;
                  if (job) {
                    $$payload4.out += "<!--[-->";
                    SavedJobCard($$payload4, { job });
                  } else {
                    $$payload4.out += "<!--[!-->";
                  }
                  $$payload4.out += `<!--]-->`;
                }
                $$payload4.out += `<!--]--></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `<div class="rounded-lg border p-6 text-center"><div class="text-muted-foreground"><p class="text-lg font-medium">No jobs match your search</p> <p class="mt-1 text-sm">Try adjusting your search terms or clear the filter.</p> `;
                Button($$payload4, {
                  variant: "outline",
                  class: "mt-4",
                  onclick: () => savedJobsFilter = "",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Clear Search`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div></div>`;
              }
              $$payload4.out += `<!--]-->`;
            } else {
              $$payload4.out += "<!--[!-->";
              EmptyState($$payload4, {
                title: "No saved jobs",
                description: "Save jobs that interest you to keep track of opportunities.",
                actionText: "Browse Jobs",
                actionHref: "/dashboard/jobs"
              });
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "alerts",
          class: "p-4",
          children: ($$payload4) => {
            $$payload4.out += `<div class="mb-6 flex items-center justify-between"><div class="flex items-center gap-4"><h2 class="text-lg font-semibold">Your Job Alerts</h2> `;
            Badge($$payload4, {
              variant: "secondary",
              class: "text-sm",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(data.jobAlerts?.length || 0)} alerts`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (data.jobAlerts?.filter((alert) => alert.enabled).length) {
              $$payload4.out += "<!--[-->";
              Badge($$payload4, {
                variant: "default",
                class: "text-sm",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->${escape_html(data.jobAlerts.filter((alert) => alert.enabled).length)} active`;
                },
                $$slots: { default: true }
              });
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> `;
            Button($$payload4, {
              variant: "default",
              onclick: handleCreateAlert,
              class: "flex items-center gap-2",
              children: ($$payload5) => {
                Plus($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Create Alert</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="rounded-lg border p-6">`;
            AlertsList($$payload4, {
              alerts: data.jobAlerts || [],
              onCreateAlert: handleCreateAlert
            });
            $$payload4.out += `<!----></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Root$1($$payload2, {
      get open() {
        return showFilterSheet;
      },
      set open($$value) {
        showFilterSheet = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Sheet_content($$payload3, {
          side: "left",
          class: "w-[400px] sm:w-[540px]",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Sheet_header($$payload4, {
              class: "border-b p-4",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Sheet_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Filter Jobs`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Sheet_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Refine your job matches with filters and sorting options.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="space-y-6 p-4"><div class="space-y-2">`;
            Label($$payload4, {
              class: "text-sm font-medium",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Match Score`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: matchScoreFilter,
              onValueChange: (value) => {
                matchScoreFilter = value || "";
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "All Matches" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_item($$payload6, {
                      value: "",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->All Matches`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "90",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->90%+ (Excellent)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "80",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->80-89% (Great)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "70",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->70-79% (Good)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "60",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->60-69% (Fair)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              class: "text-sm font-medium",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Location Type`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: locationFilter,
              onValueChange: (value) => {
                locationFilter = value || "";
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "All Locations" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_item($$payload6, {
                      value: "",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->All Locations`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "remote",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Remote`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "hybrid",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Hybrid`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "onsite",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->On-site`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              class: "text-sm font-medium",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Sort By`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: sortOption,
              onValueChange: (value) => {
                sortOption = value || "match";
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Best Match" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_item($$payload6, {
                      value: "match",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Best Match`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "newest",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Newest First`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "salary",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Highest Salary`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "company",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Company A-Z`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              class: "text-sm font-medium",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Quick Filters`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex flex-wrap gap-2">`;
            Button($$payload4, {
              variant: matchScoreFilter === "90" ? "default" : "outline",
              size: "sm",
              class: "h-8",
              onclick: () => {
                matchScoreFilter = matchScoreFilter === "90" ? "" : "90";
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->High Match (90%+)`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Button($$payload4, {
              variant: locationFilter === "remote" ? "default" : "outline",
              size: "sm",
              class: "h-8",
              onclick: () => {
                locationFilter = locationFilter === "remote" ? "" : "remote";
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->Remote Only`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Button($$payload4, {
              variant: "outline",
              size: "sm",
              class: "h-8",
              onclick: () => {
                toast.info("Tech company filter coming soon!");
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->Tech Companies`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Button($$payload4, {
              variant: "outline",
              size: "sm",
              class: "h-8",
              onclick: () => {
                toast.info("Salary filter coming soon!");
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->$100k+ Salary`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div></div> <!---->`;
            Sheet_footer($$payload4, {
              class: "border-t p-2",
              children: ($$payload5) => {
                $$payload5.out += `<div class="flex gap-2">`;
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => {
                    matchScoreFilter = "";
                    locationFilter = "";
                    sortOption = "match";
                  },
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Clear All`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----></div>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    if (showCreateAlertDialog) {
      $$payload2.out += "<!--[-->";
      CreateAlertDialog($$payload2, {
        onClose: handleAlertDialogClose,
        onCreated: handleAlertCreated,
        userId: data.user?.id
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BSe2Fn6n.js.map
