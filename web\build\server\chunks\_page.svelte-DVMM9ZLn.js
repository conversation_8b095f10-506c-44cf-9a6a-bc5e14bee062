import 'clsx';
import { p as push, q as pop } from './index3-CqUPEnZw.js';
import { R as ResumeTabs, a as Resume, E as EditDesignPanel, b as ResumeForm } from './EditDesignPanel-Dil18Jqx.js';
import './false-CRHihH2U.js';
import './button-CrucCo1G.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './input-DF0gPqYN.js';
import './buildResume-ByCXwpI3.js';
import './types-D78SXuvm.js';
import './index-server-CezSOnuG.js';
import './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import './index2-Cut0V_vU.js';
import './rotate-ccw-CmotOMz1.js';
import './Icon-A4vzmk-O.js';
import './settings-STaOxCkl.js';
import './sparkles-E4-thk3U.js';
import './textarea-DnpYDER1.js';
import './zod-DfpldWlD.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './superForm-CVYoTAIb.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './client-dNyMPa8V.js';
import './stringify-DWCARkQV.js';
import './index7-BURUpWjT.js';
import './scroll-lock-BkBz2nVp.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './chevron-down-xGjWLrZH.js';
import './dialog-description-CxPAHL_4.js';
import './dialog-description2-rfr-pd9k.js';
import './store-Dgwm3sxJ.js';
import './html-FW6Ia4bL.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './label-Dt8gTF_8.js';
import './separator-5ooeI4XN.js';
import './index15-D3NL0C7o.js';

function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  $$payload.out += `<div class="grid h-screen grid-cols-1 md:grid-cols-[2.5fr_3fr]"><div class="overflow-y-auto border border-b-0 border-l-0 border-t-0 border-zinc-900 p-6 text-white">`;
  ResumeTabs($$payload, {
    $$slots: {
      content: ($$payload2) => {
        $$payload2.out += `<div slot="content">`;
        ResumeForm($$payload2, { data: data.form.resume });
        $$payload2.out += `<!----></div>`;
      },
      design: ($$payload2) => {
        $$payload2.out += `<div slot="design">`;
        EditDesignPanel($$payload2, { data: data.form.design });
        $$payload2.out += `<!----></div>`;
      }
    }
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-center overflow-y-auto bg-neutral-950 p-4">`;
  Resume($$payload, {});
  $$payload.out += `<!----></div></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DVMM9ZLn.js.map
