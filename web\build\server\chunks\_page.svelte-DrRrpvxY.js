import { p as push, q as pop, M as ensure_array_like, O as escape_html, S as store_get, V as copy_payload, W as assign_payload, T as unsubscribe_stores, J as attr_class, P as stringify, R as spread_props, ab as store_mutate } from './index3-CqUPEnZw.js';
import 'clsx';
import './zod-DfpldWlD.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { F as Form_field, C as Control, a as Form_field_errors } from './index15-D3NL0C7o.js';
import { I as Input } from './input-DF0gPqYN.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { B as Button } from './button-CrucCo1G.js';
import { A as Avatar, a as Avatar_image, b as Avatar_fallback } from './avatar-fallback-B2wWy5ce.js';
import { X } from './x-DwZgpWRG.js';
import { U as Upload } from './upload-C2KwXIf1.js';
import { F as Form_description } from './form-description-jswnCHVb.js';
import { R as Root$1, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { F as Form_label } from './form-label-C5yTxnxS.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { S as Select_group } from './select-group-Cxqg41Dj.js';
import { w as writable } from './store-Dgwm3sxJ.js';
import { f as setMode } from './mode-CboidaPj.js';
import './index-DAbaXdpL.js';
import { S as Sun, M as Moon } from './sun-B8mCPuDt.js';
import { M as Monitor } from './monitor-_9b3qg6F.js';
import { C as Cookie } from './cookie-CTvRQAsw.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { U as User } from './user-DpDpidvb.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { E as Eye } from './eye-B2tdw2__.js';
import { S as Settings } from './settings-STaOxCkl.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './index2-Cut0V_vU.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './index-server-CezSOnuG.js';
import './client-dNyMPa8V.js';
import './stringify-DWCARkQV.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './label-Dt8gTF_8.js';

function requestPushPermission() {
  return Promise.resolve("denied");
}
async function subscribeToPush() {
  const permission = await requestPushPermission();
  if (permission !== "granted") {
    return { success: false, error: "Permission denied" };
  }
  const resp = await fetch("/api/push/vapid-key");
  if (!resp.ok) {
    return { success: false, error: "Couldn’t fetch VAPID key" };
  }
  const { publicKey } = await resp.json();
  const registration = await navigator.serviceWorker.ready;
  const subscription = await registration.pushManager.subscribe({
    userVisibleOnly: true,
    applicationServerKey: publicKey
  });
  const save = await fetch("/api/push/subscribe", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ subscription })
  });
  if (!save.ok) {
    return { success: false, error: "Failed to save subscription" };
  }
  return { success: true };
}
async function unsubscribeFromPush() {
  const registration = await navigator.serviceWorker.ready;
  const sub = await registration.pushManager.getSubscription();
  if (sub) {
    await sub.unsubscribe();
  }
  const resp = await fetch("/api/push/unsubscribe", { method: "POST" });
  if (!resp.ok) {
    return { success: false, error: "Failed to unregister on server" };
  }
  return { success: true };
}
const defaultState = {
  theme: "system",
  ui: {
    sidebarCollapsed: false,
    activeModals: {},
    lastViewedSection: null,
    viewMode: "list"
  },
  features: {},
  searchHistory: [],
  notificationPreferences: {
    job_alerts: {
      type: "job_alerts",
      channels: { email: true, push: true, in_app: true },
      enabled: true
    },
    application_updates: {
      type: "application_updates",
      channels: { email: true, push: true, in_app: true },
      enabled: true
    },
    messages: {
      type: "messages",
      channels: { email: true, push: true, in_app: true },
      enabled: true
    },
    system: {
      type: "system",
      channels: { email: true, push: false, in_app: true },
      enabled: true
    }
  },
  // Default account preferences
  account: {
    phone: "",
    bio: "",
    language: "en",
    timezone: "UTC",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12h",
    accessibility: {
      theme: "system",
      highContrast: false,
      reducedMotion: false,
      largeText: false,
      screenReader: false
    },
    privacy: {
      profileVisibility: "public",
      activityVisibility: "public",
      allowDataCollection: true,
      allowThirdPartySharing: false
    },
    cookiePreferences: {
      functional: true,
      analytics: true,
      advertising: false
    }
  }
};
function loadInitialState() {
  return defaultState;
}
const store = writable(loadInitialState());
function updateAccessibilitySettings(settings) {
  store.update((state) => {
    if (Object.keys(settings).length === 0) return state;
    const updatedAccessibility = { ...state.account.accessibility };
    if (settings.theme !== void 0) updatedAccessibility.theme = settings.theme;
    if (settings.highContrast !== void 0)
      updatedAccessibility.highContrast = settings.highContrast;
    if (settings.reducedMotion !== void 0)
      updatedAccessibility.reducedMotion = settings.reducedMotion;
    if (settings.largeText !== void 0) updatedAccessibility.largeText = settings.largeText;
    if (settings.screenReader !== void 0)
      updatedAccessibility.screenReader = settings.screenReader;
    if (settings.sidebarCollapsed !== void 0)
      updatedAccessibility.sidebarCollapsed = settings.sidebarCollapsed;
    if (settings.viewMode !== void 0) updatedAccessibility.viewMode = settings.viewMode;
    const updatedState = { ...state };
    if (settings.sidebarCollapsed !== void 0) {
      updatedState.ui = {
        ...updatedState.ui,
        sidebarCollapsed: settings.sidebarCollapsed
      };
    }
    if (settings.viewMode !== void 0) {
      updatedState.ui = {
        ...updatedState.ui,
        viewMode: settings.viewMode
      };
    }
    return {
      ...updatedState,
      account: {
        ...updatedState.account,
        accessibility: updatedAccessibility
      }
    };
  });
}
const HIRLI_COOKIE_NAME = "hi_cp";
const HIRLI_CONSENT_FLAG = "hi_consent";
function saveHirliCookiePreferences(preferences) {
  if (typeof window === "undefined") {
    return;
  }
  const cookieValue = {
    v: "1.0",
    // Version for future compatibility
    t: (/* @__PURE__ */ new Date()).getTime(),
    // Timestamp for tracking when consent was given
    p: preferences
    // The actual preferences
  };
  const stringifiedValue = JSON.stringify(cookieValue);
  localStorage.setItem(HIRLI_COOKIE_NAME, stringifiedValue);
  try {
    setCookie(HIRLI_COOKIE_NAME, btoa(stringifiedValue), { days: 365 });
  } catch (e) {
    setCookie(HIRLI_COOKIE_NAME, "true", { days: 365 });
    console.warn("Could not store full cookie preferences, using simplified version");
  }
  setCookie(HIRLI_CONSENT_FLAG, "true", { days: 365 });
}
function clearHirliCookiePreferences() {
  if (typeof window === "undefined") {
    return;
  }
  localStorage.removeItem(HIRLI_COOKIE_NAME);
  deleteCookie(HIRLI_COOKIE_NAME);
  deleteCookie(HIRLI_CONSENT_FLAG);
}
function setCookie(name, value, options = {}) {
  if (typeof window === "undefined") {
    return;
  }
  const { days = 365, path = "/", domain, secure = true } = options;
  const expiryDate = /* @__PURE__ */ new Date();
  expiryDate.setDate(expiryDate.getDate() + days);
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}; expires=${expiryDate.toUTCString()}; path=${path}; SameSite=Lax`;
  if (domain) {
    cookieString += `; domain=${domain}`;
  }
  if (secure) {
    cookieString += "; Secure";
  }
  document.cookie = cookieString;
}
function deleteCookie(name, path = "/", domain) {
  if (typeof window === "undefined") {
    return;
  }
  document.cookie = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${""}; SameSite=Lax`;
}
function Personal($$payload, $$props) {
  push();
  var $$store_subs;
  const { form, formData } = $$props;
  let fileInput;
  let uploading = false;
  let profilePicture = store_get($$store_subs ??= {}, "$formData", formData).profilePicture || null;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Personal Information</h4> <p class="text-muted-foreground text-sm">Update your personal details and profile picture.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><div class="flex flex-col items-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0"><div class="relative"><!---->`;
    Avatar($$payload2, {
      class: "h-24 w-24",
      children: ($$payload3) => {
        if (profilePicture) {
          $$payload3.out += "<!--[-->";
          $$payload3.out += `<!---->`;
          Avatar_image($$payload3, { src: profilePicture, alt: "Profile" });
          $$payload3.out += `<!---->`;
        } else {
          $$payload3.out += "<!--[!-->";
          $$payload3.out += `<!---->`;
          Avatar_fallback($$payload3, {
            class: "text-lg",
            children: ($$payload4) => {
              $$payload4.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$formData", formData).name ? store_get($$store_subs ??= {}, "$formData", formData).name.charAt(0).toUpperCase() : "U")}`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        }
        $$payload3.out += `<!--]-->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    if (profilePicture) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<button type="button" class="bg-destructive text-destructive-foreground hover:bg-destructive/90 absolute -right-2 -top-2 rounded-full p-1 shadow-sm" aria-label="Remove profile picture">`;
      X($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----></button>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--></div> <div class="flex flex-col space-y-2"><div class="text-sm font-medium">Profile Picture</div> <div class="text-muted-foreground text-xs">Upload a photo to personalize your profile.</div> <div class="flex items-center space-x-2">`;
    Button($$payload2, {
      type: "button",
      variant: "outline",
      size: "sm",
      class: "flex items-center gap-2",
      disabled: uploading,
      onclick: () => fileInput.click(),
      children: ($$payload3) => {
        Upload($$payload3, { class: "h-4 w-4" });
        $$payload3.out += `<!----> ${escape_html("Upload")}`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <input type="file" accept="image/*" class="hidden"/></div></div></div> <!---->`;
    Form_field($$payload2, {
      form,
      name: "name",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        {
          let children = function($$payload4, { props }) {
            $$payload4.out += `<div class="font-medium">Full Name</div> `;
            Input($$payload4, spread_props([
              props,
              {
                type: "text",
                get value() {
                  return store_get($$store_subs ??= {}, "$formData", formData).name;
                },
                set value($$value) {
                  store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).name = $$value);
                  $$settled = false;
                }
              }
            ]));
            $$payload4.out += `<!---->`;
          };
          Control($$payload3, { children });
        }
        $$payload3.out += `<!----> <!---->`;
        Form_description($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->Your name as it appears on your profile`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Form_field_errors($$payload3, {});
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Form_field($$payload2, {
      form,
      name: "email",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        {
          let children = function($$payload4, { props }) {
            $$payload4.out += `<div class="font-medium">Email Address</div> `;
            Input($$payload4, spread_props([
              props,
              {
                type: "email",
                value: store_get($$store_subs ??= {}, "$formData", formData).email
              }
            ]));
            $$payload4.out += `<!---->`;
          };
          Control($$payload3, { children });
        }
        $$payload3.out += `<!----> <!---->`;
        Form_description($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->Your email address (cannot be changed)`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Form_field_errors($$payload3, {});
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Form_field($$payload2, {
      form,
      name: "phone",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        {
          let children = function($$payload4, { props }) {
            $$payload4.out += `<div class="font-medium">Phone Number</div> `;
            Input($$payload4, spread_props([
              props,
              {
                type: "tel",
                get value() {
                  return store_get($$store_subs ??= {}, "$formData", formData).phone;
                },
                set value($$value) {
                  store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).phone = $$value);
                  $$settled = false;
                }
              }
            ]));
            $$payload4.out += `<!---->`;
          };
          Control($$payload3, { children });
        }
        $$payload3.out += `<!----> <!---->`;
        Form_description($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->Your contact phone number`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Form_field_errors($$payload3, {});
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Form_field($$payload2, {
      form,
      name: "bio",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        {
          let children = function($$payload4, { props }) {
            $$payload4.out += `<div class="font-medium">Bio</div> `;
            Textarea($$payload4, spread_props([
              props,
              {
                get value() {
                  return store_get($$store_subs ??= {}, "$formData", formData).bio;
                },
                set value($$value) {
                  store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).bio = $$value);
                  $$settled = false;
                }
              }
            ]));
            $$payload4.out += `<!---->`;
          };
          Control($$payload3, { children });
        }
        $$payload3.out += `<!----> <!---->`;
        Form_description($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->A brief description about yourself`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Form_field_errors($$payload3, {});
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Privacy($$payload, $$props) {
  push();
  var $$store_subs;
  const { form, formData } = $$props;
  const visibilityOptions = [
    { value: "public", label: "Public" },
    { value: "private", label: "Private" }
  ];
  $$payload.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Privacy Settings</h4> <p class="text-muted-foreground text-sm">Control your privacy and data preferences.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!---->`;
  Form_field($$payload, {
    form,
    name: "profileVisibility",
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      {
        let children = function($$payload3, { props }) {
          $$payload3.out += `<div class="space-y-0.5"><!---->`;
          Form_label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Profile Visibility`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Form_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Who can see your profile information`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> <!---->`;
          Root$1($$payload3, spread_props([
            props,
            {
              type: "single",
              value: store_get($$store_subs ??= {}, "$formData", formData).profileVisibility || "public",
              onValueChange: (value) => {
                formData.update((f) => ({ ...f, profileVisibility: value }));
                setTimeout(
                  () => {
                    const submitButton = document.getElementById("submit-button");
                    submitButton?.click();
                  },
                  100
                );
              },
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Select_trigger($$payload4, {
                  class: "w-full",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_value($$payload5, { placeholder: "Select visibility" });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Select_content($$payload4, {
                  class: "max-h-60",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_group($$payload5, {
                      children: ($$payload6) => {
                        const each_array = ensure_array_like(visibilityOptions);
                        $$payload6.out += `<!--[-->`;
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let option = each_array[$$index];
                          $$payload6.out += `<!---->`;
                          Select_item($$payload6, {
                            value: option.value,
                            label: option.label,
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->${escape_html(option.label)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!---->`;
                        }
                        $$payload6.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out += `<!---->`;
        };
        Control($$payload2, { children });
      }
      $$payload2.out += `<!----> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Who can see your profile information`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "allowDataCollection",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Data Collection</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Allow us to collect usage data to improve your experience`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).allowDataCollection),
            onCheckedChange: (checked) => {
              formData.update((f) => ({ ...f, allowDataCollection: checked }));
              setTimeout(
                () => {
                  const submitButton = document.getElementById("submit-button");
                  submitButton?.click();
                },
                100
              );
            }
          });
          $$payload3.out += `<!---->`;
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "allowThirdPartySharing",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Third-Party Data Sharing</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Allow sharing your data with trusted partners`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).allowThirdPartySharing),
            onCheckedChange: (checked) => {
              formData.update((f) => ({ ...f, allowThirdPartySharing: checked }));
              setTimeout(
                () => {
                  const submitButton = document.getElementById("submit-button");
                  submitButton?.click();
                },
                100
              );
            }
          });
          $$payload3.out += `<!---->`;
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Accessibility($$payload, $$props) {
  push();
  var $$store_subs;
  const { form, formData } = $$props;
  if (store_get($$store_subs ??= {}, "$formData", formData)) {
    if (store_get($$store_subs ??= {}, "$formData", formData).theme) {
      setMode(store_get($$store_subs ??= {}, "$formData", formData).theme);
    }
    const settings = {};
    if (store_get($$store_subs ??= {}, "$formData", formData).highContrast !== void 0) settings.highContrast = Boolean(store_get($$store_subs ??= {}, "$formData", formData).highContrast);
    if (store_get($$store_subs ??= {}, "$formData", formData).reducedMotion !== void 0) settings.reducedMotion = Boolean(store_get($$store_subs ??= {}, "$formData", formData).reducedMotion);
    if (store_get($$store_subs ??= {}, "$formData", formData).largeText !== void 0) settings.largeText = Boolean(store_get($$store_subs ??= {}, "$formData", formData).largeText);
    if (store_get($$store_subs ??= {}, "$formData", formData).screenReader !== void 0) settings.screenReader = Boolean(store_get($$store_subs ??= {}, "$formData", formData).screenReader);
    if (store_get($$store_subs ??= {}, "$formData", formData).viewMode !== void 0) settings.viewMode = store_get($$store_subs ??= {}, "$formData", formData).viewMode;
    if (Object.keys(settings).length > 0) {
      updateAccessibilitySettings(settings);
    }
  }
  function handleAccessibilityChange(setting, value) {
    console.log(`Changing ${setting} to ${value}`);
    updateAccessibilitySettings({ [setting]: value });
    formData.update((f) => ({ ...f, [setting]: value }));
    const submitButton = document.getElementById("submit-button");
    if (submitButton) submitButton.click();
  }
  $$payload.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Accessibility Settings</h4> <p class="text-muted-foreground text-sm">Customize your experience for better accessibility.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!---->`;
  Form_field($$payload, {
    form,
    name: "theme",
    children: ($$payload2) => {
      $$payload2.out += `<div class="space-y-2"><div class="font-medium">Theme Preference</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Choose your preferred theme for the application`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <div class="flex flex-col gap-4 pt-2 sm:flex-row"><button type="button"${attr_class(`hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 ${stringify(store_get($$store_subs ??= {}, "$store", store)?.account?.accessibility?.theme === "light" ? "border-primary" : "border-muted")}`)}>`;
      Sun($$payload2, { class: "mb-2 h-6 w-6 text-yellow-500" });
      $$payload2.out += `<!----> <span>Light</span></button> <button type="button"${attr_class(`hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 ${stringify(store_get($$store_subs ??= {}, "$store", store)?.account?.accessibility?.theme === "dark" ? "border-primary" : "border-muted")}`)}>`;
      Moon($$payload2, { class: "mb-2 h-6 w-6 text-blue-400" });
      $$payload2.out += `<!----> <span>Dark</span></button> <button type="button"${attr_class(`hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 ${stringify(store_get($$store_subs ??= {}, "$store", store)?.account?.accessibility?.theme === "system" ? "border-primary" : "border-muted")}`)}>`;
      Monitor($$payload2, { class: "mb-2 h-6 w-6 text-gray-500" });
      $$payload2.out += `<!----> <span>System</span></button></div></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "highContrast",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">High Contrast Mode</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Increase contrast for better visibility`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).highContrast),
            onCheckedChange: (checked) => handleAccessibilityChange("highContrast", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "pushNotifications",
    children: ($$payload2) => {
      $$payload2.out += `<div class="space-y-4"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Push Notifications</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Enable or disable browser push notifications`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).pushNotifications),
            onCheckedChange: async (checked) => {
              if (checked) {
                const result = await subscribeToPush();
                if (result.success) {
                  formData.update((f) => ({ ...f, pushNotifications: true }));
                  const submitButton = document.getElementById("submit-button");
                  if (submitButton) submitButton.click();
                  toast.success("Push notifications enabled successfully!");
                } else {
                  formData.update((f) => ({ ...f, pushNotifications: false }));
                  toast.error(result.error || "Failed to enable push notifications");
                }
              } else {
                const result = await unsubscribeFromPush();
                if (result.success) {
                  formData.update((f) => ({ ...f, pushNotifications: false }));
                  const submitButton = document.getElementById("submit-button");
                  if (submitButton) submitButton.click();
                  toast.success("Push notifications disabled successfully");
                } else {
                  formData.update((f) => ({ ...f, pushNotifications: true }));
                  toast.error(result.error || "Failed to disable push notifications");
                }
              }
            }
          });
        }
      });
      $$payload2.out += `<!----></div></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Cookie_preferences($$payload, $$props) {
  push();
  var $$store_subs;
  const { formData } = $$props;
  function updateCookiePreferences(type, checked) {
    formData.update((f) => {
      const updatedPreferences = { ...f.cookiePreferences || {}, [type]: checked };
      const allPreferences = {
        essential: true,
        // Always true
        ...updatedPreferences
      };
      saveHirliCookiePreferences(allPreferences);
      toast.success("Cookie preferences updated");
      const submitButton = document.getElementById("submit-button");
      submitButton?.click();
      return { ...f, cookiePreferences: updatedPreferences };
    });
  }
  function resetCookiePreferences() {
    clearHirliCookiePreferences();
    formData.update((f) => ({
      ...f,
      cookiePreferences: {
        functional: false,
        analytics: false,
        advertising: false
      }
    }));
    const submitButton = document.getElementById("submit-button");
    submitButton?.click();
    toast.success("Cookie preferences reset. Refresh the page to see the consent banner again.");
  }
  $$payload.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Cookie Preferences</h4> <p class="text-muted-foreground text-sm">Manage how we use cookies on our website.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Essential Cookies</div> <p class="text-muted-foreground text-sm">Required for the website to function properly. Cannot be disabled.</p></div> `;
  Switch($$payload, { checked: true, disabled: true });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Functional Cookies</div> <p class="text-muted-foreground text-sm">Enable personalized features and remember your preferences.</p></div> `;
  Switch($$payload, {
    checked: store_get($$store_subs ??= {}, "$formData", formData).cookiePreferences?.functional ?? true,
    onCheckedChange: (checked) => updateCookiePreferences("functional", checked)
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Analytics Cookies</div> <p class="text-muted-foreground text-sm">Help us understand how visitors use our website.</p></div> `;
  Switch($$payload, {
    checked: store_get($$store_subs ??= {}, "$formData", formData).cookiePreferences?.analytics ?? true,
    onCheckedChange: (checked) => updateCookiePreferences("analytics", checked)
  });
  $$payload.out += `<!----></div> <div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Advertising Cookies</div> <p class="text-muted-foreground text-sm">Used to show you relevant ads on other websites.</p></div> `;
  Switch($$payload, {
    checked: store_get($$store_subs ??= {}, "$formData", formData).cookiePreferences?.advertising ?? false,
    onCheckedChange: (checked) => updateCookiePreferences("advertising", checked)
  });
  $$payload.out += `<!----></div> <div class="mt-4 text-sm text-gray-500 dark:text-gray-400"><p>For more information about how we use cookies, please see our <a href="/legal/cookie-policy" class="text-primary hover:underline">Cookie Policy</a>.</p></div> <div class="mt-6 border-t border-gray-200 pt-6 dark:border-gray-800"><div class="flex items-center justify-between"><div><h4 class="text-base font-medium">Reset Cookie Preferences</h4> <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This will clear all cookie preferences and show the consent banner again.</p></div> <!---->`;
  Button($$payload, {
    variant: "destructive",
    size: "sm",
    onclick: resetCookiePreferences,
    class: "flex items-center gap-2",
    children: ($$payload2) => {
      Cookie($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> <span>Reset Preferences</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Application_preferences($$payload, $$props) {
  push();
  var $$store_subs;
  const { form, formData } = $$props;
  function handleSettingChange(setting, value) {
    formData.update((f) => ({ ...f, [setting]: value }));
    setTimeout(
      () => {
        const submitButton = document.getElementById("submit-button");
        submitButton?.click();
      },
      100
    );
  }
  $$payload.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Application Preferences</h4> <p class="text-muted-foreground text-sm">Configure how the application handles your job applications and resumes.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!---->`;
  Form_field($$payload, {
    form,
    name: "autoParseResumes",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Parse Resumes</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Automatically parse resume data when uploading new resumes`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).autoParseResumes),
            onCheckedChange: (checked) => handleSettingChange("autoParseResumes", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "autoSaveApplications",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Save Applications</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Automatically save job applications as you fill them out`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).autoSaveApplications),
            onCheckedChange: (checked) => handleSettingChange("autoSaveApplications", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "applicationReminders",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Application Reminders</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Receive reminders about pending applications and follow-ups`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).applicationReminders),
            onCheckedChange: (checked) => handleSettingChange("applicationReminders", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Job_search_preferences($$payload, $$props) {
  push();
  var $$store_subs;
  const { form, formData } = $$props;
  const remoteOptions = [
    { value: "remote", label: "Remote Only" },
    { value: "hybrid", label: "Hybrid" },
    { value: "onsite", label: "On-site Only" },
    { value: "flexible", label: "Flexible" }
  ];
  function handleSettingChange(setting, value) {
    formData.update((f) => ({ ...f, [setting]: value }));
    setTimeout(
      () => {
        const submitButton = document.getElementById("submit-button");
        submitButton?.click();
      },
      100
    );
  }
  $$payload.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Job Search Preferences</h4> <p class="text-muted-foreground text-sm">Configure your default job search and application preferences.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!---->`;
  Form_field($$payload, {
    form,
    name: "defaultRemotePreference",
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      {
        let children = function($$payload3, { props }) {
          $$payload3.out += `<div class="space-y-0.5"><!---->`;
          Form_label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Default Remote Preference`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Form_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Your preferred work arrangement for job searches`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> <!---->`;
          Root$1($$payload3, spread_props([
            props,
            {
              type: "single",
              value: store_get($$store_subs ??= {}, "$formData", formData).defaultRemotePreference || "hybrid",
              onValueChange: (value) => handleSettingChange("defaultRemotePreference", value),
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Select_trigger($$payload4, {
                  class: "w-full",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_value($$payload5, { placeholder: "Select remote preference" });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Select_content($$payload4, {
                  class: "max-h-60",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_group($$payload5, {
                      children: ($$payload6) => {
                        const each_array = ensure_array_like(remoteOptions);
                        $$payload6.out += `<!--[-->`;
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let option = each_array[$$index];
                          $$payload6.out += `<!---->`;
                          Select_item($$payload6, {
                            value: option.value,
                            label: option.label,
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->${escape_html(option.label)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!---->`;
                        }
                        $$payload6.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out += `<!---->`;
        };
        Control($$payload2, { children });
      }
      $$payload2.out += `<!----> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "showSalaryInListings",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Show Salary in Listings</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Display salary information when available in job listings`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).showSalaryInListings),
            onCheckedChange: (checked) => handleSettingChange("showSalaryInListings", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "autoApplyEnabled",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Apply Enabled</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Enable automatic job application features (requires premium plan)`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).autoApplyEnabled),
            onCheckedChange: (checked) => handleSettingChange("autoApplyEnabled", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function Resume_preferences($$payload, $$props) {
  push();
  var $$store_subs;
  const { form, formData } = $$props;
  const privacyOptions = [
    { value: "public", label: "Public" },
    { value: "private", label: "Private" }
  ];
  function handleSettingChange(setting, value) {
    formData.update((f) => ({ ...f, [setting]: value }));
    setTimeout(
      () => {
        const submitButton = document.getElementById("submit-button");
        submitButton?.click();
      },
      100
    );
  }
  $$payload.out += `<div class="border-border border-b px-6 py-4"><h4 class="text-md font-normal">Resume Preferences</h4> <p class="text-muted-foreground text-sm">Configure how your resumes are handled and processed.</p></div> <div class="grid gap-6 p-6 sm:grid-cols-2"><!---->`;
  Form_field($$payload, {
    form,
    name: "defaultResumeParsingEnabled",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Default Resume Parsing</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Enable resume parsing by default when uploading new resumes`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).defaultResumeParsingEnabled),
            onCheckedChange: (checked) => handleSettingChange("defaultResumeParsingEnabled", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "autoUpdateProfileFromResume",
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center justify-between"><div class="space-y-0.5"><div class="font-medium">Auto-Update Profile</div> <!---->`;
      Form_description($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->Automatically update your profile with parsed resume data`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Control($$payload2, {
        children: ($$payload3) => {
          Switch($$payload3, {
            checked: Boolean(store_get($$store_subs ??= {}, "$formData", formData).autoUpdateProfileFromResume),
            onCheckedChange: (checked) => handleSettingChange("autoUpdateProfileFromResume", checked)
          });
        }
      });
      $$payload2.out += `<!----></div> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <!---->`;
  Form_field($$payload, {
    form,
    name: "resumePrivacyLevel",
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      {
        let children = function($$payload3, { props }) {
          $$payload3.out += `<div class="space-y-0.5"><!---->`;
          Form_label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Resume Privacy Level`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Form_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Control who can access your resume information`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> <!---->`;
          Root$1($$payload3, spread_props([
            props,
            {
              type: "single",
              value: store_get($$store_subs ??= {}, "$formData", formData).resumePrivacyLevel || "private",
              onValueChange: (value) => handleSettingChange("resumePrivacyLevel", value),
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Select_trigger($$payload4, {
                  class: "w-full",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_value($$payload5, { placeholder: "Select privacy level" });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Select_content($$payload4, {
                  class: "max-h-60",
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->`;
                    Select_group($$payload5, {
                      children: ($$payload6) => {
                        const each_array = ensure_array_like(privacyOptions);
                        $$payload6.out += `<!--[-->`;
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let option = each_array[$$index];
                          $$payload6.out += `<!---->`;
                          Select_item($$payload6, {
                            value: option.value,
                            label: option.label,
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->${escape_html(option.label)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!---->`;
                        }
                        $$payload6.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            }
          ]));
          $$payload3.out += `<!---->`;
        };
        Control($$payload2, { children });
      }
      $$payload2.out += `<!----> <!---->`;
      Form_field_errors($$payload2, {});
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  const tabs = [
    {
      id: "personal",
      label: "Personal",
      icon: User,
      component: Personal
    },
    {
      id: "applications",
      label: "Applications",
      icon: Briefcase,
      component: Application_preferences
    },
    {
      id: "job-search",
      label: "Job Search",
      icon: Briefcase,
      component: Job_search_preferences
    },
    {
      id: "resume",
      label: "Resume",
      icon: File_text,
      component: Resume_preferences
    },
    {
      id: "privacy",
      label: "Privacy",
      icon: Eye,
      component: Privacy
    },
    {
      id: "accessibility",
      label: "Accessibility",
      icon: Settings,
      component: Accessibility
    },
    {
      id: "cookies",
      label: "Cookies",
      icon: Cookie,
      component: Cookie_preferences
    }
  ];
  let activeTab = "personal";
  const form = superForm(data.form, {
    dataType: "json",
    validationMethod: "auto",
    taintedMessage: false,
    // Disable the browser's "unsaved changes" warning
    resetForm: false,
    // Don't reset the form after submission
    applyAction: true,
    // Apply the result from the server action
    // Don't invalidate all fields on error
    // Don't clear the form on submit
    onUpdated: ({ form: form2 }) => {
      console.log("Form updated:", form2.data);
      if (form2.valid) {
        toast.success("Account settings updated successfully");
      }
    },
    onError: ({ result }) => {
      console.error("Form error:", result);
      toast.error(result?.error?.message || "Failed to update account settings");
    }
  });
  const {
    form: formData,
    enhance,
    submitting,
    delayed
  } = form;
  SEO($$payload, {
    title: "Account Settings - Hirli",
    description: "Manage your account settings, personal information, application preferences, job search settings, resume preferences, privacy, and accessibility options.",
    keywords: "account settings, user profile, application preferences, job search, resume settings, privacy settings, accessibility",
    url: "https://hirli.com/dashboard/settings/account"
  });
  $$payload.out += `<!----> <div class="flex flex-col justify-between p-6"><div class="flex items-center justify-between"><div class="flex flex-col"><h2 class="text-lg font-semibold">Account Settings</h2> <p class="text-muted-foreground text-sm">Manage your personal information, application preferences, job search settings, and privacy
        options.</p></div></div></div> <!---->`;
  Root($$payload, {
    value: activeTab,
    onValueChange: (value) => activeTab = value,
    children: ($$payload2) => {
      const each_array_1 = ensure_array_like(tabs);
      $$payload2.out += `<!---->`;
      Tabs_list($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(tabs);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let tab = each_array[$$index];
            $$payload3.out += `<!---->`;
            Tabs_trigger($$payload3, {
              value: tab.id,
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex items-center gap-2"><!---->`;
                tab.icon($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> <span>${escape_html(tab.label)}</span></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <form method="POST" class="space-y-8"><!--[-->`;
      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
        let tab = each_array_1[$$index_1];
        $$payload2.out += `<!---->`;
        Tabs_content($$payload2, {
          value: tab.id,
          class: "p-0",
          children: ($$payload3) => {
            if (tab.id === "personal") {
              $$payload3.out += "<!--[-->";
              Personal($$payload3, { form, formData });
            } else if (tab.id === "applications") {
              $$payload3.out += "<!--[1-->";
              Application_preferences($$payload3, { form, formData });
            } else if (tab.id === "job-search") {
              $$payload3.out += "<!--[2-->";
              Job_search_preferences($$payload3, { form, formData });
            } else if (tab.id === "resume") {
              $$payload3.out += "<!--[3-->";
              Resume_preferences($$payload3, { form, formData });
            } else if (tab.id === "privacy") {
              $$payload3.out += "<!--[4-->";
              Privacy($$payload3, { form, formData });
            } else if (tab.id === "accessibility") {
              $$payload3.out += "<!--[5-->";
              Accessibility($$payload3, { form, formData });
            } else if (tab.id === "cookies") {
              $$payload3.out += "<!--[6-->";
              Cookie_preferences($$payload3, { formData });
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      }
      $$payload2.out += `<!--]--> <button id="submit-button" type="submit" class="hidden" aria-label="Save settings"></button></form>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DrRrpvxY.js.map
