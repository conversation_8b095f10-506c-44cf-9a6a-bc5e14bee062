import { r as redirect } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const load = async ({ locals, url }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "20");
  const type = url.searchParams.get("type") || void 0;
  const includeRead = url.searchParams.get("includeRead") === "true";
  const offset = (page - 1) * limit;
  try {
    const whereClause = {
      OR: [{ userId: user.id }, { global: true }]
    };
    if (!includeRead) {
      whereClause.read = false;
    }
    if (type) {
      whereClause.type = type;
    }
    const notifications = await prisma.Notification.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: limit,
      skip: offset
    });
    const totalCount = await prisma.Notification.count({
      where: whereClause
    });
    const preferences = await prisma.notificationSettings.findUnique({
      where: { userId: user.id }
    });
    const userPreferences = preferences || await prisma.notificationSettings.create({
      data: {
        userId: user.id,
        emailEnabled: true,
        pushEnabled: true,
        browserEnabled: true,
        jobMatchEnabled: true,
        applicationStatusEnabled: true,
        marketingEnabled: false,
        productUpdatesEnabled: true
      }
    });
    const unreadCount = await prisma.Notification.count({
      where: {
        OR: [{ userId: user.id }, { global: true }],
        read: false
      }
    });
    return {
      notifications,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      },
      filters: {
        type,
        includeRead
      },
      preferences: userPreferences,
      unreadCount
    };
  } catch (error) {
    console.error("Error loading notifications:", error);
    return {
      notifications: [],
      pagination: {
        page: 1,
        limit,
        totalCount: 0,
        totalPages: 0
      },
      filters: {
        type,
        includeRead
      },
      preferences: {
        email: true,
        push: true,
        inApp: true,
        jobAlerts: true,
        applicationUpdates: true,
        marketingUpdates: false,
        systemAnnouncements: true
      },
      unreadCount: 0,
      error: "Failed to load notifications"
    };
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 39;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-tCBKNhiQ.js')).default;
const server_id = "src/routes/dashboard/notifications/+page.server.ts";
const imports = ["_app/immutable/nodes/39.BFj--DnS.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/xCOJ4D9d.js","_app/immutable/chunks/Dc4vaUpe.js","_app/immutable/chunks/26EXiO5K.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/BoNCRmBc.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/hA0h0kTo.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/BBNNmnYR.js","_app/immutable/chunks/DkmCSZhC.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/CTO_B1Jk.js","_app/immutable/chunks/CDnvByek.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/scroll-area.bHHIbcsu.css","_app/immutable/assets/index.CV-KWLNP.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=39-1x4DRtHe.js.map
