{"version": 3, "file": "concurrency-limiter.mjs", "sources": ["../src/concurrency-limiter.ts"], "sourcesContent": ["/**\n * ConcurrencyLimiter manages the number of concurrent operations that can be performed.\n * It ensures that the number of operations does not exceed a specified maximum limit.\n */\nexport class ConcurrencyLimiter {\n  current = 0\n  resolvers: Array<() => void> = []\n  constructor(public max: number) {}\n\n  /**\n   * Indicates when a slot for a new operation is ready.\n   * If under the limit, it resolves immediately; otherwise, it waits until a slot is free.\n   */\n  ready = (): Promise<void> => {\n    if (this.max === Infinity) return Promise.resolve()\n\n    if (this.current < this.max) {\n      this.current++\n      return Promise.resolve()\n    }\n\n    return new Promise<void>((resolve) => {\n      this.resolvers.push(resolve)\n    })\n  }\n\n  /**\n   * Releases a slot, decrementing the current count of operations if nothing is in the queue.\n   * If there are operations waiting, it allows the next one in the queue to proceed.\n   */\n  release = (): void => {\n    if (this.max === Infinity) return\n\n    const nextResolver = this.resolvers.shift()\n    if (nextResolver) {\n      nextResolver()\n      return\n    }\n\n    this.current = Math.max(0, this.current - 1)\n  }\n}\n"], "names": [], "mappings": ";AAIO,MAAM,mBAAmB;AAAA,EAG9B,YAAmB,KAAa;AAAb,SAAA,MAAA,KAFT,cAAA,MAAA,WAAA,CAAA,GACV,cAAA,MAAA,aAA+B,EAAC,GAOhC,cAAA,MAAA,SAAQ,MACF,KAAK,QAAQ,QAAiB,QAAQ,YAEtC,KAAK,UAAU,KAAK,OACtB,KAAK,WACE,QAAQ,aAGV,IAAI,QAAc,CAAC,YAAY;AAC/B,WAAA,UAAU,KAAK,OAAO;AAAA,IAAA,CAC5B,CAAA,GAOH,cAAA,MAAA,WAAU,MAAY;AAChB,UAAA,KAAK,QAAQ,MAAU;AAErB,YAAA,eAAe,KAAK,UAAU,MAAM;AAC1C,UAAI,cAAc;AACH,qBAAA;AACb;AAAA,MAAA;AAGF,WAAK,UAAU,KAAK,IAAI,GAAG,KAAK,UAAU,CAAC;AAAA,IAAA,CAC7C;AAAA,EAAA;AACF;"}