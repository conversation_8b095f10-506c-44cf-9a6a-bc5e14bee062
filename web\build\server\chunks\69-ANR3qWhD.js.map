{"version": 3, "file": "69-ANR3qWhD.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/team/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/69.js"], "sourcesContent": ["import { r as redirect, f as fail } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { s as superValidate } from \"../../../../../chunks/superValidate.js\";\nimport \"ts-deepmerge\";\nimport \"memoize-weak\";\nimport { z as zod } from \"../../../../../chunks/zod.js\";\nimport { z } from \"zod\";\nconst inviteSchema = z.object({\n  email: z.string().email(\"Invalid email address\"),\n  role: z.enum([\"member\", \"admin\"]).default(\"member\")\n});\nconst teamSchema = z.object({\n  name: z.string().min(1, \"Team name is required\")\n});\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user || !user.email) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const userData = await prisma.user.findUnique({\n    where: { email: user.email },\n    include: {\n      Team: {\n        include: {\n          members: {\n            include: {\n              user: true\n            }\n          }\n        }\n      },\n      TeamMember: {\n        include: {\n          team: {\n            include: {\n              owner: true,\n              members: {\n                include: {\n                  user: true\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  });\n  if (!userData) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  locals.user = userData;\n  const ownedTeams = userData.Team || [];\n  const memberTeams = userData.TeamMember?.map((tm) => tm.team) || [];\n  const allTeams = [...ownedTeams, ...memberTeams];\n  const uniqueTeams = Array.from(new Map(allTeams.map((team) => [team.id, team])).values());\n  const inviteForm = await superValidate(zod(inviteSchema));\n  const teamForm = await superValidate(zod(teamSchema));\n  return {\n    user: userData,\n    teams: uniqueTeams,\n    inviteForm,\n    teamForm\n  };\n};\nconst actions = {\n  invite: async ({ request, cookies, locals }) => {\n    const tokenData = getUserFromToken(cookies);\n    if (!tokenData || !tokenData.email) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const userData = await prisma.user.findUnique({\n      where: { email: tokenData.email }\n    });\n    if (!userData) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const form = await superValidate(request, zod(inviteSchema));\n    if (!form.valid) {\n      return fail(400, { inviteForm: form });\n    }\n    try {\n      return { inviteForm: form, success: true };\n    } catch (error) {\n      console.error(\"Error inviting team member:\", error);\n      return fail(500, { inviteForm: form, error: \"Failed to invite team member\" });\n    }\n  },\n  createTeam: async ({ request, cookies, locals }) => {\n    const tokenData = getUserFromToken(cookies);\n    if (!tokenData || !tokenData.email) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const userData = await prisma.user.findUnique({\n      where: { email: tokenData.email }\n    });\n    if (!userData) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const form = await superValidate(request, zod(teamSchema));\n    if (!form.valid) {\n      return fail(400, { teamForm: form });\n    }\n    try {\n      const newTeam = await prisma.team.create({\n        data: {\n          name: form.data.name,\n          ownerId: userData.id\n        }\n      });\n      return { teamForm: form, success: true, team: newTeam };\n    } catch (error) {\n      console.error(\"Error creating team:\", error);\n      return fail(500, { teamForm: form, error: \"Failed to create team\" });\n    }\n  },\n  removeTeamMember: async ({ request, cookies, locals }) => {\n    const tokenData = getUserFromToken(cookies);\n    if (!tokenData || !tokenData.email) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const userData = await prisma.user.findUnique({\n      where: { email: tokenData.email }\n    });\n    if (!userData) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const data = await request.formData();\n    const teamId = data.get(\"teamId\")?.toString();\n    const memberId = data.get(\"memberId\")?.toString();\n    if (!teamId || !memberId) {\n      return fail(400, { error: \"Missing required fields\" });\n    }\n    try {\n      const team = await prisma.team.findUnique({\n        where: { id: teamId }\n      });\n      if (!team || team.ownerId !== userData.id) {\n        return fail(403, { error: \"You do not have permission to remove team members\" });\n      }\n      await prisma.teamMember.delete({\n        where: {\n          id: memberId\n        }\n      });\n      return { success: true };\n    } catch (error) {\n      console.error(\"Error removing team member:\", error);\n      return fail(500, { error: \"Failed to remove team member\" });\n    }\n  }\n};\nexport {\n  actions,\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/team/_page.server.ts.js';\n\nexport const index = 69;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/team/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/team/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/69.txFTyHUb.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/DDUgF6Ik.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DgB2v2Jz.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/CWmzcjye.js\",\"_app/immutable/chunks/cbK_x0lf.js\",\"_app/immutable/chunks/DzJNq86D.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/BhJFaoL-.js\",\"_app/immutable/chunks/CrHU05dq.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/FeejBSkx.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CE9Bts7j.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BSHZ37s_.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/CzSntoiK.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/DmZyh-PW.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/CXvW3J0s.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/yPulTJ2h.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/C8B1VUaq.js\"];\nexport const stylesheets = [\"_app/immutable/assets/index.CV-KWLNP.css\",\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": ["z.object", "z.string", "z.enum"], "mappings": ";;;;;;;;;;;;;AAQA,MAAM,YAAY,GAAGA,UAAQ,CAAC;AAC9B,EAAE,KAAK,EAAEC,UAAQ,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC;AAClD,EAAE,IAAI,EAAEC,QAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ;AACpD,CAAC,CAAC;AACF,MAAM,UAAU,GAAGF,UAAQ,CAAC;AAC5B,EAAE,IAAI,EAAEC,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB;AACjD,CAAC,CAAC;AACF,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AAC5B,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;AAChC,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE;AACZ,QAAQ,OAAO,EAAE;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE;AACpB;AACA;AACA;AACA,OAAO;AACP,MAAM,UAAU,EAAE;AAClB,QAAQ,OAAO,EAAE;AACjB,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB,cAAc,KAAK,EAAE,IAAI;AACzB,cAAc,OAAO,EAAE;AACvB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,IAAI,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,CAAC,IAAI,GAAG,QAAQ;AACxB,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE;AACxC,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;AACrE,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,WAAW,CAAC;AAClD,EAAE,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC3F,EAAE,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC3D,EAAE,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACvD,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,KAAK,EAAE,WAAW;AACtB,IAAI,UAAU;AACd,IAAI;AACJ,GAAG;AACH,CAAC;AACD,MAAM,OAAO,GAAG;AAChB,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAClD,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACxC,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK;AACrC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AAC5C;AACA,IAAI,IAAI;AACR,MAAM,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;AAChD,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC;AACnF;AACA,GAAG;AACH,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACtD,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACxC,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK;AACrC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC1C;AACA,IAAI,IAAI;AACR,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC9B,UAAU,OAAO,EAAE,QAAQ,CAAC;AAC5B;AACA,OAAO,CAAC;AACR,MAAM,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;AAC7D,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAClD,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AAC1E;AACA,GAAG;AACH,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5D,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACxC,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK;AACrC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE;AACzC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE;AACjD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE;AACrD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;AAC9B,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;AAC5D;AACA,IAAI,IAAI;AACR,MAAM,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM;AAC3B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC,EAAE,EAAE;AACjD,QAAQ,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,mDAAmD,EAAE,CAAC;AACxF;AACA,MAAM,MAAM,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;AACrC,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE;AACd;AACA,OAAO,CAAC;AACR,MAAM,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE;AAC9B,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC;AACjE;AACA;AACA,CAAC;;;;;;;;ACrJW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA0D,CAAC,EAAE;AAExH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACjlF,MAAC,WAAW,GAAG,CAAC,0CAA0C,CAAC,4CAA4C;AACvG,MAAC,KAAK,GAAG;;;;"}