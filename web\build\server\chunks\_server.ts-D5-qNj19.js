import { p as prisma } from './prisma-Cit_HrSw.js';
import { R as RedisConnection } from './redis-DxlM1ibh.js';
import uploadDocumentToR2 from './r2DocumentUpload-B4r00eVy.js';
import { e as ensureUniqueDocumentName } from './documentNameUniqueness-BVI8kP6m.js';
import { d as determineDocumentSource } from './documentSource-DXqQzUYc.js';
import { c as canCreateResume, a as trackResumeCreation } from './resume-usage-B98ib_h-.js';
import crypto__default from 'crypto';
import '@prisma/client';
import 'ioredis';
import '@aws-sdk/client-s3';
import './feature-usage-SYWaZZiX.js';
import './dynamic-registry-Cmy1Wm2Q.js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './features-SWeUHekJ.js';

const rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate

let poolPtr = rnds8Pool.length;
function rng() {
  if (poolPtr > rnds8Pool.length - 16) {
    crypto__default.randomFillSync(rnds8Pool);
    poolPtr = 0;
  }

  return rnds8Pool.slice(poolPtr, poolPtr += 16);
}

/**
 * Convert array of 16 byte values to UUID string format of the form:
 * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
 */

const byteToHex = [];

for (let i = 0; i < 256; ++i) {
  byteToHex.push((i + 0x100).toString(16).slice(1));
}

function unsafeStringify(arr, offset = 0) {
  // Note: Be careful editing this code!  It's been tuned for performance
  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434
  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];
}

var native = {
  randomUUID: crypto__default.randomUUID
};

function v4(options, buf, offset) {
  if (native.randomUUID && true && !options) {
    return native.randomUUID();
  }

  options = options || {};
  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`

  rnds[6] = rnds[6] & 0x0f | 0x40;
  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided

  return unsafeStringify(rnds);
}

const JOB_STREAM = "resume-parsing::stream";
const JOB_GROUP = "resume-parsing::group";
function getMessage(skipParsing, parseIntoProfile) {
  if (skipParsing) {
    return "Resume uploaded. It was already parsed previously.";
  }
  if (parseIntoProfile) {
    return "Resume uploaded and parsing started. Profile will be updated with resume data.";
  }
  return "Resume uploaded successfully.";
}
const POST = async ({ request, locals }) => {
  const user = locals.user;
  console.log("User in resume upload:", user);
  if (!user) return new Response("Unauthorized", { status: 401 });
  if (!user.id) {
    console.error("User missing ID:", user);
    return new Response("User ID missing", { status: 400 });
  }
  const formData = await request.formData();
  const file = formData.get("file");
  const profileId = formData.get("profileId");
  let label = formData.get("label") || file.name;
  const documentType = formData.get("type") || "resume";
  const parseIntoProfileStr = formData.get("parseIntoProfile");
  const parseIntoProfile = parseIntoProfileStr === "true";
  if (!file) {
    return new Response("Missing file", { status: 400 });
  }
  if (profileId) {
    const profile = await prisma.profile.findUnique({ where: { id: profileId } });
    if (!profile) {
      return new Response("Invalid profileId", { status: 404 });
    }
  }
  label = await ensureUniqueDocumentName(label, user.id, documentType);
  try {
    const isDev = process.env.NODE_ENV === "development" || process.env.VITE_DISABLE_FEATURE_LIMITS === "true";
    if (!isDev) {
      const canCreate = await canCreateResume(user.id);
      if (!canCreate) {
        return new Response(
          JSON.stringify({
            error: "Document limit reached",
            limitReached: true,
            message: "You have reached your document upload limit. Please upgrade your plan to upload more documents."
          }),
          {
            status: 403,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
    } else {
      console.log("Development mode: Bypassing document limit check in resume upload API");
    }
    console.log("Uploading resume:", {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      documentType
    });
    const identifier = profileId || user.id;
    const uploadResult = await uploadDocumentToR2(file, "resume", identifier);
    console.log("R2 upload result:", uploadResult);
    if (!uploadResult.success) {
      return new Response(
        JSON.stringify({
          error: "File upload failed",
          details: uploadResult.error
        }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
    console.log("Creating document with data:", {
      label,
      fileUrl: uploadResult.publicPath,
      userId: user.id,
      profileId: profileId || null,
      type: documentType
    });
    console.log("User ID:", user.id);
    const document = await prisma.document.create({
      data: {
        label,
        fileUrl: uploadResult.publicPath,
        filePath: uploadResult.filePath,
        fileName: uploadResult.originalFileName,
        type: documentType,
        contentType: uploadResult.contentType,
        fileSize: uploadResult.fileSize,
        storageType: "r2",
        storageLocation: "resumes",
        // Note: 'source' field is not in the Prisma schema, so we can't set it here
        userId: user.id,
        ...profileId ? { profileId } : {}
      }
    });
    console.log("Document created:", document);
    const resume = await prisma.resume.create({
      data: {
        documentId: document.id
      },
      include: {
        document: true
      }
    });
    console.log("Resume created:", resume);
    try {
      const { updateResumeFileKey } = await import('./r2DocumentUpload-B4r00eVy.js');
      const updateResult = await updateResumeFileKey(uploadResult.filePath, resume.id);
      if (updateResult.success) {
        await prisma.document.update({
          where: { id: document.id },
          data: {
            filePath: updateResult.newFileKey,
            fileUrl: updateResult.newPublicUrl
          }
        });
        console.log(`Updated resume file key to: ${updateResult.newFileKey}`);
      } else {
        console.warn(`Failed to update resume file key: ${updateResult.error}`);
      }
    } catch (error) {
      console.warn("Error updating resume file key:", error);
    }
    await trackResumeCreation(user.id);
    let skipParsing = false;
    if (parseIntoProfile) {
      const existingResume = await prisma.resume.findUnique({
        where: { id: resume.id },
        select: { isParsed: true, parsedAt: true, parsedData: true }
      });
      console.log("Existing resume parse status:", existingResume);
      if (existingResume?.isParsed && existingResume?.parsedData) {
        console.log("Resume is already parsed, skipping parsing process");
        skipParsing = true;
      }
      if (!skipParsing) {
        const jobId = v4();
        try {
          const jobData = {
            jobId,
            resumeId: resume.id,
            filePath: uploadResult.filePath,
            // Use the filePath directly from the upload result
            fileUrl: uploadResult.publicPath,
            userId: user.id,
            profileId: profileId || "",
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          };
          try {
            await RedisConnection.xgroup("CREATE", JOB_STREAM, JOB_GROUP, "$", "MKSTREAM");
            console.log(`Created ${JOB_STREAM} stream group`);
          } catch (err) {
            if (!err.message.includes("BUSYGROUP")) {
              console.error("Error creating stream group:", err);
            }
          }
          await RedisConnection.xadd(
            JOB_STREAM,
            "*",
            // Use '*' for auto-generated ID
            "job",
            JSON.stringify(jobData)
          );
          console.log(`Parsing job added to stream '${JOB_STREAM}':`, jobId);
          await prisma.resume.update({
            where: { id: resume.id },
            data: {
              isParsed: false,
              parsedAt: null
            }
          });
          console.log("Resume updated to reset parse status");
        } catch (redisError) {
          console.error("Error adding job to Redis queue:", redisError);
        }
      }
    } else {
      console.log("Skipping parsing as parseIntoProfile is false");
    }
    const source = determineDocumentSource(document);
    const responseData = {
      resume,
      document: {
        ...document,
        source
        // Add the determined source information for the frontend
      },
      parseIntoProfile,
      // Include the parseIntoProfile flag
      profileId: profileId || null,
      // Include the profileId
      alreadyParsed: skipParsing,
      // Include whether the resume was already parsed
      message: getMessage(skipParsing, parseIntoProfile)
    };
    return new Response(JSON.stringify(responseData), {
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error("Error creating resume:", error);
    if (error?.message?.includes("File type")) {
      return new Response(
        JSON.stringify({
          error: "Invalid file type",
          details: error.message
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    return new Response(
      JSON.stringify({
        error: "Failed to create resume",
        details: error.message
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

export { POST };
//# sourceMappingURL=_server.ts-D5-qNj19.js.map
