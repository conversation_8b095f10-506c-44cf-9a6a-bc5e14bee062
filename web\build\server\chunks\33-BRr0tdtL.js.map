{"version": 3, "file": "33-BRr0tdtL.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/documents/_id_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/33.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { d as determineDocumentSource } from \"../../../../../chunks/documentSource.js\";\nimport { e as error } from \"../../../../../chunks/index.js\";\nconst load = async ({ params, locals }) => {\n  const { id } = params;\n  if (!locals.user) {\n    throw error(401, \"Unauthorized\");\n  }\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id },\n      include: {\n        resume: true\n      }\n    });\n    if (!document || document.userId !== locals.user.id) {\n      throw error(404, \"Document not found\");\n    }\n    const source = determineDocumentSource(document);\n    return {\n      id,\n      document: {\n        ...document,\n        source\n      }\n    };\n  } catch (e) {\n    console.error(\"Error loading document:\", e);\n    throw error(500, \"Failed to load document\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/documents/_id_/_page.server.ts.js';\n\nexport const index = 33;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/documents/_id_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/documents/[id]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/33.D3gJJ81M.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DigM95rE.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/DETxXRrJ.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/BnikQ10_.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/CSGDlQPw.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/BPvdPoic.js\",\"_app/immutable/chunks/zNKWipEG.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/BIUPxhhl.js\",\"_app/immutable/chunks/CTQ8y7hr.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;AACpC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;AACzD,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC;AAC5C;AACA,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC;AACpD,IAAI,OAAO;AACX,MAAM,EAAE;AACR,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,QAAQ;AACnB,QAAQ;AACR;AACA,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC,CAAC;AAC/C,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,yBAAyB,CAAC;AAC/C;AACA,CAAC;;;;;;;AC5BW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA2D,CAAC,EAAE;AAEzH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7+D,MAAC,WAAW,GAAG,CAAC,4CAA4C;AAC5D,MAAC,KAAK,GAAG;;;;"}