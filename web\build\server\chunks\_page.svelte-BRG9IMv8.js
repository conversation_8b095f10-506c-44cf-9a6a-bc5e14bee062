import 'clsx';
import { p as push, V as copy_payload, W as assign_payload, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import './false-CRHihH2U.js';

function _page($$payload, $$props) {
  push();
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, { title: "Admin Settings - Hirli" });
    $$payload2.out += `<!----> `;
    {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BRG9IMv8.js.map
