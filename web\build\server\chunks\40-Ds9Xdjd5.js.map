{"version": 3, "file": "40-Ds9Xdjd5.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/resumes/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/40.js"], "sourcesContent": ["import { r as redirect } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport \"../../../../chunks/auth.js\";\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const resumes = await prisma.resume.findMany({\n    where: {\n      OR: [\n        {\n          userId: user.id\n          // Directly fetch resumes associated with the user's ID\n        },\n        {\n          profile: {\n            team: {\n              members: {\n                some: {\n                  userId: user.id\n                  // Fetch resumes associated with a team's profiles\n                }\n              }\n            }\n          }\n        }\n      ]\n    },\n    include: {\n      document: true,\n      // Include the document to get label, fileUrl, etc.\n      profile: {\n        include: {\n          jobSearches: true\n        }\n      }\n    },\n    orderBy: {\n      createdAt: \"desc\"\n    }\n  });\n  const profiles = await prisma.profile.findMany({\n    where: {\n      OR: [\n        { userId: user.id },\n        // Fetch profiles directly associated with the user\n        {\n          team: {\n            members: {\n              some: { userId: user.id }\n              // Fetch profiles associated with teams the user is a member of\n            }\n          }\n        }\n      ]\n    }\n  });\n  return {\n    resumes,\n    profiles\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/resumes/_page.server.ts.js';\n\nexport const index = 40;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/resumes/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/resumes/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/40.CMrHxvZb.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/CTn0v-X8.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/CodWuqwu.js\",\"_app/immutable/chunks/CQeqUgF6.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/DigM95rE.js\",\"_app/immutable/chunks/BMZasLyv.js\",\"_app/immutable/chunks/iTBjRg9v.js\",\"_app/immutable/chunks/YNp1uWxB.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/CTO_B1Jk.js\",\"_app/immutable/chunks/DHNQRrgO.js\",\"_app/immutable/chunks/bK-q0z-2.js\",\"_app/immutable/chunks/FAbXdqfL.js\"];\nexport const stylesheets = [\"_app/immutable/assets/index.CV-KWLNP.css\",\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC/C,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE;AACV,QAAQ;AACR,UAAU,MAAM,EAAE,IAAI,CAAC;AACvB;AACA,SAAS;AACT,QAAQ;AACR,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,MAAM,EAAE,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,QAAQ,EAAE,IAAI;AACpB;AACA,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,WAAW,EAAE;AACvB;AACA;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC3B;AACA,QAAQ;AACR,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI;AACJ,GAAG;AACH,CAAC;;;;;;;AC5DW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAoD,CAAC,EAAE;AAElH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACj8E,MAAC,WAAW,GAAG,CAAC,0CAA0C,CAAC,4CAA4C;AACvG,MAAC,KAAK,GAAG;;;;"}