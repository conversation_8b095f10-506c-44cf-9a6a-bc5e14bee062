import { r as redirect } from './index-Ddp2AB5f.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { r as resumeFormSchema, d as designFormSchema, a as designDefaultValues } from './buildResume-ByCXwpI3.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';
import './_commonjsHelpers-BFTU3MAI.js';

const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) throw redirect(302, "/auth/sign-in");
  const design = await superValidate(designDefaultValues, zod(designFormSchema));
  const resume = await superValidate({}, zod(resumeFormSchema));
  return { form: { resume, design }, user };
};
const actions = {
  default: async (event) => {
    const resume = await superValidate(event, zod(resumeFormSchema));
    const design = await superValidate(event, zod(designFormSchema));
    if (!resume.valid || !design.valid) {
      return {
        status: 400,
        body: { form: { resume, design } }
      };
    }
    return { form: { resume, design } };
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 29;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DVMM9ZLn.js')).default;
const server_id = "src/routes/dashboard/builder/+page.server.ts";
const imports = ["_app/immutable/nodes/29.Cr1xS1qG.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/BAWahpTV.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/CPe_16wQ.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/C3y1xd2Y.js","_app/immutable/chunks/BoNCRmBc.js","_app/immutable/chunks/DRGimm5x.js","_app/immutable/chunks/CrpvsheG.js","_app/immutable/chunks/lZwfPN85.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/CTX2GAsz.js","_app/immutable/chunks/Dd0-sqM0.js","_app/immutable/chunks/CrHU05dq.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/7AwcL9ec.js","_app/immutable/chunks/6UJoWgvL.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/Dc4vaUpe.js","_app/immutable/chunks/DKxpkEzC.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/0ykhD7u6.js","_app/immutable/chunks/FeejBSkx.js","_app/immutable/chunks/C8B1VUaq.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/scroll-area.bHHIbcsu.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=29-CqrK8Xa-.js.map
