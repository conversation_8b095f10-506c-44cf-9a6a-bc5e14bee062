import {type TransactionLogMutation} from '../transactionLog'
import {
  type CreateIfNotExistsMutation,
  type CreateMutation,
  type CreateOrReplaceMutation,
  type DeleteMutation,
  type Mutation,
  type PatchMutation,
} from './types'

/** @internal */
export function isCreateMutation(
  mutation: Mutation | TransactionLogMutation,
): mutation is CreateMutation {
  return 'create' in mutation
}

/** @internal */
export function isCreateIfNotExistsMutation(
  mutation: Mutation | TransactionLogMutation,
): mutation is CreateIfNotExistsMutation {
  return 'createIfNotExists' in mutation
}

/** @internal */
export function isCreateOrReplaceMutation(
  mutation: Mutation | TransactionLogMutation,
): mutation is CreateOrReplaceMutation {
  return 'createOrReplace' in mutation
}

/** @internal */
export function isDeleteMutation(
  mutation: Mutation | TransactionLogMutation,
): mutation is DeleteMutation {
  return 'delete' in mutation
}

/** @internal */
export function isPatchMutation(
  mutation: Mutation | TransactionLogMutation,
): mutation is PatchMutation {
  return 'patch' in mutation
}
