import 'clsx';
import { p as push, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { g as goto } from './client-dNyMPa8V.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { J as JobSearch, a as JobFeed } from './JobFeed-lICPS7Rb.js';
import { d as debounce } from './utils-pWl1tgmi.js';
import './false-CRHihH2U.js';
import './index2-Cut0V_vU.js';
import './input-DF0gPqYN.js';
import './index12-H6t3LX3-.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './multi-combobox-BJ-pW9qf.js';
import './index14-C2WSwUih.js';
import './button-CrucCo1G.js';
import './index-DjwFQdT_.js';
import 'tailwind-merge';
import './dropdown-store-B4Dfz2ZI.js';
import './clone-BRGVxGEr.js';
import './check-WP_4Msti.js';
import './Icon-A4vzmk-O.js';
import './chevron-down-xGjWLrZH.js';
import './search-input-CbGkN9s9.js';
import './search-B0oHlTPS.js';
import './index4-HpJcNJHQ.js';
import './index10-F28UXWIO.js';
import './dialog-overlay-CspOQRJq.js';
import './index7-BURUpWjT.js';
import './x-DwZgpWRG.js';
import './dialog-description2-rfr-pd9k.js';
import './switch-CwRjBz3R.js';
import './select-value-nUrqCsCq.js';
import './select-group-Cxqg41Dj.js';
import './sliders-vertical-or4TQDCk.js';
import './sheet-footer-B80ycEhL.js';
import './dialog-description-CxPAHL_4.js';
import './badge-C9pSznab.js';
import './bookmark-DazMkrfp.js';
import './map-pin-BBU2RKZV.js';
import './dollar-sign-CXBwKToB.js';
import './briefcase-DBFF7i-g.js';
import './clock-BHOPwoCS.js';
import './share-2-ihgFYKw2.js';
import './flag-CSTMD-YC.js';
import './sparkles-E4-thk3U.js';
import './html-FW6Ia4bL.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './loader-circle-BG7dEt2I.js';
import 'date-fns';

async function searchJobs(params) {
  try {
    if (!params.title && !params.location && !params.companies?.length) {
      throw new Error("At least one search parameter is required");
    }
    const response = await fetch("/api/jobs/search", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(params)
    });
    const data = await response.json();
    if (!response.ok) {
      console.error("Job search error:", data.error);
      return {
        results: [],
        error: data.error || "Failed to search jobs",
        limitReached: data.limitReached || false
      };
    }
    return {
      searchId: data.searchId || null,
      results: data.results || [],
      saved: data.saved || false
    };
  } catch (error) {
    console.error("Error searching jobs:", error);
    return {
      results: [],
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}
function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  let isSearching = false;
  let jobs = [];
  let isLoading = false;
  let selectedJob = null;
  let totalJobCount = 0;
  let savedJobs = [];
  let appliedJobs = [];
  let searchParams = {
    ...data.searchParams,
    // Convert location string to locations array if needed
    locations: data.searchParams?.location ? [data.searchParams.location] : [],
    // Ensure these arrays exist
    locationType: data.searchParams?.locationType || [],
    experience: data.searchParams?.experience || [],
    category: [],
    education: []
  };
  async function handleSearch(params) {
    Object.keys(params).forEach((key) => {
      searchParams[key] = params[key];
    });
    if (!searchParams.title) searchParams.title = "";
    if (!searchParams.locations) searchParams.locations = [];
    if (!searchParams.locationType) searchParams.locationType = [];
    if (!searchParams.experience) searchParams.experience = [];
    if (!searchParams.salary) searchParams.salary = "";
    isSearching = true;
    try {
      debouncedUpdateUrl(searchParams);
      if (!params.saveSearch || !params.title || !data.user) {
        return;
      }
      let location = null;
      if (params.locations?.length > 0) {
        try {
          const loc = params.locations[0];
          if (loc.includes("|")) {
            const [_, name, stateCode] = loc.split("|");
            location = `${name}, ${stateCode}`;
          } else {
            location = loc;
          }
        } catch (error) {
          console.error("Error extracting location:", error);
        }
      }
      const userId = data.user.id;
      if (!userId) return;
      if (params.saveSearch) {
        try {
          const searchParams2 = {
            title: params.title,
            location,
            locationType: params.locationType || [],
            experience: params.experience || [],
            category: params.category || [],
            education: params.education || [],
            salary: params.salary || "",
            saveSearch: true
            // Explicitly set to true since we're in this block
          };
          const result = await searchJobs(searchParams2);
          console.log("Search completed successfully");
          if (result.error) {
            if (result.limitReached) {
              toast.error("You have reached your search limit");
              console.warn("Search limit reached");
            } else {
              console.warn("Failed to perform search:", result.error);
            }
            return;
          }
          if (result.saved && result.searchId) {
            toast.success("Search saved as job alert", {
              description: "View your saved search",
              action: {
                label: "View",
                onClick: () => goto(`/dashboard/jobs/${result.searchId}`)
              }
            });
          }
        } catch (error) {
          console.warn("Error performing search:", error);
        }
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error("Failed to complete search", { description: "Please try again later" });
    } finally {
      isSearching = false;
    }
  }
  function updateUrlFromParams(params) {
    return;
  }
  async function loadMore() {
    return [];
  }
  async function handleApply(job) {
    if (appliedJobs.includes(job.id)) {
      goto();
      return;
    }
    try {
      const response = await fetch(`/api/jobs/${job.id}/apply`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({})
      });
      if (!response.ok) {
        const data2 = await response.json();
        throw new Error(data2.error || "Failed to apply to job");
      }
      appliedJobs = [...appliedJobs, job.id];
      toast.success("Application started", {
        description: "Tracking this application in your dashboard"
      });
      setTimeout(
        () => {
          goto("/dashboard/tracker");
        },
        1500
      );
    } catch (error) {
      console.error("Error applying to job:", error);
      toast.error("Failed to apply to job");
    }
  }
  async function handleSave(job) {
    if (!data.user) {
      window.location.href = "/auth/sign-in";
      return;
    }
    try {
      const response = await fetch(`/api/jobs/${job.id}/save`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notes: "" })
      });
      if (!response.ok) {
        const data2 = await response.json();
        throw new Error(data2.error || "Failed to save job");
      }
      if (!savedJobs.includes(job.id)) {
        savedJobs = [...savedJobs, job.id];
      }
      toast.success("Job saved", { description: "Added to your saved jobs" });
    } catch (error) {
      console.error("Error saving job:", error);
      toast.error("Failed to save job");
    }
  }
  function handleJobSelect(job) {
    selectedJob = job;
  }
  let isUrlUpdateInProgress = false;
  const debouncedUpdateUrl = debounce(
    (params) => {
      if (!isUrlUpdateInProgress) {
        isUrlUpdateInProgress = true;
        console.log("Debounced URL update triggered");
        try {
          updateUrlFromParams(params);
        } finally {
          isUrlUpdateInProgress = false;
        }
      } else {
        console.log("Skipping debounced URL update - another update is in progress");
      }
    },
    800
  );
  SEO($$payload, {
    title: "Job Search | Hirli",
    description: "Search for jobs that match your profile and experience. Track your job applications and get insights on your job search progress.",
    keywords: "job search, job applications, job tracking, career search, job matching, application tracking"
  });
  $$payload.out += `<!----> `;
  JobSearch($$payload, {
    onSearch: handleSearch,
    isSearching,
    initialParams: searchParams,
    user: data.user
  });
  $$payload.out += `<!----> `;
  JobFeed($$payload, {
    jobs,
    isAuthenticated: true,
    isLoading,
    onLoadMore: loadMore,
    onApply: handleApply,
    onSave: handleSave,
    selectedJob,
    onSelectJob: handleJobSelect,
    searchParams,
    totalJobCount,
    savedJobs,
    appliedJobs
  });
  $$payload.out += `<!---->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DwSHDNEE.js.map
