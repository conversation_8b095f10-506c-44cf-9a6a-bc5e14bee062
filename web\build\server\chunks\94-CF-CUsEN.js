const load = async () => {
  return {};
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 94;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BXtwj-Zm.js')).default;
const server_id = "src/routes/studio/+page.server.ts";
const imports = ["_app/immutable/nodes/94.BXegiwqe.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Dx8hstp8.js"];
const stylesheets = ["_app/immutable/assets/94.DmtFXJ4w.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=94-CF-CUsEN.js.map
