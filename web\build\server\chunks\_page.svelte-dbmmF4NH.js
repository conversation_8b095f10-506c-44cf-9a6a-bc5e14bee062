import 'clsx';
import { p as push, q as pop } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { g as goto } from './client-dNyMPa8V.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './index2-Cut0V_vU.js';

function _page($$payload, $$props) {
  push();
  let isLoading = false;
  async function seedServiceFeatures() {
    isLoading = true;
    try {
      toast.loading("Seeding service features...");
      const response = await fetch("/api/admin/features/seed-service", { method: "POST" });
      const result = await response.json();
      if (result.success) {
        toast.dismiss();
        toast.success("Service features seeded successfully!");
        console.log("Seed results:", result);
      } else {
        toast.dismiss();
        toast.error(`Failed to seed service features: ${result.error}`);
      }
    } catch (error) {
      console.error("Error seeding service features:", error);
      toast.dismiss();
      toast.error(`Error seeding service features: ${error.message}`);
    } finally {
      isLoading = false;
    }
  }
  async function seedAllFeatures() {
    isLoading = true;
    try {
      toast.loading("Seeding all features...");
      const response = await fetch("/api/admin/features/seed-all", { method: "POST" });
      const result = await response.json();
      if (result.success) {
        toast.dismiss();
        toast.success("All features seeded successfully!");
        console.log("Seed results:", result);
      } else {
        toast.dismiss();
        toast.error(`Failed to seed all features: ${result.error}`);
      }
    } catch (error) {
      console.error("Error seeding all features:", error);
      toast.dismiss();
      toast.error(`Error seeding all features: ${error.message}`);
    } finally {
      isLoading = false;
    }
  }
  async function seedAnalysisFeatures() {
    isLoading = true;
    try {
      toast.loading("Seeding analysis features...");
      const response = await fetch("/api/admin/features/seed-analysis", { method: "POST" });
      const result = await response.json();
      if (result.success) {
        toast.dismiss();
        toast.success("Analysis features seeded successfully!");
        console.log("Seed results:", result);
      } else {
        toast.dismiss();
        toast.error(`Failed to seed analysis features: ${result.error}`);
      }
    } catch (error) {
      console.error("Error seeding analysis features:", error);
      toast.dismiss();
      toast.error(`Error seeding analysis features: ${error.message}`);
    } finally {
      isLoading = false;
    }
  }
  $$payload.out += `<div class="container mx-auto p-6"><div class="mb-6 flex items-center justify-between"><h1 class="text-2xl font-bold">Seed Features</h1> `;
  Button($$payload, {
    variant: "outline",
    onclick: () => goto(),
    children: ($$payload2) => {
      $$payload2.out += `<!---->Back to Admin`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">Service Features</h2> <p class="text-muted-foreground mb-4 text-sm">Seed service features including document storage with storage limits.</p> `;
  Button($$payload, {
    onclick: seedServiceFeatures,
    disabled: isLoading,
    class: "w-full",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Seed Service Features`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">All Features</h2> <p class="text-muted-foreground mb-4 text-sm">Seed all features including core, resume, job search, and application features.</p> `;
  Button($$payload, {
    onclick: seedAllFeatures,
    disabled: isLoading,
    class: "w-full",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Seed All Features`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="rounded-lg border p-6 shadow-sm"><h2 class="mb-4 text-xl font-semibold">Analysis Features</h2> <p class="text-muted-foreground mb-4 text-sm">Seed analysis features for job market insights and resume analysis.</p> `;
  Button($$payload, {
    onclick: seedAnalysisFeatures,
    disabled: isLoading,
    class: "w-full",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Seed Analysis Features`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-dbmmF4NH.js.map
