import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, aa as maybe_selected, O as escape_html, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { T as Table, a as Table_header, c as Table_row, d as Table_head, b as Table_body, e as Table_cell } from './table-row-CyhLzMgE.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { C as Checkbox } from './checkbox-Bu-4wGff.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { S as SEO } from './SEO-UItXytUy.js';
import { A as Avatar, a as Avatar_image, b as Avatar_fallback } from './avatar-fallback-B2wWy5ce.js';
import { B as Badge } from './badge-C9pSznab.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { S as Send } from './send-BkGWvFu2.js';
import { S as Search } from './search-B0oHlTPS.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { B as Bell } from './bell-C9_YgkSj.js';
import { I as Info } from './info-Ce09B-Yv.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './hidden-input-1eDzjGOB.js';
import './clone-BRGVxGEr.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './index2-Cut0V_vU.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let filteredUsers;
  let data = $$props["data"];
  const { users, currentUser } = data;
  let title = "";
  let message = "";
  let url = "";
  let type = "info";
  let global = false;
  let loading = false;
  let selectedUsers = [];
  let searchQuery = "";
  let sentNotifications = [];
  let loadingSentNotifications = false;
  let activeTab = "send";
  let testLogs = [];
  function toggleUserSelection(userId) {
    if (selectedUsers.includes(userId)) {
      selectedUsers = selectedUsers.filter((id) => id !== userId);
    } else {
      selectedUsers = [...selectedUsers, userId];
    }
  }
  function selectAllUsers() {
    if (selectedUsers.length === filteredUsers.length) {
      selectedUsers = [];
    } else {
      selectedUsers = filteredUsers.map((user) => user.id);
    }
  }
  async function sendNotification() {
    if (!title || !message) {
      toast.error("Title and message are required");
      return;
    }
    if (!global && selectedUsers.length === 0) {
      toast.error("Please select at least one user or enable global notification");
      return;
    }
    loading = true;
    try {
      if (global) {
        const response = await fetch("/api/notifications/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            title,
            message,
            url: url || void 0,
            type,
            global: true
          })
        });
        const data2 = await response.json();
        if (response.ok) {
          toast.success("Global notification sent successfully");
          resetForm();
        } else {
          toast.error(data2.error || "Failed to send notification");
        }
      } else {
        const promises = selectedUsers.map((userId) => fetch("/api/notifications/send", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            title,
            message,
            url: url || void 0,
            type,
            userId
          })
        }));
        const results = await Promise.allSettled(promises);
        const successCount = results.filter((result) => result.status === "fulfilled").length;
        if (successCount > 0) {
          toast.success(`Sent notifications to ${successCount} users`);
          resetForm();
        } else {
          toast.error("Failed to send notifications");
        }
      }
      await loadSentNotifications();
    } catch (error) {
      toast.error("An error occurred while sending notifications");
      console.error("Error sending notifications:", error);
    } finally {
      loading = false;
    }
  }
  function resetForm() {
    title = "";
    message = "";
    url = "";
    type = "info";
    global = false;
    selectedUsers = [];
    searchQuery = "";
  }
  async function loadSentNotifications() {
    loadingSentNotifications = true;
    try {
      const response = await fetch("/api/notifications/history");
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to load notification history");
      }
      const data2 = await response.json();
      sentNotifications = data2.notifications;
    } catch (error) {
      console.error("Error loading sent notifications:", error);
      toast.error("Failed to load notification history");
      sentNotifications = [];
    } finally {
      loadingSentNotifications = false;
    }
  }
  function getTypeIcon(type2) {
    switch (type2) {
      case "message":
        return Message_square;
      case "job":
        return Briefcase;
      case "error":
        return Triangle_alert;
      case "success":
        return Circle_check_big;
      case "info":
      default:
        return Info;
    }
  }
  function addTestLog(entry) {
    testLogs = [entry, ...testLogs].slice(0, 50);
    console.log(entry);
  }
  async function refreshNotifications() {
    if (loading) return;
    try {
      loading = true;
      addTestLog("Refreshing notifications from server...");
      const response = await fetch("/api/notifications");
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to refresh notifications");
      }
      const data2 = await response.json();
      addTestLog(`Successfully refreshed ${data2.notifications.length} notifications`);
      addTestLog(`Unread count: ${data2.unreadCount}`);
      await loadSentNotifications();
      addTestLog("Notification history refreshed");
    } catch (error) {
      console.error("Error refreshing notifications:", error);
      addTestLog(`Error refreshing notifications: ${error}`);
    } finally {
      loading = false;
    }
  }
  async function createTestNotification() {
    if (loading) return;
    try {
      loading = true;
      addTestLog("Creating test notification...");
      const notificationData = { title, message, url: url || void 0, type };
      addTestLog(`Notification data: ${JSON.stringify(notificationData)}`);
      const response = await fetch("/api/admin/test-notification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(notificationData)
      });
      const result = await response.json();
      if (response.ok && result.success) {
        addTestLog(`Test notification created successfully`);
        addTestLog(`Database ID: ${result.databaseId}`);
        addTestLog("The notification should now appear in the dropdown");
        await loadSentNotifications();
      } else {
        addTestLog(`Error from server: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Error creating test notification:", error);
      addTestLog(`Error creating test notification: ${error}`);
    } finally {
      loading = false;
    }
  }
  if (activeTab === "test") {
    if (testLogs.length === 0) {
      testLogs = [
        "Test tab initialized. Create a notification to test the system."
      ];
    }
  }
  filteredUsers = users.filter((user) => user.email.toLowerCase().includes(searchQuery.toLowerCase()) || user.name && user.name.toLowerCase().includes(searchQuery.toLowerCase()));
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, { title: "Admin Notifications - Hirli" });
    $$payload2.out += `<!----> <div class="border-border flex flex-col gap-1 border-b p-4"><div class="flex items-center justify-between"><h1 class="text-2xl font-bold">Admin Notifications</h1></div></div> `;
    Root($$payload2, {
      class: "w-full",
      get value() {
        return activeTab;
      },
      set value($$value) {
        activeTab = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<div class="border-border border-b p-0">`;
        Tabs_list($$payload3, {
          class: "flex flex-row gap-2 divide-x",
          children: ($$payload4) => {
            Tabs_trigger($$payload4, {
              value: "send",
              class: "flex-1 border-none",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Send Notifications`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Tabs_trigger($$payload4, {
              value: "history",
              class: "flex-1 border-none",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Notification History`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Tabs_trigger($$payload4, {
              value: "test",
              class: "flex-1 border-none",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Test Notifications`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> `;
        Tabs_content($$payload3, {
          value: "send",
          children: ($$payload4) => {
            $$payload4.out += `<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">`;
            Card($$payload4, {
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Send Notification`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Create and send notifications to users or globally`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<form class="space-y-4"><div class="space-y-2">`;
                    Label($$payload6, {
                      for: "title",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Title`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      id: "title",
                      placeholder: "Notification title",
                      get value() {
                        return title;
                      },
                      set value($$value) {
                        title = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> <div class="space-y-2">`;
                    Label($$payload6, {
                      for: "message",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Message`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Textarea($$payload6, {
                      get value() {
                        return message;
                      },
                      set value($$value) {
                        message = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> <div class="space-y-2">`;
                    Label($$payload6, {
                      for: "url",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->URL (optional)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      id: "url",
                      placeholder: "https://example.com",
                      get value() {
                        return url;
                      },
                      set value($$value) {
                        url = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> <div class="space-y-2">`;
                    Label($$payload6, {
                      for: "type",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Type`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <select class="border-input bg-background ring-offset-background w-full rounded-md border px-3 py-2 text-sm">`;
                    $$payload6.select_value = type;
                    $$payload6.out += `<option value="info"${maybe_selected($$payload6, "info")}>Info</option><option value="success"${maybe_selected($$payload6, "success")}>Success</option><option value="error"${maybe_selected($$payload6, "error")}>Error</option><option value="job"${maybe_selected($$payload6, "job")}>Job</option><option value="message"${maybe_selected($$payload6, "message")}>Message</option>`;
                    $$payload6.select_value = void 0;
                    $$payload6.out += `</select></div> <div class="flex items-center space-x-2">`;
                    Switch($$payload6, {
                      id: "global",
                      get checked() {
                        return global;
                      },
                      set checked($$value) {
                        global = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----> `;
                    Label($$payload6, {
                      for: "global",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Send to all users (global notification)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></div></form>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_footer($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      onclick: sendNotification,
                      disabled: loading,
                      children: ($$payload7) => {
                        if (loading) {
                          $$payload7.out += "<!--[-->";
                          Refresh_cw($$payload7, { class: "mr-2 h-4 w-4 animate-spin" });
                        } else {
                          $$payload7.out += "<!--[!-->";
                          Send($$payload7, { class: "mr-2 h-4 w-4" });
                        }
                        $$payload7.out += `<!--]--> ${escape_html(loading ? "Sending..." : "Send Notification")}`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Card($$payload4, {
              class: global ? "opacity-50" : "",
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Select Recipients`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Choose which users will receive the notification`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<div class="mb-4 flex items-center space-x-2"><div class="relative flex-1">`;
                    Search($$payload6, {
                      class: "text-muted-foreground absolute left-2 top-2.5 h-4 w-4"
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      placeholder: "Search users...",
                      class: "pl-8",
                      get value() {
                        return searchQuery;
                      },
                      set value($$value) {
                        searchQuery = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> `;
                    Button($$payload6, {
                      variant: "outline",
                      onclick: selectAllUsers,
                      disabled: global,
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(selectedUsers.length === filteredUsers.length ? "Deselect All" : "Select All")}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></div> <div class="max-h-[400px] overflow-y-auto rounded-md border">`;
                    if (filteredUsers.length === 0) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="flex h-20 items-center justify-center"><p class="text-muted-foreground">No users found</p></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      Table($$payload6, {
                        children: ($$payload7) => {
                          Table_header($$payload7, {
                            children: ($$payload8) => {
                              Table_row($$payload8, {
                                children: ($$payload9) => {
                                  Table_head($$payload9, { class: "w-[50px]" });
                                  $$payload9.out += `<!----> `;
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->User`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!----> `;
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->Role`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!---->`;
                                },
                                $$slots: { default: true }
                              });
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> `;
                          Table_body($$payload7, {
                            children: ($$payload8) => {
                              const each_array = ensure_array_like(filteredUsers);
                              $$payload8.out += `<!--[-->`;
                              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                                let user = each_array[$$index];
                                Table_row($$payload8, {
                                  children: ($$payload9) => {
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        Checkbox($$payload10, {
                                          checked: selectedUsers.includes(user.id),
                                          onCheckedChange: () => toggleUserSelection(user.id),
                                          disabled: global
                                        });
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!----> `;
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        $$payload10.out += `<div class="flex items-center gap-2">`;
                                        Avatar($$payload10, {
                                          class: "h-8 w-8",
                                          children: ($$payload11) => {
                                            Avatar_image($$payload11, {
                                              src: user.image || "",
                                              alt: user.name || user.email
                                            });
                                            $$payload11.out += `<!----> `;
                                            Avatar_fallback($$payload11, {
                                              children: ($$payload12) => {
                                                $$payload12.out += `<!---->${escape_html(user.name ? user.name.split(" ").map((n) => n[0]).join("").toUpperCase() : user.email.substring(0, 2).toUpperCase())}`;
                                              },
                                              $$slots: { default: true }
                                            });
                                            $$payload11.out += `<!---->`;
                                          },
                                          $$slots: { default: true }
                                        });
                                        $$payload10.out += `<!----> <div><p class="text-sm font-medium">${escape_html(user.name || "Unnamed User")}</p> <p class="text-muted-foreground text-xs">${escape_html(user.email)}</p></div></div>`;
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!----> `;
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        Badge($$payload10, {
                                          variant: user.role === "admin" ? "default" : "outline",
                                          children: ($$payload11) => {
                                            $$payload11.out += `<!---->${escape_html(user.role || "user")}`;
                                          },
                                          $$slots: { default: true }
                                        });
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!---->`;
                                  },
                                  $$slots: { default: true }
                                });
                              }
                              $$payload8.out += `<!--]-->`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!---->`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]--></div> <div class="mt-4 flex items-center justify-between"><p class="text-muted-foreground text-sm">${escape_html(selectedUsers.length)} of ${escape_html(users.length)} users selected</p></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Tabs_content($$payload3, {
          value: "history",
          children: ($$payload4) => {
            Card($$payload4, {
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<div class="flex items-center justify-between">`;
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Notification History`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Button($$payload6, {
                      variant: "outline",
                      size: "sm",
                      onclick: loadSentNotifications,
                      children: ($$payload7) => {
                        Refresh_cw($$payload7, {
                          class: `mr-2 h-4 w-4 ${loadingSentNotifications ? "animate-spin" : ""}`
                        });
                        $$payload7.out += `<!----> Refresh`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></div> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->View previously sent notifications`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    if (loadingSentNotifications) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="flex h-40 items-center justify-center">`;
                      Refresh_cw($$payload6, {
                        class: "text-muted-foreground h-8 w-8 animate-spin"
                      });
                      $$payload6.out += `<!----></div>`;
                    } else if (sentNotifications.length === 0) {
                      $$payload6.out += "<!--[1-->";
                      $$payload6.out += `<div class="flex h-40 flex-col items-center justify-center">`;
                      Bell($$payload6, {
                        class: "text-muted-foreground mb-2 h-12 w-12 opacity-20"
                      });
                      $$payload6.out += `<!----> <p class="text-muted-foreground">No notifications have been sent yet</p></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      Table($$payload6, {
                        children: ($$payload7) => {
                          Table_header($$payload7, {
                            children: ($$payload8) => {
                              Table_row($$payload8, {
                                children: ($$payload9) => {
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->Type`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!----> `;
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->Title`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!----> `;
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->Recipients`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!----> `;
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->Sent By`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!----> `;
                                  Table_head($$payload9, {
                                    children: ($$payload10) => {
                                      $$payload10.out += `<!---->Sent At`;
                                    },
                                    $$slots: { default: true }
                                  });
                                  $$payload9.out += `<!---->`;
                                },
                                $$slots: { default: true }
                              });
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!----> `;
                          Table_body($$payload7, {
                            children: ($$payload8) => {
                              const each_array_1 = ensure_array_like(sentNotifications);
                              $$payload8.out += `<!--[-->`;
                              for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                                let notification = each_array_1[$$index_1];
                                Table_row($$payload8, {
                                  children: ($$payload9) => {
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        $$payload10.out += `<div class="flex items-center gap-2"><!---->`;
                                        getTypeIcon(notification.type)?.($$payload10, { class: "text-muted-foreground h-4 w-4" });
                                        $$payload10.out += `<!----> <span class="capitalize">${escape_html(notification.type)}</span></div>`;
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!----> `;
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        $$payload10.out += `<div><p class="font-medium">${escape_html(notification.title)}</p> <p class="text-muted-foreground text-xs">${escape_html(notification.message)}</p></div>`;
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!----> `;
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        Badge($$payload10, {
                                          variant: notification.global ? "default" : "outline",
                                          children: ($$payload11) => {
                                            $$payload11.out += `<!---->${escape_html(notification.global ? "All Users" : notification.recipients)}`;
                                          },
                                          $$slots: { default: true }
                                        });
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!----> `;
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        $$payload10.out += `<!---->${escape_html(notification.sentBy || currentUser.email)}`;
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!----> `;
                                    Table_cell($$payload9, {
                                      children: ($$payload10) => {
                                        $$payload10.out += `<!---->${escape_html(new Date(notification.sentAt).toLocaleString())}`;
                                      },
                                      $$slots: { default: true }
                                    });
                                    $$payload9.out += `<!---->`;
                                  },
                                  $$slots: { default: true }
                                });
                              }
                              $$payload8.out += `<!--]-->`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!---->`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Tabs_content($$payload3, {
          value: "test",
          children: ($$payload4) => {
            Card($$payload4, {
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Test Notification System`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Test creating notifications in the database and displaying them in the UI`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<div class="grid grid-cols-1 gap-6 lg:grid-cols-2"><div><form class="space-y-4"><div class="space-y-2">`;
                    Label($$payload6, {
                      for: "test-title",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Title`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      id: "test-title",
                      get value() {
                        return title;
                      },
                      set value($$value) {
                        title = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> <div class="space-y-2">`;
                    Label($$payload6, {
                      for: "test-message",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Message`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Textarea($$payload6, {
                      get value() {
                        return message;
                      },
                      set value($$value) {
                        message = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> <div class="space-y-2">`;
                    Label($$payload6, {
                      for: "test-url",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->URL (optional)`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      id: "test-url",
                      get value() {
                        return url;
                      },
                      set value($$value) {
                        url = $$value;
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!----></div> <div class="space-y-2">`;
                    Label($$payload6, {
                      for: "test-type",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Type`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <select id="test-type" class="border-input bg-background ring-offset-background w-full rounded-md border px-3 py-2 text-sm">`;
                    $$payload6.select_value = type;
                    $$payload6.out += `<option value="info"${maybe_selected($$payload6, "info")}>Info</option><option value="success"${maybe_selected($$payload6, "success")}>Success</option><option value="error"${maybe_selected($$payload6, "error")}>Error</option><option value="job"${maybe_selected($$payload6, "job")}>Job</option><option value="message"${maybe_selected($$payload6, "message")}>Message</option>`;
                    $$payload6.select_value = void 0;
                    $$payload6.out += `</select></div></form> <div class="mt-4 flex gap-2">`;
                    Button($$payload6, {
                      variant: "outline",
                      onclick: refreshNotifications,
                      disabled: loading,
                      children: ($$payload7) => {
                        Refresh_cw($$payload7, {
                          class: `mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`
                        });
                        $$payload7.out += `<!----> Refresh Notifications`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Button($$payload6, {
                      onclick: createTestNotification,
                      disabled: loading,
                      children: ($$payload7) => {
                        if (loading) {
                          $$payload7.out += "<!--[-->";
                          Refresh_cw($$payload7, { class: "mr-2 h-4 w-4 animate-spin" });
                        } else {
                          $$payload7.out += "<!--[!-->";
                          Bell($$payload7, { class: "mr-2 h-4 w-4" });
                        }
                        $$payload7.out += `<!--]--> Create Notification`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></div></div> <div><div class="mb-2 font-medium">Test Logs</div> <div class="h-[400px] overflow-y-auto rounded-md border bg-gray-50 p-2 dark:bg-gray-900">`;
                    if (testLogs.length === 0) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="flex h-full items-center justify-center text-gray-400">No logs yet. Create a notification to see logs.</div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      const each_array_2 = ensure_array_like(testLogs);
                      $$payload6.out += `<!--[-->`;
                      for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                        let log = each_array_2[$$index_2];
                        $$payload6.out += `<div class="mb-1 border-b pb-1 font-mono text-sm">${escape_html(log)}</div>`;
                      }
                      $$payload6.out += `<!--]-->`;
                    }
                    $$payload6.out += `<!--]--></div></div></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DPANq7Qj.js.map
