import { r as redirect } from './index-Ddp2AB5f.js';

const load = async () => {
  throw redirect(302, "/dashboard/settings/admin/email");
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 60;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BM14y1IE.js')).default;
const server_id = "src/routes/dashboard/settings/email/+page.server.ts";
const imports = ["_app/immutable/nodes/60.BlB6jO6o.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Dx8hstp8.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=60-B2jqDxkb.js.map
