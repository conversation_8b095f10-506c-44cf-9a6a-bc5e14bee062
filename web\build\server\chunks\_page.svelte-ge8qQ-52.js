import { p as push, N as attr, M as ensure_array_like, q as pop, O as escape_html } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { T as Table, a as Table_header, c as Table_row, d as Table_head, b as Table_body, e as Table_cell } from './table-row-CyhLzMgE.js';
import { B as Badge } from './badge-C9pSznab.js';
import { S as Skeleton } from './skeleton-C-NLefl9.js';
import { S as SEO } from './SEO-UItXytUy.js';
import 'clsx';
import { R as Root, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import { S as Search } from './search-B0oHlTPS.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './Icon-A4vzmk-O.js';

function SubscriptionPagination($$payload, $$props) {
  push();
  const {
    currentPage = 1,
    pageSize = 10,
    totalItems = 0,
    totalPages = 1,
    onPageChange,
    onPageSizeChange
  } = $$props;
  function getValidCurrentPage() {
    if (totalPages <= 0) return 1;
    return Math.min(currentPage, totalPages);
  }
  let validCurrentPage = getValidCurrentPage();
  let canPreviousPage = validCurrentPage > 1 && totalPages > 0;
  let canNextPage = validCurrentPage < totalPages && totalPages > 1;
  function goToFirstPage() {
    if (canPreviousPage) {
      onPageChange(1);
    }
  }
  function goToPreviousPage() {
    if (canPreviousPage) {
      onPageChange(currentPage - 1);
    }
  }
  function goToNextPage() {
    if (canNextPage) {
      onPageChange(currentPage + 1);
    }
  }
  function goToLastPage() {
    if (canNextPage) {
      onPageChange(totalPages);
    }
  }
  $$payload.out += `<div class="flex items-center justify-between px-2"><div class="text-muted-foreground flex-1 text-sm">${escape_html(totalItems)} total items</div> <div class="flex items-center space-x-6 lg:space-x-8"><div class="flex items-center space-x-2"><p class="text-sm font-medium">Rows per page</p> <!---->`;
  Root($$payload, {
    type: "single",
    value: pageSize.toString(),
    onValueChange: (value) => onPageSizeChange(Number(value)),
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Select_trigger($$payload2, {
        class: "h-8 w-[70px]",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Select_value($$payload3, { placeholder: pageSize.toString() });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Select_content($$payload2, {
        class: "min-w-[70px]",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Select_item($$payload3, {
            value: "5",
            children: ($$payload4) => {
              $$payload4.out += `<!---->5`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Select_item($$payload3, {
            value: "10",
            children: ($$payload4) => {
              $$payload4.out += `<!---->10`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Select_item($$payload3, {
            value: "20",
            children: ($$payload4) => {
              $$payload4.out += `<!---->20`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Select_item($$payload3, {
            value: "30",
            children: ($$payload4) => {
              $$payload4.out += `<!---->30`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Select_item($$payload3, {
            value: "50",
            children: ($$payload4) => {
              $$payload4.out += `<!---->50`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="flex w-[100px] items-center justify-center text-sm font-medium">Page ${escape_html(validCurrentPage)} of ${escape_html(Math.max(1, totalPages))}</div> <div class="flex items-center space-x-2">`;
  Button($$payload, {
    variant: "outline",
    class: "hidden h-8 w-8 p-0 lg:flex",
    onclick: goToFirstPage,
    disabled: !canPreviousPage,
    children: ($$payload2) => {
      $$payload2.out += `<span class="sr-only">Go to first page</span> <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.85355 3.85355C7.04882 3.65829 7.04882 3.34171 6.85355 3.14645C6.65829 2.95118 6.34171 2.95118 6.14645 3.14645L2.14645 7.14645C1.95118 7.34171 1.95118 7.65829 2.14645 7.85355L6.14645 11.8536C6.34171 12.0488 6.65829 12.0488 6.85355 11.8536C7.04882 11.6583 7.04882 11.3417 6.85355 11.1464L3.20711 7.5L6.85355 3.85355ZM12.8536 3.85355C13.0488 3.65829 13.0488 3.34171 12.8536 3.14645C12.6583 2.95118 12.3417 2.95118 12.1464 3.14645L8.14645 7.14645C7.95118 7.34171 7.95118 7.65829 8.14645 7.85355L12.1464 11.8536C12.3417 12.0488 12.6583 12.0488 12.8536 11.8536C13.0488 11.6583 13.0488 11.3417 12.8536 11.1464L9.20711 7.5L12.8536 3.85355Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "h-8 w-8 p-0",
    onclick: goToPreviousPage,
    disabled: !canPreviousPage,
    children: ($$payload2) => {
      $$payload2.out += `<span class="sr-only">Go to previous page</span> `;
      Chevron_left($$payload2, { size: "15" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "h-8 w-8 p-0",
    onclick: goToNextPage,
    disabled: !canNextPage,
    children: ($$payload2) => {
      $$payload2.out += `<span class="sr-only">Go to next page</span> `;
      Chevron_right($$payload2, { size: "15" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "hidden h-8 w-8 p-0 lg:flex",
    onclick: goToLastPage,
    disabled: !canNextPage,
    children: ($$payload2) => {
      $$payload2.out += `<span class="sr-only">Go to last page</span> <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.14645 11.1464C1.95118 11.3417 1.95118 11.6583 2.14645 11.8536C2.34171 12.0488 2.65829 12.0488 2.85355 11.8536L6.85355 7.85355C7.04882 7.65829 7.04882 7.34171 6.85355 7.14645L2.85355 3.14645C2.65829 2.95118 2.34171 2.95118 2.14645 3.14645C1.95118 3.34171 1.95118 3.65829 2.14645 3.85355L5.79289 7.5L2.14645 11.1464ZM8.14645 11.1464C7.95118 11.3417 7.95118 11.6583 8.14645 11.8536C8.34171 12.0488 8.65829 12.0488 8.85355 11.8536L12.8536 7.85355C13.0488 7.65829 13.0488 7.34171 12.8536 7.14645L8.85355 3.14645C8.65829 2.95118 8.34171 2.95118 8.14645 3.14645C7.95118 3.34171 7.95118 3.65829 8.14645 3.85355L11.7929 7.5L8.14645 11.1464Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div>`;
  pop();
}
function _page($$payload, $$props) {
  push();
  let users = [];
  let searchQuery = "";
  let currentPage = 1;
  let pageSize = 10;
  function getFilteredUsers() {
    if (!searchQuery.trim()) {
      return [...users];
    }
    const query = searchQuery.toLowerCase();
    return users.filter((user) => user.email.toLowerCase().includes(query) || user.name?.toLowerCase().includes(query) || user.role?.toLowerCase().includes(query));
  }
  function getPaginatedUsers() {
    const filteredUsers = getFilteredUsers();
    const totalPages = Math.ceil(filteredUsers.length / pageSize);
    if (currentPage > totalPages && totalPages > 0) {
      currentPage = totalPages;
    }
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return filteredUsers.slice(start, end);
  }
  let paginatedUsers = getPaginatedUsers();
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.error("Invalid date:", dateString);
        return "Invalid date";
      }
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Error";
    }
  }
  function getStatusVariant(status) {
    switch (status) {
      case "active":
        return "success";
      case "trialing":
        return "warning";
      case "canceled":
      case "incomplete_expired":
        return "destructive";
      case "incomplete":
      case "past_due":
        return "destructive";
      case "paused":
        return "outline";
      default:
        return "secondary";
    }
  }
  SEO($$payload, { title: "User Subscriptions" });
  $$payload.out += `<!----> <div class="border-border flex items-center justify-between border-b px-4 py-2"><h2 class="text-lg font-semibold">User Subscriptions</h2> <div class="flex gap-2"><div class="relative">`;
  Search($$payload, {
    class: "text-muted-foreground absolute left-2.5 top-2.5 h-4 w-4"
  });
  $$payload.out += `<!----> <input type="search" placeholder="Search users..." class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-[250px] rounded-md border px-3 py-2 pl-8 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"${attr("value", searchQuery)}/></div></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (users.length === 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(Array(5));
    $$payload.out += `<div class="space-y-4"><div class="flex items-center space-x-4 p-4">`;
    Skeleton($$payload, { class: "h-12 w-12 rounded-full" });
    $$payload.out += `<!----> <div class="space-y-2">`;
    Skeleton($$payload, { class: "h-4 w-[250px]" });
    $$payload.out += `<!----> `;
    Skeleton($$payload, { class: "h-4 w-[200px]" });
    $$payload.out += `<!----></div></div> <!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      each_array[$$index];
      $$payload.out += `<div class="flex items-center justify-between p-4"><div class="flex items-center space-x-4">`;
      Skeleton($$payload, { class: "h-12 w-12 rounded-full" });
      $$payload.out += `<!----> <div class="space-y-2">`;
      Skeleton($$payload, { class: "h-4 w-[250px]" });
      $$payload.out += `<!----> `;
      Skeleton($$payload, { class: "h-4 w-[200px]" });
      $$payload.out += `<!----></div></div> <div class="flex space-x-2">`;
      Skeleton($$payload, { class: "h-8 w-8 rounded-full" });
      $$payload.out += `<!----> `;
      Skeleton($$payload, { class: "h-8 w-8 rounded-full" });
      $$payload.out += `<!----> `;
      Skeleton($$payload, { class: "h-8 w-8 rounded-full" });
      $$payload.out += `<!----></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="p-4"><!---->`;
    Table($$payload, {
      class: "border-border border",
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Table_header($$payload2, {
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Table_row($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Table_head($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->User`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Table_head($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Plan`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Table_head($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Status`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Table_head($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->Start Date`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Table_head($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->End Date`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Table_body($$payload2, {
          children: ($$payload3) => {
            if (paginatedUsers.length === 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<!---->`;
              Table_row($$payload3, {
                children: ($$payload4) => {
                  $$payload4.out += `<!---->`;
                  Table_cell($$payload4, {
                    class: "h-24 text-center",
                    colspan: 5,
                    children: ($$payload5) => {
                      $$payload5.out += `<!---->${escape_html(getFilteredUsers().length === 0 ? "No users found." : "No users match your search criteria.")}`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            } else {
              $$payload3.out += "<!--[!-->";
              const each_array_1 = ensure_array_like(paginatedUsers);
              $$payload3.out += `<!--[-->`;
              for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                let user = each_array_1[$$index_1];
                $$payload3.out += `<!---->`;
                Table_row($$payload3, {
                  children: ($$payload4) => {
                    $$payload4.out += `<!---->`;
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="font-medium">${escape_html(user.email)}</div> <div class="text-muted-foreground text-sm">${escape_html(user.name || "No name")}</div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> <!---->`;
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        Badge($$payload5, {
                          variant: "outline",
                          children: ($$payload6) => {
                            $$payload6.out += `<!---->${escape_html(user.role || "free")}`;
                          },
                          $$slots: { default: true }
                        });
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> <!---->`;
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        if (user.subscription) {
                          $$payload5.out += "<!--[-->";
                          Badge($$payload5, {
                            variant: getStatusVariant(user.subscription.status),
                            children: ($$payload6) => {
                              $$payload6.out += `<!---->${escape_html(user.subscription.status)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload5.out += `<!----> `;
                          if (user.subscription.cancelAtPeriodEnd) {
                            $$payload5.out += "<!--[-->";
                            Badge($$payload5, {
                              variant: "outline",
                              class: "ml-1",
                              children: ($$payload6) => {
                                $$payload6.out += `<!---->Canceling`;
                              },
                              $$slots: { default: true }
                            });
                          } else {
                            $$payload5.out += "<!--[!-->";
                          }
                          $$payload5.out += `<!--]-->`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                          Badge($$payload5, {
                            variant: "outline",
                            children: ($$payload6) => {
                              $$payload6.out += `<!---->No subscription`;
                            },
                            $$slots: { default: true }
                          });
                        }
                        $$payload5.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> <!---->`;
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        if (user.subscription && user.subscription.currentPeriodStart) {
                          $$payload5.out += "<!--[-->";
                          $$payload5.out += `${escape_html(formatDate(user.subscription.currentPeriodStart))}`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                          $$payload5.out += `<span class="text-muted-foreground">N/A</span>`;
                        }
                        $$payload5.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> <!---->`;
                    Table_cell($$payload4, {
                      children: ($$payload5) => {
                        if (user.subscription && user.subscription.currentPeriodEnd) {
                          $$payload5.out += "<!--[-->";
                          $$payload5.out += `${escape_html(formatDate(user.subscription.currentPeriodEnd))}`;
                        } else {
                          $$payload5.out += "<!--[!-->";
                          $$payload5.out += `<span class="text-muted-foreground">N/A</span>`;
                        }
                        $$payload5.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload3.out += `<!---->`;
              }
              $$payload3.out += `<!--]-->`;
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> <div class="mt-4">`;
    SubscriptionPagination($$payload, {
      currentPage,
      pageSize,
      totalItems: getFilteredUsers().length,
      totalPages: Math.ceil(getFilteredUsers().length / pageSize),
      onPageChange: (page) => currentPage = page,
      onPageSizeChange: (size) => {
        pageSize = size;
        currentPage = 1;
      }
    });
    $$payload.out += `<!----></div></div>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-ge8qQ-52.js.map
