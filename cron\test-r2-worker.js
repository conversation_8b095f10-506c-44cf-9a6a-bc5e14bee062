import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import dotenv from 'dotenv';

dotenv.config();

const R2_CONFIG = {
  endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto",
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID ?? "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY ?? "",
  },
};

const r2Client = new S3Client(R2_CONFIG);

async function testR2Worker() {
  try {
    console.log('🧪 Testing R2 worker setup...');
    
    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    // Upload test image to company logos bucket
    const uploadCommand = new PutObjectCommand({
      Bucket: 'hirli-company-logos',
      Key: 'test-logo.png',
      Body: testImageBuffer,
      ContentType: 'image/png',
      Metadata: {
        originalName: 'test-logo.png',
        uploadedAt: new Date().toISOString(),
        fileType: 'companyLogos',
        bucketType: 'company',
      },
    });
    
    await r2Client.send(uploadCommand);
    console.log('✅ Test image uploaded to R2');
    
    // Test worker URL
    const workerUrl = 'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/logos/test-logo.png';
    console.log(`🔗 Testing worker URL: ${workerUrl}`);
    
    const response = await fetch(workerUrl);
    console.log(`📊 Worker response: ${response.status} ${response.statusText}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    console.log(`🔒 CORS headers: ${response.headers.get('access-control-allow-origin')}`);
    
    if (response.ok) {
      const buffer = await response.arrayBuffer();
      console.log(`📦 Response size: ${buffer.byteLength} bytes`);
      console.log('🎉 Worker is working correctly!');
    } else {
      console.log('❌ Worker test failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testR2Worker();
