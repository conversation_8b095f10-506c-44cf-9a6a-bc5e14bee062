import 'clsx';
import { p as push, q as pop, K as fallback, a0 as slot, Q as bind_props } from './index3-CqUPEnZw.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { g as goto } from './client-dNyMPa8V.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { A as Activity } from './activity-BF3HC42u.js';
import { T as Trending_up } from './trending-up-BKR_Sbhj.js';
import { B as Briefcase_business } from './briefcase-business-QHQ7bmQH.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { G as Globe } from './globe-B6sBOhFF.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { L as Lightbulb } from './lightbulb-CGr-7VNq.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

function FeatureGuardSimple($$payload, $$props) {
  push();
  let userData = $$props["userData"];
  let featureId = $$props["featureId"];
  let limitId = fallback($$props["limitId"], void 0);
  let showUpgradeButton = fallback($$props["showUpgradeButton"], true);
  let upgradeButtonText = fallback($$props["upgradeButtonText"], "Upgrade Plan");
  let upgradeButtonLink = fallback($$props["upgradeButtonLink"], "/dashboard/settings/billing");
  let limitReachedMessage = fallback($$props["limitReachedMessage"], "You have reached the limit for this feature.");
  let notIncludedMessage = fallback($$props["notIncludedMessage"], "This feature is not included in your current plan.");
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", {}, null);
    $$payload.out += `<!---->`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, {
    userData,
    featureId,
    limitId,
    showUpgradeButton,
    upgradeButtonText,
    upgradeButtonLink,
    limitReachedMessage,
    notIncludedMessage
  });
  pop();
}
function _page($$payload, $$props) {
  push();
  let { data } = $$props;
  let userData = data?.user || {};
  let applications = data?.applications || [];
  let resumes = data?.resumes || [];
  let activeTab = "skills";
  function handleTabChange(value) {
    activeTab = value;
  }
  SEO($$payload, {
    title: "Career Analysis | Hirli",
    description: "Analyze your career progress, skills, and application performance",
    keywords: "career analysis, skill gap, job applications, resume effectiveness",
    url: "https://hirli.com/dashboard/settings/analysis"
  });
  $$payload.out += `<!----> <div class="border-border flex flex-col justify-between border-b p-6"><h2 class="text-lg font-semibold">Career Analysis</h2> <p class="text-muted-foreground text-foreground/80">Gain insights into your skills, applications, and career trajectory.</p></div> <div class="p-6"><!---->`;
  Root($$payload, {
    value: activeTab,
    onValueChange: handleTabChange,
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Tabs_list($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Tabs_trigger($$payload3, {
            value: "skills",
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Activity($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Skill Analysis</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_trigger($$payload3, {
            value: "career",
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Trending_up($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Career Path</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_trigger($$payload3, {
            value: "applications",
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Briefcase_business($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Application Performance</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_trigger($$payload3, {
            value: "resume",
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              File_text($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Resume Effectiveness</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Tabs_trigger($$payload3, {
            value: "market",
            class: "flex items-center gap-2",
            children: ($$payload4) => {
              Globe($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span>Market Insights</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "skills",
        class: "mt-6",
        children: ($$payload3) => {
          FeatureGuardSimple($$payload3, {
            userData,
            featureId: "skill_gap_analysis",
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-6"><!---->`;
              Card($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Card_header($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Card_title($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Skill Gap Analysis`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <!---->`;
                      Card_description($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Compare your skills against job requirements to identify areas for growth`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Card_content($$payload5, {
                    children: ($$payload6) => {
                      if (applications.length === 0 || resumes.length === 0) {
                        $$payload6.out += "<!--[1-->";
                        $$payload6.out += `<div class="flex items-center justify-center p-6 text-center"><div class="max-w-md">`;
                        Triangle_alert($$payload6, { class: "text-warning mx-auto h-12 w-12" });
                        $$payload6.out += `<!----> <h3 class="mt-4 text-lg font-medium">Insufficient Data</h3> <p class="text-muted-foreground mt-2">We need more information about your skills and job applications to provide an
                      analysis. Update your resume or apply to more jobs to see insights here.</p> <div class="mt-6 flex justify-center gap-4">`;
                        Button($$payload6, {
                          variant: "outline",
                          onclick: () => goto(),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Update Resume`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Button($$payload6, {
                          variant: "outline",
                          onclick: () => goto(),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Browse Jobs`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></div></div></div>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                        $$payload6.out += `<p class="text-muted-foreground mb-6">This feature is coming soon. We're working on analyzing your skills against job
                  requirements to provide personalized insights and recommendations.</p> <div class="rounded-md border p-4"><div class="flex gap-3">`;
                        Lightbulb($$payload6, { class: "text-primary h-5 w-5" });
                        $$payload6.out += `<!----> <div><h4 class="font-medium">What you'll see here soon:</h4> <ul class="text-muted-foreground mt-2 space-y-2 text-sm"><li class="flex items-start gap-2">`;
                        Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                        $$payload6.out += `<!----> <span>Skill match percentage against your target jobs</span></li> <li class="flex items-start gap-2">`;
                        Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                        $$payload6.out += `<!----> <span>Identification of missing skills in your profile</span></li> <li class="flex items-start gap-2">`;
                        Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                        $$payload6.out += `<!----> <span>Personalized recommendations for skill development</span></li></ul></div></div></div>`;
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "career",
        class: "mt-6",
        children: ($$payload3) => {
          FeatureGuardSimple($$payload3, {
            userData,
            featureId: "career_trajectory",
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-6"><!---->`;
              Card($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Card_header($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Card_title($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Career Trajectory Analysis`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <!---->`;
                      Card_description($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Visualize your career progression options based on your experience`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Card_content($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<p class="text-muted-foreground mb-6">This feature is coming soon. We're working on analyzing career paths based on your
                experience and target roles to help you plan your next career move.</p> <div class="rounded-md border p-4"><div class="flex gap-3">`;
                      Lightbulb($$payload6, { class: "text-primary h-5 w-5" });
                      $$payload6.out += `<!----> <div><h4 class="font-medium">What you'll see here soon:</h4> <ul class="text-muted-foreground mt-2 space-y-2 text-sm"><li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Visualization of potential career paths</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Recommended next roles based on your experience</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Skill requirements for career advancement</span></li></ul></div></div></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "applications",
        class: "mt-6",
        children: ($$payload3) => {
          FeatureGuardSimple($$payload3, {
            userData,
            featureId: "application_analytics",
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-6"><!---->`;
              Card($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Card_header($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Card_title($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Application Performance`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <!---->`;
                      Card_description($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Track and analyze your job application success rates`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Card_content($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<p class="text-muted-foreground mb-6">This feature is coming soon. We're working on analyzing your application performance
                to help you optimize your job search strategy.</p> <div class="rounded-md border p-4"><div class="flex gap-3">`;
                      Lightbulb($$payload6, { class: "text-primary h-5 w-5" });
                      $$payload6.out += `<!----> <div><h4 class="font-medium">What you'll see here soon:</h4> <ul class="text-muted-foreground mt-2 space-y-2 text-sm"><li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Application success rates by job type and industry</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Response and interview conversion metrics</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Recommendations to improve application outcomes</span></li></ul></div></div></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "resume",
        class: "mt-6",
        children: ($$payload3) => {
          FeatureGuardSimple($$payload3, {
            userData,
            featureId: "resume_effectiveness",
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-6"><!---->`;
              Card($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Card_header($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Card_title($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Resume Effectiveness`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <!---->`;
                      Card_description($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Measure how well your resume performs against job requirements`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Card_content($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<p class="text-muted-foreground mb-6">This feature is coming soon. We're working on analyzing your resume effectiveness to
                help you optimize your resume for better job matches.</p> <div class="rounded-md border p-4"><div class="flex gap-3">`;
                      Lightbulb($$payload6, { class: "text-primary h-5 w-5" });
                      $$payload6.out += `<!----> <div><h4 class="font-medium">What you'll see here soon:</h4> <ul class="text-muted-foreground mt-2 space-y-2 text-sm"><li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Keyword match analysis against target jobs</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Resume optimization score and suggestions</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Performance comparison of different resume versions</span></li></ul></div></div></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "market",
        class: "mt-6",
        children: ($$payload3) => {
          FeatureGuardSimple($$payload3, {
            userData,
            featureId: "market_intelligence",
            children: ($$payload4) => {
              $$payload4.out += `<div class="space-y-6"><!---->`;
              Card($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Card_header($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Card_title($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Market Insights`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <!---->`;
                      Card_description($$payload6, {
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Get insights on job market trends relevant to your career`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Card_content($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<p class="text-muted-foreground mb-6">This feature is coming soon. We're working on gathering market intelligence to help
                you make informed career decisions.</p> <div class="rounded-md border p-4"><div class="flex gap-3">`;
                      Lightbulb($$payload6, { class: "text-primary h-5 w-5" });
                      $$payload6.out += `<!----> <div><h4 class="font-medium">What you'll see here soon:</h4> <ul class="text-muted-foreground mt-2 space-y-2 text-sm"><li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Industry hiring trends and demand forecasts</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Salary benchmarks for your target roles</span></li> <li class="flex items-start gap-2">`;
                      Circle_check_big($$payload6, { class: "mt-0.5 h-4 w-4" });
                      $$payload6.out += `<!----> <span>Emerging skill requirements in your field</span></li></ul></div></div></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BtGcnToV.js.map
