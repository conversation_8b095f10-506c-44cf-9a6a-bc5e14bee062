import { r as redirect, f as fail } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { g as getUserFromToken } from './auth-BPad-IlN.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, s as stringType, e as enumType } from './types-D78SXuvm.js';
import '@prisma/client';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const inviteSchema = objectType({
  email: stringType().email("Invalid email address"),
  role: enumType(["member", "admin"]).default("member")
});
const teamSchema = objectType({
  name: stringType().min(1, "Team name is required")
});
const load = async ({ locals }) => {
  const user = locals.user;
  if (!user || !user.email) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      Team: {
        include: {
          members: {
            include: {
              user: true
            }
          }
        }
      },
      TeamMember: {
        include: {
          team: {
            include: {
              owner: true,
              members: {
                include: {
                  user: true
                }
              }
            }
          }
        }
      }
    }
  });
  if (!userData) {
    throw redirect(302, "/auth/sign-in");
  }
  locals.user = userData;
  const ownedTeams = userData.Team || [];
  const memberTeams = userData.TeamMember?.map((tm) => tm.team) || [];
  const allTeams = [...ownedTeams, ...memberTeams];
  const uniqueTeams = Array.from(new Map(allTeams.map((team) => [team.id, team])).values());
  const inviteForm = await superValidate(zod(inviteSchema));
  const teamForm = await superValidate(zod(teamSchema));
  return {
    user: userData,
    teams: uniqueTeams,
    inviteForm,
    teamForm
  };
};
const actions = {
  invite: async ({ request, cookies, locals }) => {
    const tokenData = getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(inviteSchema));
    if (!form.valid) {
      return fail(400, { inviteForm: form });
    }
    try {
      return { inviteForm: form, success: true };
    } catch (error) {
      console.error("Error inviting team member:", error);
      return fail(500, { inviteForm: form, error: "Failed to invite team member" });
    }
  },
  createTeam: async ({ request, cookies, locals }) => {
    const tokenData = getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(teamSchema));
    if (!form.valid) {
      return fail(400, { teamForm: form });
    }
    try {
      const newTeam = await prisma.team.create({
        data: {
          name: form.data.name,
          ownerId: userData.id
        }
      });
      return { teamForm: form, success: true, team: newTeam };
    } catch (error) {
      console.error("Error creating team:", error);
      return fail(500, { teamForm: form, error: "Failed to create team" });
    }
  },
  removeTeamMember: async ({ request, cookies, locals }) => {
    const tokenData = getUserFromToken(cookies);
    if (!tokenData || !tokenData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData) {
      throw redirect(302, "/auth/sign-in");
    }
    const data = await request.formData();
    const teamId = data.get("teamId")?.toString();
    const memberId = data.get("memberId")?.toString();
    if (!teamId || !memberId) {
      return fail(400, { error: "Missing required fields" });
    }
    try {
      const team = await prisma.team.findUnique({
        where: { id: teamId }
      });
      if (!team || team.ownerId !== userData.id) {
        return fail(403, { error: "You do not have permission to remove team members" });
      }
      await prisma.teamMember.delete({
        where: {
          id: memberId
        }
      });
      return { success: true };
    } catch (error) {
      console.error("Error removing team member:", error);
      return fail(500, { error: "Failed to remove team member" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 69;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-TM1ERvmF.js')).default;
const server_id = "src/routes/dashboard/settings/team/+page.server.ts";
const imports = ["_app/immutable/nodes/69.D1Fe2Xis.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/BS2FzWLA.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/D85ENLd-.js","_app/immutable/chunks/ByUTvV5u.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u-Lut8o2.js","_app/immutable/chunks/CrHU05dq.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/FeejBSkx.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CE9Bts7j.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BSHZ37s_.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/CzSntoiK.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/CXvW3J0s.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/yPulTJ2h.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/C8B1VUaq.js"];
const stylesheets = ["_app/immutable/assets/index.CV-KWLNP.css","_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=69-Dd9fBc9e.js.map
