import { e as error, j as json } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const GET = async ({ params, locals }) => {
  if (!locals.user) {
    throw error(401, "Unauthorized");
  }
  try {
    const { id } = params;
    if (!id) {
      throw error(400, "Document ID is required");
    }
    const document = await prisma.document.findUnique({
      where: { id }
    });
    if (!document || document.userId !== locals.user.id) {
      throw error(404, "Document not found");
    }
    let directUrl = document.fileUrl || "/placeholder.pdf";
    let isExternal = false;
    if (directUrl.startsWith("http://") || directUrl.startsWith("https://")) {
      isExternal = true;
    } else if (directUrl !== "/placeholder.pdf") {
      if (!directUrl.startsWith("/")) {
        directUrl = "/" + directUrl;
      }
      if (!directUrl.includes("/uploads/")) {
        const fileExtension = directUrl.split(".").pop()?.toLowerCase();
        let folder = "documents";
        if (document.type === "resume") folder = "resumes";
        else if (document.type === "cover_letter") folder = "cover-letters";
        else if (document.type === "reference") folder = "references";
        directUrl = `/uploads/${folder}${directUrl}`;
      }
    }
    return json({
      success: true,
      document: {
        ...document,
        directUrl,
        isExternal,
        fileExists: true
        // Assume files exist since they're in database
      }
    });
  } catch (err) {
    console.error("Error serving document:", err);
    throw error(500, "Failed to serve document");
  }
};

export { GET };
//# sourceMappingURL=_server.ts-bGc5-k6j.js.map
