{"version": 3, "file": "45-YKuCga-S.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/45.js"], "sourcesContent": ["import { r as redirect } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const userData = await prisma.user.findUnique({\n    where: { id: user.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!userData || !userData.isAdmin && userData.role !== \"admin\") {\n    throw redirect(302, \"/dashboard/settings\");\n  }\n  return {};\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/admin/_page.server.ts.js';\n\nexport const index = 45;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/admin/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/admin/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/45.B0dOzSSD.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/ByUTvV5u.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/Cs0qIT7f.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/tjBMsfLi.js\",\"_app/immutable/chunks/B-l1ubNa.js\",\"_app/immutable/chunks/yPulTJ2h.js\",\"_app/immutable/chunks/hA0h0kTo.js\",\"_app/immutable/chunks/BhzFx1Wy.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;AACnE,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,qBAAqB,CAAC;AAC9C;AACA,EAAE,OAAO,EAAE;AACX,CAAC;;;;;;;ACbW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA2D,CAAC,EAAE;AAEzH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACr2C,MAAC,WAAW,GAAG,CAAC,4CAA4C;AAC5D,MAAC,KAAK,GAAG;;;;"}