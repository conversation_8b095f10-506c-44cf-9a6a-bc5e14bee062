{"version": 3, "file": "96-BIBu5bkD.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/system-status/history/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/96.js"], "sourcesContent": ["import \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../chunks/logger.js\";\nconst load = async ({ url }) => {\n  try {\n    const monthParam2 = url.searchParams.get(\"month\");\n    const yearParam2 = url.searchParams.get(\"year\");\n    const now = /* @__PURE__ */ new Date();\n    const month = monthParam2 ? parseInt(monthParam2) : now.getMonth() + 1;\n    const year = yearParam2 ? parseInt(yearParam2) : now.getFullYear();\n    const startDate = new Date(year, month - 1, 1);\n    const endDate = new Date(year, month, 0);\n    let maintenanceEvents = [];\n    try {\n      if (prisma && \"maintenanceEvent\" in prisma) {\n        maintenanceEvents = await prisma.maintenanceEvent.findMany({\n          where: {\n            OR: [\n              {\n                // Events that start in the specified month\n                startTime: {\n                  gte: startDate,\n                  lte: endDate\n                }\n              },\n              {\n                // Events that end in the specified month\n                endTime: {\n                  gte: startDate,\n                  lte: endDate\n                }\n              }\n            ]\n          },\n          orderBy: {\n            startTime: \"desc\"\n          },\n          include: {\n            history: true\n          }\n        });\n      } else {\n        try {\n          const events = await prisma.$queryRaw`\n            SELECT * FROM \"web\".\"MaintenanceEvent\"\n            WHERE\n              (\"startTime\" >= ${startDate} AND \"startTime\" <= ${endDate})\n              OR\n              (\"endTime\" >= ${startDate} AND \"endTime\" <= ${endDate})\n            ORDER BY \"startTime\" DESC\n          `;\n          if (Array.isArray(events)) {\n            maintenanceEvents = events;\n            for (const event of maintenanceEvents) {\n              try {\n                const history = await prisma.$queryRaw`\n                  SELECT * FROM \"web\".\"MaintenanceEventHistory\"\n                  WHERE \"eventId\" = ${event.id}\n                  ORDER BY \"createdAt\" ASC\n                `;\n                event.history = Array.isArray(history) ? history : [];\n              } catch (historyError) {\n                logger.warn(`Error fetching history for event ${event.id}:`, historyError);\n                event.history = [];\n              }\n            }\n          }\n        } catch (sqlError) {\n          logger.warn(\"Error fetching maintenance events with raw SQL:\", sqlError);\n        }\n      }\n    } catch (dbError) {\n      logger.warn(\"Error querying maintenance events:\", dbError);\n    }\n    const eventsWithUpdates = maintenanceEvents.map((event) => {\n      const updates = (event.history || []).map((historyEntry) => ({\n        id: historyEntry.id,\n        timestamp: historyEntry.createdAt,\n        message: historyEntry.comment || (historyEntry.changeType === \"status_change\" ? `Status changed from ${historyEntry.previousStatus || \"unknown\"} to ${historyEntry.newStatus || \"unknown\"}` : \"Update received\")\n      }));\n      if (updates.length === 0) {\n        updates.push({\n          id: \"initial\",\n          timestamp: event.createdAt,\n          message: `Maintenance event created with status: ${event.status}`\n        });\n      }\n      return {\n        ...event,\n        updates\n      };\n    });\n    return {\n      maintenance: eventsWithUpdates,\n      currentMonth: month,\n      currentYear: year,\n      hasNextMonth: month < now.getMonth() + 1 || year < now.getFullYear(),\n      hasPrevMonth: true\n      // We always allow going back in history\n    };\n  } catch (err) {\n    logger.error(\"Error loading maintenance history:\", err);\n    const now = /* @__PURE__ */ new Date();\n    const month = monthParam ? parseInt(monthParam) : now.getMonth() + 1;\n    const year = yearParam ? parseInt(yearParam) : now.getFullYear();\n    return {\n      maintenance: [],\n      currentMonth: month,\n      currentYear: year,\n      hasNextMonth: month < now.getMonth() + 1 || year < now.getFullYear(),\n      hasPrevMonth: true\n    };\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/system-status/history/_page.server.ts.js';\n\nexport const index = 96;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/system-status/history/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/system-status/history/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/96.DJ5XU_xz.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/ByUTvV5u.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/BPr9JIwg.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/CcFQTcQh.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/D0KcwhQz.js\",\"_app/immutable/chunks/CKg8MWp_.js\",\"_app/immutable/chunks/BAIxhb6t.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/CTO_B1Jk.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/DvO_AOqy.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/BuYRPDDz.js\",\"_app/immutable/chunks/QtAhPN2H.js\",\"_app/immutable/chunks/BBNNmnYR.js\",\"_app/immutable/chunks/DkmCSZhC.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAChC,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AACrD,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;AACnD,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,KAAK,GAAG,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;AAC1E,IAAI,MAAM,IAAI,GAAG,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE;AACtE,IAAI,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AAC5C,IAAI,IAAI,iBAAiB,GAAG,EAAE;AAC9B,IAAI,IAAI;AACR,MAAM,IAAI,MAAM,IAAI,kBAAkB,IAAI,MAAM,EAAE;AAClD,QAAQ,iBAAiB,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACnE,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,EAAE;AAChB,cAAc;AACd;AACA,gBAAgB,SAAS,EAAE;AAC3B,kBAAkB,GAAG,EAAE,SAAS;AAChC,kBAAkB,GAAG,EAAE;AACvB;AACA,eAAe;AACf,cAAc;AACd;AACA,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,GAAG,EAAE,SAAS;AAChC,kBAAkB,GAAG,EAAE;AACvB;AACA;AACA;AACA,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,SAAS,EAAE;AACvB,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB;AACA,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,IAAI;AACZ,UAAU,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS;AAC/C;AACA;AACA,8BAA8B,EAAE,SAAS,CAAC,oBAAoB,EAAE,OAAO,CAAC;AACxE;AACA,4BAA4B,EAAE,SAAS,CAAC,kBAAkB,EAAE,OAAO,CAAC;AACpE;AACA,UAAU,CAAC;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACrC,YAAY,iBAAiB,GAAG,MAAM;AACtC,YAAY,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE;AACnD,cAAc,IAAI;AAClB,gBAAgB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS;AACtD;AACA,oCAAoC,EAAE,KAAK,CAAC,EAAE;AAC9C;AACA,gBAAgB,CAAC;AACjB,gBAAgB,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE;AACrE,eAAe,CAAC,OAAO,YAAY,EAAE;AACrC,gBAAgB,MAAM,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;AAC1F,gBAAgB,KAAK,CAAC,OAAO,GAAG,EAAE;AAClC;AACA;AACA;AACA,SAAS,CAAC,OAAO,QAAQ,EAAE;AAC3B,UAAU,MAAM,CAAC,IAAI,CAAC,iDAAiD,EAAE,QAAQ,CAAC;AAClF;AACA;AACA,KAAK,CAAC,OAAO,OAAO,EAAE;AACtB,MAAM,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,OAAO,CAAC;AAChE;AACA,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAC/D,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAY,MAAM;AACnE,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE;AAC3B,QAAQ,SAAS,EAAE,YAAY,CAAC,SAAS;AACzC,QAAQ,OAAO,EAAE,YAAY,CAAC,OAAO,KAAK,YAAY,CAAC,UAAU,KAAK,eAAe,GAAG,CAAC,oBAAoB,EAAE,YAAY,CAAC,cAAc,IAAI,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,GAAG,iBAAiB;AACvN,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,QAAQ,OAAO,CAAC,IAAI,CAAC;AACrB,UAAU,EAAE,EAAE,SAAS;AACvB,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS;AACpC,UAAU,OAAO,EAAE,CAAC,uCAAuC,EAAE,KAAK,CAAC,MAAM,CAAC;AAC1E,SAAS,CAAC;AACV;AACA,MAAM,OAAO;AACb,QAAQ,GAAG,KAAK;AAChB,QAAQ;AACR,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,YAAY,EAAE,KAAK;AACzB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,YAAY,EAAE,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE;AAC1E,MAAM,YAAY,EAAE;AACpB;AACA,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,GAAG,CAAC;AAC3D,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;AACxE,IAAI,MAAM,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE;AACpE,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,KAAK;AACzB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,YAAY,EAAE,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE;AAC1E,MAAM,YAAY,EAAE;AACpB,KAAK;AACL;AACA,CAAC;;;;;;;AC/GW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAwD,CAAC,EAAE;AAEtH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACjvD,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}