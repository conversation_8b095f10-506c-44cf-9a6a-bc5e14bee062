import { p as push, ag as store_set, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, K as fallback, M as ensure_array_like, O as escape_html, N as attr, S as store_get, ab as store_mutate, T as unsubscribe_stores } from './index3-CqUPEnZw.js';
import { i as invalidateAll } from './client-dNyMPa8V.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { B as Button } from './button-CrucCo1G.js';
import { B as Badge } from './badge-C9pSznab.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { R as Root$1, d as Dialog_overlay, D as Dialog_content, P as Portal } from './index7-BURUpWjT.js';
import { S as StatusTag, a as StatusBar, b as SeverityBadge } from './StatusBar-DynsEX84.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { I as Input } from './input-DF0gPqYN.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { C as Checkbox } from './checkbox-Bu-4wGff.js';
import { R as Root$2, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { S as Scroll_area } from './scroll-area-Dn69zlyp.js';
import { H as History } from './history-T6kmbk1Q.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import './zod-DfpldWlD.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { S as Square_pen } from './square-pen-DCE_ltl5.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './index2-Cut0V_vU.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './circle-alert-BcRZk-Zc.js';
import './search-B0oHlTPS.js';
import './play-DKNYqs4c.js';
import './info-Ce09B-Yv.js';
import './dialog-description2-rfr-pd9k.js';
import './clone-BRGVxGEr.js';
import './hidden-input-1eDzjGOB.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './use-debounce.svelte-gxToHznJ.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './stringify-DWCARkQV.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';

function MaintenanceHistoryDialog($$payload, $$props) {
  push();
  let eventId = $$props["eventId"];
  let open = fallback($$props["open"], false);
  let onClose = fallback($$props["onClose"], () => {
  });
  let history = [];
  let isLoading = false;
  let error = null;
  function formatDate(date) {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    }).format(new Date(date));
  }
  function getChangeTypeIcon(changeType) {
    switch (changeType) {
      case "status_change":
        return Refresh_cw;
      case "comment":
        return Message_square;
      case "update":
        return Clock;
      default:
        return Clock;
    }
  }
  async function fetchHistory() {
    if (!eventId) return;
    isLoading = true;
    error = null;
    try {
      const response = await fetch(`/api/maintenance/${eventId}/history`);
      if (!response.ok) {
        throw new Error(`Failed to fetch history: ${response.status}`);
      }
      const data = await response.json();
      history = data;
    } catch (err) {
      console.error("Error fetching maintenance history:", err);
      error = err instanceof Error ? err.message : "Failed to load history";
    } finally {
      isLoading = false;
    }
  }
  if (open && eventId) {
    fetchHistory();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_overlay($$payload4, {});
            $$payload4.out += `<!----> `;
            Dialog_content($$payload4, {
              class: "sm:max-w-[600px]",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Maintenance History`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->View the history of updates and status changes for this maintenance event.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="py-4">`;
                if (isLoading) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="flex justify-center py-8"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
                } else if (error) {
                  $$payload5.out += "<!--[1-->";
                  $$payload5.out += `<div class="border-destructive/50 bg-destructive/10 rounded-lg border p-4 text-center"><p class="text-destructive">${escape_html(error)}</p> `;
                  Button($$payload5, {
                    variant: "outline",
                    class: "mt-2",
                    onclick: fetchHistory,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Retry`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></div>`;
                } else if (history.length === 0) {
                  $$payload5.out += "<!--[2-->";
                  $$payload5.out += `<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No history available for this maintenance event.</p></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  const each_array = ensure_array_like(history);
                  $$payload5.out += `<div class="space-y-4"><!--[-->`;
                  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
                    let item = each_array[$$index_1];
                    $$payload5.out += `<div class="rounded-lg border p-4"><div class="flex items-start gap-3"><div class="mt-0.5"><!---->`;
                    {
                      $$payload5.out += `<!---->`;
                      getChangeTypeIcon(item.changeType)?.($$payload5, { class: "text-muted-foreground h-5 w-5" });
                      $$payload5.out += `<!---->`;
                    }
                    $$payload5.out += `<!----></div> <div class="flex-1"><div class="mb-1 flex items-center justify-between"><p class="text-sm font-medium">`;
                    if (item.changeType === "status_change") {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `Status changed from `;
                      if (item.previousStatus) {
                        $$payload5.out += "<!--[-->";
                        StatusTag($$payload5, { status: item.previousStatus });
                      } else {
                        $$payload5.out += "<!--[!-->";
                      }
                      $$payload5.out += `<!--]--> to `;
                      if (item.newStatus) {
                        $$payload5.out += "<!--[-->";
                        StatusTag($$payload5, { status: item.newStatus });
                      } else {
                        $$payload5.out += "<!--[!-->";
                      }
                      $$payload5.out += `<!--]-->`;
                    } else if (item.changeType === "comment") {
                      $$payload5.out += "<!--[1-->";
                      $$payload5.out += `Comment added`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `Event updated`;
                    }
                    $$payload5.out += `<!--]--></p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(item.createdAt))}</p></div> `;
                    if (item.comment) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="bg-muted mt-2 rounded-md p-3"><p class="text-sm">${escape_html(item.comment)}</p></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> `;
                    if (item.changeType === "update" && item.metadata?.changedFields?.length > 0) {
                      $$payload5.out += "<!--[-->";
                      const each_array_1 = ensure_array_like(item.metadata.changedFields);
                      $$payload5.out += `<div class="mt-2"><p class="text-muted-foreground text-xs">Changed fields:</p> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
                      for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                        let field = each_array_1[$$index];
                        Badge($$payload5, {
                          variant: "outline",
                          class: "text-xs",
                          children: ($$payload6) => {
                            $$payload6.out += `<!---->${escape_html(field)}`;
                          },
                          $$slots: { default: true }
                        });
                      }
                      $$payload5.out += `<!--]--></div></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div></div></div>`;
                  }
                  $$payload5.out += `<!--]--></div>`;
                }
                $$payload5.out += `<!--]--></div> `;
                Dialog_footer($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      onclick: onClose,
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Close`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { eventId, open, onClose });
  pop();
}
function MaintenanceCreateDialog($$payload, $$props) {
  push();
  var $$store_subs;
  let open = $$props["open"];
  let createForm = $$props["createForm"];
  let createErrors = $$props["createErrors"];
  let serviceOptions = $$props["serviceOptions"];
  let onClose = $$props["onClose"];
  let onSubmit = $$props["onSubmit"];
  let resetForm = $$props["resetForm"];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-[600px]",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Schedule Maintenance`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Create a new scheduled maintenance event. This will be displayed on the system status page.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <form method="POST" action="?/create" id="create-maintenance-form"><input type="hidden" name="affectedServices"${attr("value", store_get($$store_subs ??= {}, "$createForm", createForm).affectedServices)}/> <div class="grid gap-4 py-4"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "title",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Title`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "title",
              placeholder: "Database Maintenance",
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).title;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).title = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$createErrors", createErrors).title) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$createErrors", createErrors).title)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "description",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Description`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).description;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).description = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$createErrors", createErrors).description) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$createErrors", createErrors).description)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "startTime",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Start Time`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "startTime",
              type: "datetime-local",
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).startTime;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).startTime = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$createErrors", createErrors).startTime) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$createErrors", createErrors).startTime)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "endTime",
              children: ($$payload5) => {
                $$payload5.out += `<!---->End Time`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "endTime",
              type: "datetime-local",
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).endTime;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).endTime = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$createErrors", createErrors).endTime) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$createErrors", createErrors).endTime)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> `;
            if (store_get($$store_subs ??= {}, "$createForm", createForm).startTime && store_get($$store_subs ??= {}, "$createForm", createForm).endTime) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="mt-2">`;
              Label($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Progress`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> `;
              StatusBar($$payload4, {
                startTime: store_get($$store_subs ??= {}, "$createForm", createForm).startTime,
                endTime: store_get($$store_subs ??= {}, "$createForm", createForm).endTime,
                status: store_get($$store_subs ??= {}, "$createForm", createForm).status
              });
              $$payload4.out += `<!----></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "status",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Status`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex items-center gap-2">`;
            Root$2($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).status;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).status = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select status" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_item($$payload6, {
                      value: "scheduled",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Scheduled`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "in-progress",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->In Progress`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "completed",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Completed`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "cancelled",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Cancelled`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            StatusTag($$payload4, {
              status: store_get($$store_subs ??= {}, "$createForm", createForm).status
            });
            $$payload4.out += `<!----></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "severity",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Severity`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex items-center gap-2">`;
            Root$2($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).severity;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).severity = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select severity" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_item($$payload6, {
                      value: "info",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Information`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "maintenance",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Maintenance`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "minor",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Minor Outage`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "major",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Major Outage`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "critical",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Critical Outage`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            SeverityBadge($$payload4, {
              severity: store_get($$store_subs ??= {}, "$createForm", createForm).severity
            });
            $$payload4.out += `<!----></div></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "affectedServices",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Affected Services`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div>`;
            Root$2($$payload4, {
              type: "single",
              onValueChange: (value) => {
                if (value) {
                  store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).affectedServices = [value]);
                } else {
                  store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).affectedServices = [serviceOptions[0].value]);
                }
              },
              get value() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).affectedServices[0];
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).affectedServices[0] = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select a service" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(serviceOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let option = each_array[$$index];
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> `;
            if (store_get($$store_subs ??= {}, "$createErrors", createErrors).affectedServices) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$createErrors", createErrors).affectedServices)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <p class="text-muted-foreground text-xs">Select the service that will be affected by this maintenance</p></div> <div class="flex items-center space-x-2">`;
            Checkbox($$payload4, {
              id: "sendNotification",
              name: "sendNotification",
              get checked() {
                return store_get($$store_subs ??= {}, "$createForm", createForm).sendNotification;
              },
              set checked($$value) {
                store_mutate($$store_subs ??= {}, "$createForm", createForm, store_get($$store_subs ??= {}, "$createForm", createForm).sendNotification = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            Label($$payload4, {
              for: "sendNotification",
              class: "text-sm font-normal",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Send notification to all users`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: () => {
                    resetForm();
                    onClose();
                  },
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "submit",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Create`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    open,
    createForm,
    createErrors,
    serviceOptions,
    onClose,
    onSubmit,
    resetForm
  });
  pop();
}
function MaintenanceEditDialog($$payload, $$props) {
  push();
  var $$store_subs;
  let open = $$props["open"];
  let editForm = $$props["editForm"];
  let editErrors = $$props["editErrors"];
  let serviceOptions = $$props["serviceOptions"];
  let eventHistory = $$props["eventHistory"];
  let onClose = $$props["onClose"];
  let onSubmit = $$props["onSubmit"];
  let resetForm = $$props["resetForm"];
  let onOpenHistory = $$props["onOpenHistory"];
  let onOpenAddUpdate = $$props["onOpenAddUpdate"];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-[600px]",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Edit Maintenance Event`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Update the maintenance event details.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <form method="POST" action="?/update" id="edit-maintenance-form"><input type="hidden" name="id"${attr("value", store_get($$store_subs ??= {}, "$editForm", editForm).id)}/> <input type="hidden" name="affectedServices"${attr("value", store_get($$store_subs ??= {}, "$editForm", editForm).affectedServices)}/> <div class="grid gap-4 py-4"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-title",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Title`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "edit-title",
              placeholder: "Database Maintenance",
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).title;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).title = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$editErrors", editErrors).title) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$editErrors", editErrors).title)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-description",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Description`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).description;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).description = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$editErrors", editErrors).description) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$editErrors", editErrors).description)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-startTime",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Start Time`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "edit-startTime",
              type: "datetime-local",
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).startTime;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).startTime = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$editErrors", editErrors).startTime) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$editErrors", editErrors).startTime)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-endTime",
              children: ($$payload5) => {
                $$payload5.out += `<!---->End Time`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              id: "edit-endTime",
              type: "datetime-local",
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).endTime;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).endTime = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$editErrors", editErrors).endTime) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$editErrors", editErrors).endTime)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> <div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-status",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Current Status`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex items-center gap-2">`;
            Root$2($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).status;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).status = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select status" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_item($$payload6, {
                      value: "scheduled",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Scheduled`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "in-progress",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->In Progress`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "completed",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Completed`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "cancelled",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Cancelled`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            StatusTag($$payload4, {
              status: store_get($$store_subs ??= {}, "$editForm", editForm).status
            });
            $$payload4.out += `<!----></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-severity",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Severity`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex items-center gap-2">`;
            Root$2($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).severity;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).severity = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select severity" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_item($$payload6, {
                      value: "info",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Information`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "maintenance",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Maintenance`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "minor",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Minor Outage`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "major",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Major Outage`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "critical",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Critical Outage`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            SeverityBadge($$payload4, {
              severity: store_get($$store_subs ??= {}, "$editForm", editForm).severity
            });
            $$payload4.out += `<!----></div></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "edit-affectedServices",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Affected Services`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Root$2($$payload4, {
              type: "single",
              onValueChange: (value) => {
                if (value) {
                  store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).affectedServices = [value]);
                } else {
                  store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).affectedServices = [serviceOptions[0].value]);
                }
              },
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).affectedServices[0];
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).affectedServices[0] = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select a service" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(serviceOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let option = each_array[$$index];
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$editErrors", editErrors).affectedServices) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$editErrors", editErrors).affectedServices)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="flex items-center justify-between"><h3 class="text-sm font-medium">Comments &amp; Status Updates</h3> `;
            Button($$payload4, {
              type: "button",
              variant: "outline",
              class: "gap-2",
              onclick: onOpenAddUpdate,
              children: ($$payload5) => {
                Message_square($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> Add Status Update`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> `;
            Scroll_area($$payload4, {
              orientation: "vertical",
              class: "!mb-0 flex h-[125px] flex-col space-y-4",
              children: ($$payload5) => {
                if (eventHistory && eventHistory.length > 0) {
                  $$payload5.out += "<!--[-->";
                  const each_array_1 = ensure_array_like(eventHistory);
                  $$payload5.out += `<div class="max-h-60 rounded-md border p-3"><div class="space-y-3"><!--[-->`;
                  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                    let item = each_array_1[$$index_1];
                    $$payload5.out += `<div class="relative border-l-2 border-gray-200 pl-4 dark:border-gray-700"><div class="absolute -left-1.5 top-1 h-3 w-3 rounded-full bg-gray-200 dark:bg-gray-700"></div> <div class="flex items-center justify-between"><div class="flex items-center gap-2">`;
                    if (item.changeType === "status_change" && item.newStatus) {
                      $$payload5.out += "<!--[-->";
                      StatusTag($$payload5, { status: item.newStatus });
                    } else if (item.changeType === "comment") {
                      $$payload5.out += "<!--[1-->";
                      $$payload5.out += `<span class="text-xs font-medium">Comment</span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<span class="text-xs font-medium">Update</span>`;
                    }
                    $$payload5.out += `<!--]--> <p class="text-xs font-medium">${escape_html(new Date(item.createdAt).toLocaleString("en-US", {
                      month: "short",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit"
                    }))}</p></div></div> `;
                    if (item.comment) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<p class="mt-1 text-sm">${escape_html(item.comment)}</p>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div>`;
                  }
                  $$payload5.out += `<!--]--></div></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="mt-4 flex items-center space-x-2">`;
            Checkbox($$payload4, {
              id: "edit-sendNotification",
              get checked() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).sendNotification;
              },
              set checked($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).sendNotification = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            Label($$payload4, {
              for: "edit-sendNotification",
              class: "text-sm font-normal",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Send notification to all users about this update`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<div class="flex w-full items-center justify-between">`;
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  size: "sm",
                  class: "gap-1",
                  onclick: onOpenHistory,
                  children: ($$payload6) => {
                    History($$payload6, { class: "h-4 w-4" });
                    $$payload6.out += `<!----> View History`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="flex gap-2">`;
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: () => {
                    resetForm();
                    onClose();
                  },
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "submit",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Update`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----></div></div>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    open,
    editForm,
    editErrors,
    serviceOptions,
    eventHistory,
    onClose,
    onSubmit,
    resetForm,
    onOpenHistory,
    onOpenAddUpdate
  });
  pop();
}
function MaintenanceDeleteDialog($$payload, $$props) {
  push();
  var $$store_subs;
  let open = $$props["open"];
  let deleteForm = $$props["deleteForm"];
  let selectedEvent = $$props["selectedEvent"];
  let onClose = $$props["onClose"];
  let onSubmit = $$props["onSubmit"];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Delete Maintenance Event`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Are you sure you want to delete this maintenance event? This action cannot be undone.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (selectedEvent) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<form method="POST" action="?/delete" id="delete-maintenance-form"><input type="hidden" name="id"${attr("value", store_get($$store_subs ??= {}, "$deleteForm", deleteForm).id)}/> <div class="py-4"><p class="font-medium">${escape_html(selectedEvent.title)}</p> <p class="text-muted-foreground text-sm">${escape_html(selectedEvent.description)}</p></div> `;
              Dialog_footer($$payload4, {
                children: ($$payload5) => {
                  Button($$payload5, {
                    type: "button",
                    variant: "outline",
                    onclick: onClose,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Cancel`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Button($$payload5, {
                    type: "submit",
                    variant: "destructive",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Delete`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></form>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    open,
    deleteForm,
    selectedEvent,
    onClose,
    onSubmit
  });
  pop();
}
function MaintenanceCommentDialog($$payload, $$props) {
  push();
  let actionTitle, statusTag, statusText, buttonText, currentDate;
  let open = $$props["open"];
  let commentAction = $$props["commentAction"];
  let commentEvent = $$props["commentEvent"];
  let commentText = $$props["commentText"];
  let onClose = $$props["onClose"];
  let onSubmit = $$props["onSubmit"];
  let onCommentTextChange = $$props["onCommentTextChange"];
  let sendNotification = false;
  onCommentTextChange(commentText);
  actionTitle = commentAction === "start" ? "Start Maintenance" : "Complete Maintenance";
  statusTag = commentAction === "start" ? "in-progress" : "resolved";
  statusText = commentAction === "start" ? "In Progress" : "Resolved";
  buttonText = commentAction === "start" ? "Start Maintenance" : "Complete Maintenance";
  currentDate = (/* @__PURE__ */ new Date()).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  });
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-[500px]",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html(actionTitle)}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Update the maintenance status and add a comment. This will be recorded in the history.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="py-4"><div class="grid gap-4"><div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2">`;
            Label($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Status Update`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex items-center gap-2">`;
            StatusTag($$payload4, { status: statusTag });
            $$payload4.out += `<!----> <p class="text-sm">${escape_html(statusText)}</p></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "comment-date",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Comment Date`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="bg-muted flex h-9 items-center rounded-md border px-3 py-1">${escape_html(currentDate)}</div> <p class="text-muted-foreground text-xs">Current date and time will be used for this comment</p></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "comment",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Comment`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return commentText;
              },
              set value($$value) {
                commentText = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> <p class="text-muted-foreground text-xs">This comment will be recorded in the maintenance history along with the status change</p></div> <div class="mt-4 flex items-center space-x-2">`;
            Checkbox($$payload4, {
              id: "comment-sendNotification",
              get checked() {
                return sendNotification;
              },
              set checked($$value) {
                sendNotification = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            Label($$payload4, {
              for: "comment-sendNotification",
              class: "text-sm font-normal",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Send notification to all users about this update`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div></div> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: onClose,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "button",
                  onclick: onSubmit,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html(buttonText)}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, {
    open,
    commentAction,
    commentEvent,
    commentText,
    onClose,
    onSubmit,
    onCommentTextChange
  });
  pop();
}
function MaintenanceUpdateDialog($$payload, $$props) {
  push();
  var $$store_subs;
  let currentDate;
  let open = $$props["open"];
  let editForm = $$props["editForm"];
  let onClose = $$props["onClose"];
  let onSubmit = $$props["onSubmit"];
  currentDate = (/* @__PURE__ */ new Date()).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  });
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> `;
        Dialog_content($$payload3, {
          class: "sm:max-w-[500px]",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Add Status Update`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Update the maintenance status and add a comment. This will be recorded in the history.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="py-4"><div class="grid gap-4"><div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="grid gap-2">`;
            Label($$payload4, {
              for: "update-status",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Update Status`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Root$2($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).commentStatus;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).commentStatus = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_value($$payload6, { placeholder: "Select status update" });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Select_content($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    Select_item($$payload6, {
                      value: "investigating",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Investigating`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "identified",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Issue Identified`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "in-progress",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->In Progress`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "monitoring",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Monitoring`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_item($$payload6, {
                      value: "resolved",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Resolved`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "update-date",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Comment Date`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="bg-muted flex h-9 items-center rounded-md border px-3 py-1">${escape_html(currentDate)}</div></div></div> <div class="grid gap-2">`;
            Label($$payload4, {
              for: "update-comment",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Comment`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).comment;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).comment = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> <p class="text-muted-foreground text-xs">This comment will be recorded in the maintenance history along with the status update</p></div> <div class="mt-4 flex items-center space-x-2">`;
            Checkbox($$payload4, {
              id: "update-sendNotification",
              get checked() {
                return store_get($$store_subs ??= {}, "$editForm", editForm).sendNotification;
              },
              set checked($$value) {
                store_mutate($$store_subs ??= {}, "$editForm", editForm, store_get($$store_subs ??= {}, "$editForm", editForm).sendNotification = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            Label($$payload4, {
              for: "update-sendNotification",
              class: "text-sm font-normal",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Send notification to all users about this update`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div></div> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: onClose,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "button",
                  onclick: onSubmit,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Add Update`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { open, editForm, onClose, onSubmit });
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let activeTab = "upcoming";
  const serviceOptions = [
    {
      value: "Matches",
      label: "Matches (Job matching and recommendations)"
    },
    {
      value: "Jobs",
      label: "Jobs (Job search and listings)"
    },
    {
      value: "Tracker",
      label: "Tracker (Application tracking)"
    },
    {
      value: "Documents",
      label: "Documents (Resume and document management)"
    },
    {
      value: "Automation",
      label: "Automation (Automated job application tools)"
    },
    {
      value: "System",
      label: "System (Core system services)"
    },
    {
      value: "Website",
      label: "Website (Website and user interface)"
    }
  ];
  const {
    form: createForm,
    reset: resetCreateForm,
    errors: createErrors
  } = superForm(data.createForm, {
    resetForm: true,
    validationMethod: "submit-only",
    onResult({ result }) {
      if (result.type === "success") {
        dialogState.isCreateDialogOpen = false;
        toast.success("Maintenance event created successfully");
        invalidateAll();
      } else if (result.type === "failure") {
        toast.error(typeof result.data === "string" ? result.data : "Failed to create maintenance event");
      }
    },
    dataType: "json"
  });
  const {
    form: editForm,
    reset: resetEditForm,
    errors: editErrors
  } = superForm(data.editForm, {
    resetForm: true,
    validationMethod: "submit-only",
    onResult: ({ result }) => {
      if (result.type === "success") {
        dialogState.isEditDialogOpen = false;
        toast.success("Maintenance event updated successfully");
        invalidateAll();
      } else if (result.type === "failure") {
        toast.error(typeof result.data === "string" ? result.data : "Failed to update maintenance event");
      }
    },
    dataType: "json"
  });
  const { form: deleteForm } = superForm(data.deleteForm, {
    resetForm: true,
    onResult: ({ result }) => {
      if (result.type === "success") {
        dialogState.isDeleteDialogOpen = false;
        toast.success("Maintenance event deleted successfully");
        invalidateAll();
      } else if (result.type === "failure") {
        toast.error(typeof result.data === "string" ? result.data : "Failed to delete maintenance event");
      }
    }
  });
  const dialogState = {
    isCreateDialogOpen: false,
    isEditDialogOpen: false,
    isDeleteDialogOpen: false,
    isHistoryDialogOpen: false,
    isAddUpdateDialogOpen: false,
    isCommentDialogOpen: false
  };
  const commentState = {
    commentAction: "start",
    commentEvent: null,
    commentText: "",
    sendNotification: false
  };
  const eventState = {
    selectedEvent: null,
    selectedEventId: "",
    eventHistory: []
  };
  store_set(createForm, {
    title: "",
    description: "",
    startTime: "",
    endTime: "",
    status: "scheduled",
    severity: "maintenance",
    affectedServices: [serviceOptions[0].value],
    sendNotification: false
  });
  store_set(editForm, {
    id: "",
    title: "",
    description: "",
    startTime: "",
    endTime: "",
    status: "scheduled",
    severity: "maintenance",
    affectedServices: [serviceOptions[0].value],
    sendNotification: false,
    comment: "",
    commentStatus: "investigating"
  });
  function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  function getStatusBadgeVariant(status) {
    switch (status) {
      case "scheduled":
        return "secondary";
      case "in-progress":
        return "warning";
      case "completed":
        return "success";
      case "cancelled":
        return "destructive";
      default:
        return "outline";
    }
  }
  function getSeverityBadgeVariant(severity) {
    switch (severity) {
      case "info":
        return "secondary";
      case "warning":
        return "warning";
      case "critical":
        return "destructive";
      default:
        return "outline";
    }
  }
  function getStatusIcon(status) {
    switch (status) {
      case "scheduled":
        return Clock;
      case "in-progress":
        return Triangle_alert;
      case "completed":
        return Circle_check_big;
      case "cancelled":
        return Circle_x;
      default:
        return Triangle_alert;
    }
  }
  function openEditDialog(event) {
    eventState.selectedEvent = event;
    eventState.selectedEventId = event.id;
    store_set(editForm, {
      id: event.id,
      title: event.title,
      description: event.description,
      startTime: new Date(event.startTime).toISOString().slice(0, 16),
      endTime: new Date(event.endTime).toISOString().slice(0, 16),
      status: event.status,
      severity: event.severity,
      // Ensure we're passing the full array of affected services
      affectedServices: Array.isArray(event.affectedServices) && event.affectedServices.length > 0 ? event.affectedServices : [serviceOptions[0].value],
      // Default to first service if none provided
      sendNotification: false,
      comment: "",
      commentStatus: "investigating"
      // Default comment status
    });
    fetchEventHistory(event.id);
    dialogState.isEditDialogOpen = true;
  }
  function openDeleteDialog(event) {
    eventState.selectedEvent = event;
    store_set(deleteForm, { id: event.id });
    dialogState.isDeleteDialogOpen = true;
  }
  function openHistoryDialog(event) {
    eventState.selectedEventId = event.id;
    dialogState.isHistoryDialogOpen = true;
    fetchEventHistory(event.id);
  }
  async function fetchEventHistory(eventId) {
    try {
      const response = await fetch(`/api/maintenance/${eventId}/history`);
      if (!response.ok) {
        throw new Error(`Failed to fetch history: ${response.status}`);
      }
      const data2 = await response.json();
      eventState.eventHistory = data2;
    } catch (error) {
      console.error("Error fetching event history:", error);
      toast.error("Failed to load event history");
    }
  }
  function openCommentDialog(action, event) {
    commentState.commentAction = action;
    commentState.commentEvent = event;
    commentState.commentText = "";
    dialogState.isCommentDialogOpen = true;
  }
  function startMaintenance(event) {
    openCommentDialog("start", event);
  }
  async function submitStartMaintenance() {
    try {
      if (!commentState.commentEvent) return;
      const response = await fetch("/api/maintenance", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "same-origin",
        body: JSON.stringify({
          id: commentState.commentEvent.id,
          title: commentState.commentEvent.title,
          description: commentState.commentEvent.description,
          startTime: commentState.commentEvent.startTime,
          endTime: commentState.commentEvent.endTime,
          status: "in-progress",
          severity: commentState.commentEvent.severity,
          affectedServices: commentState.commentEvent.affectedServices,
          sendNotification: commentState.sendNotification,
          comment: commentState.commentText || void 0,
          commentStatus: "in-progress"
          // Use the swimlane status
        })
      });
      const result = await response.json();
      if (response.ok) {
        toast.success("Maintenance started successfully");
        dialogState.isCommentDialogOpen = false;
        invalidateAll();
      } else {
        toast.error(result.error || "Failed to start maintenance");
      }
    } catch (error) {
      toast.error("An error occurred while starting maintenance");
      console.error("Error starting maintenance:", error);
    }
  }
  function completeMaintenance(event) {
    openCommentDialog("complete", event);
  }
  async function submitCompleteMaintenance() {
    try {
      if (!commentState.commentEvent) return;
      const response = await fetch("/api/maintenance", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "same-origin",
        body: JSON.stringify({
          id: commentState.commentEvent.id,
          title: commentState.commentEvent.title,
          description: commentState.commentEvent.description,
          startTime: commentState.commentEvent.startTime,
          endTime: commentState.commentEvent.endTime,
          status: "completed",
          severity: commentState.commentEvent.severity,
          affectedServices: commentState.commentEvent.affectedServices,
          sendNotification: commentState.sendNotification,
          comment: commentState.commentText || void 0,
          commentStatus: "resolved"
          // Use the swimlane status
        })
      });
      const result = await response.json();
      if (response.ok) {
        toast.success("Maintenance completed successfully");
        dialogState.isCommentDialogOpen = false;
        invalidateAll();
      } else {
        toast.error(result.error || "Failed to complete maintenance");
      }
    } catch (error) {
      toast.error("An error occurred while completing maintenance");
      console.error("Error completing maintenance:", error);
    }
  }
  function handleCommentTextChange(text) {
    commentState.commentText = text;
    const checkbox = document.getElementById("comment-sendNotification");
    commentState.sendNotification = checkbox?.checked || false;
  }
  function handleAddUpdateSubmit() {
    const form = document.getElementById("edit-maintenance-form");
    if (form) {
      try {
        form.dispatchEvent(new Event("submit", { cancelable: true }));
        dialogState.isAddUpdateDialogOpen = false;
      } catch (error) {
        console.error("Error submitting form:", error);
      }
    }
  }
  function handleCommentSubmit() {
    if (commentState.commentAction === "start") {
      submitStartMaintenance();
    } else if (commentState.commentAction === "complete") {
      submitCompleteMaintenance();
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, { title: "Maintenance Management - Hirli" });
    $$payload2.out += `<!----> <div class="border-border flex flex-col gap-1 border-b p-4"><div class="flex items-center justify-between"><h1 class="text-2xl font-bold">Maintenance Management</h1> `;
    Button($$payload2, {
      variant: "outline",
      onclick: () => {
        dialogState.isCreateDialogOpen = true;
      },
      children: ($$payload3) => {
        Plus($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Schedule Maintenance`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> `;
    Root($$payload2, {
      get value() {
        return activeTab;
      },
      set value($$value) {
        activeTab = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<div class="border-border border-b p-0">`;
        Tabs_list($$payload3, {
          class: "flex flex-row gap-2 divide-x",
          children: ($$payload4) => {
            Tabs_trigger($$payload4, {
              value: "upcoming",
              class: "flex-1 border-none",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Upcoming`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Tabs_trigger($$payload4, {
              value: "past",
              class: "flex-1 border-none",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Past`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Tabs_trigger($$payload4, {
              value: "all",
              class: "flex-1 border-none",
              children: ($$payload5) => {
                $$payload5.out += `<!---->All Events`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> `;
        Tabs_content($$payload3, {
          value: "upcoming",
          children: ($$payload4) => {
            Card($$payload4, {
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Upcoming Maintenance`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Scheduled and in-progress maintenance events`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    if (data.upcomingEvents.length === 0) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No upcoming maintenance events</p></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      const each_array = ensure_array_like(data.upcomingEvents);
                      $$payload6.out += `<div class="space-y-4"><!--[-->`;
                      for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
                        let event = each_array[$$index_1];
                        $$payload6.out += `<div class="flex items-start justify-between rounded-lg border p-4"><div class="flex-1"><div class="mb-2 flex items-center gap-2"><h3 class="font-medium">${escape_html(event.title)}</h3> `;
                        Badge($$payload6, {
                          variant: getStatusBadgeVariant(event.status),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            {
                              $$payload7.out += `<!---->`;
                              getStatusIcon(event.status)?.($$payload7, { class: "mr-1 h-3 w-3" });
                              $$payload7.out += `<!---->`;
                            }
                            $$payload7.out += `<!----> ${escape_html(event.status.charAt(0).toUpperCase() + event.status.slice(1))}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Badge($$payload6, {
                          variant: getSeverityBadgeVariant(event.severity),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->${escape_html(event.severity.charAt(0).toUpperCase() + event.severity.slice(1))}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></div> <p class="text-muted-foreground mb-2 text-sm">${escape_html(event.description)}</p> <div class="grid grid-cols-1 gap-2 sm:grid-cols-2"><div><p class="text-xs font-medium">Start Time</p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(event.startTime))}</p></div> <div><p class="text-xs font-medium">End Time</p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(event.endTime))}</p></div></div> `;
                        if (event.affectedServices && event.affectedServices.length > 0) {
                          $$payload6.out += "<!--[-->";
                          const each_array_1 = ensure_array_like(event.affectedServices);
                          $$payload6.out += `<div class="mt-2"><p class="text-xs font-medium">Affected Services</p> <div class="mt-1 flex flex-wrap gap-1"><!--[-->`;
                          for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                            let service = each_array_1[$$index];
                            Badge($$payload6, {
                              variant: "outline",
                              class: "text-xs",
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(service)}`;
                              },
                              $$slots: { default: true }
                            });
                          }
                          $$payload6.out += `<!--]--></div></div>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--></div> <div class="ml-4 flex flex-col gap-2">`;
                        if (event.status === "scheduled") {
                          $$payload6.out += "<!--[-->";
                          Button($$payload6, {
                            variant: "outline",
                            size: "sm",
                            onclick: () => startMaintenance(event),
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Start`;
                            },
                            $$slots: { default: true }
                          });
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> `;
                        if (event.status === "in-progress") {
                          $$payload6.out += "<!--[-->";
                          Button($$payload6, {
                            variant: "outline",
                            size: "sm",
                            onclick: () => completeMaintenance(event),
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Complete`;
                            },
                            $$slots: { default: true }
                          });
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> `;
                        Button($$payload6, {
                          variant: "ghost",
                          size: "icon",
                          onclick: () => openEditDialog(event),
                          children: ($$payload7) => {
                            Square_pen($$payload7, { class: "h-4 w-4" });
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Button($$payload6, {
                          variant: "ghost",
                          size: "icon",
                          onclick: () => openDeleteDialog(event),
                          children: ($$payload7) => {
                            Trash_2($$payload7, { class: "h-4 w-4" });
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></div></div>`;
                      }
                      $$payload6.out += `<!--]--></div>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Tabs_content($$payload3, {
          value: "past",
          children: ($$payload4) => {
            Card($$payload4, {
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Past Maintenance`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Completed and cancelled maintenance events`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    if (data.pastEvents.length === 0) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No past maintenance events</p></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      const each_array_2 = ensure_array_like(data.pastEvents);
                      $$payload6.out += `<div class="space-y-4"><!--[-->`;
                      for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                        let event = each_array_2[$$index_2];
                        $$payload6.out += `<div class="flex items-start justify-between rounded-lg border p-4"><div class="flex-1"><div class="mb-2 flex items-center gap-2"><h3 class="font-medium">${escape_html(event.title)}</h3> `;
                        Badge($$payload6, {
                          variant: getStatusBadgeVariant(event.status),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            {
                              $$payload7.out += `<!---->`;
                              getStatusIcon(event.status)?.($$payload7, { class: "mr-1 h-3 w-3" });
                              $$payload7.out += `<!---->`;
                            }
                            $$payload7.out += `<!----> ${escape_html(event.status.charAt(0).toUpperCase() + event.status.slice(1))}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></div> <p class="text-muted-foreground mb-2 text-sm">${escape_html(event.description)}</p> <div class="grid grid-cols-1 gap-2 sm:grid-cols-2"><div><p class="text-xs font-medium">Start Time</p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(event.startTime))}</p></div> <div><p class="text-xs font-medium">End Time</p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(event.endTime))}</p></div></div></div></div>`;
                      }
                      $$payload6.out += `<!--]--></div>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Tabs_content($$payload3, {
          value: "all",
          children: ($$payload4) => {
            Card($$payload4, {
              children: ($$payload5) => {
                Card_header($$payload5, {
                  children: ($$payload6) => {
                    Card_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->All Maintenance Events`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Card_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Complete history of maintenance events`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Card_content($$payload5, {
                  children: ($$payload6) => {
                    if (data.maintenanceEvents.length === 0) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="rounded-lg border p-6 text-center"><p class="text-muted-foreground">No maintenance events found</p></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      const each_array_3 = ensure_array_like(data.maintenanceEvents);
                      $$payload6.out += `<div class="space-y-4"><!--[-->`;
                      for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                        let event = each_array_3[$$index_3];
                        $$payload6.out += `<div class="flex items-start justify-between rounded-lg border p-4"><div class="flex-1"><div class="mb-2 flex items-center gap-2"><h3 class="font-medium">${escape_html(event.title)}</h3> `;
                        Badge($$payload6, {
                          variant: getStatusBadgeVariant(event.status),
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->`;
                            {
                              $$payload7.out += `<!---->`;
                              getStatusIcon(event.status)?.($$payload7, { class: "mr-1 h-3 w-3" });
                              $$payload7.out += `<!---->`;
                            }
                            $$payload7.out += `<!----> ${escape_html(event.status.charAt(0).toUpperCase() + event.status.slice(1))}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></div> <p class="text-muted-foreground mb-2 text-sm">${escape_html(event.description)}</p> <div class="grid grid-cols-1 gap-2 sm:grid-cols-2"><div><p class="text-xs font-medium">Start Time</p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(event.startTime))}</p></div> <div><p class="text-xs font-medium">End Time</p> <p class="text-muted-foreground text-xs">${escape_html(formatDate(event.endTime))}</p></div></div></div> <div class="ml-4 flex flex-col gap-2">`;
                        if (event.status === "scheduled") {
                          $$payload6.out += "<!--[-->";
                          Button($$payload6, {
                            variant: "outline",
                            size: "sm",
                            onclick: () => startMaintenance(event),
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Start`;
                            },
                            $$slots: { default: true }
                          });
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> `;
                        if (event.status === "in-progress") {
                          $$payload6.out += "<!--[-->";
                          Button($$payload6, {
                            variant: "outline",
                            size: "sm",
                            onclick: () => completeMaintenance(event),
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Complete`;
                            },
                            $$slots: { default: true }
                          });
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> `;
                        Button($$payload6, {
                          variant: "ghost",
                          size: "icon",
                          onclick: () => openEditDialog(event),
                          children: ($$payload7) => {
                            Square_pen($$payload7, { class: "h-4 w-4" });
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Button($$payload6, {
                          variant: "ghost",
                          size: "icon",
                          onclick: () => openHistoryDialog(event),
                          children: ($$payload7) => {
                            History($$payload7, { class: "h-4 w-4" });
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Button($$payload6, {
                          variant: "ghost",
                          size: "icon",
                          onclick: () => openDeleteDialog(event),
                          children: ($$payload7) => {
                            Trash_2($$payload7, { class: "h-4 w-4" });
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----></div></div>`;
                      }
                      $$payload6.out += `<!--]--></div>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    MaintenanceCreateDialog($$payload2, {
      open: dialogState.isCreateDialogOpen,
      createForm,
      createErrors,
      serviceOptions,
      onClose: () => dialogState.isCreateDialogOpen = false,
      onSubmit: () => {
        const form = document.getElementById("create-maintenance-form");
        if (form) {
          try {
            form.dispatchEvent(new Event("submit", { cancelable: true }));
          } catch (error) {
            console.error("Error submitting form:", error);
          }
        }
      },
      resetForm: resetCreateForm
    });
    $$payload2.out += `<!----> `;
    MaintenanceEditDialog($$payload2, {
      open: dialogState.isEditDialogOpen,
      editForm,
      editErrors,
      serviceOptions,
      eventHistory: eventState.eventHistory,
      onClose: () => dialogState.isEditDialogOpen = false,
      onSubmit: () => {
        const form = document.getElementById("edit-maintenance-form");
        if (form) {
          try {
            form.dispatchEvent(new Event("submit", { cancelable: true }));
          } catch (error) {
            console.error("Error submitting form:", error);
          }
        }
      },
      resetForm: resetEditForm,
      onOpenHistory: () => {
        dialogState.isHistoryDialogOpen = true;
        dialogState.isEditDialogOpen = false;
      },
      onOpenAddUpdate: () => dialogState.isAddUpdateDialogOpen = true
    });
    $$payload2.out += `<!----> `;
    MaintenanceDeleteDialog($$payload2, {
      open: dialogState.isDeleteDialogOpen,
      deleteForm,
      selectedEvent: eventState.selectedEvent,
      onClose: () => dialogState.isDeleteDialogOpen = false,
      onSubmit: () => {
        const form = document.getElementById("delete-maintenance-form");
        if (form) {
          try {
            form.dispatchEvent(new Event("submit", { cancelable: true }));
          } catch (error) {
            console.error("Error submitting form:", error);
          }
        }
      }
    });
    $$payload2.out += `<!----> `;
    MaintenanceHistoryDialog($$payload2, {
      eventId: eventState.selectedEventId,
      onClose: () => dialogState.isHistoryDialogOpen = false,
      get open() {
        return dialogState.isHistoryDialogOpen;
      },
      set open($$value) {
        dialogState.isHistoryDialogOpen = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    MaintenanceUpdateDialog($$payload2, {
      open: dialogState.isAddUpdateDialogOpen,
      editForm,
      onClose: () => dialogState.isAddUpdateDialogOpen = false,
      onSubmit: handleAddUpdateSubmit
    });
    $$payload2.out += `<!----> `;
    MaintenanceCommentDialog($$payload2, {
      open: dialogState.isCommentDialogOpen,
      commentAction: commentState.commentAction,
      commentEvent: commentState.commentEvent,
      commentText: commentState.commentText,
      onClose: () => dialogState.isCommentDialogOpen = false,
      onSubmit: handleCommentSubmit,
      onCommentTextChange: handleCommentTextChange
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BgHaJFIY.js.map
