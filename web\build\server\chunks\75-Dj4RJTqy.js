import { e as error } from './index-Ddp2AB5f.js';
import { d as getHelpArticlesByCategory, g as getHelpArticles } from './client2-BLTPQNYX.js';
import '@sanity/client';

const load = async ({ params }) => {
  const { slug } = params;
  try {
    const categoryArticles = await getHelpArticlesByCategory(slug);
    if (!categoryArticles || categoryArticles.length === 0) {
      throw error(404, "Category not found or has no articles");
    }
    const categoryName = getCategoryName(slug);
    const categoryIcon = getCategoryIcon(slug);
    const allArticles = await getHelpArticles();
    const articlesByCategory = allArticles.reduce((acc, article) => {
      if (!acc[article.category]) {
        acc[article.category] = {
          name: getCategoryName(article.category),
          slug: article.category,
          icon: getCategoryIcon(article.category),
          articles: []
        };
      }
      acc[article.category].articles.push({
        id: article._id,
        title: article.title,
        slug: article.slug.current
      });
      return acc;
    }, {});
    const categories = Object.values(articlesByCategory).sort(
      (a, b) => a.name.localeCompare(b.name)
    );
    const formattedArticles = categoryArticles.map((article) => ({
      ...article,
      id: article._id,
      slug: article.slug.current,
      excerpt: article.description,
      category: {
        id: article.category,
        name: getCategoryName(article.category),
        slug: article.category,
        icon: getCategoryIcon(article.category)
      },
      tags: article.tags?.map((tag) => ({
        id: tag,
        name: tag,
        slug: tag.toLowerCase().replace(/\s+/g, "-")
      })) || []
    }));
    return {
      category: {
        name: categoryName,
        slug,
        icon: categoryIcon
      },
      articles: formattedArticles,
      categories
    };
  } catch (err) {
    console.error(`Error loading category ${slug}:`, err);
    throw error(404, "Category not found");
  }
};
function getCategoryName(slug) {
  const categoryMap = {
    "getting-started": "Getting Started",
    "auto-apply": "Using Auto Apply",
    "account-billing": "Account & Billing",
    troubleshooting: "Troubleshooting",
    "privacy-security": "Privacy & Security"
  };
  return categoryMap[slug] || slug;
}
function getCategoryIcon(slug) {
  const iconMap = {
    "getting-started": "BookOpen",
    "auto-apply": "FileText",
    "account-billing": "CreditCard",
    troubleshooting: "HelpCircle",
    "privacy-security": "Shield"
  };
  return iconMap[slug] || "HelpCircle";
}

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 75;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DLfbM2e4.js')).default;
const server_id = "src/routes/help/category/[slug]/+page.server.ts";
const imports = ["_app/immutable/nodes/75.I2nk7XX6.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/TEtCs8J_.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/CfcZq63z.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/jRvHGFcG.js","_app/immutable/chunks/CGtH72Kl.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/3WmhYGjL.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/CBdr9r-W.js","_app/immutable/chunks/BPr9JIwg.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/C8-oZ3V_.js","_app/immutable/chunks/CsOU4yHs.js","_app/immutable/chunks/BJwwRUaF.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/CxmsTEaf.js","_app/immutable/chunks/rNI1Perp.js","_app/immutable/chunks/DDpHsKo4.js","_app/immutable/chunks/B-l1ubNa.js","_app/immutable/chunks/DVGNPJty.js","_app/immutable/chunks/Ce6y1v79.js","_app/immutable/chunks/BV675lZR.js","_app/immutable/chunks/B_tyjpYb.js","_app/immutable/chunks/mCB4pHNc.js","_app/immutable/chunks/BnV6AXQp.js","_app/immutable/chunks/Ce4BqqU6.js","_app/immutable/chunks/hA0h0kTo.js","_app/immutable/chunks/1gTNXEeM.js","_app/immutable/chunks/C3y1xd2Y.js","_app/immutable/chunks/BM9SsHQg.js","_app/immutable/chunks/eW6QhNR3.js","_app/immutable/chunks/CIPPbbaT.js","_app/immutable/chunks/iTqMWrIH.js","_app/immutable/chunks/DfWpXjG9.js","_app/immutable/chunks/DxcWIogY.js","_app/immutable/chunks/BLiq6Dlm.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/C2AK_5VT.js","_app/immutable/chunks/ITUnHPIu.js","_app/immutable/chunks/DZCYCPd3.js","_app/immutable/chunks/A-1J-2PQ.js","_app/immutable/chunks/whJ0cJ1Q.js","_app/immutable/chunks/Bx0dWF_O.js","_app/immutable/chunks/JqDL1wc2.js","_app/immutable/chunks/CXUk17vb.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/BBNNmnYR.js","_app/immutable/chunks/DkmCSZhC.js","_app/immutable/chunks/-vfp2Q9I.js","_app/immutable/chunks/CKg8MWp_.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/D6Qh9vtB.js","_app/immutable/chunks/BAIxhb6t.js","_app/immutable/chunks/BIQwBPm4.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/BxlgRp1U.js","_app/immutable/chunks/lZwfPN85.js","_app/immutable/chunks/bEtmAhPN.js","_app/immutable/chunks/DLZV8qTT.js","_app/immutable/chunks/Dt_Sfkn6.js","_app/immutable/chunks/BRdyUBC_.js","_app/immutable/chunks/6BxQgNmX.js","_app/immutable/chunks/tr-scC-m.js","_app/immutable/chunks/DdoUfFy4.js","_app/immutable/chunks/8b74MdfD.js","_app/immutable/chunks/zNKWipEG.js","_app/immutable/chunks/6UJoWgvL.js","_app/immutable/chunks/7AwcL9ec.js","_app/immutable/chunks/CY_6SfHi.js","_app/immutable/chunks/BEVim9wJ.js","_app/immutable/chunks/D1zde6Ej.js","_app/immutable/chunks/DQB68x0Z.js","_app/immutable/chunks/CqJi5rQC.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/w9xFoQXV.js","_app/immutable/chunks/CLdCqm7k.js","_app/immutable/chunks/iDciRV2n.js","_app/immutable/chunks/DRGimm5x.js","_app/immutable/chunks/Cl1ZeFOf.js","_app/immutable/chunks/CrpvsheG.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/DHNQRrgO.js","_app/immutable/chunks/CHsAkgDv.js","_app/immutable/chunks/yPulTJ2h.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/DSDNnczY.js","_app/immutable/chunks/BQS6hE8b.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/lirlZJ-b.js","_app/immutable/chunks/2KCyzleV.js","_app/immutable/chunks/aemnuA_0.js","_app/immutable/chunks/BBh-2PfQ.js","_app/immutable/chunks/tjBMsfLi.js","_app/immutable/chunks/DOf_JqyE.js","_app/immutable/chunks/DvO_AOqy.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/BMRJMPdn.js","_app/immutable/chunks/CTQ8y7hr.js","_app/immutable/chunks/BHzYYMdu.js","_app/immutable/chunks/D871oxnv.js","_app/immutable/chunks/BoNCRmBc.js","_app/immutable/chunks/BAawoUIy.js","_app/immutable/chunks/D8pQCLOH.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/DumgozFE.js","_app/immutable/chunks/CYoZicO9.js","_app/immutable/chunks/CZ8wIJN8.js","_app/immutable/chunks/BNVswwUK.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/Bpd96RWU.js","_app/immutable/chunks/Csk_I0QV.js","_app/immutable/chunks/CTO_B1Jk.js","_app/immutable/chunks/G5Oo-PmU.js","_app/immutable/chunks/CzSntoiK.js","_app/immutable/chunks/B_6ivTD3.js","_app/immutable/chunks/BSHZ37s_.js","_app/immutable/chunks/PxawOV43.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/1zwBog76.js","_app/immutable/chunks/BIUPxhhl.js"];
const stylesheets = ["_app/immutable/assets/scroll-area.bHHIbcsu.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=75-Dj4RJTqy.js.map
