import { p as push, V as copy_payload, W as assign_payload, q as pop, M as ensure_array_like, O as escape_html, N as attr } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { R as Root, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { I as Input } from './input-DF0gPqYN.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './index2-Cut0V_vU.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let marketingTemplates = [];
  let audiences = [];
  let selectedTemplate = null;
  let selectedAudience = null;
  let subject = "";
  let title = "";
  let content = "";
  let imageUrl = "";
  let ctaText = "";
  let ctaUrl = "";
  let scheduledAt = "";
  let previewHtml = "";
  let isPreviewLoading = false;
  async function loadPreview() {
    if (!selectedTemplate) {
      toast.error("Please select a template");
      return;
    }
    isPreviewLoading = true;
    try {
      const previewData = {
        title: title || "Newsletter Title",
        content: content || "<p>Newsletter content goes here.</p>",
        imageUrl: imageUrl || "",
        ctaText: ctaText || "",
        ctaUrl: ctaUrl || "",
        firstName: "Preview",
        unsubscribeUrl: "#",
        currentYear: (/* @__PURE__ */ new Date()).getFullYear()
      };
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject || "Email Preview"}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 20px 0; }
            .content { padding: 20px 0; }
            .footer { text-align: center; padding: 20px 0; font-size: 12px; color: #666; border-top: 1px solid #eee; }
            .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            img { max-width: 100%; height: auto; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${previewData.title}</h1>
          </div>
          <div class="content">
            ${previewData.content}
            ${previewData.imageUrl ? `<img src="${previewData.imageUrl}" alt="Newsletter Image" />` : ""}
            ${previewData.ctaText && previewData.ctaUrl ? `<p style="text-align: center; margin-top: 20px;"><a href="${previewData.ctaUrl}" class="button">${previewData.ctaText}</a></p>` : ""}
          </div>
          <div class="footer">
            <p>This is a preview of your ${selectedTemplate.label} email.</p>
            <p>© ${previewData.currentYear} Your Company. All rights reserved.</p>
            <p><a href="${previewData.unsubscribeUrl}">Unsubscribe</a></p>
          </div>
        </body>
        </html>
      `;
      previewHtml = html;
      toast.success("Preview generated");
    } catch (error) {
      console.error("Error generating preview:", error);
      toast.error("Failed to generate preview");
    } finally {
      isPreviewLoading = false;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800"><div class="flex items-center">`;
      Triangle_alert($$payload2, { class: "mr-2 h-5 w-5" });
      $$payload2.out += `<!----> <h3 class="text-sm font-medium">Resend API Key Not Configured</h3></div> <div class="mt-2 text-sm"><p>The Resend API key is not configured. You need to set the RESEND_API_KEY environment
        variable to use audience and broadcast features.</p></div></div>`;
    }
    $$payload2.out += `<!--]--> <div class="grid grid-cols-1 gap-6 md:grid-cols-2"><!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Create New Broadcast`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Send a broadcast email to a selected audience`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<form><div class="space-y-4"><div><label for="templateName" class="mb-1 block text-sm font-medium">Template</label> <!---->`;
            Root($$payload4, {
              selected: selectedTemplate ? {
                value: selectedTemplate.name,
                label: selectedTemplate.label
              } : null,
              onSelectedChange: (selected) => {
                if (selected) {
                  const template = marketingTemplates.find((t) => t.name === selected.value);
                  if (template) {
                    selectedTemplate = template;
                  }
                } else {
                  selectedTemplate = null;
                }
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select template" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(marketingTemplates);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let template = each_array[$$index];
                      $$payload6.out += `<!---->`;
                      Select_item($$payload6, {
                        value: { value: template.name, label: template.label },
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(template.label)}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="templateName"${attr("value", selectedTemplate?.name || "")}/></div> <div><label for="audienceId" class="mb-1 block text-sm font-medium">Audience</label> <!---->`;
            Root($$payload4, {
              selected: selectedAudience ? {
                value: selectedAudience.id,
                label: selectedAudience.name
              } : null,
              onSelectedChange: (selected) => {
                if (selected) {
                  const audience = audiences.find((a) => a.id === selected.value);
                  if (audience) {
                    selectedAudience = audience;
                  }
                } else {
                  selectedAudience = null;
                }
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  class: "w-full",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select audience" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    const each_array_1 = ensure_array_like(audiences);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                      let audience = each_array_1[$$index_1];
                      $$payload6.out += `<!---->`;
                      Select_item($$payload6, {
                        value: { value: audience.id, label: audience.name },
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(audience.name)}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="audienceId"${attr("value", selectedAudience?.id || "")}/> `;
            if (audiences.length === 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-muted-foreground mt-1 text-xs">No audiences found. Create an audience in Resend first.</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div><label for="subject" class="mb-1 block text-sm font-medium">Subject</label> <!---->`;
            Input($$payload4, {
              id: "subject",
              name: "subject",
              placeholder: "Email subject line",
              get value() {
                return subject;
              },
              set value($$value) {
                subject = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div><label for="title" class="mb-1 block text-sm font-medium">Title</label> <!---->`;
            Input($$payload4, {
              id: "title",
              name: "title",
              placeholder: "Email title",
              get value() {
                return title;
              },
              set value($$value) {
                title = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div><label for="content" class="mb-1 block text-sm font-medium">Content</label> <!---->`;
            Textarea($$payload4, {
              id: "content",
              name: "content",
              placeholder: "Email content (HTML supported)",
              rows: 6,
              get value() {
                return content;
              },
              set value($$value) {
                content = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div><label for="imageUrl" class="mb-1 block text-sm font-medium">Image URL (Optional)</label> <!---->`;
            Input($$payload4, {
              id: "imageUrl",
              name: "imageUrl",
              placeholder: "https://example.com/image.jpg",
              get value() {
                return imageUrl;
              },
              set value($$value) {
                imageUrl = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><label for="ctaText" class="mb-1 block text-sm font-medium">CTA Text (Optional)</label> <!---->`;
            Input($$payload4, {
              id: "ctaText",
              name: "ctaText",
              placeholder: "Call to action text",
              get value() {
                return ctaText;
              },
              set value($$value) {
                ctaText = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div><label for="ctaUrl" class="mb-1 block text-sm font-medium">CTA URL (Optional)</label> <!---->`;
            Input($$payload4, {
              id: "ctaUrl",
              name: "ctaUrl",
              placeholder: "https://example.com/action",
              get value() {
                return ctaUrl;
              },
              set value($$value) {
                ctaUrl = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div></div> <div><label for="scheduledAt" class="mb-1 block text-sm font-medium">Schedule (Optional)</label> <!---->`;
            Input($$payload4, {
              id: "scheduledAt",
              name: "scheduledAt",
              type: "datetime-local",
              get value() {
                return scheduledAt;
              },
              set value($$value) {
                scheduledAt = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> <p class="text-muted-foreground mt-1 text-xs">Leave empty to send immediately</p></div> <div class="flex justify-between pt-4"><!---->`;
            Button($$payload4, {
              type: "button",
              variant: "outline",
              onclick: loadPreview,
              disabled: isPreviewLoading || !selectedTemplate,
              children: ($$payload5) => {
                if (isPreviewLoading) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--> Preview`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Button($$payload4, {
              type: "submit",
              disabled: !selectedTemplate || !selectedAudience || !subject || !title || !content,
              children: ($$payload5) => {
                {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--> ${escape_html(scheduledAt ? "Schedule" : "Send")} Broadcast`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Preview`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Preview how your email will look`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            if (previewHtml) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="overflow-hidden rounded-md border"><iframe title="Email Preview"${attr("srcdoc", previewHtml)} class="h-[600px] w-full border-0" sandbox="allow-same-origin"></iframe></div>`;
            } else if (isPreviewLoading) {
              $$payload4.out += "<!--[1-->";
              $$payload4.out += `<div class="flex h-[600px] items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `<div class="text-muted-foreground flex h-[600px] flex-col items-center justify-center"><p>Select a template and click Preview to see how your email will look</p> <!---->`;
              Button($$payload4, {
                variant: "outline",
                class: "mt-4",
                onclick: loadPreview,
                disabled: !selectedTemplate,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Preview`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <!---->`;
    Card($$payload2, {
      class: "mt-6",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Broadcast History`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->View and manage your email broadcasts`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex h-40 items-center justify-center"><div class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DM7wLP_d.js.map
