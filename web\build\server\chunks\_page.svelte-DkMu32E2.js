import { p as push, O as escape_html, q as pop } from './index3-CqUPEnZw.js';
import 'clsx';
import { B as Button } from './button-CrucCo1G.js';
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { E as Ellipsis } from './ellipsis-C7bKlkmn.js';
import { D as Dropdown_menu_label } from './dropdown-menu-label-rJ1q7A04.js';
import { D as Dropdown_menu_separator } from './dropdown-menu-separator-B5VQzuNH.js';
import { E as Eye_off } from './eye-off-BH_BafP_.js';
import { E as Eye } from './eye-B2tdw2__.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './use-grace-area.svelte-CrXiOQDy.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  let profile = {
    ...data.profile
  };
  let profileData = {};
  console.log("Profile data:", data.profile);
  function createCompleteProfile() {
    const typedProfileData = profileData;
    const resumeData = typedProfileData.resumeData || {};
    const jobSearchStatus = typedProfileData.jobPreferences?.jobSearchStatus || "actively_looking";
    return {
      header: {
        profileName: profile.name || "",
        fullName: typedProfileData.fullName || typedProfileData.personalInfo?.fullName || typedProfileData.header?.fullName || "",
        jobTitle: typedProfileData.jobType || typedProfileData.personalInfo?.jobTitle || "",
        jobSearchStatus
      },
      visibility: {
        showToRecruiters: typedProfileData.visibility?.showToRecruiters || false,
        getDiscovered: typedProfileData.visibility?.getDiscovered || true,
        hideFromCurrentEmployer: typedProfileData.visibility?.hideFromCurrentEmployer || false
      },
      personalInfo: {
        email: typedProfileData.email || typedProfileData.personalInfo?.email || "",
        phone: typedProfileData.phone || typedProfileData.personalInfo?.phone || "",
        address: typedProfileData.personalInfo?.address || "",
        city: typedProfileData.personalInfo?.city || "",
        state: typedProfileData.personalInfo?.state || "",
        zip: typedProfileData.personalInfo?.zip || "",
        country: typedProfileData.personalInfo?.country || "USA"
      },
      resume: resumeData.id || typedProfileData.resumeId ? {
        resumeId: resumeData.id || typedProfileData.resumeId,
        fileName: resumeData.name || typedProfileData.resumeName || "resume.pdf",
        uploadedAt: resumeData.updatedAt || typedProfileData.resumeUpdatedAt,
        isDefault: true
      } : null,
      workExperiences: typedProfileData.workExperience || typedProfileData.workExperiences || [],
      educations: typedProfileData.education || typedProfileData.educations || [],
      projects: typedProfileData.projects || [],
      portfolioLinks: {
        linkedinUrl: typedProfileData.portfolioLinks?.linkedin || typedProfileData.personalInfo?.linkedin || "",
        githubUrl: typedProfileData.portfolioLinks?.github || typedProfileData.personalInfo?.github || "",
        portfolioUrl: typedProfileData.website || typedProfileData.personalInfo?.website || "",
        otherUrl: typedProfileData.portfolioLinks?.other || ""
      },
      skills: {
        skills: Array.isArray(typedProfileData.skills) ? typedProfileData.skills : typedProfileData.skillsData?.list || typedProfileData.skillsData?.technical || []
      },
      languages: typedProfileData.languages ? typedProfileData.languages.map((lang) => ({
        ...lang,
        proficiency: lang.proficiency || "intermediate"
      })) : []
    };
  }
  let completeProfile = createCompleteProfile();
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  const lastUpdated = profile?.updatedAt ? formatDate(profile.updatedAt) : "Recently";
  SEO($$payload, {
    title: `${profile?.name || "Profile"} - Hirli`,
    description: "Edit your professional profile for job applications.",
    keywords: "profile, resume, job search, career profile, professional information",
    url: `https://hirli.com/dashboard/settings/profile/${profile?.id}`
  });
  $$payload.out += `<!----> <div class="border-border flex items-center justify-between border-b p-6"><div><h1 class="text-xl font-semibold">${escape_html(profile?.name || "Profile")}</h1> <p class="text-muted-foreground text-sm">Last updated: ${escape_html(lastUpdated)}</p></div> <div><!---->`;
  Root($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Dropdown_menu_trigger($$payload2, {
        children: ($$payload3) => {
          Button($$payload3, {
            variant: "ghost",
            size: "icon",
            class: "h-8 w-8 rounded-full",
            children: ($$payload4) => {
              Ellipsis($$payload4, { class: "h-4 w-4" });
              $$payload4.out += `<!----> <span class="sr-only">Open menu</span>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Dropdown_menu_content($$payload2, {
        align: "end",
        class: "w-48",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Dropdown_menu_label($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Profile Actions`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Dropdown_menu_separator($$payload3, {});
          $$payload3.out += `<!----> <button class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50">`;
          if (completeProfile.visibility.showToRecruiters) {
            $$payload3.out += "<!--[-->";
            Eye_off($$payload3, { class: "mr-2 h-4 w-4" });
            $$payload3.out += `<!----> <span>Hide from Recruiters</span>`;
          } else {
            $$payload3.out += "<!--[!-->";
            Eye($$payload3, { class: "mr-2 h-4 w-4" });
            $$payload3.out += `<!----> <span>Show to Recruiters</span>`;
          }
          $$payload3.out += `<!--]--></button> <!---->`;
          Dropdown_menu_separator($$payload3, {});
          $$payload3.out += `<!----> <button class="text-destructive hover:bg-destructive hover:text-destructive-foreground focus:bg-destructive focus:text-destructive-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors">`;
          Trash_2($$payload3, { class: "mr-2 h-4 w-4" });
          $$payload3.out += `<!----> <span>Delete Profile</span></button>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="container mx-auto p-6">`;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center py-12"><div class="border-primary h-12 w-12 animate-spin rounded-full border-4 border-t-transparent"></div> <p class="mt-4 text-lg">Loading profile...</p></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DkMu32E2.js.map
