import { M as ensure_array_like, O as escape_html } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { S as Search } from './search-B0oHlTPS.js';
import { C as Chart_column, U as User_check } from './user-check-CJipw8LV.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { B as Building } from './building-8WHBOPYC.js';
import { T as Target } from './target-VMK77SRs.js';
import { A as Award } from './award-DpjdNTEA.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

function _page($$payload) {
  const features = {
    talentAcquisition: {
      title: "Streamline Your Hiring Process",
      description: "Find the perfect candidates faster with our AI-powered talent acquisition platform.",
      secondary: [
        {
          icon: Search,
          title: "Smart Candidate Matching",
          description: "Our AI analyzes thousands of profiles to find candidates that truly match your requirements."
        },
        {
          icon: Chart_column,
          title: "Hiring Analytics",
          description: "Gain valuable insights into your recruitment process with comprehensive analytics."
        },
        {
          icon: Clock,
          title: "Reduce Time-to-Hire",
          description: "Cut your hiring timeline by up to 50% with our streamlined processes and automation."
        },
        {
          icon: Shield,
          title: "Quality Assurance",
          description: "Our pre-screening tools ensure you only review candidates who meet your standards."
        }
      ]
    },
    employerBranding: {
      title: "Enhance Your Employer Brand",
      description: "Showcase your company culture and attract top talent with our employer branding solutions.",
      secondary: [
        {
          icon: Building,
          title: "Company Profile Optimization",
          description: "Create a compelling company profile that highlights your unique culture and benefits."
        },
        {
          icon: User_check,
          title: "Candidate Experience",
          description: "Provide a seamless application experience that reflects positively on your brand."
        },
        {
          icon: Target,
          title: "Targeted Outreach",
          description: "Reach passive candidates who align with your company values and mission."
        },
        {
          icon: Award,
          title: "Reputation Management",
          description: "Monitor and improve your employer reputation across multiple platforms."
        }
      ]
    }
  };
  const testimonials = [
    {
      quote: "Hirli has transformed our recruitment process. We've reduced our time-to-hire by 40% and improved the quality of our candidates significantly.",
      author: "Sarah Johnson",
      position: "Head of Talent Acquisition",
      company: "TechGrowth Inc."
    },
    {
      quote: "The analytics provided by Hirli have given us unprecedented insights into our hiring process, allowing us to make data-driven decisions.",
      author: "Michael Chen",
      position: "Director of HR",
      company: "Innovate Solutions"
    },
    {
      quote: "We've seen a 35% increase in qualified applicants since implementing Hirli's employer branding solutions.",
      author: "Jessica Williams",
      position: "Recruitment Manager",
      company: "Global Systems"
    }
  ];
  const each_array = ensure_array_like(features.talentAcquisition.secondary);
  const each_array_1 = ensure_array_like(features.employerBranding.secondary);
  const each_array_2 = ensure_array_like(testimonials);
  SEO($$payload, {
    title: "Hirli for Employers - Streamline Your Hiring Process",
    description: "Find the perfect candidates faster with our AI-powered talent acquisition platform. Reduce time-to-hire and improve candidate quality.",
    keywords: "talent acquisition, hiring, recruitment, employer branding, HR technology",
    url: "https://hirli.com/employers",
    image: "/assets/og-image-employers.jpg"
  });
  $$payload.out += `<!----> <section class="border border-l border-r border-t py-16 md:py-40"><div class="grid grid-cols-10 items-center gap-12"><div class="col-span-4 col-start-2"><div class="leading-tighter mb-8 w-[90%] text-4xl font-light md:text-5xl lg:text-[80px]">Find <span class="gradient-text">Top Talent</span> Faster &amp; Smarter</div> <p class="mb-12 text-gray-600 md:text-2xl">Our AI-powered platform helps you identify, attract, and hire the best candidates while
        saving time and resources.</p> <div class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">`;
  Button($$payload, {
    class: "rounded-none border border-transparent bg-neutral-200 p-8 text-lg font-medium text-white transition-colors hover:bg-blue-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Schedule a Demo`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    class: "group flex items-center rounded-none border border-gray-300 p-8 text-lg font-medium transition-colors hover:bg-gray-50",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Learn More `;
      Arrow_right($$payload2, { class: "ml-2 h-4 w-4" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="mt-8 flex items-center text-sm text-gray-500"><div class="mr-3 flex -space-x-2"><img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Employer" class="h-8 w-8 rounded-full border-2 border-white"/> <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Employer" class="h-8 w-8 rounded-full border-2 border-white"/> <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Employer" class="h-8 w-8 rounded-full border-2 border-white"/></div> <span>Trusted by <span class="font-semibold">500+</span> companies worldwide</span></div></div> <div class="relative col-span-2 col-start-9"><div class="h-[500px] w-full rounded-lg bg-gradient-to-br from-blue-100 to-blue-200"></div></div></div></section> <section class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><div class="grid grid-cols-1 gap-8 md:grid-cols-3"><div class="text-center"><div class="text-5xl font-bold text-blue-600">40%</div> <p class="mt-2 text-xl text-gray-600">Reduction in Time-to-Hire</p></div> <div class="text-center"><div class="text-5xl font-bold text-blue-600">3x</div> <p class="mt-2 text-xl text-gray-600">More Qualified Candidates</p></div> <div class="text-center"><div class="text-5xl font-bold text-blue-600">65%</div> <p class="mt-2 text-xl text-gray-600">Cost Savings on Recruitment</p></div></div></div></section> <section class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><h2 class="mb-16 text-center text-4xl font-light">Key Features for Employers</h2> <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"><div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600">`;
  Search($$payload, { class: "h-8 w-8" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-medium">AI-Powered Matching</h3> <p class="text-gray-600">Our advanced algorithms analyze job requirements and candidate profiles to find the
          perfect match, reducing screening time by up to 75%.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600">`;
  Chart_column($$payload, { class: "h-8 w-8" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-medium">Recruitment Analytics</h3> <p class="text-gray-600">Gain valuable insights into your hiring process with comprehensive analytics dashboards
          that help you optimize your recruitment strategy.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600">`;
  Clock($$payload, { class: "h-8 w-8" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-medium">Automated Screening</h3> <p class="text-gray-600">Automate initial candidate screening with customizable assessment tools that evaluate
          skills, experience, and cultural fit.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600">`;
  Shield($$payload, { class: "h-8 w-8" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-medium">Enterprise Security</h3> <p class="text-gray-600">Protect sensitive candidate data with enterprise-grade security measures, including
          encryption, access controls, and compliance with regulations.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600">`;
  Building($$payload, { class: "h-8 w-8" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-medium">Employer Branding</h3> <p class="text-gray-600">Showcase your company culture and values with customizable employer profiles that attract
          top talent to your organization.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg"><div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600">`;
  User_check($$payload, { class: "h-8 w-8" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-medium">Seamless Integration</h3> <p class="text-gray-600">Integrate with your existing HR tech stack, including ATS, HRIS, and calendar systems for
          a streamlined recruitment workflow.</p></div></div> <div class="mt-16 text-center">`;
  Button($$payload, {
    class: "rounded-none border border-transparent bg-neutral-200 p-6 text-lg font-medium text-white transition-colors hover:bg-blue-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Explore All Features`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></section> <section id="talent-acquisition" class="border border-b border-l border-r border-neutral-200"><div class="flex flex-col"><div class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.talentAcquisition.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.talentAcquisition.description)}</p> <a href="#contact" class="flex w-48 flex-row items-center justify-between rounded-md bg-blue-500 px-6 py-3 text-white transition-colors hover:bg-blue-600">Get Started `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div> <div class="bg-grid border-left-neutral col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t"></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    $$payload.out += `<div class="p-22 rounded-none border border-gray-100 bg-white shadow-md transition-shadow duration-300 hover:shadow-lg"><div class="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500/10"><!---->`;
    feature.icon?.($$payload, { class: "h-6 w-6 text-blue-500" });
    $$payload.out += `<!----></div> <h3 class="font-normal! mb-4 text-3xl">${escape_html(feature.title)}</h3> <p class="text-md text-gray-600">${escape_html(feature.description)}</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></section> <section id="employer-branding" class="border border-b border-l border-r border-neutral-200"><div class="flex flex-col"><div class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="bg-grid bg-grid-blue-200 dark:bg-grid-blue-600 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l border-neutral-200"></div> <div class="p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.employerBranding.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.employerBranding.description)}</p> <a href="#contact" class="flex w-48 flex-row items-center justify-between rounded-md bg-blue-500 px-6 py-3 text-white transition-colors hover:bg-blue-600">Learn More `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let feature = each_array_1[$$index_1];
    $$payload.out += `<div class="p-22 rounded-none border border-gray-100 bg-white shadow-md transition-shadow duration-300 hover:shadow-lg"><div class="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500/10"><!---->`;
    feature.icon?.($$payload, { class: "h-6 w-6 text-blue-500" });
    $$payload.out += `<!----></div> <h3 class="font-normal! mb-4 text-3xl">${escape_html(feature.title)}</h3> <p class="text-md text-gray-600">${escape_html(feature.description)}</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></section> <section class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><h2 class="mb-12 text-center text-4xl font-light">What Our Clients Say</h2> <div class="grid grid-cols-1 gap-8 md:grid-cols-3"><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let testimonial = each_array_2[$$index_2];
    $$payload.out += `<div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md"><p class="mb-4 text-gray-600">"${escape_html(testimonial.quote)}"</p> <div class="flex items-center"><div class="mr-4 h-12 w-12 rounded-full bg-gray-300"></div> <div><p class="font-semibold">${escape_html(testimonial.author)}</p> <p class="text-sm text-gray-600">${escape_html(testimonial.position)}, ${escape_html(testimonial.company)}</p></div></div></div>`;
  }
  $$payload.out += `<!--]--></div></div></section> <section id="contact" class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><div class="mx-auto max-w-4xl"><h2 class="mb-6 text-center text-4xl font-light">Ready to Transform Your Hiring Process?</h2> <p class="mx-auto mb-12 max-w-2xl text-center text-xl text-gray-600">Fill out the form below to schedule a demo with our team and see how Hirli can help you find
        and hire the best talent faster.</p> <div class="grid grid-cols-1 gap-8 md:grid-cols-2"><div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md"><form class="space-y-6"><div class="space-y-2"><label for="name" class="text-sm font-medium">Full Name</label> `;
  Input($$payload, {
    id: "name",
    type: "text",
    placeholder: "John Doe",
    class: "w-full"
  });
  $$payload.out += `<!----></div> <div class="space-y-2"><label for="company" class="text-sm font-medium">Company</label> `;
  Input($$payload, {
    id: "company",
    type: "text",
    placeholder: "Acme Inc.",
    class: "w-full"
  });
  $$payload.out += `<!----></div> <div class="space-y-2"><label for="email" class="text-sm font-medium">Work Email</label> `;
  Input($$payload, {
    id: "email",
    type: "email",
    placeholder: "<EMAIL>",
    class: "w-full"
  });
  $$payload.out += `<!----></div> <div class="space-y-2"><label for="phone" class="text-sm font-medium">Phone Number</label> `;
  Input($$payload, {
    id: "phone",
    type: "tel",
    placeholder: "(*************",
    class: "w-full"
  });
  $$payload.out += `<!----></div> <div class="space-y-2"><label for="message" class="text-sm font-medium">How can we help?</label> `;
  Textarea($$payload, {
    id: "message",
    placeholder: "Tell us about your hiring needs...",
    class: "min-h-[120px] w-full"
  });
  $$payload.out += `<!----></div> `;
  Button($$payload, {
    class: "w-full rounded-none border border-transparent bg-neutral-200 p-6 text-lg font-medium text-white transition-colors hover:bg-blue-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Schedule a Demo`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></form></div> <div class="flex flex-col justify-between space-y-8"><div><h3 class="mb-4 text-2xl font-light">Enterprise Solutions</h3> <p class="mb-6 text-gray-600">Our enterprise solutions are tailored to meet the unique needs of large organizations.
              Get in touch with our team to learn more about how we can help you streamline your
              hiring process.</p> <div class="space-y-4"><div class="flex items-start"><div class="mr-3 mt-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4"><path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"></path></svg></div> <div><h4 class="font-medium">Email Us</h4> <p class="text-gray-600"><EMAIL></p></div></div></div></div> <div class="rounded-lg bg-gray-50 p-6"><p class="italic text-gray-600">"Hirli's enterprise solution has transformed our hiring process. We've reduced our
              time-to-hire by 40% and improved the quality of our candidates significantly."</p> <div class="mt-4 flex items-center"><div class="mr-3 h-10 w-10 rounded-full bg-gray-300"></div> <div><p class="font-medium">Sarah Johnson</p> <p class="text-sm text-gray-600">Head of Talent Acquisition, TechGrowth Inc.</p></div></div></div></div></div></div></div></section> <section id="faq" class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><div class="mx-auto max-w-4xl"><h2 class="mb-12 text-center text-4xl font-light">Frequently Asked Questions</h2> <div class="grid grid-cols-1 gap-8 md:grid-cols-2"><div class="space-y-6"><div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"><h3 class="mb-3 text-xl font-medium">How does Hirli help with talent acquisition?</h3> <p class="text-gray-600">Hirli uses AI to analyze job requirements and candidate profiles, matching the right
              talent to your open positions. Our platform automates screening, scheduling, and
              initial interviews, saving your team valuable time.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"><h3 class="mb-3 text-xl font-medium">What size companies can benefit from Hirli?</h3> <p class="text-gray-600">Hirli is designed to scale with your needs. We serve companies of all sizes, from
              startups to enterprise organizations. Our platform can be customized to match your
              specific hiring workflows and volume.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"><h3 class="mb-3 text-xl font-medium">How secure is candidate data on your platform?</h3> <p class="text-gray-600">We take security seriously. All candidate data is encrypted and stored securely. We
              are GDPR and CCPA compliant, and we implement enterprise-grade security measures to
              protect your information.</p></div></div> <div class="space-y-6"><div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"><h3 class="mb-3 text-xl font-medium">Can Hirli integrate with our existing ATS?</h3> <p class="text-gray-600">Yes, Hirli integrates seamlessly with most popular Applicant Tracking Systems. Our API
              allows for custom integrations with your existing HR tech stack, ensuring a smooth
              workflow.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"><h3 class="mb-3 text-xl font-medium">How much time can we save using Hirli?</h3> <p class="text-gray-600">Our clients typically report a 40-60% reduction in time-to-hire. By automating
              repetitive tasks and streamlining the candidate screening process, your team can focus
              on high-value activities.</p></div> <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm"><h3 class="mb-3 text-xl font-medium">What kind of support do you offer?</h3> <p class="text-gray-600">We provide comprehensive support including implementation assistance, training, and
              ongoing technical support. Enterprise clients receive dedicated account managers and
              priority support.</p></div></div></div> <div class="mt-12 text-center"><p class="mb-6 text-gray-600">Still have questions about how Hirli can help your organization?</p> `;
  Button($$payload, {
    class: "rounded-none border border-transparent bg-neutral-200 p-6 text-lg font-medium text-white transition-colors hover:bg-blue-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Contact Our Team`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></section> <section class="bg-gray-50 py-16"><div class="container mx-auto px-4"><div class="grid grid-cols-1 items-center gap-16 md:grid-cols-2"><div><h3 class="mb-6 text-2xl font-light">Secure candidate data with enterprise-grade protection</h3> <p class="mb-8 text-lg text-gray-600">Protect sensitive candidate information with our robust security measures, including
          encryption, access controls, and compliance with data protection regulations.</p> `;
  Button($$payload, {
    variant: "link",
    class: "flex items-center gap-2 p-0 text-lg text-black",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Learn more about our security features `;
      Arrow_right($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div><img src="/images/employers-security.jpg" alt="Security Features" class="h-auto w-full shadow-lg"/></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-4xl"><h2 class="mb-16 text-center text-3xl font-light">Explore how companies use Hirli</h2> <div class="relative aspect-video bg-gray-100 shadow-lg"><div class="absolute inset-0 flex items-center justify-center"><img src="/images/video-thumbnail.jpg" alt="Video Thumbnail" class="h-full w-full object-cover"/> <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30"><button class="flex h-20 w-20 items-center justify-center rounded-full bg-white" aria-label="Play video"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 text-black"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg></button></div></div></div> <div class="mt-16 bg-gray-50 p-12"><blockquote class="mb-8 text-2xl font-light italic">"Hirli has completely transformed our hiring process. We've reduced our time-to-hire by
          60% and improved the quality of our candidates significantly."</blockquote> <div class="flex items-center"><img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Sarah Johnson" class="mr-4 h-12 w-12 rounded-full"/> <div><p class="font-medium">Sarah Johnson</p> <p class="text-gray-600">Head of Talent Acquisition, TechCorp</p></div></div></div></div></div></section> <footer class="border border-b border-l border-r bg-gray-50 py-12"><div class="container mx-auto px-4"><div class="grid grid-cols-1 gap-8 md:grid-cols-4"><div><h3 class="mb-4 text-lg font-semibold">Hirli for Employers</h3> <p class="text-gray-600">The AI-powered platform that helps you find and hire the best talent faster.</p></div> <div><h3 class="mb-4 text-lg font-semibold">Features</h3> <ul class="space-y-2 text-gray-600"><li><a href="#talent-acquisition" class="hover:text-blue-500">Talent Acquisition</a></li> <li><a href="#employer-branding" class="hover:text-blue-500">Employer Branding</a></li> <li><a href="#candidate-experience" class="hover:text-blue-500">Candidate Experience</a></li></ul></div> <div><h3 class="mb-4 text-lg font-semibold">Resources</h3> <ul class="space-y-2 text-gray-600"><li><a href="/blog" class="hover:text-blue-500">Blog</a></li> <li><a href="/case-studies" class="hover:text-blue-500">Case Studies</a></li> <li><a href="/webinars" class="hover:text-blue-500">Webinars</a></li></ul></div> <div><h3 class="mb-4 text-lg font-semibold">Contact</h3> <ul class="space-y-2 text-gray-600"><li>Email: <EMAIL></li> <li>Address: 123 Main St, City, Country</li></ul></div></div> <div class="mt-8 border-t border-gray-200 pt-8 text-center text-gray-600"><p>© 2023 Hirli. All rights reserved.</p></div></div></footer>`;
}

export { _page as default };
//# sourceMappingURL=_page.svelte-C8yI6ED4.js.map
