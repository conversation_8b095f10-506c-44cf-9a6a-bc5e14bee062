import { p as push, S as store_get, V as copy_payload, W as assign_payload, T as unsubscribe_stores, Q as bind_props, q as pop, O as escape_html, ab as store_mutate } from './index3-CqUPEnZw.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { F as Form_field, C as Control, a as Form_field_errors } from './index15-D3NL0C7o.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { p as page } from './stores-DSLMNPqo.js';
import 'clsx';
import './zod-DfpldWlD.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { F as Form_label } from './form-label-C5yTxnxS.js';
import { F as Form_description } from './form-description-jswnCHVb.js';
import { S as Settings } from './settings-STaOxCkl.js';
import { G as Globe } from './globe-B6sBOhFF.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { U as Users } from './users-e7-Uhkka.js';
import { B as Bell } from './bell-C9_YgkSj.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './index2-Cut0V_vU.js';
import './index-DjwFQdT_.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';
import './index4-HpJcNJHQ.js';
import './index-server-CezSOnuG.js';
import './client-dNyMPa8V.js';
import './stringify-DWCARkQV.js';
import './label-Dt8gTF_8.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './use-id-CcFpwo20.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let userData, hasTeamAccess;
  let data = $$props["data"];
  const form = superForm({
    id: "general-settings",
    data: data.form.data,
    onUpdated: ({ form: form2 }) => {
      if (form2.valid) {
        toast.success("General settings updated successfully");
      }
    },
    onError: () => {
      toast.error("Failed to update general settings");
    }
  });
  const { form: formData, enhance, submitting } = form;
  userData = store_get($$store_subs ??= {}, "$page", page).data.user;
  hasTeamAccess = userData?.teamId || userData?.hasTeamFeature || false;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "General Settings - Hirli",
      description: "Configure general application settings including site name, description, contact information, and regional preferences.",
      keywords: "general settings, site configuration, application settings, regional settings, language settings",
      url: "https://hirli.com/dashboard/settings/general"
    });
    $$payload2.out += `<!----> <div class="space-y-6"><div class="border-border flex flex-row justify-between border-b p-6"><h2 class="text-lg font-semibold">General Settings</h2> <p class="text-muted-foreground text-foreground/80">Configure general application settings and preferences.</p></div> <form method="POST" class="space-y-8">`;
    Card($$payload2, {
      children: ($$payload3) => {
        Card_header($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Site Information`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Basic information about your site.`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Card_content($$payload3, {
          class: "space-y-6 p-6 pt-0",
          children: ($$payload4) => {
            Form_field($$payload4, {
              form,
              name: "siteName",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Site Name`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      type: "text",
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).siteName;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).siteName = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->The name of your site`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Form_field($$payload4, {
              form,
              name: "siteDescription",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Site Description`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Textarea($$payload6, {
                      rows: 3,
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).siteDescription;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).siteDescription = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->A brief description of your site`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Form_field($$payload4, {
              form,
              name: "contactEmail",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Contact Email`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      type: "email",
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).contactEmail;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).contactEmail = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->The primary contact email for your site`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Card($$payload2, {
      children: ($$payload3) => {
        Card_header($$payload3, {
          class: "p-6",
          children: ($$payload4) => {
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Regional Settings`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Configure regional preferences.`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Card_content($$payload3, {
          class: "space-y-6 p-6 pt-0",
          children: ($$payload4) => {
            $$payload4.out += `<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">`;
            Form_field($$payload4, {
              form,
              name: "timezone",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Timezone`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      type: "text",
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).timezone;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).timezone = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Your default timezone`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Form_field($$payload4, {
              form,
              name: "language",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Language`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      type: "text",
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).language;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).language = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Your preferred language`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Form_field($$payload4, {
              form,
              name: "dateFormat",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Date Format`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      type: "text",
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).dateFormat;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).dateFormat = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Your preferred date format`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Form_field($$payload4, {
              form,
              name: "timeFormat",
              children: ($$payload5) => {
                Control($$payload5, {
                  children: ($$payload6) => {
                    Form_label($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Time Format`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Input($$payload6, {
                      type: "text",
                      get value() {
                        return store_get($$store_subs ??= {}, "$formData", formData).timeFormat;
                      },
                      set value($$value) {
                        store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).timeFormat = $$value);
                        $$settled = false;
                      }
                    });
                    $$payload6.out += `<!---->`;
                  }
                });
                $$payload5.out += `<!----> `;
                Form_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Your preferred time format`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Form_field_errors($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <div class="flex justify-end">`;
    Button($$payload2, {
      type: "submit",
      disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
      children: ($$payload3) => {
        $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, "$submitting", submitting) ? "Saving..." : "Save Changes")}`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></form> <div class="flex items-center justify-between rounded-lg border p-4"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">`;
    Settings($$payload2, { class: "text-primary h-5 w-5" });
    $$payload2.out += `<!----></div> <div><h3 class="font-medium">Other Settings</h3> <p class="text-muted-foreground text-sm">Configure additional settings in other sections</p></div></div> <div class="flex gap-2">`;
    Button($$payload2, {
      variant: "outline",
      onclick: () => window.location.href = "/dashboard/settings/account",
      children: ($$payload3) => {
        Globe($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Account`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "outline",
      onclick: () => window.location.href = "/dashboard/settings/security",
      children: ($$payload3) => {
        Shield($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Security`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    if (hasTeamAccess) {
      $$payload2.out += "<!--[-->";
      Button($$payload2, {
        variant: "outline",
        onclick: () => window.location.href = "/dashboard/settings/team",
        children: ($$payload3) => {
          Users($$payload3, { class: "mr-2 h-4 w-4" });
          $$payload3.out += `<!----> Team`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    Button($$payload2, {
      variant: "outline",
      onclick: () => window.location.href = "/dashboard/settings/notifications",
      children: ($$payload3) => {
        Bell($$payload3, { class: "mr-2 h-4 w-4" });
        $$payload3.out += `<!----> Notifications`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DvoupwzW.js.map
