import 'clsx';
import { p as push, V as copy_payload, W as assign_payload, q as pop, K as fallback, Q as bind_props, S as store_get, M as ensure_array_like, ab as store_mutate, O as escape_html, T as unsubscribe_stores } from './index3-CqUPEnZw.js';
import { R as Root, P as Portal, D as Dialog_content, d as Dialog_overlay } from './index7-BURUpWjT.js';
import { R as Root$1, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { I as Input } from './input-DF0gPqYN.js';
import { B as Button } from './button-CrucCo1G.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { w as writable } from './index2-Cut0V_vU.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { C as Cloud_upload } from './cloud-upload-D9PNI7eQ.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { S as Search } from './search-B0oHlTPS.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { E as Eye } from './eye-B2tdw2__.js';
import { S as SEO } from './SEO-UItXytUy.js';
import './false-CRHihH2U.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './hidden-input-1eDzjGOB.js';
import './index-DjwFQdT_.js';
import './dialog-description2-rfr-pd9k.js';

function DocumentUpload($$payload, $$props) {
  push();
  var $$store_subs;
  let profiles = fallback($$props["profiles"], () => [], true);
  let documentTypes = fallback(
    $$props["documentTypes"],
    () => [
      { id: "resume", name: "Resume" },
      { id: "cover_letter", name: "Cover Letter" },
      {
        id: "question_response",
        name: "Question Response"
      },
      {
        id: "letter_of_recommendation",
        name: "Letter of Recommendation"
      },
      { id: "references", name: "References" },
      {
        id: "employment_certification",
        name: "Employment Certification"
      }
    ],
    true
  );
  let initialDocumentType = fallback($$props["initialDocumentType"], "resume");
  let open = fallback($$props["open"], false);
  const formData = writable({
    documentType: initialDocumentType,
    profileId: "",
    label: "",
    file: null
  });
  const formErrors = writable({ documentType: "", label: "", file: "" });
  function resetForm() {
    formData.set({
      documentType: initialDocumentType,
      profileId: "",
      label: "",
      file: null
    });
    formErrors.set({ documentType: "", label: "", file: "" });
  }
  if (!open) {
    resetForm();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_overlay($$payload4, {});
            $$payload4.out += `<!----> `;
            Dialog_content($$payload4, {
              class: "sm:max-w-md",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  class: "mb-4 flex flex-col gap-1",
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Upload a Document`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Select a document type and upload your file.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <form><div class="grid gap-4"><div class="flex flex-col gap-2">`;
                Label($$payload5, {
                  for: "documentType",
                  class: "block text-xs",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Document Type`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Root$1($$payload5, {
                  type: "single",
                  get value() {
                    return store_get($$store_subs ??= {}, "$formData", formData).documentType;
                  },
                  set value($$value) {
                    store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).documentType = $$value);
                    $$settled = false;
                  },
                  children: ($$payload6) => {
                    Select_trigger($$payload6, {
                      class: "p-2",
                      children: ($$payload7) => {
                        Select_value($$payload7, {
                          placeholder: store_get($$store_subs ??= {}, "$formData", formData).documentType ? documentTypes.find((o) => o.id === store_get($$store_subs ??= {}, "$formData", formData).documentType)?.name : "Choose a document type"
                        });
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Select_content($$payload6, {
                      children: ($$payload7) => {
                        const each_array = ensure_array_like(documentTypes);
                        $$payload7.out += `<!--[-->`;
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let type = each_array[$$index];
                          Select_item($$payload7, {
                            value: type.id,
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->${escape_html(type.name)}`;
                            },
                            $$slots: { default: true }
                          });
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                if (store_get($$store_subs ??= {}, "$formErrors", formErrors).documentType) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<p class="text-xs text-red-600">${escape_html(store_get($$store_subs ??= {}, "$formErrors", formErrors).documentType)}</p>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--></div> `;
                if (profiles.length > 0) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="flex flex-col gap-2">`;
                  Label($$payload5, {
                    for: "profileId",
                    class: "block text-xs",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Select Profile (Optional)`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  Root$1($$payload5, {
                    type: "single",
                    get value() {
                      return store_get($$store_subs ??= {}, "$formData", formData).profileId;
                    },
                    set value($$value) {
                      store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).profileId = $$value);
                      $$settled = false;
                    },
                    children: ($$payload6) => {
                      Select_trigger($$payload6, {
                        class: "p-2",
                        children: ($$payload7) => {
                          Select_value($$payload7, {
                            placeholder: store_get($$store_subs ??= {}, "$formData", formData).profileId ? profiles.find((o) => o.id === store_get($$store_subs ??= {}, "$formData", formData).profileId)?.name : "Choose a profile (optional)"
                          });
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Select_content($$payload6, {
                        children: ($$payload7) => {
                          const each_array_1 = ensure_array_like(profiles);
                          $$payload7.out += `<!--[-->`;
                          for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                            let profile = each_array_1[$$index_1];
                            Select_item($$payload7, {
                              value: profile.id,
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(profile.name)}`;
                              },
                              $$slots: { default: true }
                            });
                          }
                          $$payload7.out += `<!--]-->`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--> <div class="flex flex-col gap-2">`;
                Label($$payload5, {
                  for: "label",
                  class: "block text-xs",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Document Name`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Input($$payload5, {
                  id: "label",
                  placeholder: "e.g. Software Engineer Resume",
                  get value() {
                    return store_get($$store_subs ??= {}, "$formData", formData).label;
                  },
                  set value($$value) {
                    store_mutate($$store_subs ??= {}, "$formData", formData, store_get($$store_subs ??= {}, "$formData", formData).label = $$value);
                    $$settled = false;
                  }
                });
                $$payload5.out += `<!----> `;
                if (store_get($$store_subs ??= {}, "$formErrors", formErrors).label) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<p class="text-xs text-red-600">${escape_html(store_get($$store_subs ??= {}, "$formErrors", formErrors).label)}</p>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--></div> <div class="flex w-full flex-col justify-center gap-1">`;
                Label($$payload5, {
                  for: "file",
                  class: "border-border flex h-40 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed",
                  children: ($$payload6) => {
                    if (store_get($$store_subs ??= {}, "$formData", formData).file) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="flex flex-col items-center">`;
                      Cloud_upload($$payload6, { class: "mb-3 h-12 w-12 text-green-500" });
                      $$payload6.out += `<!----> <p class="mb-1 text-sm text-gray-500">Selected:</p> <p class="text-md break-all text-gray-500">${escape_html(store_get($$store_subs ??= {}, "$formData", formData).file.name)}</p></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `<div class="flex flex-col items-center justify-center pb-6 pt-5">`;
                      Cloud_upload($$payload6, { class: "mb-3 h-12 w-12 text-gray-500" });
                      $$payload6.out += `<!----> <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p> <p class="text-xs text-gray-500">PDF, DOC, DOCX (MAX. 5MB)</p></div>`;
                    }
                    $$payload6.out += `<!--]--> <input id="file" type="file" accept=".pdf,.doc,.docx" class="hidden"/>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                if (store_get($$store_subs ??= {}, "$formErrors", formErrors).file) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<p class="text-xs text-red-600">${escape_html(store_get($$store_subs ??= {}, "$formErrors", formErrors).file)}</p>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--></div> `;
                Dialog_footer($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      type: "submit",
                      disabled: !store_get($$store_subs ??= {}, "$formData", formData).file || store_get($$store_subs ??= {}, "$formData", formData).label.length === 0 || store_get($$store_subs ??= {}, "$formData", formData).documentType.length === 0,
                      children: ($$payload7) => {
                        {
                          $$payload7.out += "<!--[!-->";
                          $$payload7.out += `Upload`;
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----></div></form>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    profiles,
    documentTypes,
    initialDocumentType,
    open
  });
  pop();
}
function ResumeCreate($$payload, $$props) {
  push();
  let filteredResumes;
  let open = fallback($$props["open"], false);
  let profiles = fallback($$props["profiles"], () => [], true);
  let resumes = fallback($$props["resumes"], () => [], true);
  let searchTerm = "";
  let resumeName = "New Resume";
  profiles.length > 0 ? profiles[0].id : null;
  let showProfileWarning = profiles.length === 0;
  filteredResumes = resumes.filter((resume) => resume.label.toLowerCase().includes(searchTerm.toLowerCase()));
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_content($$payload3, {
          class: "bg-zinc-900 text-white sm:max-w-[600px]",
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  class: "text-2xl font-bold",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->How would you like to create this new resume?`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  class: "text-gray-400",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Use an existing resume as base or start from your profile information.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="grid gap-6 py-4">`;
            if (showProfileWarning) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="mb-4 rounded-md bg-yellow-900/30 p-4 text-yellow-300"><h4 class="mb-2 font-semibold">No Profile Found</h4> <p class="text-sm">You don't have any profiles yet. A profile contains your personal information,
            education, and work experience. Your resume will be created without a profile, and you
            can add your information manually in the resume builder.</p> <p class="mt-2 text-sm font-medium">Don't worry! You can still create a resume and add your information directly.</p></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div class="mb-4"><label for="resume-name" class="mb-2 block text-sm font-medium text-white">Resume Name</label> `;
            Input($$payload4, {
              id: "resume-name",
              type: "text",
              placeholder: "Enter a name for your resume",
              class: "border-zinc-700 bg-zinc-800 text-white placeholder:text-gray-500",
              get value() {
                return resumeName;
              },
              set value($$value) {
                resumeName = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div role="button" tabindex="0" class="flex cursor-pointer items-center justify-between rounded-lg border border-zinc-700 p-6 hover:border-blue-500 hover:bg-zinc-800"><div class="flex items-center"><div class="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-blue-600 text-white">`;
            Plus($$payload4, { class: "h-5 w-5" });
            $$payload4.out += `<!----></div> <div><h3 class="text-lg font-medium text-white">Start From Scratch</h3> <p class="text-sm text-gray-400">`;
            if (profiles.length > 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `Start from your profile information and tailor this resume.`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `Create a new resume and add your information manually.`;
            }
            $$payload4.out += `<!--]--></p></div></div> <div class="text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"></path></svg></div></div> <div class="flex items-center justify-center"><div class="w-full border-t border-zinc-700"></div> <span class="mx-4 text-sm text-gray-400">OR</span> <div class="w-full border-t border-zinc-700"></div></div> <div><h3 class="mb-2 text-lg font-medium text-white">Use Existing Resume</h3> <p class="mb-4 text-sm text-gray-400">A new resume will be created with information prefilled from the selected resume. Uploaded
          resumes can not be used as a base and will not appear in this list.</p> `;
            if (resumes.length === 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="rounded-md border border-zinc-700 p-4 text-center text-gray-400"><p>You don't have any existing resumes yet.</p> <p class="mt-2 text-sm">Create your first resume using the "Start From Scratch" option above.</p></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
              $$payload4.out += `<div class="relative mb-4">`;
              Search($$payload4, {
                class: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"
              });
              $$payload4.out += `<!----> `;
              Input($$payload4, {
                type: "text",
                placeholder: "Search by name",
                class: "border-zinc-700 bg-zinc-800 pl-10 text-white placeholder:text-gray-500",
                get value() {
                  return searchTerm;
                },
                set value($$value) {
                  searchTerm = $$value;
                  $$settled = false;
                }
              });
              $$payload4.out += `<!----></div> <div class="max-h-60 overflow-y-auto rounded border border-zinc-700">`;
              if (filteredResumes.length === 0) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div class="p-4 text-center text-gray-400">${escape_html(searchTerm ? "No resumes match your search" : "No existing resumes found")}</div>`;
              } else {
                $$payload4.out += "<!--[!-->";
                const each_array = ensure_array_like(filteredResumes);
                $$payload4.out += `<!--[-->`;
                for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                  let resume = each_array[$$index];
                  $$payload4.out += `<div role="button" tabindex="0" class="flex cursor-pointer items-center border-b border-zinc-700 p-4 hover:bg-zinc-800"><div class="mr-3 text-blue-500">`;
                  File_text($$payload4, { class: "h-5 w-5" });
                  $$payload4.out += `<!----></div> <div class="flex-1"><p class="font-medium text-white">${escape_html(resume.label || "Untitled Resume")}</p> <div class="flex items-center">`;
                  if (resume.source === "generated") {
                    $$payload4.out += "<!--[-->";
                    $$payload4.out += `<span class="mr-2 inline-block rounded bg-purple-900 px-2 py-0.5 text-xs text-purple-300">Generated</span>`;
                  } else if (resume.source === "created") {
                    $$payload4.out += "<!--[1-->";
                    $$payload4.out += `<span class="mr-2 inline-block rounded bg-blue-900 px-2 py-0.5 text-xs text-blue-300">Created</span>`;
                  } else {
                    $$payload4.out += "<!--[!-->";
                    $$payload4.out += `<span class="mr-2 inline-block rounded bg-green-900 px-2 py-0.5 text-xs text-green-300">Uploaded</span>`;
                  }
                  $$payload4.out += `<!--]--> <span class="text-xs text-gray-400">Modified: ${escape_html(new Date(resume.updatedAt || resume.createdAt).toLocaleDateString())}</span></div></div> <div class="flex items-center gap-2"><button class="rounded-full p-2 text-gray-400 hover:bg-zinc-700 hover:text-white" title="View Resume">`;
                  Eye($$payload4, { class: "h-4 w-4" });
                  $$payload4.out += `<!----></button> <button class="rounded bg-zinc-700 px-3 py-1 text-sm text-white hover:bg-blue-600">Select</button></div></div>`;
                }
                $$payload4.out += `<!--]-->`;
              }
              $$payload4.out += `<!--]--></div>`;
            }
            $$payload4.out += `<!--]--></div></div> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => open = false,
                  class: "border-zinc-700 text-white hover:bg-zinc-800 hover:text-white",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { open, profiles, resumes });
  pop();
}
function Data_table($$payload, $$props) {
  push();
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex h-24 items-center justify-center"><p>Loading...</p></div>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function _page($$payload, $$props) {
  push();
  const { data } = $$props;
  let documents = [];
  let profiles = [];
  let showUploadModal = false;
  let showResumeCreateModal = false;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Documents | Hirli",
      description: "Manage your resumes, cover letters, and other professional documents. Upload, create, and organize your career documents.",
      keywords: "resume management, document management, cover letters, professional documents, career documents, job application documents"
    });
    $$payload2.out += `<!----> <div class="flex h-full flex-col"><div class="flex items-center justify-between px-6 py-4"><div><h1 class="text-2xl font-bold">Documents</h1> <p class="text-muted-foreground text-sm">Manage your resumes, cover letters, and other documents</p></div> <div class="flex space-x-2">`;
    Button($$payload2, {
      variant: "outline",
      onclick: () => showUploadModal = true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Upload Document`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      variant: "default",
      onclick: () => showResumeCreateModal = true,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Create Resume`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <div class="border-border w-full flex-1 border">`;
    if (documents && documents.length > 0) {
      $$payload2.out += "<!--[-->";
      Data_table($$payload2);
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div class="flex h-64 w-full items-center justify-center"><p class="text-muted-foreground">Loading documents...</p></div>`;
    }
    $$payload2.out += `<!--]--></div></div> `;
    DocumentUpload($$payload2, {
      profiles,
      get open() {
        return showUploadModal;
      },
      set open($$value) {
        showUploadModal = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    ResumeCreate($$payload2, {
      profiles,
      get open() {
        return showResumeCreateModal;
      },
      set open($$value) {
        showResumeCreateModal = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CyXejiF8.js.map
