{"version": 3, "file": "legacyDateFormat.mjs", "sources": ["../src/legacyDateFormat.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-shadow */\nimport moment from 'moment'\n\nexport const DEFAULT_DATE_FORMAT = 'YYYY-MM-DD'\nexport const DEFAULT_TIME_FORMAT = 'HH:mm'\n\nexport type ParseResult = {isValid: boolean; date?: Date; error?: string} & (\n  | {isValid: true; date: Date}\n  | {isValid: false; error?: string}\n)\n\n// todo: find a way to get rid of moment there.\n// note: the format comes from peoples schema types, so we need to deprecate it for a while and\n// find a way to tell people that they need to change it\nexport function format(input: Date, format: string, useUTC = false) {\n  if (useUTC) return moment.utc(input).format(format)\n\n  return moment(input).format(format)\n}\n\nexport function parse(dateString: string, format: string): ParseResult {\n  const parsed = moment(dateString, format, true)\n  if (parsed.isValid()) {\n    return {isValid: true, date: parsed.toDate()}\n  }\n  return {isValid: false, error: `Invalid date. Must be on the format \"${format}\"`}\n}\n"], "names": ["format"], "mappings": ";AAGa,MAAA,sBAAsB,cACtB,sBAAsB;AAU5B,SAAS,OAAO,OAAaA,SAAgB,SAAS,IAAO;AAClE,SAAI,SAAe,OAAO,IAAI,KAAK,EAAE,OAAOA,OAAM,IAE3C,OAAO,KAAK,EAAE,OAAOA,OAAM;AACpC;AAEgB,SAAA,MAAM,YAAoBA,SAA6B;AACrE,QAAM,SAAS,OAAO,YAAYA,SAAQ,EAAI;AAC9C,SAAI,OAAO,YACF,EAAC,SAAS,IAAM,MAAM,OAAO,OAAO,EAAA,IAEtC,EAAC,SAAS,IAAO,OAAO,wCAAwCA,OAAM,IAAG;AAClF;"}