import { p as push, V as copy_payload, W as assign_payload, q as pop, M as ensure_array_like, K as fallback, Q as bind_props, O as escape_html, o as setContext, a0 as slot, N as attr, J as attr_class, P as stringify, a5 as getContext } from './index3-CqUPEnZw.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root$1, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { o as openPricingModal } from './pricing-D13CEnfk.js';
import { o as onDestroy } from './index-server-CezSOnuG.js';
import { R as Root$2, P as Portal, d as Dialog_overlay, D as Dialog_content } from './index7-BURUpWjT.js';
import { B as Button } from './button-CrucCo1G.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { R as Root$3, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { P as PlanFeaturesList } from './PlanFeaturesList-C6rEbMbF.js';
import { C as Credit_card } from './credit-card-8KNeZIt3.js';
import { P as Play } from './play-DKNYqs4c.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { S as Settings } from './settings-STaOxCkl.js';
import { C as Chevron_down } from './chevron-down-xGjWLrZH.js';
import { D as Dropdown_menu_label } from './dropdown-menu-label-rJ1q7A04.js';
import { D as Dropdown_menu_separator } from './dropdown-menu-separator-B5VQzuNH.js';
import { D as Dropdown_menu_item } from './dropdown-menu-item-DwivDmnZ.js';
import { P as Pause, R as Receipt } from './receipt-DFGkwZKr.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { C as Check } from './check-WP_4Msti.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { D as Download } from './download-CLn66Ope.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './scroll-lock-BkBz2nVp.js';
import './dialog-description2-rfr-pd9k.js';
import './index2-Cut0V_vU.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './index-DjwFQdT_.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './use-grace-area.svelte-CrXiOQDy.js';

var V3_URL = 'https://js.stripe.com/v3';
var V3_URL_REGEX = /^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/;
var EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';
var findScript = function findScript() {
  var scripts = document.querySelectorAll("script[src^=\"".concat(V3_URL, "\"]"));

  for (var i = 0; i < scripts.length; i++) {
    var script = scripts[i];

    if (!V3_URL_REGEX.test(script.src)) {
      continue;
    }

    return script;
  }

  return null;
};

var injectScript = function injectScript(params) {
  var queryString = '';
  var script = document.createElement('script');
  script.src = "".concat(V3_URL).concat(queryString);
  var headOrBody = document.head || document.body;

  if (!headOrBody) {
    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');
  }

  headOrBody.appendChild(script);
  return script;
};

var registerWrapper = function registerWrapper(stripe, startTime) {
  if (!stripe || !stripe._registerWrapper) {
    return;
  }

  stripe._registerWrapper({
    name: 'stripe-js',
    version: "4.6.0",
    startTime: startTime
  });
};

var stripePromise$1 = null;
var onErrorListener = null;
var onLoadListener = null;

var onError = function onError(reject) {
  return function () {
    reject(new Error('Failed to load Stripe.js'));
  };
};

var onLoad = function onLoad(resolve, reject) {
  return function () {
    if (window.Stripe) {
      resolve(window.Stripe);
    } else {
      reject(new Error('Stripe.js not available'));
    }
  };
};

var loadScript = function loadScript(params) {
  // Ensure that we only attempt to load Stripe.js at most once
  if (stripePromise$1 !== null) {
    return stripePromise$1;
  }

  stripePromise$1 = new Promise(function (resolve, reject) {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      // Resolve to null when imported server side. This makes the module
      // safe to import in an isomorphic code base.
      resolve(null);
      return;
    }

    if (window.Stripe) {
      resolve(window.Stripe);
      return;
    }

    try {
      var script = findScript();

      if (script && params) ; else if (!script) {
        script = injectScript(params);
      } else if (script && onLoadListener !== null && onErrorListener !== null) {
        var _script$parentNode;

        // remove event listeners
        script.removeEventListener('load', onLoadListener);
        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,
        // reload script to trigger 'load' event

        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);
        script = injectScript(params);
      }

      onLoadListener = onLoad(resolve, reject);
      onErrorListener = onError(reject);
      script.addEventListener('load', onLoadListener);
      script.addEventListener('error', onErrorListener);
    } catch (error) {
      reject(error);
      return;
    }
  }); // Resets stripePromise on error

  return stripePromise$1["catch"](function (error) {
    stripePromise$1 = null;
    return Promise.reject(error);
  });
};
var initStripe = function initStripe(maybeStripe, args, startTime) {
  if (maybeStripe === null) {
    return null;
  }

  var stripe = maybeStripe.apply(undefined, args);
  registerWrapper(stripe, startTime);
  return stripe;
}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types

var stripePromise$1$1;
var loadCalled = false;

var getStripePromise = function getStripePromise() {
  if (stripePromise$1$1) {
    return stripePromise$1$1;
  }

  stripePromise$1$1 = loadScript(null)["catch"](function (error) {
    // clear cache on error
    stripePromise$1$1 = null;
    return Promise.reject(error);
  });
  return stripePromise$1$1;
}; // Execute our own script injection after a tick to give users time to do their
// own script injection.


Promise.resolve().then(function () {
  return getStripePromise();
})["catch"](function (error) {
  if (!loadCalled) {
    console.warn(error);
  }
});
var loadStripe = function loadStripe() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }

  loadCalled = true;
  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script

  return getStripePromise().then(function (maybeStripe) {
    return initStripe(maybeStripe, args, startTime);
  });
};

const isServer = typeof window === "undefined";
function register(stripe) {
  if (!isServer) {
    return stripe.registerAppInfo({
      name: "hirli.co",
      url: "https://hirli.co"
    });
  }
}
const stripePublishableKey = "pk_live_51R8SnHL0zwkUpKXmx71gq4MzhNjxyIM56jJHQFisYQaG22f9cGu8Rcg7eI2z8pUe7ef1n3WRsC2zTo7TjwsaFQ9X00BuQFNCqO";
if (!isServer) {
  console.log(
    "Stripe key available:",
    true,
    `(starts with ${stripePublishableKey.substring(0, 3)}...)`
  );
}
const stripePromise = loadStripe(stripePublishableKey);
function PaymentElement($$payload, $$props) {
  push();
  let element;
  const { elements } = getContext("stripe");
  let options = fallback($$props["options"], void 0);
  function blur() {
    element.blur();
  }
  function clear() {
    element.clear();
  }
  function destroy() {
    element.destroy();
  }
  function focus() {
    element.focus();
  }
  $$payload.out += `<div></div>`;
  bind_props($$props, { options, blur, clear, destroy, focus });
  pop();
}
function Elements($$payload, $$props) {
  push();
  let appearance;
  let stripe = $$props["stripe"];
  let mode = fallback($$props["mode"], void 0);
  let theme = fallback($$props["theme"], "stripe");
  let variables = fallback($$props["variables"], () => ({}), true);
  let rules = fallback($$props["rules"], () => ({}), true);
  let labels = fallback($$props["labels"], "above");
  let loader = fallback($$props["loader"], "auto");
  let fonts = fallback($$props["fonts"], () => [], true);
  let locale = fallback($$props["locale"], "auto");
  let currency = fallback($$props["currency"], void 0);
  let amount = fallback($$props["amount"], void 0);
  let clientSecret = fallback($$props["clientSecret"], void 0);
  let elements = fallback($$props["elements"], null);
  appearance = { theme, variables, rules, labels };
  if (stripe && !elements) {
    elements = stripe.elements({
      mode,
      currency,
      amount,
      appearance,
      clientSecret,
      fonts,
      loader,
      locale
    });
    register(stripe);
    setContext("stripe", { stripe, elements });
  }
  if (elements) {
    elements.update({ appearance, locale });
  }
  if (stripe && elements) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", {}, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, {
    stripe,
    mode,
    theme,
    variables,
    rules,
    labels,
    loader,
    fonts,
    locale,
    currency,
    amount,
    clientSecret,
    elements
  });
  pop();
}
function AddPaymentMethodModal($$payload, $$props) {
  push();
  let open = fallback($$props["open"], false);
  let onSuccess = fallback($$props["onSuccess"], () => {
  });
  let isLoading = false;
  let error = null;
  let clientSecret = null;
  let stripeInstance = null;
  let initialized = false;
  let paymentElementReady = false;
  let elementsComponent = null;
  async function initializeStripe() {
    try {
      stripeInstance = await stripePromise;
      if (!stripeInstance) {
        console.error("Failed to initialize Stripe: No Stripe instance created");
        error = "Failed to initialize Stripe. Please try again later.";
        return;
      }
      console.log("Stripe initialized successfully", {
        type: typeof stripeInstance,
        hasConfirmSetup: !!stripeInstance.confirmSetup
      });
      await createSetupIntent();
      if (stripeInstance && clientSecret) {
        console.log("Client secret obtained, ready to create Elements instance");
      }
    } catch (err) {
      console.error("Error initializing Stripe:", err);
      error = "Failed to initialize payment system. Please try again later.";
    }
  }
  async function createSetupIntent() {
    try {
      isLoading = true;
      error = null;
      console.log("Creating setup intent...");
      const authCheckResponse = await fetch("/api/auth/check-session", {
        method: "GET",
        credentials: "include"
        // Ensure cookies are sent
      });
      if (!authCheckResponse.ok) {
        console.warn("User is not authenticated");
        error = "You need to be signed in to add a payment method. Please sign in and try again.";
        throw new Error("Authentication required");
      }
      const response = await fetch("/api/billing/create-setup-intent", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        // Ensure cookies are sent
        body: JSON.stringify({ paymentMethod: "card" })
      });
      if (!response.ok) {
        let errorMessage = "Unknown error";
        try {
          const errorData = await response.json();
          console.error("Setup intent error response:", errorData);
          errorMessage = errorData.error || "Failed to create setup intent";
        } catch (e) {
          const errorText = await response.text();
          console.error("Setup intent error response:", errorText);
          errorMessage = errorText;
        }
        if (response.status === 401) {
          error = "You need to be signed in to add a payment method. Please sign in and try again.";
          throw new Error("Authentication required");
        } else {
          throw new Error(`Failed to create setup intent: ${errorMessage}`);
        }
      }
      const data = await response.json();
      console.log("Setup intent created successfully");
      clientSecret = data.clientSecret;
    } catch (err) {
      console.error("Error creating setup intent:", err);
      if (!error) {
        error = "Failed to initialize payment form. Please try again later.";
      }
    } finally {
      isLoading = false;
    }
  }
  onDestroy(() => {
    initialized = false;
  });
  async function handleSubmit(event = null) {
    if (event) event.preventDefault();
    if (!stripeInstance || !clientSecret || !elementsComponent) {
      error = "Payment processing is not available. Please try again later.";
      return;
    }
    if (!paymentElementReady) {
      error = "Payment form is still loading. Please wait a moment and try again.";
      return;
    }
    isLoading = true;
    error = null;
    try {
      console.log("Confirming card setup...");
      const { error: confirmError, setupIntent } = await stripeInstance.confirmSetup({
        elements: elementsComponent,
        confirmParams: {},
        // Don't include return_url to prevent automatic redirect
        redirect: "if_required"
      });
      console.log("Setup confirmation result:", { confirmError, setupIntent });
      if (confirmError) {
        console.error("Confirmation error:", confirmError);
        error = confirmError.message || "An error occurred while processing your payment method.";
      } else {
        console.log("Payment method added successfully");
        toast.success("Your payment method has been added successfully.");
        try {
          const response = await fetch("/api/billing/get-payment-methods", {
            method: "GET",
            credentials: "include"
            // Ensure cookies are sent
          });
          if (response.ok) {
            const data = await response.json();
            console.log("Updated payment methods:", data.paymentMethods);
            open = false;
            onSuccess(data.paymentMethods);
          } else {
            console.error("Failed to fetch updated payment methods");
            open = false;
            onSuccess();
          }
        } catch (err) {
          console.error("Error fetching updated payment methods:", err);
          open = false;
          onSuccess();
        }
      }
    } catch (err) {
      console.error("Error submitting payment form:", err);
      if (err.message === "Stripe is not properly initialized") {
        error = "Payment system is not properly initialized. Please refresh the page and try again.";
      } else if (err.message && err.message.includes("Stripe")) {
        error = `Stripe error: ${err.message}`;
      } else {
        error = "Failed to process your payment method. Please try again later.";
      }
    } finally {
      isLoading = false;
    }
  }
  if (open && !initialized) {
    error = null;
    isLoading = false;
    initialized = true;
    paymentElementReady = false;
    initializeStripe();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$2($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_overlay($$payload4, {});
            $$payload4.out += `<!----> `;
            Dialog_content($$payload4, {
              class: "p-0 sm:max-w-[425px]",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  class: "border-border gap-1 border-b p-4",
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Add Payment Method`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Add a new payment method to your account.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="p-4">`;
                if (isLoading && !clientSecret) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="flex justify-center">`;
                  Loader_circle($$payload5, { class: "text-primary h-8 w-8 animate-spin" });
                  $$payload5.out += `<!----></div>`;
                } else if (error) {
                  $$payload5.out += "<!--[1-->";
                  $$payload5.out += `<div class="bg-destructive/10 text-destructive rounded-md"><p>${escape_html(error)}</p></div>`;
                } else if (stripeInstance && clientSecret) {
                  $$payload5.out += "<!--[2-->";
                  $$payload5.out += `<form id="payment-form">`;
                  Elements($$payload5, {
                    stripe: stripeInstance,
                    clientSecret,
                    get elements() {
                      return elementsComponent;
                    },
                    set elements($$value) {
                      elementsComponent = $$value;
                      $$settled = false;
                    },
                    children: ($$payload6) => {
                      PaymentElement($$payload6, {});
                      $$payload6.out += `<!----> `;
                      if (error) {
                        $$payload6.out += "<!--[-->";
                        $$payload6.out += `<div class="text-destructive mt-2 text-sm"><p>${escape_html(error)}</p></div>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></form>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  $$payload5.out += `<div class="flex justify-center"><p class="text-muted-foreground">Loading payment form...</p></div>`;
                }
                $$payload5.out += `<!--]--></div> `;
                Dialog_footer($$payload5, {
                  class: "border-border gap-2 border-t p-2",
                  children: ($$payload6) => {
                    Button($$payload6, {
                      onclick: () => open = false,
                      disabled: isLoading,
                      type: "button",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Cancel`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Button($$payload6, {
                      variant: "outline",
                      onclick: handleSubmit,
                      disabled: isLoading || !clientSecret || !stripeInstance || !elementsComponent || !paymentElementReady,
                      type: "button",
                      children: ($$payload7) => {
                        if (isLoading) {
                          $$payload7.out += "<!--[-->";
                          Loader_circle($$payload7, { class: "mr-2 h-4 w-4 animate-spin" });
                          $$payload7.out += `<!----> Processing...`;
                        } else {
                          $$payload7.out += "<!--[!-->";
                          $$payload7.out += `Add Card`;
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { open, onSuccess });
  pop();
}
async function setDefaultPaymentMethod(paymentMethodId) {
  try {
    const response = await fetch("/api/billing/set-default-payment-method", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      credentials: "include",
      // Ensure cookies are sent
      body: JSON.stringify({ paymentMethodId })
    });
    if (!response.ok) {
      throw new Error("Failed to set default payment method");
    }
    const data = await response.json();
    return data.paymentMethods;
  } catch (error) {
    console.error("Error setting default payment method:", error);
    throw error;
  }
}
async function deletePaymentMethod(paymentMethodId) {
  try {
    const response = await fetch("/api/billing/delete-payment-method", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      credentials: "include",
      // Ensure cookies are sent
      body: JSON.stringify({ paymentMethodId })
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.message || "Failed to delete payment method");
    }
    const data = await response.json();
    return data.paymentMethods;
  } catch (error) {
    console.error("Error deleting payment method:", error);
    throw error;
  }
}
function Status($$payload, $$props) {
  push();
  const { $$slots, $$events, ...props } = $$props;
  const STATUS_CONFIG = {
    FREE: {
      text: "Free",
      class: "bg-gray-100 text-gray-800"
    },
    INACTIVE: {
      text: "Inactive",
      class: "bg-gray-100 text-gray-800"
    },
    INCOMPLETE: {
      text: "Incomplete",
      class: "bg-orange-100 text-orange-800"
    },
    PAUSED: {
      text: "Paused",
      class: "bg-yellow-100 text-yellow-800"
    },
    CANCELING: {
      text: "Canceling",
      class: "bg-red-100 text-red-800"
    },
    PAST_DUE: {
      text: "Past Due",
      class: "bg-orange-100 text-orange-800"
    },
    TRIAL: {
      text: "Trial",
      class: "bg-blue-100 text-blue-800"
    },
    ACTIVE: {
      text: "Active",
      class: "bg-green-100 text-green-800"
    }
  };
  function getSubscriptionStatus(subscription) {
    if (!subscription) {
      return STATUS_CONFIG.FREE;
    }
    const status2 = subscription.status;
    if (["canceled", "unpaid", "incomplete_expired"].includes(status2)) {
      return STATUS_CONFIG.INACTIVE;
    }
    if (status2 === "incomplete") {
      return STATUS_CONFIG.INCOMPLETE;
    }
    if (subscription.isPaused || subscription.metadata?.pause_at_period_end === "true" || subscription.pause_collection || status2 === "pausing_at_period_end" || status2 === "paused" || (subscription.cancel_at_period_end || subscription.cancelAtPeriodEnd) && subscription.metadata?.action_at_period_end === "pause") {
      return STATUS_CONFIG.PAUSED;
    }
    if (subscription.cancel_at_period_end || subscription.cancelAtPeriodEnd) {
      return STATUS_CONFIG.CANCELING;
    }
    if (status2 === "past_due") {
      return STATUS_CONFIG.PAST_DUE;
    }
    if (status2 === "trialing") {
      return STATUS_CONFIG.TRIAL;
    }
    return STATUS_CONFIG.ACTIVE;
  }
  let status = getSubscriptionStatus(props.subscription);
  let statusText = status.text;
  let statusClass = status.class;
  $$payload.out += `<span${attr_class(`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${stringify(statusClass)}`)}>${escape_html(statusText)}</span>`;
  pop();
}
function Subscription($$payload, $$props) {
  push();
  const { $$slots, $$events, ...props } = $$props;
  function getUserRole() {
    return props.user?.role || "free";
  }
  function hasStripeCustomerId() {
    return !!props.user?.stripeCustomerId;
  }
  function formatDate(date) {
    console.log("Formatting date:", date, typeof date);
    if (!date) {
      console.warn("No date provided, using fallback date");
      date = new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    }
    try {
      const d = new Date(date);
      console.log("Parsed date:", d, "isValid:", !isNaN(d.getTime()));
      if (isNaN(d.getTime())) {
        console.warn("Invalid date:", date, "using fallback date");
        return new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric"
        });
      }
      return d.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    } catch (e) {
      console.error("Error formatting date:", e, date, "using fallback date");
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    }
  }
  function getPlanChangesDate(subscription) {
    if (!subscription) {
      console.warn("No subscription provided, using fallback date");
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    }
    console.log("Getting plan changes date from subscription:", {
      planChangesOnDate: subscription.planChangesOnDate,
      current_period_end: subscription.current_period_end,
      currentPeriodEnd: subscription.currentPeriodEnd,
      items: subscription.items
    });
    try {
      if (subscription.planChangesOnDate) {
        if (subscription.planChangesOnDate instanceof Date) {
          return subscription.planChangesOnDate;
        }
        const date = new Date(subscription.planChangesOnDate);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
      if (subscription.current_period_end) {
        if (subscription.current_period_end instanceof Date) {
          return subscription.current_period_end;
        }
        if (typeof subscription.current_period_end === "number") {
          const date = new Date(subscription.current_period_end * 1e3);
          if (!isNaN(date.getTime())) {
            return date;
          }
        } else {
          const date = new Date(subscription.current_period_end);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
      }
      if (subscription.currentPeriodEnd) {
        if (subscription.currentPeriodEnd instanceof Date) {
          return subscription.currentPeriodEnd;
        }
        const date = new Date(subscription.currentPeriodEnd);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
      if (subscription.items && Array.isArray(subscription.items) && subscription.items.length > 0) {
        const firstItem = subscription.items[0];
        console.log("Checking subscription item for period end date:", firstItem);
        if (firstItem.current_period_end) {
          const date = new Date(firstItem.current_period_end * 1e3);
          if (!isNaN(date.getTime())) {
            console.log("Found valid date in subscription item:", date);
            return date;
          }
        }
      }
      console.warn("Could not find valid date in subscription, using fallback date", subscription);
    } catch (error) {
      console.error("Error getting plan changes date:", error);
    }
    return new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
  }
  function formatCurrency(amount, currency = "USD") {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase()
    }).format(amount / 100);
  }
  $$payload.out += `<div class="border-border flex items-end justify-between border-b p-4"><div class="flex flex-col gap-0"><h4 class="text-md font-normal">Current Plan</h4> <p class="text-muted-foreground text-sm">Your current subscription plan and usage.</p></div> `;
  Status($$payload, { subscription: props.subscription });
  $$payload.out += `<!----></div> <div class="grid grid-cols-2 gap-6 p-4"><div class="flex w-2/3 flex-col"><h3 class="text-lg font-medium">${escape_html(props.currentPlan?.name || getUserRole().charAt(0).toUpperCase() + getUserRole().slice(1))} Plan</h3> <p class="text-muted-foreground">`;
  if (props.subscription?.price?.unitAmount) {
    $$payload.out += "<!--[-->";
    $$payload.out += `${escape_html(formatCurrency(props.subscription.price.unitAmount, props.subscription.price.currency))}
        /${escape_html(props.subscription.price.interval)} `;
    if (props.subscription.price.intervalCount > 1) {
      $$payload.out += "<!--[-->";
      $$payload.out += `(billed every ${escape_html(props.subscription.price.intervalCount)}
          ${escape_html(props.subscription.price.interval)}s)`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `$${escape_html((props.currentPlan?.monthlyPrice || 0) / 100)}/month`;
  }
  $$payload.out += `<!--]--></p> `;
  if (props.subscription) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-2 text-sm"><span class="text-muted-foreground">`;
    if (props.subscription.status === "canceled" || props.subscription.status === "cancelled") {
      $$payload.out += "<!--[-->";
      $$payload.out += `Your subscription has been cancelled. You can upgrade to a new plan at any time.`;
    } else if (props.subscription.isPaused || props.subscription.pause_collection || props.subscription.metadata?.pause_at_period_end === "true" || props.subscription.status === "paused" || props.subscription.status === "pausing_at_period_end") {
      $$payload.out += "<!--[1-->";
      $$payload.out += `Your subscription is paused. You can resume your subscription at any time before it
            ends.`;
    } else if (props.subscription.cancelAtPeriodEnd || props.subscription.cancel_at_period_end) {
      $$payload.out += "<!--[2-->";
      $$payload.out += `Your subscription will be canceled at the end of the current billing period. You can
            reactivate your subscription at any time before the period ends.`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `Your subscription is active and will automatically renew at the end of the billing
            period.`;
    }
    $$payload.out += `<!--]--></span></div> <div class="mt-2 flex items-center gap-2"><div class="flex items-center"><span class="text-sm">`;
    if (props.subscription.status === "canceled" || props.subscription.status === "cancelled") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="font-medium">Cancelled</span> `;
      if (props.subscription.canceled_at || props.subscription.canceledAt) {
        $$payload.out += "<!--[-->";
        $$payload.out += ` on ${escape_html(formatDate(props.subscription.canceled_at || props.subscription.canceledAt))}`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]-->`;
    } else if (props.subscription.isPaused || props.subscription.pause_collection || props.subscription.metadata?.pause_at_period_end === "true" || props.subscription.status === "paused" || props.subscription.status === "pausing_at_period_end") {
      $$payload.out += "<!--[1-->";
      $$payload.out += `<span class="font-medium">Paused</span>`;
    } else if (props.subscription.cancelAtPeriodEnd || props.subscription.cancel_at_period_end) {
      $$payload.out += "<!--[2-->";
      const endDate = props.subscription.current_period_end instanceof Date ? props.subscription.current_period_end : props.subscription.currentPeriodEnd instanceof Date ? props.subscription.currentPeriodEnd : props.subscription.items?.[0]?.current_period_end ? new Date(props.subscription.items[0].current_period_end * 1e3) : null;
      $$payload.out += `<span class="font-medium">Ends on:</span>  `;
      if (endDate) {
        $$payload.out += "<!--[-->";
        $$payload.out += ` ${escape_html(formatDate(endDate))}`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += ` ${escape_html(formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3)))}`;
      }
      $$payload.out += `<!--]-->`;
    } else {
      $$payload.out += "<!--[!-->";
      const endDate = props.subscription.current_period_end instanceof Date ? props.subscription.current_period_end : props.subscription.currentPeriodEnd instanceof Date ? props.subscription.currentPeriodEnd : props.subscription.items?.[0]?.current_period_end ? new Date(props.subscription.items[0].current_period_end * 1e3) : null;
      $$payload.out += `<span class="font-medium">Renews on:</span>  `;
      if (endDate) {
        $$payload.out += "<!--[-->";
        $$payload.out += ` ${escape_html(formatDate(endDate))}`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += ` ${escape_html(formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3)))}`;
      }
      $$payload.out += `<!--]-->`;
    }
    $$payload.out += `<!--]--></span></div> `;
    if (props.subscription.status === "canceled" || props.subscription.status === "cancelled") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="text-sm text-red-500">(Subscription cancelled)</span>`;
    } else if (props.subscription.isPaused || props.subscription.pause_collection || props.subscription.metadata?.pause_at_period_end === "true" || props.subscription.status === "paused" || props.subscription.status === "pausing_at_period_end") {
      $$payload.out += "<!--[1-->";
    } else if (props.subscription.cancelAtPeriodEnd || props.subscription.cancel_at_period_end) {
      $$payload.out += "<!--[2-->";
      $$payload.out += `<span class="text-sm text-red-500">(Cancels at end of period)</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> `;
    if (!props.subscription.isPaused && !props.subscription.pause_collection && props.subscription.status !== "canceled" && props.subscription.status !== "cancelled" && !props.subscription.cancelAtPeriodEnd && !props.subscription.cancel_at_period_end) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="mt-1 text-sm"><span class="text-muted-foreground">Current period:</span> <span>`;
      if (props.subscription) {
        $$payload.out += "<!--[-->";
        const startDate = props.subscription.current_period_start instanceof Date ? props.subscription.current_period_start : props.subscription.currentPeriodStart instanceof Date ? props.subscription.currentPeriodStart : props.subscription.items?.[0]?.current_period_start ? new Date(props.subscription.items[0].current_period_start * 1e3) : null;
        const endDate = props.subscription.current_period_end instanceof Date ? props.subscription.current_period_end : props.subscription.currentPeriodEnd instanceof Date ? props.subscription.currentPeriodEnd : props.subscription.items?.[0]?.current_period_end ? new Date(props.subscription.items[0].current_period_end * 1e3) : null;
        $$payload.out += `${escape_html(console.log("Subscription period data in component:", {
          start_snake: props.subscription.current_period_start,
          start_camel: props.subscription.currentPeriodStart,
          end_snake: props.subscription.current_period_end,
          end_camel: props.subscription.currentPeriodEnd,
          items: props.subscription.items,
          subscription: props.subscription
        }))}  `;
        if (startDate && endDate) {
          $$payload.out += "<!--[-->";
          $$payload.out += `${escape_html(formatDate(startDate))} - ${escape_html(formatDate(endDate))}`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `${escape_html(formatDate(/* @__PURE__ */ new Date()))} - ${escape_html(formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3)))}`;
        }
        $$payload.out += `<!--]-->`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (props.subscription.isPaused || props.subscription.pause_collection) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="mt-1 text-sm"><span class="text-muted-foreground">Subscription ends:</span> <span class="text-yellow-600">`;
      if (props.subscription.planChangesOnDate instanceof Date) {
        $$payload.out += "<!--[-->";
        $$payload.out += `${escape_html(formatDate(props.subscription.planChangesOnDate))}`;
      } else {
        $$payload.out += "<!--[!-->";
        const endDate = props.subscription.current_period_end instanceof Date ? props.subscription.current_period_end : props.subscription.currentPeriodEnd instanceof Date ? props.subscription.currentPeriodEnd : props.subscription.items?.[0]?.current_period_end ? new Date(props.subscription.items[0].current_period_end * 1e3) : null;
        if (endDate) {
          $$payload.out += "<!--[-->";
          $$payload.out += `${escape_html(formatDate(endDate))}`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `${escape_html(formatDate(getPlanChangesDate(props.subscription)))}`;
        }
        $$payload.out += `<!--]-->`;
      }
      $$payload.out += `<!--]--></span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (props.subscription.status === "canceled" || props.subscription.status === "cancelled") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="mt-1 text-sm"><span class="text-muted-foreground">Subscription cancelled:</span> <span class="text-red-600">`;
      if (props.subscription.canceled_at || props.subscription.canceledAt) {
        $$payload.out += "<!--[-->";
        const cancelDate = props.subscription.canceled_at instanceof Date ? props.subscription.canceled_at : props.subscription.canceledAt instanceof Date ? props.subscription.canceledAt : typeof props.subscription.canceled_at === "number" ? new Date(props.subscription.canceled_at * 1e3) : typeof props.subscription.canceledAt === "number" ? new Date(props.subscription.canceledAt * 1e3) : null;
        if (cancelDate) {
          $$payload.out += "<!--[-->";
          $$payload.out += `${escape_html(formatDate(cancelDate))}`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `${escape_html(formatDate(/* @__PURE__ */ new Date()))}`;
        }
        $$payload.out += `<!--]-->`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `${escape_html(formatDate(/* @__PURE__ */ new Date()))}`;
      }
      $$payload.out += `<!--]--></span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (props.subscription.cancelAtPeriodEnd || props.subscription.cancel_at_period_end) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="mt-1 text-sm"><span class="text-muted-foreground">Subscription will be cancelled on:</span> <span class="text-red-600">`;
      if (props.subscription) {
        $$payload.out += "<!--[-->";
        if (props.subscription.current_period_end instanceof Date) {
          $$payload.out += "<!--[-->";
          $$payload.out += `${escape_html(formatDate(props.subscription.current_period_end))}`;
        } else if (props.subscription.currentPeriodEnd instanceof Date) {
          $$payload.out += "<!--[1-->";
          $$payload.out += `${escape_html(formatDate(props.subscription.currentPeriodEnd))}`;
        } else if (props.subscription.items?.[0]?.current_period_end) {
          $$payload.out += "<!--[2-->";
          $$payload.out += `${escape_html(formatDate(new Date(props.subscription.items[0].current_period_end * 1e3)))}`;
        } else {
          $$payload.out += "<!--[!-->";
          $$payload.out += `${escape_html(formatDate(getPlanChangesDate(props.subscription)))}`;
        }
        $$payload.out += `<!--]-->`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  } else if (getUserRole() !== "free") {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="mt-2 text-sm"><span class="text-muted-foreground">Your ${escape_html(props.currentPlan?.name)} plan is active. You can manage your subscription settings below.</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="mt-6 flex gap-2">`;
  if (props.subscription) {
    $$payload.out += "<!--[-->";
    if (props.subscription.status === "canceled" || props.subscription.status === "cancelled") {
      $$payload.out += "<!--[-->";
      Button($$payload, {
        variant: "outline",
        disabled: props.isLoading,
        onclick: props.handleOpenPricingModal,
        children: ($$payload2) => {
          if (props.isLoading) {
            $$payload2.out += "<!--[-->";
            Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
            $$payload2.out += `<!----> Loading...`;
          } else {
            $$payload2.out += "<!--[!-->";
            Credit_card($$payload2, { class: "mr-2 h-4 w-4" });
            $$payload2.out += `<!----> Upgrade Plan`;
          }
          $$payload2.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    } else if (props.subscription.isPaused || props.subscription.pause_collection || props.subscription.metadata?.pause_at_period_end === "true" || props.subscription.status === "paused" || props.subscription.status === "pausing_at_period_end") {
      $$payload.out += "<!--[1-->";
      Button($$payload, {
        variant: "outline",
        disabled: props.isSubscriptionActionLoading,
        onclick: props.handleResumeSubscription,
        children: ($$payload2) => {
          if (props.isSubscriptionActionLoading) {
            $$payload2.out += "<!--[-->";
            Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
            $$payload2.out += `<!----> Resuming...`;
          } else {
            $$payload2.out += "<!--[!-->";
            Play($$payload2, { class: "mr-2 h-4 w-4" });
            $$payload2.out += `<!----> Resume Subscription`;
          }
          $$payload2.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    } else if (props.subscription.cancelAtPeriodEnd || props.subscription.cancel_at_period_end) {
      $$payload.out += "<!--[2-->";
      Button($$payload, {
        variant: "outline",
        disabled: props.isSubscriptionActionLoading,
        onclick: props.handleResumeSubscription,
        children: ($$payload2) => {
          if (props.isSubscriptionActionLoading) {
            $$payload2.out += "<!--[-->";
            Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
            $$payload2.out += `<!----> Reactivating...`;
          } else {
            $$payload2.out += "<!--[!-->";
            Refresh_cw($$payload2, { class: "mr-2 h-4 w-4" });
            $$payload2.out += `<!----> Reactivate Subscription`;
          }
          $$payload2.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<!---->`;
      Root$3($$payload, {
        children: ($$payload2) => {
          $$payload2.out += `<!---->`;
          Dropdown_menu_trigger($$payload2, {
            children: ($$payload3) => {
              Button($$payload3, {
                variant: "outline",
                disabled: props.isSubscriptionActionLoading,
                children: ($$payload4) => {
                  Settings($$payload4, { class: "mr-2 h-4 w-4" });
                  $$payload4.out += `<!----> Manage Subscription `;
                  Chevron_down($$payload4, { class: "ml-2 h-4 w-4" });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> <!---->`;
          Dropdown_menu_content($$payload2, {
            children: ($$payload3) => {
              $$payload3.out += `<!---->`;
              Dropdown_menu_label($$payload3, {
                children: ($$payload4) => {
                  $$payload4.out += `<!---->Subscription Options`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----> <!---->`;
              Dropdown_menu_separator($$payload3, {});
              $$payload3.out += `<!----> <!---->`;
              Dropdown_menu_item($$payload3, {
                onclick: props.handleOpenPricingModal,
                children: ($$payload4) => {
                  Credit_card($$payload4, { class: "mr-2 h-4 w-4" });
                  $$payload4.out += `<!----> <span>Change Plan</span>`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----> <!---->`;
              Dropdown_menu_item($$payload3, {
                onclick: () => props.setPauseDialogOpen(true),
                children: ($$payload4) => {
                  Pause($$payload4, { class: "mr-2 h-4 w-4" });
                  $$payload4.out += `<!----> <span>Pause Subscription</span>`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----> <!---->`;
              Dropdown_menu_item($$payload3, {
                onclick: () => props.setCancelDialogOpen(true),
                children: ($$payload4) => {
                  Circle_x($$payload4, { class: "mr-2 h-4 w-4" });
                  $$payload4.out += `<!----> <span>Cancel Subscription</span>`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload.out += `<!---->`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    Button($$payload, {
      variant: "outline",
      disabled: props.isLoading,
      onclick: props.handleOpenPricingModal,
      children: ($$payload2) => {
        if (props.isLoading) {
          $$payload2.out += "<!--[-->";
          Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
          $$payload2.out += `<!----> Loading...`;
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `${escape_html(hasStripeCustomerId() ? "Change Plan" : "Upgrade")}`;
        }
        $$payload2.out += `<!--]-->`;
      },
      $$slots: { default: true }
    });
  }
  $$payload.out += `<!--]--></div></div> <div class="flex w-1/3 flex-col space-y-4"><div class="rounded-md border p-4"><h4 class="mb-4 font-medium">Plan Features</h4> `;
  if (props.currentPlan) {
    $$payload.out += "<!--[-->";
    PlanFeaturesList($$payload, { plan: props.currentPlan });
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="text-muted-foreground text-sm">No features available for this plan.</p>`;
  }
  $$payload.out += `<!--]--></div></div></div>`;
  pop();
}
function Payment($$payload, $$props) {
  push();
  const { $$slots, $$events, ...props } = $$props;
  $$payload.out += `<div><div class="border-border flex items-end justify-between border-b p-4"><div class="flex flex-col gap-0"><h4 class="text-md font-normal">Payment Methods</h4> <p class="text-muted-foreground text-sm">View and manage your payment methods.</p></div> `;
  if (props.paymentMethods && props.paymentMethods.length > 0) {
    $$payload.out += "<!--[-->";
    Button($$payload, {
      variant: "outline",
      size: "sm",
      onclick: () => props.setAddPaymentMethodModalOpen(true),
      children: ($$payload2) => {
        Plus($$payload2, { class: "mr-2 h-4 w-4" });
        $$payload2.out += `<!----> Add Method`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="p-4">`;
  if (props.paymentMethods && props.paymentMethods.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(props.paymentMethods);
    $$payload.out += `<div class="space-y-4"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let method = each_array[$$index];
      $$payload.out += `<div class="flex items-center justify-between rounded-lg border p-4"><div class="flex items-center gap-4"><div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">`;
      Credit_card($$payload, { class: "text-primary h-5 w-5" });
      $$payload.out += `<!----></div> <div><p class="font-medium">${escape_html(method.card.brand.charAt(0).toUpperCase() + method.card.brand.slice(1))} ending in
                ${escape_html(method.card.last4)}</p> <p class="text-muted-foreground text-sm">Expires ${escape_html(method.card.exp_month)}/${escape_html(method.card.exp_year)}</p></div></div> <div class="flex items-center gap-2">`;
      if (method.isDefault) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">Default</span>`;
      } else {
        $$payload.out += "<!--[!-->";
        Button($$payload, {
          variant: "outline",
          size: "sm",
          disabled: props.isPaymentMethodLoading,
          onclick: () => props.setDefaultPaymentMethod(method.id),
          children: ($$payload2) => {
            if (props.isPaymentMethodLoading) {
              $$payload2.out += "<!--[-->";
              Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
              $$payload2.out += `<!----> Setting...`;
            } else {
              $$payload2.out += "<!--[!-->";
              Check($$payload2, { class: "mr-2 h-4 w-4" });
              $$payload2.out += `<!----> Set Default`;
            }
            $$payload2.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
      }
      $$payload.out += `<!--]--> <div class="relative"${attr("title", method.isDefault ? "Cannot delete the default payment method" : props.paymentMethods.length <= 1 ? "Cannot delete your only payment method" : "Remove this payment method")}>`;
      Button($$payload, {
        variant: "ghost",
        size: "sm",
        disabled: props.isDeletePaymentMethodLoading || method.isDefault || props.paymentMethods.length <= 1,
        onclick: () => props.openDeleteDialog(method.id),
        children: ($$payload2) => {
          if (props.isDeletePaymentMethodLoading && props.paymentMethodToDelete === method.id) {
            $$payload2.out += "<!--[-->";
            Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
            $$payload2.out += `<!----> Deleting...`;
          } else {
            $$payload2.out += "<!--[!-->";
            Trash_2($$payload2, { class: "mr-2 h-4 w-4" });
            $$payload2.out += `<!----> Remove`;
          }
          $$payload2.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload.out += `<!----></div></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="rounded-lg border border-dashed p-6 text-center"><div class="mb-4 flex justify-center"><div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">`;
    Credit_card($$payload, { class: "text-primary h-6 w-6" });
    $$payload.out += `<!----></div></div> <h3 class="mb-2 text-lg font-medium">No Payment Methods</h3> <p class="text-muted-foreground">You haven't added any payment methods yet.</p> <div class="mt-6">`;
    Button($$payload, {
      variant: "default",
      disabled: !props.user?.stripeCustomerId,
      onclick: props.user?.stripeCustomerId ? () => props.setAddPaymentMethodModalOpen(true) : () => props.handlePlanChange("casual"),
      children: ($$payload2) => {
        Credit_card($$payload2, { class: "mr-2 h-4 w-4" });
        $$payload2.out += `<!----> ${escape_html(props.user?.stripeCustomerId ? "Add First Payment Method" : "Subscribe to Add Payment Method")}`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div></div>`;
  }
  $$payload.out += `<!--]--></div> <div class="flex justify-between p-6"><div class="flex items-center gap-2">`;
  Shield($$payload, { class: "text-muted-foreground h-5 w-5" });
  $$payload.out += `<!----> <p class="text-muted-foreground text-sm">Your payment information is secure and encrypted</p></div></div>`;
  pop();
}
function Invoices($$payload, $$props) {
  push();
  const { $$slots, $$events, ...props } = $$props;
  function formatDate(date) {
    if (!date) return "N/A";
    try {
      const d = new Date(date);
      if (isNaN(d.getTime())) {
        console.warn("Invalid date:", date);
        return "N/A";
      }
      return d.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    } catch (e) {
      console.error("Error formatting date:", e, date);
      return "N/A";
    }
  }
  function getPaymentStatusLabel(invoice) {
    if (invoice.status === "paid") {
      return {
        text: "Paid",
        class: "bg-green-100 text-green-800"
      };
    } else if (invoice.status === "open") {
      return {
        text: "Unpaid",
        class: "bg-yellow-100 text-yellow-800"
      };
    } else if (invoice.status === "draft") {
      return {
        text: "Draft",
        class: "bg-gray-100 text-gray-800"
      };
    } else if (invoice.status === "void") {
      return {
        text: "Void",
        class: "bg-red-100 text-red-800"
      };
    } else {
      return {
        text: invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1),
        class: "bg-gray-100 text-gray-800"
      };
    }
  }
  $$payload.out += `<div class="border-border flex flex-row items-end justify-between border-b p-4"><div class="flex flex-col gap-0"><h4 class="text-md font-normal">Billing History</h4> <p class="text-muted-foreground text-sm">View and download your invoices.</p></div> <div class="flex gap-2">`;
  Button($$payload, {
    variant: "outline",
    size: "sm",
    disabled: props.isPortalLoading || !props.user?.stripeCustomerId,
    onclick: props.openCustomerPortal,
    children: ($$payload2) => {
      if (props.isPortalLoading) {
        $$payload2.out += "<!--[-->";
        Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
        $$payload2.out += `<!----> Loading...`;
      } else {
        $$payload2.out += "<!--[!-->";
        Receipt($$payload2, { class: "mr-2 h-4 w-4" });
        $$payload2.out += `<!----> Manage Billing`;
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="p-4">`;
  if (props.upcomingInvoice) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6 rounded-lg border border-dashed p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">`;
    Calendar($$payload, { class: "h-5 w-5 text-blue-600" });
    $$payload.out += `<!----></div> <div><h3 class="font-medium">Upcoming Invoice</h3> <p class="text-muted-foreground text-sm">Next billing date: ${escape_html(formatDate(new Date(props.upcomingInvoice.period_end * 1e3)))}</p></div></div> <div class="text-right"><p class="font-medium">${escape_html(new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: props.upcomingInvoice.currency.toUpperCase()
    }).format(props.upcomingInvoice.amount_due / 100))}</p> <p class="text-muted-foreground text-sm">${escape_html(props.upcomingInvoice.lines.data.length)} item${escape_html(props.upcomingInvoice.lines.data.length !== 1 ? "s" : "")}</p></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (props.invoices && props.invoices.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(props.invoices);
    $$payload.out += `<div class="rounded-md border"><div class="grid grid-cols-5 gap-4 border-b px-4 py-2 text-sm font-normal"><div>Invoice</div> <div>Date</div> <div>Amount</div> <div>Status</div> <div class="text-right">Actions</div></div> <!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let invoice = each_array[$$index];
      $$payload.out += `<div class="grid grid-cols-5 gap-4 border-b p-4 last:border-0"><div>${escape_html(invoice.number || "Draft")}</div> <div>${escape_html(formatDate(new Date(invoice.created * 1e3)))}</div> <div>${escape_html(new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: invoice.currency.toUpperCase()
      }).format(invoice.amount_paid / 100))}</div> <div>`;
      if (invoice.status) {
        $$payload.out += "<!--[-->";
        const status = getPaymentStatusLabel(invoice);
        $$payload.out += `<span${attr_class(`rounded-full px-2.5 py-0.5 text-xs font-medium ${status.class}`)}>${escape_html(status.text)}</span>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="flex justify-end gap-2">`;
      if (invoice.hosted_invoice_url) {
        $$payload.out += "<!--[-->";
        Button($$payload, {
          variant: "ghost",
          size: "sm",
          onclick: () => window.open(invoice.hosted_invoice_url, "_blank"),
          children: ($$payload2) => {
            Receipt($$payload2, { class: "mr-2 h-4 w-4" });
            $$payload2.out += `<!----> View`;
          },
          $$slots: { default: true }
        });
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (invoice.invoice_pdf) {
        $$payload.out += "<!--[-->";
        Button($$payload, {
          variant: "ghost",
          size: "sm",
          onclick: () => window.open(invoice.invoice_pdf, "_blank"),
          children: ($$payload2) => {
            Download($$payload2, { class: "mr-2 h-4 w-4" });
            $$payload2.out += `<!----> Download`;
          },
          $$slots: { default: true }
        });
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="rounded-lg border border-dashed p-6 text-center"><div class="mb-4 flex justify-center"><div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">`;
    Receipt($$payload, { class: "text-primary h-6 w-6" });
    $$payload.out += `<!----></div></div> <h3 class="mb-2 text-lg font-medium">No Invoices</h3> <p class="text-muted-foreground">You don't have any invoices yet.</p> <p class="text-muted-foreground mt-2 text-sm">When you make a payment, your invoices will appear here.</p></div>`;
  }
  $$payload.out += `<!--]--></div> <div class="flex justify-between p-6"><div class="flex items-center gap-2">`;
  Shield($$payload, { class: "text-muted-foreground h-5 w-5" });
  $$payload.out += `<!----> <p class="text-muted-foreground text-sm">Your billing information is secure and encrypted</p></div></div>`;
  pop();
}
function _page($$payload, $$props) {
  push();
  const TOAST_MESSAGES = {
    // Success messages
    SUCCESS: {
      SUBSCRIPTION_UPDATED: (planName) => ({
        title: "Subscription Updated",
        description: `Your subscription has been successfully updated to the ${planName || "new"} plan.`,
        duration: 5e3
      }),
      SUBSCRIPTION_RESUMED: {
        title: "Subscription resumed successfully",
        duration: 3e3
      },
      SUBSCRIPTION_CANCELED: (atPeriodEnd) => ({
        title: atPeriodEnd ? "Your subscription will be canceled at the end of the billing period" : "Your subscription has been canceled",
        duration: 3e3
      }),
      SUBSCRIPTION_PAUSED: {
        title: "Your subscription will be paused at the end of the billing period",
        duration: 3e3
      },
      PAYMENT_METHOD_ADDED: {
        title: "Payment method added successfully",
        duration: 3e3
      },
      PAYMENT_METHOD_DELETED: {
        title: "Payment method deleted successfully",
        duration: 3e3
      },
      PAYMENT_METHOD_DEFAULT: {
        title: "Default payment method updated successfully",
        duration: 3e3
      }
    },
    // Error messages
    ERROR: {
      NO_SUBSCRIPTION: {
        title: "No subscription found",
        description: "You need to have an active subscription to view billing history."
      },
      PORTAL_NOT_CONFIGURED: {
        title: "Stripe Portal not configured",
        description: "The Stripe Customer Portal has not been set up. Please configure it in the Stripe Dashboard.",
        action: {
          label: "Configure",
          onClick: () => {
            const isProd = window.location.hostname !== "localhost" && !window.location.hostname.includes("127.0.0.1");
            const portalConfigUrl = isProd ? "https://dashboard.stripe.com/settings/billing/portal" : "https://dashboard.stripe.com/test/settings/billing/portal";
            window.open(portalConfigUrl, "_blank");
          }
        }
      },
      STRIPE_CONFIG_ISSUE: {
        title: "Stripe configuration issue",
        description: "There is a configuration issue with Stripe. Please contact support."
      },
      INVALID_REQUEST: {
        title: "Invalid request to Stripe",
        description: "There was a problem with the request to Stripe. Please try again later."
      },
      AUTH_ERROR: {
        title: "Authentication error",
        description: "There was a problem authenticating with Stripe. Please contact support."
      },
      PORTAL_FAILED: {
        title: "Failed to open customer portal",
        description: "Failed to open customer portal. Please try again later."
      },
      DELETE_PAYMENT_METHOD: (message) => ({
        title: "Failed to delete payment method",
        description: message || "Please try again later."
      }),
      RESUME_SUBSCRIPTION: {
        title: "Failed to resume subscription",
        description: "Failed to resume subscription. Please try again later."
      },
      CANCEL_SUBSCRIPTION: {
        title: "Failed to cancel subscription",
        description: "Failed to cancel subscription. Please try again later."
      },
      PAUSE_SUBSCRIPTION: {
        title: "Failed to pause subscription",
        description: "Failed to pause subscription. Please try again later."
      },
      CANNOT_DELETE_ONLY_PAYMENT_METHOD: {
        title: "Cannot Delete Payment Method",
        description: "You cannot delete your only payment method. Please add another payment method first."
      },
      CANNOT_DELETE_DEFAULT_PAYMENT_METHOD: {
        title: "Cannot Delete Default Payment Method",
        description: "You cannot delete your default payment method. Please set another payment method as default first."
      }
    },
    // Info messages
    INFO: {
      SUBSCRIPTION_ALREADY_ACTIVE: {
        title: "Your subscription is already active",
        duration: 3e3
      },
      VIEW_INVOICES_DIRECTLY: {
        title: "You can view your invoices directly",
        description: "You can view and download your invoices directly from this page.",
        duration: 5e3
      },
      NO_INVOICES: {
        title: "No invoices available",
        description: "You don't have any invoices yet. They will appear here after your first payment.",
        duration: 5e3
      }
    }
  };
  const { $$slots, $$events, ...props } = $$props;
  try {
    console.log("Page component initialized with props:", JSON.stringify({
      hasProps: !!props,
      hasData: !!props?.data,
      hasUser: !!props?.data?.user,
      hasBilling: !!props?.data?.billing
    }));
    if (!props.data) {
      console.error("props.data is undefined or null");
      toast.error("Error loading billing data", {
        description: "Please try refreshing the page or contact support if the issue persists."
      });
    } else {
      console.log("Data from server structure:", {
        hasUser: !!props.data.user,
        hasBilling: !!props.data.billing,
        userKeys: props.data.user ? Object.keys(props.data.user) : [],
        billingKeys: props.data.billing ? Object.keys(props.data.billing) : []
      });
    }
  } catch (error) {
    console.error("Error during page initialization check:", error);
  }
  let user = props?.data?.user || {};
  let currentPlan = props?.data?.billing?.currentPlan || { name: "Free", id: "free", features: [] };
  let paymentMethods = props?.data?.billing?.paymentMethods || [];
  let invoices = props?.data?.billing?.invoices || [];
  let upcomingInvoice = props?.data?.billing?.upcomingInvoice || null;
  let subscription = props?.data?.billing?.subscription || null;
  try {
    console.log("Initialized state variables:", {
      hasUser: !!user,
      hasCurrentPlan: !!currentPlan,
      hasPaymentMethods: paymentMethods.length > 0,
      hasInvoices: invoices.length > 0,
      hasUpcomingInvoice: !!upcomingInvoice,
      hasSubscription: !!subscription
    });
  } catch (error) {
    console.error("Error logging initialized state variables:", error);
  }
  async function reloadPage() {
    try {
      console.log("Reloading page to get fresh data...");
      if (typeof window !== "undefined") {
        setTimeout(
          () => {
            window.location.href = window.location.pathname;
          },
          500
        );
      } else {
        console.warn("Cannot reload page: not in browser context");
      }
      return true;
    } catch (error) {
      console.error("Error reloading page:", error);
      return false;
    }
  }
  let isLoading = false;
  let isPortalLoading = false;
  let isPaymentMethodLoading = false;
  let isDeletePaymentMethodLoading = false;
  let isSubscriptionActionLoading = false;
  let deleteDialogOpen = false;
  let paymentMethodToDelete = "";
  let cancelDialogOpen = false;
  let pauseDialogOpen = false;
  let addPaymentMethodModalOpen = false;
  let activeTab = "subscription";
  new URLSearchParams("");
  async function openCustomerPortal() {
    if (isPortalLoading) return;
    if (!user.stripeCustomerId) {
      const msg = TOAST_MESSAGES.ERROR.NO_SUBSCRIPTION;
      toast.error(msg.title, { description: msg.description });
      return;
    }
    isPortalLoading = true;
    try {
      console.log("Opening customer portal for customer ID:", user.stripeCustomerId);
      const response = await fetch("/api/billing/create-portal-session", {
        method: "POST",
        headers: { "Content-Type": "application/json" }
      });
      let data;
      try {
        data = await response.json();
      } catch (e) {
        console.error("Failed to parse response as JSON:", e);
        throw new Error("Invalid response from server");
      }
      if (!response.ok) {
        const errorMessage = data?.error || "Failed to create portal session";
        const errorDetails = data?.details || "unknown_error";
        const errorCode = data?.code || "unknown_code";
        console.error("Error opening customer portal:", {
          message: errorMessage,
          details: errorDetails,
          code: errorCode
        });
        if (errorMessage.includes("No configuration provided") || errorMessage.includes("default configuration has not been created")) {
          console.log("Stripe Customer Portal not configured. Please set up the portal in the Stripe Dashboard.");
          const msg = TOAST_MESSAGES.ERROR.PORTAL_NOT_CONFIGURED;
          toast.error(msg.title, {
            description: msg.description,
            action: msg.action
          });
        } else if (errorDetails === "StripeInvalidRequestError" && errorCode === "parameter_unknown") {
          console.log("Stripe API version issue detected. Please update the Stripe configuration.");
          const msg = TOAST_MESSAGES.ERROR.STRIPE_CONFIG_ISSUE;
          toast.error(msg.title, { description: msg.description });
        } else if (errorDetails === "invalid_request_error") {
          const msg = TOAST_MESSAGES.ERROR.INVALID_REQUEST;
          toast.error(msg.title, { description: msg.description });
        } else if (errorDetails === "authentication_error") {
          const msg = TOAST_MESSAGES.ERROR.AUTH_ERROR;
          toast.error(msg.title, { description: msg.description });
        } else {
          const msg = TOAST_MESSAGES.ERROR.PORTAL_FAILED;
          toast.error(msg.title, { description: errorMessage || msg.description });
        }
        throw new Error(errorMessage);
      }
      if (data?.url) {
        if (typeof window !== "undefined") {
          window.location.href = data.url;
        } else {
          console.warn("Cannot redirect: not in browser context");
        }
      } else {
        throw new Error("No portal URL returned from server");
      }
    } catch (error) {
      console.error("Error opening customer portal:", error);
      if (!error.message || error.message === "Failed to create portal session") {
        const msg = TOAST_MESSAGES.ERROR.PORTAL_FAILED;
        toast.error(msg.title, { description: msg.description });
      }
      if (invoices && invoices.length > 0) {
        const msg = TOAST_MESSAGES.INFO.VIEW_INVOICES_DIRECTLY;
        toast.info(msg.title, {
          description: msg.description,
          duration: msg.duration
        });
        activeTab = "invoices";
      } else {
        const msg = TOAST_MESSAGES.INFO.NO_INVOICES;
        toast.info(msg.title, {
          description: msg.description,
          duration: msg.duration
        });
      }
    } finally {
      isPortalLoading = false;
    }
  }
  async function handlePlanChange(planId, billingCycle = "monthly") {
    if (isLoading) return;
    isLoading = true;
    try {
      const response = await fetch("/api/billing/create-checkout-session", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ planId, billingCycle })
      });
      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }
      const { url } = await response.json();
      if (typeof window !== "undefined") {
        window.location.href = url;
      } else {
        console.warn("Cannot redirect: not in browser context");
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast.error("Failed to create checkout session. Please try again later.");
    } finally {
      isLoading = false;
    }
  }
  function handleOpenPricingModal() {
    openPricingModal({
      section: "pro",
      currentPlanId: currentPlan?.id || null,
      onSelectPlan: handlePlanChange
    });
  }
  async function setDefaultPaymentMethod$1(paymentMethodId) {
    if (isPaymentMethodLoading) return;
    isPaymentMethodLoading = true;
    try {
      await setDefaultPaymentMethod(paymentMethodId);
      const msg = TOAST_MESSAGES.SUCCESS.PAYMENT_METHOD_DEFAULT;
      toast.success(msg.title, { duration: msg.duration });
      reloadPage();
    } catch (error) {
      console.error("Error setting default payment method:", error);
      const errorMsg = TOAST_MESSAGES.ERROR.INVALID_REQUEST;
      toast.error(errorMsg.title, { description: errorMsg.description });
    } finally {
      isPaymentMethodLoading = false;
    }
  }
  function handlePaymentMethodAdded(_updatedPaymentMethods = null) {
    const msg = TOAST_MESSAGES.SUCCESS.PAYMENT_METHOD_ADDED;
    toast.success(msg.title, { duration: msg.duration });
    activeTab = "payment";
    reloadPage();
  }
  function openDeleteDialog(paymentMethodId) {
    if (isDeletePaymentMethodLoading) return;
    if (paymentMethods.length <= 1) {
      const msg = TOAST_MESSAGES.ERROR.CANNOT_DELETE_ONLY_PAYMENT_METHOD;
      toast.error(msg.title, { description: msg.description });
      return;
    }
    const method = paymentMethods.find((m) => m.id === paymentMethodId);
    if (method?.isDefault) {
      const msg = TOAST_MESSAGES.ERROR.CANNOT_DELETE_DEFAULT_PAYMENT_METHOD;
      toast.error(msg.title, { description: msg.description });
      return;
    }
    paymentMethodToDelete = paymentMethodId;
    deleteDialogOpen = true;
  }
  async function deletePaymentMethod$1() {
    if (isDeletePaymentMethodLoading || !paymentMethodToDelete) return;
    isDeletePaymentMethodLoading = true;
    try {
      await deletePaymentMethod(paymentMethodToDelete);
      const msg = TOAST_MESSAGES.SUCCESS.PAYMENT_METHOD_DELETED;
      toast.success(msg.title, { duration: msg.duration });
      activeTab = "payment";
      reloadPage();
    } catch (error) {
      console.error("Error deleting payment method:", error);
      const msg = TOAST_MESSAGES.ERROR.DELETE_PAYMENT_METHOD(error.message);
      toast.error(msg.title, { description: msg.description });
    } finally {
      isDeletePaymentMethodLoading = false;
      deleteDialogOpen = false;
    }
  }
  async function handleResumeSubscription() {
    if (isSubscriptionActionLoading) return;
    try {
      if (subscription && !subscription.isPaused && !subscription.pause_collection && !subscription.cancel_at_period_end && subscription.status === "active" && !subscription.metadata?.pause_at_period_end) {
        console.log("Subscription is already active, no need to resume");
        toast.info("Your subscription is already active");
        return;
      }
      isSubscriptionActionLoading = true;
      if (!subscription) {
        console.error("Cannot resume subscription: subscription object is null or undefined");
        toast.error("Error resuming subscription", { description: "Subscription data is missing" });
        isSubscriptionActionLoading = false;
        return;
      }
      console.log("Resuming subscription with current state:", {
        cancel_at_period_end: subscription?.cancel_at_period_end,
        pause_collection: subscription?.pause_collection,
        status: subscription?.status,
        metadata: subscription?.metadata
      });
      try {
        const response = await fetch("/api/billing/resume-subscription", {
          method: "POST",
          headers: { "Content-Type": "application/json" }
        });
        let data;
        try {
          data = await response.json();
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
          throw new Error("Invalid response from server");
        }
        if (!response.ok) {
          console.error("API error resuming subscription:", data);
          throw new Error(data?.error || data?.details || "Failed to resume subscription");
        }
        if (data.success) {
          console.log("Subscription resumed successfully");
          if (data.message === "Subscription is already active") {
            const msg = TOAST_MESSAGES.INFO.SUBSCRIPTION_ALREADY_ACTIVE;
            toast.info(msg.title, { duration: msg.duration });
          } else {
            const msg = TOAST_MESSAGES.SUCCESS.SUBSCRIPTION_RESUMED;
            toast.success(msg.title, { duration: msg.duration });
            console.log("Redirecting to refresh page after successful resume...");
            if (typeof window !== "undefined") {
              setTimeout(
                () => {
                  window.location.href = window.location.pathname + "?resumed=true";
                },
                500
              );
            } else {
              console.warn("Cannot redirect: not in browser context");
            }
          }
        } else {
          const msg = TOAST_MESSAGES.ERROR.RESUME_SUBSCRIPTION;
          toast.error(msg.title, { description: msg.description });
        }
      } catch (fetchError) {
        console.error("Error making API request:", fetchError);
        const msg = TOAST_MESSAGES.ERROR.RESUME_SUBSCRIPTION;
        toast.error(msg.title, {
          description: fetchError.message || msg.description
        });
      }
    } catch (error) {
      console.error("Unexpected error in handleResumeSubscription:", error);
      const msg = TOAST_MESSAGES.ERROR.RESUME_SUBSCRIPTION;
      toast.error(msg.title, { description: error.message || msg.description });
    } finally {
      isSubscriptionActionLoading = false;
    }
  }
  async function handleCancelSubscription(cancelAtPeriodEnd = true) {
    if (isSubscriptionActionLoading) return;
    isSubscriptionActionLoading = true;
    try {
      const response = await fetch("/api/billing/cancel-subscription", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ cancelAtPeriodEnd })
      });
      if (!response.ok) {
        throw new Error("Failed to cancel subscription");
      }
      const data = await response.json();
      if (data.success) {
        console.log("Subscription canceled successfully");
        const msg = TOAST_MESSAGES.SUCCESS.SUBSCRIPTION_CANCELED(cancelAtPeriodEnd);
        toast.success(msg.title, { duration: msg.duration });
        cancelDialogOpen = false;
        console.log("Redirecting to refresh page after successful cancel...");
        if (typeof window !== "undefined") {
          window.location.href = window.location.pathname + "?canceled=true";
        } else {
          console.warn("Cannot redirect: not in browser context");
        }
      } else {
        const msg = TOAST_MESSAGES.ERROR.CANCEL_SUBSCRIPTION;
        toast.error(msg.title, { description: msg.description });
      }
    } catch (error) {
      console.error("Error canceling subscription:", error);
      const msg = TOAST_MESSAGES.ERROR.CANCEL_SUBSCRIPTION;
      toast.error(msg.title, { description: msg.description });
    } finally {
      isSubscriptionActionLoading = false;
    }
  }
  async function handlePauseSubscription() {
    if (isSubscriptionActionLoading) return;
    isSubscriptionActionLoading = true;
    try {
      if (!subscription) {
        console.error("Cannot pause subscription: subscription object is null or undefined");
        toast.error("Error pausing subscription", { description: "Subscription data is missing" });
        isSubscriptionActionLoading = false;
        return;
      }
      console.log("Pausing subscription with current state:", {
        cancel_at_period_end: subscription?.cancel_at_period_end,
        metadata: subscription?.metadata,
        status: subscription?.status
      });
      const response = await fetch("/api/billing/cancel-subscription", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ pauseAtPeriodEnd: true })
      });
      if (!response.ok) {
        throw new Error("Failed to pause subscription");
      }
      const data = await response.json();
      console.log("Pause subscription response:", data);
      if (data.success) {
        console.log("Subscription paused successfully");
        const msg = TOAST_MESSAGES.SUCCESS.SUBSCRIPTION_PAUSED;
        toast.success(msg.title, { duration: msg.duration });
        pauseDialogOpen = false;
        console.log("Redirecting to refresh page after successful pause...");
        if (typeof window !== "undefined") {
          window.location.href = window.location.pathname + "?paused=true";
        } else {
          console.warn("Cannot redirect: not in browser context");
        }
      } else {
        const msg = TOAST_MESSAGES.ERROR.PAUSE_SUBSCRIPTION;
        toast.error(msg.title, { description: msg.description });
      }
    } catch (error) {
      console.error("Error pausing subscription:", error);
      const msg = TOAST_MESSAGES.ERROR.PAUSE_SUBSCRIPTION;
      toast.error(msg.title, { description: msg.description });
    } finally {
      isSubscriptionActionLoading = false;
    }
  }
  const ALERT_DIALOGS = [
    {
      get isOpen() {
        return deleteDialogOpen;
      },
      set isOpen(value) {
        deleteDialogOpen = value;
      },
      title: "Delete Payment Method",
      description: "Are you sure you want to delete this payment method? This action cannot be undone.",
      cancelText: "Cancel",
      confirmText: "Delete",
      loadingText: "Deleting...",
      get isLoading() {
        return isDeletePaymentMethodLoading;
      },
      onCancel: () => deleteDialogOpen = false,
      onConfirm: deletePaymentMethod$1,
      confirmClass: "bg-destructive text-destructive-foreground hover:bg-destructive/90"
    },
    {
      get isOpen() {
        return cancelDialogOpen;
      },
      set isOpen(value) {
        cancelDialogOpen = value;
      },
      title: "Cancel Subscription",
      description: "Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.",
      cancelText: "Keep Subscription",
      confirmText: "Cancel Subscription",
      loadingText: "Canceling...",
      get isLoading() {
        return isSubscriptionActionLoading;
      },
      onCancel: () => cancelDialogOpen = false,
      onConfirm: () => handleCancelSubscription(true)
    },
    {
      get isOpen() {
        return pauseDialogOpen;
      },
      set isOpen(value) {
        pauseDialogOpen = value;
      },
      title: "Pause Subscription",
      description: "Your subscription will be paused at the end of the current billing period. You can resume your subscription at any time.",
      cancelText: "Keep Active",
      confirmText: "Pause Subscription",
      loadingText: "Pausing...",
      get isLoading() {
        return isSubscriptionActionLoading;
      },
      onCancel: () => pauseDialogOpen = false,
      onConfirm: () => handlePauseSubscription()
    }
  ];
  console.log("asdasdsadasd" + JSON.stringify(subscription, null, 2));
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array = ensure_array_like(ALERT_DIALOGS);
    SEO($$payload2, {
      title: "Billing & Subscription - Hirli",
      description: "Manage your subscription plan, payment methods, and view your billing history and invoices.",
      keywords: "billing, subscription, payment methods, invoices, pricing plans, upgrade subscription",
      url: "https://hirli.com/dashboard/settings/billing"
    });
    $$payload2.out += `<!----> <div><div class="flex flex-col justify-between p-6"><h2 class="text-lg font-semibold">Billing &amp; Subscription</h2> <p class="text-muted-foreground">Manage your subscription plan, payment methods, and billing history.</p></div> <!---->`;
    Root($$payload2, {
      get value() {
        return activeTab;
      },
      set value($$value) {
        activeTab = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Tabs_list($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Tabs_trigger($$payload4, {
              value: "subscription",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Subscription`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "payment",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Payment Methods`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "invoices",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Invoices`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "subscription",
          children: ($$payload4) => {
            Subscription($$payload4, {
              subscription,
              currentPlan,
              isSubscriptionActionLoading,
              isLoading,
              user,
              handleResumeSubscription,
              handleOpenPricingModal,
              setPauseDialogOpen: (open) => pauseDialogOpen = open,
              setCancelDialogOpen: (open) => cancelDialogOpen = open
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "payment",
          children: ($$payload4) => {
            Payment($$payload4, {
              paymentMethods,
              isPaymentMethodLoading,
              isDeletePaymentMethodLoading,
              paymentMethodToDelete,
              user,
              handlePlanChange,
              setDefaultPaymentMethod: setDefaultPaymentMethod$1,
              openDeleteDialog,
              setAddPaymentMethodModalOpen: (open) => addPaymentMethodModalOpen = open
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "invoices",
          children: ($$payload4) => {
            Invoices($$payload4, {
              invoices,
              upcomingInvoice,
              user,
              isPortalLoading,
              openCustomerPortal
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> `;
    AddPaymentMethodModal($$payload2, {
      onSuccess: handlePaymentMethodAdded,
      get open() {
        return addPaymentMethodModalOpen;
      },
      set open($$value) {
        addPaymentMethodModalOpen = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> <!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let dialog = each_array[$$index];
      $$payload2.out += `<!---->`;
      Root$1($$payload2, {
        get open() {
          return dialog.isOpen;
        },
        set open($$value) {
          dialog.isOpen = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Alert_dialog_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Alert_dialog_header($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Alert_dialog_title($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->${escape_html(dialog.title)}`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Alert_dialog_description($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->${escape_html(dialog.description)}`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Alert_dialog_footer($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Alert_dialog_cancel($$payload5, {
                    onclick: dialog.onCancel,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->${escape_html(dialog.cancelText)}`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Alert_dialog_action($$payload5, {
                    onclick: dialog.onConfirm,
                    disabled: dialog.isLoading,
                    class: dialog.confirmClass || "",
                    children: ($$payload6) => {
                      if (dialog.isLoading) {
                        $$payload6.out += "<!--[-->";
                        Loader_circle($$payload6, { class: "mr-2 h-4 w-4 animate-spin" });
                        $$payload6.out += `<!----> ${escape_html(dialog.loadingText)}`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                        $$payload6.out += `${escape_html(dialog.confirmText)}`;
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BAlXYD1N.js.map
