#!/usr/bin/env node

/**
 * Simple script to run company logo processing for ALL companies
 * Usage: node run-logo-processing.cjs
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🎨 Starting Company Logo Processing for ALL Companies');
console.log('📊 This will process ALL companies with active jobs (no limit)');
console.log('⏱️  This may take 30-60 minutes depending on the number of companies');
console.log('🔄 Press Ctrl+C to stop at any time\n');

// Ask for confirmation
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Do you want to proceed? (y/N): ', (answer) => {
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log('❌ Logo processing cancelled');
    rl.close();
    process.exit(0);
  }

  console.log('\n🚀 Starting logo processing...\n');
  rl.close();

  // Run the TypeScript script
  const scriptPath = path.join(__dirname, 'scripts', 'add-company-logos.ts');
  const child = spawn('npx', ['tsx', scriptPath], {
    stdio: 'inherit',
    cwd: __dirname
  });

  child.on('close', (code) => {
    if (code === 0) {
      console.log('\n✅ Logo processing completed successfully!');
      console.log('🎉 Check the Hero section to see the new logos');
    } else {
      console.log(`\n❌ Logo processing failed with exit code ${code}`);
    }
    process.exit(code);
  });

  child.on('error', (error) => {
    console.error('❌ Error running logo processing:', error);
    process.exit(1);
  });

  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n⏹️  Stopping logo processing...');
    child.kill('SIGINT');
    process.exit(0);
  });
});
