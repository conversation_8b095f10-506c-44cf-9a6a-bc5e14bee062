import { p as push, J as attr_class, _ as clsx, O as escape_html, q as pop, M as ensure_array_like, $ as attr_style, P as stringify } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { S as Search } from './search-B0oHlTPS.js';
import { P as Play } from './play-DKNYqs4c.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { I as Info } from './info-Ce09B-Yv.js';

function StatusTag($$payload, $$props) {
  push();
  const { status, severity, className = "" } = $$props;
  function getTagColor(status2, severity2) {
    if (severity2) {
      switch (severity2) {
        case "critical":
          return "bg-red-100 text-red-800 border-red-200";
        case "major":
          return "bg-orange-100 text-orange-800 border-orange-200";
        case "minor":
          return "bg-yellow-100 text-yellow-800 border-yellow-200";
        case "maintenance":
          return "bg-blue-100 text-blue-800 border-blue-200";
        case "info":
          return "bg-gray-100 text-gray-800 border-gray-200";
      }
    }
    switch (status2) {
      case "resolved":
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "monitoring":
      case "in-progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "investigating":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "identified":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "scheduled":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  }
  function getStatusIcon(status2) {
    switch (status2) {
      case "resolved":
      case "completed":
        return Circle_check_big;
      case "monitoring":
        return Circle_alert;
      case "in-progress":
        return Play;
      case "investigating":
        return Search;
      case "identified":
        return Triangle_alert;
      case "scheduled":
        return Clock;
      case "cancelled":
        return Circle_x;
      default:
        return Circle_alert;
    }
  }
  function formatStatusText(status2) {
    switch (status2) {
      case "in-progress":
        return "In Progress";
      default:
        return status2.charAt(0).toUpperCase() + status2.slice(1);
    }
  }
  const tagColor = getTagColor(status, severity);
  const StatusIcon = getStatusIcon(status);
  const statusText = formatStatusText(status);
  $$payload.out += `<div${attr_class(clsx(cn("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold", tagColor, className)))}><!---->`;
  StatusIcon($$payload, { class: "mr-1 h-3 w-3" });
  $$payload.out += `<!----> ${escape_html(statusText)}</div>`;
  pop();
}
function SeverityBadge($$payload, $$props) {
  push();
  const { severity, className = "" } = $$props;
  function getBadgeColor(severity2) {
    switch (severity2) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200";
      case "major":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "minor":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "maintenance":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "info":
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  }
  function getSeverityIcon(severity2) {
    switch (severity2) {
      case "critical":
        return Circle_alert;
      case "major":
      case "minor":
        return Triangle_alert;
      case "maintenance":
      case "info":
      default:
        return Info;
    }
  }
  function formatSeverityText(severity2) {
    switch (severity2) {
      case "critical":
        return "Critical Outage";
      case "major":
        return "Major Outage";
      case "minor":
        return "Minor Outage";
      case "maintenance":
        return "Maintenance";
      case "info":
      default:
        return "Information";
    }
  }
  const badgeColor = getBadgeColor(severity);
  const SeverityIcon = getSeverityIcon(severity);
  const severityText = formatSeverityText(severity);
  $$payload.out += `<div${attr_class(clsx(cn("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold", badgeColor, className)))}><!---->`;
  SeverityIcon($$payload, { class: "mr-1 h-3 w-3" });
  $$payload.out += `<!----> ${escape_html(severityText)}</div>`;
  pop();
}
function StatusBar($$payload, $$props) {
  push();
  const {
    progress = 0,
    startTime,
    endTime,
    status,
    showTimes = true,
    className = ""
  } = $$props;
  let calculatedProgress = () => {
    if (progress > 0) return progress;
    if (status === "completed" || status === "resolved") return 100;
    if (status === "cancelled") return 100;
    if (status === "scheduled" && new Date(startTime) > /* @__PURE__ */ new Date()) return 0;
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const now = Date.now();
    if (now <= start) return 0;
    if (now >= end) {
      if (status !== "completed" && status !== "resolved") return 90;
      return 100;
    }
    return Math.round((now - start) / (end - start) * 100);
  };
  function formatTime(date) {
    return new Date(date).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true
    });
  }
  function getStatusColor(status2) {
    switch (status2) {
      case "resolved":
      case "completed":
        return "bg-green-500";
      case "monitoring":
      case "in-progress":
        return "bg-blue-500";
      case "investigating":
      case "scheduled":
        return "bg-yellow-500";
      case "identified":
        return "bg-orange-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  }
  function getProgressSegments() {
    const progressValue = calculatedProgress;
    if (status === "completed" || status === "cancelled") {
      return [
        {
          color: getStatusColor(status),
          width: "100%"
        }
      ];
    }
    if (status === "in-progress" || status === "monitoring") {
      return [
        {
          color: getStatusColor(status),
          width: `${progressValue}%`
        },
        {
          color: "bg-gray-200",
          width: `${100 - progressValue}%`
        }
      ];
    }
    if (status === "scheduled") {
      return [
        { color: "bg-gray-200", width: "100%" },
        {
          color: getStatusColor(status),
          width: "10%",
          pulse: true
        }
      ];
    }
    return [
      {
        color: getStatusColor(status),
        width: `${progressValue}%`
      },
      {
        color: "bg-gray-200",
        width: `${100 - progressValue}%`
      }
    ];
  }
  const segments = () => getProgressSegments();
  const each_array = ensure_array_like(segments);
  $$payload.out += `<div${attr_class(clsx(cn("w-full space-y-1", className)))}><div class="flex h-2 w-full overflow-hidden rounded-full bg-gray-200"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let segment = each_array[$$index];
    $$payload.out += `<div${attr_class(clsx(cn(segment.color, "h-full transition-all duration-500 ease-in-out", segment.pulse && "animate-pulse")))}${attr_style(`width: ${stringify(segment.width)}`)}></div>`;
  }
  $$payload.out += `<!--]--></div> `;
  if (showTimes) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex justify-between text-xs text-gray-500"><span>${escape_html(formatTime(startTime))}</span> <span>${escape_html(formatTime(endTime))}</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}

export { StatusTag as S, StatusBar as a, SeverityBadge as b };
//# sourceMappingURL=StatusBar-DynsEX84.js.map
