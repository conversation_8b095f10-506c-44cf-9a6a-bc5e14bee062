import { r as redirect, f as fail } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, e as enumType, b as booleanType, s as stringType } from './types-D78SXuvm.js';
import '@prisma/client';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const accountSchema = objectType({
  // Personal Information
  name: stringType().min(1, "Name is required").max(100, "Name is too long"),
  email: stringType().email("Please enter a valid email address"),
  phone: stringType().optional(),
  bio: stringType().max(500, "Bio should be less than 500 characters").optional(),
  profilePicture: stringType().optional().nullable(),
  // UI Preferences
  theme: enumType(["light", "dark", "system"]).default("system"),
  viewMode: enumType(["list", "grid", "compact"]).default("list"),
  // Accessibility
  highContrast: booleanType().default(false),
  pushNotifications: booleanType().default(true),
  // Privacy
  profileVisibility: enumType(["public", "private"]).default("public"),
  allowDataCollection: booleanType().default(true),
  allowThirdPartySharing: booleanType().default(false),
  // Application Preferences
  autoParseResumes: booleanType().default(true),
  autoSaveApplications: booleanType().default(true),
  applicationReminders: booleanType().default(true),
  // Job Search Preferences
  defaultRemotePreference: enumType(["remote", "hybrid", "onsite", "flexible"]).default("hybrid"),
  showSalaryInListings: booleanType().default(true),
  autoApplyEnabled: booleanType().default(false),
  // Resume Preferences
  defaultResumeParsingEnabled: booleanType().default(true),
  autoUpdateProfileFromResume: booleanType().default(true),
  resumePrivacyLevel: enumType(["public", "private"]).default("private"),
  // Cookie Preferences
  cookiePreferences: objectType({
    functional: booleanType().default(true),
    analytics: booleanType().default(true),
    advertising: booleanType().default(false)
  }).optional()
});
const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { id: user.id }
  });
  if (!userData) {
    throw redirect(302, "/auth/sign-in");
  }
  const preferences = userData.preferences || {};
  const accountPrefs = preferences.account || {};
  const userPreferences = {
    // Personal Information
    name: userData.name || "",
    email: userData.email,
    phone: accountPrefs.phone,
    bio: accountPrefs.bio,
    profilePicture: userData.image,
    // UI Preferences
    theme: accountPrefs.ui?.theme || accountPrefs.accessibility?.theme,
    viewMode: accountPrefs.ui?.viewMode || accountPrefs.accessibility?.viewMode,
    // Accessibility
    highContrast: accountPrefs.accessibility?.highContrast,
    pushNotifications: accountPrefs.pushNotifications,
    // Privacy Settings
    profileVisibility: accountPrefs.privacy?.profileVisibility,
    allowDataCollection: accountPrefs.privacy?.allowDataCollection,
    allowThirdPartySharing: accountPrefs.privacy?.allowThirdPartySharing,
    // Application Preferences
    autoParseResumes: accountPrefs.application?.autoParseResumes,
    autoSaveApplications: accountPrefs.application?.autoSaveApplications,
    applicationReminders: accountPrefs.application?.applicationReminders,
    // Job Search Preferences
    defaultRemotePreference: accountPrefs.jobSearch?.defaultRemotePreference,
    showSalaryInListings: accountPrefs.jobSearch?.showSalaryInListings,
    autoApplyEnabled: accountPrefs.jobSearch?.autoApplyEnabled,
    // Resume Preferences
    defaultResumeParsingEnabled: accountPrefs.resume?.defaultResumeParsingEnabled,
    autoUpdateProfileFromResume: accountPrefs.resume?.autoUpdateProfileFromResume,
    resumePrivacyLevel: accountPrefs.resume?.resumePrivacyLevel,
    // Cookie Preferences
    cookiePreferences: accountPrefs.cookiePreferences
  };
  const form = await superValidate(userPreferences, zod(accountSchema));
  return {
    user: userData,
    form
  };
};
const actions = {
  default: async ({ request, locals }) => {
    if (!locals.user) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData = await prisma.user.findUnique({
      where: { id: locals.user.id }
    });
    if (!userData) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(accountSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    try {
      const preferences = userData.preferences || {};
      const updatedPreferences = {
        ...preferences,
        account: {
          ...preferences.account || {},
          phone: form.data.phone,
          bio: form.data.bio,
          ui: {
            theme: form.data.theme,
            viewMode: form.data.viewMode
          },
          accessibility: {
            highContrast: form.data.highContrast
          },
          pushNotifications: form.data.pushNotifications,
          privacy: {
            profileVisibility: form.data.profileVisibility,
            allowDataCollection: form.data.allowDataCollection,
            allowThirdPartySharing: form.data.allowThirdPartySharing
          },
          application: {
            autoParseResumes: form.data.autoParseResumes,
            autoSaveApplications: form.data.autoSaveApplications,
            applicationReminders: form.data.applicationReminders
          },
          jobSearch: {
            defaultRemotePreference: form.data.defaultRemotePreference,
            showSalaryInListings: form.data.showSalaryInListings,
            autoApplyEnabled: form.data.autoApplyEnabled
          },
          resume: {
            defaultResumeParsingEnabled: form.data.defaultResumeParsingEnabled,
            autoUpdateProfileFromResume: form.data.autoUpdateProfileFromResume,
            resumePrivacyLevel: form.data.resumePrivacyLevel
          },
          cookiePreferences: form.data.cookiePreferences || {
            functional: true,
            analytics: true,
            advertising: false
          }
        }
      };
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          name: form.data.name,
          image: form.data.profilePicture,
          preferences: updatedPreferences
        }
      });
      return {
        form,
        success: true,
        user: {
          ...userData,
          name: form.data.name,
          image: form.data.profilePicture,
          preferences: updatedPreferences
        }
      };
    } catch (error) {
      console.error("Error updating account settings:", error);
      return fail(500, { form, error: "Failed to update account settings" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 44;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DrRrpvxY.js')).default;
const server_id = "src/routes/dashboard/settings/account/+page.server.ts";
const imports = ["_app/immutable/nodes/44.Btdiiok0.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/ByUTvV5u.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u-Lut8o2.js","_app/immutable/chunks/D85ENLd-.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/FeejBSkx.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/CE9Bts7j.js","_app/immutable/chunks/BA1W9HJN.js","_app/immutable/chunks/Dc4vaUpe.js","_app/immutable/chunks/G5Oo-PmU.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/ByFxH6T3.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/CXvW3J0s.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/CVVv9lPb.js","_app/immutable/chunks/BlYzNxlg.js","_app/immutable/chunks/BQ5jqT_2.js","_app/immutable/chunks/aemnuA_0.js","_app/immutable/chunks/2KCyzleV.js","_app/immutable/chunks/DLZV8qTT.js","_app/immutable/chunks/B_6ivTD3.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/7AwcL9ec.js","_app/immutable/chunks/BoNCRmBc.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/index.CV-KWLNP.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=44-CY9A-jWv.js.map
