import { p as push, V as copy_payload, W as assign_payload, q as pop, O as escape_html } from './index3-CqUPEnZw.js';
import 'clsx';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { B as Button } from './button-CrucCo1G.js';
import { I as Input } from './input-DF0gPqYN.js';
import { R as Root, D as Dialog_content } from './index7-BURUpWjT.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './index2-Cut0V_vU.js';
import './dialog-description2-rfr-pd9k.js';

function _page($$payload, $$props) {
  push();
  let audiences = [];
  let newAudienceName = "";
  let isCreatingAudience = false;
  let isAudienceDialogOpen = false;
  let isContactDialogOpen = false;
  let newContactEmail = "";
  let newContactFirstName = "";
  let newContactLastName = "";
  let isCreatingContact = false;
  let isImportDialogOpen = false;
  let isImporting = false;
  const API_BASE_URL = "/api/email";
  async function createAudience() {
    if (!newAudienceName) {
      toast.error("Audience name is required");
      return;
    }
    isCreatingAudience = true;
    try {
      const response = await fetch(`${API_BASE_URL}/audiences`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newAudienceName })
      });
      if (response.ok) {
        const audience = await response.json();
        audiences = [...audiences, audience];
        newAudienceName = "";
        isAudienceDialogOpen = false;
        toast.success("Audience created successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to create audience");
      }
    } catch (error) {
      console.error("Error creating audience:", error);
      toast.error("Failed to create audience");
    } finally {
      isCreatingAudience = false;
    }
  }
  async function createContact() {
    {
      toast.error("No audience selected");
      return;
    }
  }
  async function importContacts() {
    {
      toast.error("No audience selected");
      return;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-800"><div class="flex items-center">`;
      Triangle_alert($$payload2, { class: "mr-2 h-5 w-5" });
      $$payload2.out += `<!----> <h3 class="text-sm font-medium">Resend API Key Not Configured</h3></div> <div class="mt-2 text-sm"><p>The Resend API key is not configured. You need to set the RESEND_API_KEY environment
        variable to use audience and broadcast features.</p></div></div>`;
    }
    $$payload2.out += `<!--]--> <div class="grid grid-cols-1 gap-6 md:grid-cols-3"><div class="md:col-span-1"><!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-center justify-between"><div><!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Audiences`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->Manage your email audiences`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <!---->`;
            Button($$payload4, {
              variant: "outline",
              size: "sm",
              onclick: () => isAudienceDialogOpen = true,
              children: ($$payload5) => {
                Plus($$payload5, { class: "mr-2 h-4 w-4" });
                $$payload5.out += `<!----> New`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex h-40 items-center justify-center"><div class="h-6 w-6 animate-spin rounded-full border-2 border-current border-t-transparent"></div></div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <div class="md:col-span-2"><!---->`;
    Card($$payload2, {
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Card_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<div class="flex items-center justify-between"><div><!---->`;
            Card_title($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html("Contacts")}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Card_description($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html("Select an audience to view contacts")}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Card_content($$payload3, {
          children: ($$payload4) => {
            {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="text-muted-foreground flex h-40 items-center justify-center"><p>Select an audience to view contacts</p></div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <!---->`;
    Root($$payload2, {
      open: isAudienceDialogOpen,
      onOpenChange: (open) => isAudienceDialogOpen = open,
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Create New Audience`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Create a new audience for your email campaigns`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="space-y-4 py-4"><div class="space-y-2"><label for="audienceName" class="text-sm font-medium">Audience Name</label> <!---->`;
            Input($$payload4, {
              id: "audienceName",
              placeholder: "Enter audience name",
              get value() {
                return newAudienceName;
              },
              set value($$value) {
                newAudienceName = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div></div> <!---->`;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => isAudienceDialogOpen = false,
                  disabled: isCreatingAudience,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Button($$payload5, {
                  onclick: createAudience,
                  disabled: isCreatingAudience || !newAudienceName,
                  children: ($$payload6) => {
                    if (isCreatingAudience) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--> Create Audience`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Root($$payload2, {
      open: isContactDialogOpen,
      onOpenChange: (open) => isContactDialogOpen = open,
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html("Add Contact")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->${escape_html("Add a new contact to the audience")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="space-y-4 py-4"><div class="space-y-2"><label for="contactEmail" class="text-sm font-medium">Email *</label> <!---->`;
            Input($$payload4, {
              id: "contactEmail",
              type: "email",
              placeholder: "Enter email address",
              get value() {
                return newContactEmail;
              },
              set value($$value) {
                newContactEmail = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2"><label for="contactFirstName" class="text-sm font-medium">First Name</label> <!---->`;
            Input($$payload4, {
              id: "contactFirstName",
              placeholder: "Enter first name",
              get value() {
                return newContactFirstName;
              },
              set value($$value) {
                newContactFirstName = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2"><label for="contactLastName" class="text-sm font-medium">Last Name</label> <!---->`;
            Input($$payload4, {
              id: "contactLastName",
              placeholder: "Enter last name",
              get value() {
                return newContactLastName;
              },
              set value($$value) {
                newContactLastName = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div></div> <!---->`;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => isContactDialogOpen = false,
                  disabled: isCreatingContact,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Button($$payload5, {
                  onclick: createContact,
                  disabled: !newContactEmail,
                  children: ($$payload6) => {
                    {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--> ${escape_html("Add Contact")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Root($$payload2, {
      open: isImportDialogOpen,
      onOpenChange: (open) => isImportDialogOpen = open,
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_content($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Import Contacts`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Import contacts from a CSV file`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="space-y-4 py-4"><div class="space-y-2"><label for="importFile" class="text-sm font-medium">CSV File</label> <!---->`;
            Input($$payload4, {
              id: "importFile",
              type: "file",
              accept: ".csv"
            });
            $$payload4.out += `<!----> <p class="text-muted-foreground mt-1 text-xs">CSV file should have columns: email, first_name, last_name</p></div></div> <!---->`;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Button($$payload5, {
                  variant: "outline",
                  onclick: () => isImportDialogOpen = false,
                  disabled: isImporting,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Button($$payload5, {
                  onclick: importContacts,
                  disabled: true,
                  children: ($$payload6) => {
                    {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--> Import Contacts`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CtA_VDdn.js.map
