import { p as push, V as copy_payload, W as assign_payload, q as pop, M as ensure_array_like, O as escape_html } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root$1, P as Portal, d as Dialog_overlay, D as Dialog_content } from './index7-BURUpWjT.js';
import { B as Badge } from './badge-C9pSznab.js';
import { I as Input } from './input-DF0gPqYN.js';
import { B as Button } from './button-CrucCo1G.js';
import 'clsx';
import { S as Sparkles } from './sparkles-E4-thk3U.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { S as Search } from './search-B0oHlTPS.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { B as Building } from './building-8WHBOPYC.js';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './x-DwZgpWRG.js';
import './Icon-A4vzmk-O.js';
import './index-DjwFQdT_.js';
import './dialog-description2-rfr-pd9k.js';

function _page($$payload, $$props) {
  push();
  let activeTab = "sessions";
  let showNewSessionModal = false;
  let searchTerm = "";
  let sessions = [];
  let applications = [];
  sessions.filter((session) => session.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()));
  function startNewSession() {
    showNewSessionModal = true;
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "AI Interview Coach | Auto Apply",
      description: "Practice for your interviews with AI-powered coaching"
    });
    $$payload2.out += `<!----> <div class="flex h-full flex-col"><div class="border-border flex flex-col justify-between border-b p-6"><div class="flex items-center">`;
    Sparkles($$payload2, { class: "mr-2 h-5 w-5 text-blue-500" });
    $$payload2.out += `<!----> <h2 class="text-lg font-semibold">AI Interview Coach</h2></div> <p class="text-muted-foreground">Practice for your interviews with AI-powered coaching.</p></div> <div class="flex-1 p-6"><!---->`;
    Root($$payload2, {
      value: activeTab,
      onValueChange: (value) => activeTab = value,
      children: ($$payload3) => {
        $$payload3.out += `<div class="flex items-center justify-between"><!---->`;
        Tabs_list($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Tabs_trigger($$payload4, {
              value: "sessions",
              class: "flex items-center gap-2",
              children: ($$payload5) => {
                Message_square($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Coaching Sessions</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "history",
              class: "flex items-center gap-2",
              children: ($$payload5) => {
                Clock($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>History</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> `;
        Button($$payload3, {
          onclick: startNewSession,
          children: ($$payload4) => {
            Plus($$payload4, { class: "mr-2 h-4 w-4" });
            $$payload4.out += `<!----> New Session`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> <!---->`;
        Tabs_content($$payload3, {
          value: "sessions",
          class: "mt-6",
          children: ($$payload4) => {
            $$payload4.out += `<div class="mb-4"><div class="relative">`;
            Search($$payload4, {
              class: "absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              type: "search",
              placeholder: "Search sessions...",
              class: "pl-8",
              get value() {
                return searchTerm;
              },
              set value($$value) {
                searchTerm = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div></div> `;
            {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex h-64 items-center justify-center"><p class="text-gray-500">Loading sessions...</p></div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "history",
          class: "mt-6",
          children: ($$payload4) => {
            $$payload4.out += `<div class="mb-4"><div class="relative">`;
            Search($$payload4, {
              class: "absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              type: "search",
              placeholder: "Search history...",
              class: "pl-8",
              get value() {
                return searchTerm;
              },
              set value($$value) {
                searchTerm = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div></div> `;
            {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="flex h-64 items-center justify-center"><p class="text-gray-500">Loading history...</p></div>`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></div> <!---->`;
    Root$1($$payload2, {
      get open() {
        return showNewSessionModal;
      },
      set open($$value) {
        showNewSessionModal = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Portal($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_overlay($$payload4, {});
            $$payload4.out += `<!----> <!---->`;
            Dialog_content($$payload4, {
              class: "sm:max-w-md",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Select an Application`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Choose a job application to practice for an interview.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                if (applications.length === 0) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="mb-4 rounded-md bg-blue-50 p-4 text-sm text-blue-700"><p>No job applications found. Add a job application first.</p></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  const each_array_2 = ensure_array_like(applications);
                  $$payload5.out += `<div class="mb-4 max-h-64 overflow-y-auto rounded-md border"><!--[-->`;
                  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                    let application = each_array_2[$$index_2];
                    $$payload5.out += `<div class="flex cursor-pointer items-center justify-between border-b p-3 hover:bg-gray-50" role="button" tabindex="0"><div><div class="font-medium">${escape_html(application.jobTitle)}</div> <div class="flex items-center text-sm text-gray-500">`;
                    Building($$payload5, { class: "mr-1 h-3 w-3" });
                    $$payload5.out += `<!----> ${escape_html(application.company)}</div></div> `;
                    Badge($$payload5, {
                      variant: application.status === "interview" ? "default" : "outline",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(application.status)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div>`;
                  }
                  $$payload5.out += `<!--]--></div>`;
                }
                $$payload5.out += `<!--]--> <!---->`;
                Dialog_footer($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      onclick: () => showNewSessionModal = false,
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Cancel`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Bul3fPc5.js.map
