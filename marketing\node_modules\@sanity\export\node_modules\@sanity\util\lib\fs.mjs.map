{"version": 3, "file": "fs.mjs", "sources": ["../src/fsTools.ts"], "sourcesContent": ["import fs from 'node:fs/promises'\nimport os from 'node:os'\nimport path from 'node:path'\n\nexport async function pathIsEmpty(dir: string): Promise<boolean> {\n  try {\n    const content = await fs.readdir(absolutify(dir))\n    return content.length === 0\n  } catch (err) {\n    if (err.code === 'ENOENT') {\n      return true\n    }\n\n    throw err\n  }\n}\n\nexport function expandHome(filePath: string): string {\n  if (\n    filePath.charCodeAt(0) === 126\n    /* ~ */\n  ) {\n    if (\n      filePath.charCodeAt(1) === 43\n      /* + */\n    ) {\n      return path.join(process.cwd(), filePath.slice(2))\n    }\n\n    const home = os.homedir()\n    return home ? path.join(home, filePath.slice(1)) : filePath\n  }\n\n  return filePath\n}\n\nexport function absolutify(dir: string): string {\n  const pathName = expandHome(dir)\n  return path.isAbsolute(pathName) ? pathName : path.resolve(process.cwd(), pathName)\n}\n"], "names": [], "mappings": ";;;AAIA,eAAsB,YAAY,KAA+B;AAC3D,MAAA;AAEF,YADgB,MAAM,GAAG,QAAQ,WAAW,GAAG,CAAC,GACjC,WAAW;AAAA,WACnB,KAAK;AACZ,QAAI,IAAI,SAAS;AACR,aAAA;AAGH,UAAA;AAAA,EAAA;AAEV;AAEO,SAAS,WAAW,UAA0B;AACnD,MACE,SAAS,WAAW,CAAC,MAAM,KAE3B;AAEE,QAAA,SAAS,WAAW,CAAC,MAAM;AAGpB,aAAA,KAAK,KAAK,QAAQ,OAAO,SAAS,MAAM,CAAC,CAAC;AAG7C,UAAA,OAAO,GAAG,QAAQ;AACjB,WAAA,OAAO,KAAK,KAAK,MAAM,SAAS,MAAM,CAAC,CAAC,IAAI;AAAA,EAAA;AAG9C,SAAA;AACT;AAEO,SAAS,WAAW,KAAqB;AACxC,QAAA,WAAW,WAAW,GAAG;AACxB,SAAA,KAAK,WAAW,QAAQ,IAAI,WAAW,KAAK,QAAQ,QAAQ,IAAI,GAAG,QAAQ;AACpF;"}