{"version": 3, "file": "client.esm.js", "sources": ["../src/client/concurrency-limiter/createClientConcurrencyLimiter.ts"], "sourcesContent": ["import {type ObservableSanityClient, type SanityClient} from '@sanity/client'\nimport {finalize, from, switchMap} from 'rxjs'\n\nimport {ConcurrencyLimiter} from '../../concurrency-limiter'\n\n/**\n * Decorates a sanity client to limit the concurrency of `client.fetch`\n * requests. Keeps the concurrency limit state and returns wrapped clients with\n * that same state if the `clone` `config` or `withConfig` methods are called.\n */\nexport function createClientConcurrencyLimiter(\n  maxConcurrency: number,\n): (input: SanityClient) => SanityClient {\n  const limiter = new ConcurrencyLimiter(maxConcurrency)\n\n  function wrapClient(client: SanityClient): SanityClient {\n    return new Proxy(client, {\n      get: (target, property) => {\n        switch (property) {\n          case 'fetch': {\n            return async (...args: Parameters<SanityClient['fetch']>) => {\n              await limiter.ready()\n              try {\n                // note we want to await before we return so the finally block\n                // will run after the promise has been fulfilled or rejected\n                return await target.fetch(...args)\n              } finally {\n                limiter.release()\n              }\n            }\n          }\n          case 'clone': {\n            return (...args: Parameters<SanityClient['clone']>) => {\n              return wrapClient(target.clone(...args))\n            }\n          }\n          case 'config': {\n            return (...args: Parameters<SanityClient['config']>) => {\n              const result = target.config(...args)\n\n              // if there is a config, it returns a client so we need to wrap again\n              if (args[0]) return wrapClient(result)\n              return result\n            }\n          }\n          case 'withConfig': {\n            return (...args: Parameters<SanityClient['withConfig']>) => {\n              return wrapClient(target.withConfig(...args))\n            }\n          }\n          case 'observable': {\n            return wrapObservableClient(target.observable)\n          }\n          default: {\n            return target[property as keyof SanityClient]\n          }\n        }\n      },\n    })\n  }\n\n  function wrapObservableClient(\n    observableSanityClient: ObservableSanityClient,\n  ): ObservableSanityClient {\n    return new Proxy(observableSanityClient, {\n      get: (target, property) => {\n        switch (property) {\n          case 'fetch': {\n            return (...args: Parameters<ObservableSanityClient['fetch']>) =>\n              from(limiter.ready()).pipe(\n                switchMap(() => target.fetch(...args)),\n                finalize(() => limiter.release()),\n              )\n          }\n          case 'clone': {\n            return (...args: Parameters<ObservableSanityClient['clone']>) => {\n              return wrapObservableClient(target.clone(...args))\n            }\n          }\n          case 'config': {\n            return (...args: Parameters<ObservableSanityClient['config']>) => {\n              const result = target.config(...args)\n\n              // if there is a config, it returns a client so we need to wrap again\n              if (args[0]) return wrapObservableClient(result)\n              return result\n            }\n          }\n          case 'withConfig': {\n            return (...args: Parameters<ObservableSanityClient['withConfig']>) => {\n              return wrapObservableClient(target.withConfig(...args))\n            }\n          }\n          default: {\n            return target[property as keyof ObservableSanityClient]\n          }\n        }\n      },\n    })\n  }\n\n  return wrapClient\n}\n"], "names": [], "mappings": ";;AAUO,SAAS,+BACd,gBACuC;AACjC,QAAA,UAAU,IAAI,mBAAmB,cAAc;AAErD,WAAS,WAAW,QAAoC;AAC/C,WAAA,IAAI,MAAM,QAAQ;AAAA,MACvB,KAAK,CAAC,QAAQ,aAAa;AACzB,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,UAAU,SAA4C;AAC3D,oBAAM,QAAQ,MAAM;AAChB,kBAAA;AAGF,uBAAO,MAAM,OAAO,MAAM,GAAG,IAAI;AAAA,cAAA,UACjC;AACA,wBAAQ,QAAQ;AAAA,cAAA;AAAA,YAEpB;AAAA,UAEF,KAAK;AACH,mBAAO,IAAI,SACF,WAAW,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA,UAG3C,KAAK;AACH,mBAAO,IAAI,SAA6C;AACtD,oBAAM,SAAS,OAAO,OAAO,GAAG,IAAI;AAGpC,qBAAI,KAAK,CAAC,IAAU,WAAW,MAAM,IAC9B;AAAA,YACT;AAAA,UAEF,KAAK;AACH,mBAAO,IAAI,SACF,WAAW,OAAO,WAAW,GAAG,IAAI,CAAC;AAAA,UAGhD,KAAK;AACI,mBAAA,qBAAqB,OAAO,UAAU;AAAA,UAE/C;AACE,mBAAO,OAAO,QAA8B;AAAA,QAAA;AAAA,MAEhD;AAAA,IACF,CACD;AAAA,EAAA;AAGH,WAAS,qBACP,wBACwB;AACjB,WAAA,IAAI,MAAM,wBAAwB;AAAA,MACvC,KAAK,CAAC,QAAQ,aAAa;AACzB,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,IAAI,SACT,KAAK,QAAQ,MAAO,CAAA,EAAE;AAAA,cACpB,UAAU,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA,cACrC,SAAS,MAAM,QAAQ,QAAS,CAAA;AAAA,YAClC;AAAA,UAEJ,KAAK;AACH,mBAAO,IAAI,SACF,qBAAqB,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA,UAGrD,KAAK;AACH,mBAAO,IAAI,SAAuD;AAChE,oBAAM,SAAS,OAAO,OAAO,GAAG,IAAI;AAGpC,qBAAI,KAAK,CAAC,IAAU,qBAAqB,MAAM,IACxC;AAAA,YACT;AAAA,UAEF,KAAK;AACH,mBAAO,IAAI,SACF,qBAAqB,OAAO,WAAW,GAAG,IAAI,CAAC;AAAA,UAG1D;AACE,mBAAO,OAAO,QAAwC;AAAA,QAAA;AAAA,MAE1D;AAAA,IACF,CACD;AAAA,EAAA;AAGI,SAAA;AACT;"}