import { p as push, q as pop, M as ensure_array_like, O as escape_html, V as copy_payload, W as assign_payload, N as attr, aa as maybe_selected } from './index3-CqUPEnZw.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { R as Root$1, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { B as Button } from './button-CrucCo1G.js';
import { R as Root, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { C as Card } from './card-D-TLkt4h.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { L as LimitType, F as FeatureCategory } from './features-SWeUHekJ.js';
import { C as Chart_bar, T as Trash } from './trash-D4HQqzyK.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { R as Root$2, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { L as Layers } from './layers-Bh452qa5.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { o as objectType, g as coerce, s as stringType, h as nativeEnumType, a as arrayType, b as booleanType } from './types-D78SXuvm.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index-DjwFQdT_.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './index2-Cut0V_vU.js';
import './Icon-A4vzmk-O.js';
import './dialog-overlay-CspOQRJq.js';
import './dialog-description2-rfr-pd9k.js';

const featureLimitSchema = objectType({
  id: stringType().min(1, { message: "Limit ID is required" }),
  name: stringType().min(1, { message: "Limit name is required" }),
  description: stringType().optional(),
  defaultValue: coerce.number().min(0, { message: "Default value must be a positive number" }),
  type: nativeEnumType(LimitType),
  unit: stringType().optional(),
  resetDay: coerce.number().min(1).max(31).optional()
});
objectType({
  id: stringType().min(1, { message: "Feature ID is required" }),
  name: stringType().min(1, { message: "Feature name is required" }),
  description: stringType().optional(),
  category: nativeEnumType(FeatureCategory),
  icon: stringType().optional(),
  beta: booleanType().default(false),
  limits: arrayType(featureLimitSchema).default([])
});
const defaultFeature = {
  id: "",
  name: "",
  description: "",
  category: FeatureCategory.Core,
  icon: "",
  beta: false,
  limits: []
};
const defaultFeatureLimit = {
  id: "",
  name: "",
  description: "",
  defaultValue: 10,
  type: LimitType.Monthly,
  unit: "",
  resetDay: 1
};
function AddFeatureTab($$payload, $$props) {
  push();
  let newFeature = { ...defaultFeature };
  let newLimit = { ...defaultFeatureLimit };
  Card($$payload, {
    children: ($$payload2) => {
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(Object.values(FeatureCategory));
          $$payload3.out += `<form><div class="grid grid-cols-2 gap-4"><div class="space-y-2">`;
          Label($$payload3, {
            for: "feature-id",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Feature ID`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="feature-id"${attr("value", newFeature.id)} placeholder="e.g. custom_reports" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <p class="text-muted-foreground text-xs">A unique identifier for the feature</p></div> <div class="space-y-2">`;
          Label($$payload3, {
            for: "feature-name",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Feature Name`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="feature-name"${attr("value", newFeature.name)} placeholder="e.g. Custom Reports" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <p class="text-muted-foreground text-xs">Display name for the feature</p></div> <div class="col-span-2 space-y-2">`;
          Label($$payload3, {
            for: "feature-description",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Description`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <textarea id="feature-description" placeholder="Describe what this feature does" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[60px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50">`;
          const $$body = escape_html(newFeature.description);
          if ($$body) {
            $$payload3.out += `${$$body}`;
          }
          $$payload3.out += `</textarea> <p class="text-muted-foreground text-xs">Detailed explanation of the feature</p></div> <div class="space-y-2">`;
          Label($$payload3, {
            for: "feature-category",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Category`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <select id="feature-category" class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500">`;
          $$payload3.select_value = newFeature.category;
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let category = each_array[$$index];
            $$payload3.out += `<option${attr("value", category)}${maybe_selected($$payload3, category)}>${escape_html(category)}</option>`;
          }
          $$payload3.out += `<!--]-->`;
          $$payload3.select_value = void 0;
          $$payload3.out += `</select> <p class="text-muted-foreground text-xs">Group this feature belongs to</p></div> <div class="space-y-2">`;
          Label($$payload3, {
            for: "feature-icon",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Icon (optional)`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="feature-icon"${attr("value", newFeature.icon)} placeholder="e.g. file-bar-chart" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/> <p class="text-muted-foreground text-xs">Lucide icon name for the feature</p></div> <div class="col-span-2 flex items-center space-y-2"><input type="checkbox" id="feature-beta"${attr("checked", newFeature.beta, true)} class="mr-2"/> `;
          Label($$payload3, {
            for: "feature-beta",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Beta Feature`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> <div class="col-span-2 mt-4 border-t pt-4"><h4 class="mb-2 text-base font-semibold">Feature Limits</h4> `;
          if (newFeature.limits && newFeature.limits.length > 0) {
            $$payload3.out += "<!--[-->";
            const each_array_1 = ensure_array_like(newFeature.limits);
            $$payload3.out += `<div class="mb-4 space-y-2"><!--[-->`;
            for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
              let limit = each_array_1[$$index_1];
              $$payload3.out += `<div class="flex items-center justify-between rounded-md border p-2"><div><div class="font-medium">${escape_html(limit.name)}</div> <div class="text-muted-foreground text-xs">ID: ${escape_html(limit.id)} | Default: ${escape_html(limit.defaultValue)}
                      ${escape_html(limit.unit || "")}</div> <div class="text-muted-foreground text-xs">${escape_html(limit.description)}</div></div> <button class="text-destructive hover:bg-destructive/10 hover:text-destructive rounded-full p-1">`;
              Trash($$payload3, { class: "h-4 w-4" });
              $$payload3.out += `<!----></button></div>`;
            }
            $$payload3.out += `<!--]--></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<div class="text-muted-foreground mb-4 rounded-md border border-dashed p-4 text-center">No limits defined for this feature.</div>`;
          }
          $$payload3.out += `<!--]--> <div class="rounded-md border p-3"><h5 class="mb-2 text-sm font-semibold">Add New Limit</h5> <div class="grid grid-cols-2 gap-2"><div>`;
          Label($$payload3, {
            for: "new-limit-id",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Limit ID`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="new-limit-id"${attr("value", newLimit.id)} placeholder="e.g. monthly_usage" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
          Label($$payload3, {
            for: "new-limit-name",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Name`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="new-limit-name"${attr("value", newLimit.name)} placeholder="e.g. Monthly Usage" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2">`;
          Label($$payload3, {
            for: "new-limit-description",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Description`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="new-limit-description"${attr("value", newLimit.description)} placeholder="e.g. Maximum usage per month" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
          Label($$payload3, {
            for: "new-limit-default",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Default Value`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="new-limit-default" type="number"${attr("value", newLimit.defaultValue)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
          Label($$payload3, {
            for: "new-limit-unit",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Unit (optional)`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="new-limit-unit"${attr("value", newLimit.unit)} placeholder="e.g. uses, items, GB" class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div>`;
          Label($$payload3, {
            for: "new-limit-type",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Limit Type`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <select id="new-limit-type" class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500">`;
          $$payload3.select_value = newLimit.type;
          $$payload3.out += `<option${attr("value", LimitType.Monthly)}${maybe_selected($$payload3, LimitType.Monthly)}>Monthly</option><option${attr("value", LimitType.Total)}${maybe_selected($$payload3, LimitType.Total)}>Total</option><option${attr("value", LimitType.Concurrent)}${maybe_selected($$payload3, LimitType.Concurrent)}>Concurrent</option><option${attr("value", LimitType.Unlimited)}${maybe_selected($$payload3, LimitType.Unlimited)}>Unlimited</option>`;
          $$payload3.select_value = void 0;
          $$payload3.out += `</select></div> <div>`;
          Label($$payload3, {
            for: "new-limit-reset",
            class: "text-xs",
            children: ($$payload4) => {
              $$payload4.out += `<!---->Reset Day (Monthly only)`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <input id="new-limit-reset" type="number"${attr("value", newLimit.resetDay)}${attr("disabled", newLimit.type !== LimitType.Monthly, true)} class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"/></div> <div class="col-span-2 mt-2"><button type="button" class="border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50">`;
          Plus($$payload3, { class: "mr-1.5 h-4 w-4" });
          $$payload3.out += `<!----> Add Limit</button></div></div></div></div></div> <div class="mt-6 flex justify-end"><button type="submit" class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50">`;
          Plus($$payload3, { class: "mr-2 h-4 w-4" });
          $$payload3.out += `<!----> Add Feature</button></div></form>`;
        },
        $$slots: { default: true }
      });
    },
    $$slots: { default: true }
  });
  pop();
}
function ManageFeaturesTab($$payload, $$props) {
  push();
  let featuresByCategory;
  let features = [];
  let deleteDialogOpen = false;
  let featureToDelete = null;
  let isDeleting = false;
  ({
    category: FeatureCategory.Core
  });
  ({
    type: LimitType.Monthly
  });
  async function removeFeature() {
    return;
  }
  featuresByCategory = Array.isArray(features) ? features.reduce(
    (acc, feature) => {
      const category = feature.category || "uncategorized";
      if (!acc[category]) {
        acc[category] = [];
      }
      if (!feature.limits) {
        feature.limits = [];
      }
      acc[category].push(feature);
      return acc;
    },
    {}
  ) : {};
  Object.keys(featuresByCategory).forEach((category) => {
    if (!Array.isArray(featuresByCategory[category])) {
      featuresByCategory[category] = [];
    }
  });
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex h-64 items-center justify-center"><div class="text-center"><div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div> <p class="mt-4 text-lg">Loading features...</p></div></div>`;
    }
    $$payload2.out += `<!--]--> `;
    Root$2($$payload2, {
      get open() {
        return deleteDialogOpen;
      },
      set open($$value) {
        deleteDialogOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Alert_dialog_content($$payload3, {
          children: ($$payload4) => {
            Alert_dialog_header($$payload4, {
              children: ($$payload5) => {
                Alert_dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Delete Feature`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Alert_dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Are you sure you want to remove feature "${escape_html(featureToDelete)}"? This will also remove it from
        all plans. This action cannot be undone.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Alert_dialog_footer($$payload4, {
              children: ($$payload5) => {
                Alert_dialog_cancel($$payload5, {
                  onclick: () => deleteDialogOpen = false,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Alert_dialog_action($$payload5, {
                  onclick: removeFeature,
                  disabled: isDeleting,
                  class: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
                  children: ($$payload6) => {
                    {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `Delete`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
const FEATURE_TYPES = [
  {
    id: "service",
    name: "Service Features",
    description: "Core service features including Auto-Apply, Co-Pilot, Job Tracker, Resume Builder, etc.",
    endpoint: "/api/admin/features/seed-service",
    icon: "briefcase"
  },
  {
    id: "analysis",
    name: "Analysis Features",
    description: "Analytics and data analysis features for job market insights and career planning.",
    endpoint: "/api/admin/features/seed-analysis",
    icon: "chart-bar"
  },
  {
    id: "all",
    name: "All Features",
    description: "Seed all feature types at once.",
    endpoint: "/api/admin/features/seed-all",
    icon: "layers"
  }
];
function _page($$payload, $$props) {
  push();
  let activeTab = "manage";
  let isSeeding = false;
  let currentSeedingType = "";
  async function handleFeatureTypeChange(value) {
    if (isSeeding) return;
    const featureType = FEATURE_TYPES.find((ft) => ft.id === value);
    if (!featureType) {
      toast.error("Invalid feature type selected");
      return;
    }
    try {
      isSeeding = true;
      currentSeedingType = featureType.id;
      toast.loading(`Seeding ${featureType.name}...`, { id: "seeding-toast" });
      const response = await fetch(featureType.endpoint, { method: "POST", credentials: "include" });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `Failed to seed ${featureType.name}: ${response.status}`);
      }
      const result = await response.json();
      toast.success(`${featureType.name} seeded`, {
        id: "seeding-toast",
        description: result.message || `${featureType.name} have been added to the system`,
        duration: 3e3
      });
      window.dispatchEvent(new CustomEvent("featureAdded"));
    } catch (err) {
      console.error(`Error seeding ${featureType.name}:`, err);
      toast.error(`Failed to seed ${featureType.name}`, {
        id: "seeding-toast",
        description: err.message,
        duration: 5e3
      });
    } finally {
      isSeeding = false;
      currentSeedingType = "";
    }
  }
  SEO($$payload, { title: "Feature Management" });
  $$payload.out += `<!----> <div class="flex items-center justify-between gap-1 border-b px-4 py-2"><h2 class="text-lg font-semibold">Feature Management</h2> <div class="space-y-4"><div class="flex gap-2">`;
  Button($$payload, {
    variant: "outline",
    onclick: () => window.location.href = "/dashboard/settings/admin/plans",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Back to Plans`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <div class="flex items-center gap-2">`;
  if (isSeeding) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center gap-2 rounded-md border px-3 py-2">`;
    Loader_circle($$payload, { class: "h-4 w-4 animate-spin" });
    $$payload.out += `<!----> <span>Seeding ${escape_html(currentSeedingType)}...</span></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<!---->`;
    Root($$payload, {
      type: "single",
      onValueChange: handleFeatureTypeChange,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Select_trigger($$payload2, {
          class: "w-[200px]",
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            Select_value($$payload3, { placeholder: "Seed Features..." });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Select_content($$payload2, {
          class: "w-[200px]",
          children: ($$payload3) => {
            const each_array = ensure_array_like(FEATURE_TYPES);
            $$payload3.out += `<!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let featureType = each_array[$$index];
              $$payload3.out += `<!---->`;
              Select_item($$payload3, {
                value: featureType.id,
                children: ($$payload4) => {
                  $$payload4.out += `<div class="flex items-center gap-2">`;
                  if (featureType.icon === "briefcase") {
                    $$payload4.out += "<!--[-->";
                    Briefcase($$payload4, { class: "h-4 w-4" });
                  } else if (featureType.icon === "chart-bar") {
                    $$payload4.out += "<!--[1-->";
                    Chart_bar($$payload4, { class: "h-4 w-4" });
                  } else if (featureType.icon === "layers") {
                    $$payload4.out += "<!--[2-->";
                    Layers($$payload4, { class: "h-4 w-4" });
                  } else {
                    $$payload4.out += "<!--[!-->";
                    Plus($$payload4, { class: "h-4 w-4" });
                  }
                  $$payload4.out += `<!--]--> <span>${escape_html(featureType.name)}</span></div>`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  }
  $$payload.out += `<!--]--></div></div></div></div> <!---->`;
  Root$1($$payload, {
    value: activeTab,
    class: "w-full",
    onValueChange: (value) => activeTab = value,
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        class: "border-border border-b p-0",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Tabs_list($$payload3, {
            class: "flex flex-row gap-2 divide-x",
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Tabs_trigger($$payload4, {
                value: "manage",
                class: "flex-1 rounded-none border-none",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Manage Features`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_trigger($$payload4, {
                value: "add",
                class: "flex-1",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Add New Feature`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "manage",
        class: "p-4",
        children: ($$payload3) => {
          ManageFeaturesTab($$payload3);
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Tabs_content($$payload2, {
        value: "add",
        class: "p-4",
        children: ($$payload3) => {
          AddFeatureTab($$payload3);
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BOt7KOIr.js.map
