import { j as json } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const GET = async () => {
  try {
    console.log("🔍 Testing database connection...");
    if (!prisma) {
      console.error("❌ Prisma client is not initialized");
      return json({ error: "Database connection not available" }, { status: 500 });
    }
    console.log("✅ Prisma client is available");
    const totalCompanies = await prisma.company.count();
    console.log(`📊 Total companies: ${totalCompanies}`);
    const companies = await prisma.company.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        logoUrl: true,
        activeJobCount: true
      }
    });
    console.log(`📋 Sample companies:`, companies);
    return json({
      success: true,
      totalCompanies,
      sampleCompanies: companies
    });
  } catch (error) {
    console.error("💥 Database test error:", error);
    return json({ error: "Database test failed", details: error }, { status: 500 });
  }
};

export { GET };
//# sourceMappingURL=_server.ts-Bro_Ojbp.js.map
