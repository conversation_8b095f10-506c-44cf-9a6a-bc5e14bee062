import 'clsx';
import { p as push, q as pop, K as fallback, O as escape_html, M as ensure_array_like, Q as bind_props, $ as attr_style, P as stringify, J as attr_class } from './index3-CqUPEnZw.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { subDays, startOfDay, endOfDay } from 'date-fns';
import { $ as $35ea8db9cb2ccb90$export$99faa760c7908e4f, R as Range_calendar, S as Select_separator, a as $14e0f24ef4ac5c92$export$aa8b41735afcabd2 } from './index16-Dse3U8B8.js';
import { B as Button } from './button-CrucCo1G.js';
import { R as Root$2, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { T as Table, a as Table_header, c as Table_row, b as Table_body, d as Table_head, e as Table_cell } from './table-row-CyhLzMgE.js';
import { R as Root$1, P as Popover_trigger, a as Popover_content } from './index14-C2WSwUih.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { S as Select_group } from './select-group-Cxqg41Dj.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { D as Download } from './download-CLn66Ope.js';
import { M as Mail } from './mail-Brqxil2x.js';
import { M as Mouse_pointer_click, U as User_x } from './user-x-BlXWgQj5.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './index2-Cut0V_vU.js';
import './separator-5ooeI4XN.js';
import './chevron-right-C2rn-JeO.js';
import './Icon2-DkOdBr51.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './index-DjwFQdT_.js';
import './check2-Bg6barQb.js';
import './scroll-lock-BkBz2nVp.js';
import './events-CUVXVLW9.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './Icon-A4vzmk-O.js';

function OverviewTab($$payload, $$props) {
  push();
  let dateRange = $$props["dateRange"];
  let calendarDateRange = $$props["calendarDateRange"];
  let templateFilter = $$props["templateFilter"];
  let templates = fallback($$props["templates"], () => [], true);
  let emailStats = fallback(
    $$props["emailStats"],
    () => ({
      total: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      bounced: 0
    }),
    true
  );
  let chartData = fallback($$props["chartData"], () => [], true);
  let topEmails = fallback($$props["topEmails"], () => [], true);
  let isLoading = fallback($$props["isLoading"], false);
  let isExporting = fallback($$props["isExporting"], false);
  let handleCalendarDateChange = $$props["handleCalendarDateChange"];
  let handleFilterChange = $$props["handleFilterChange"];
  let loadEmailStats = $$props["loadEmailStats"];
  let exportData = $$props["exportData"];
  $$payload.out += `<div class="mb-6 flex flex-wrap gap-4"><div><label for="timeRange" class="mb-1 block text-sm font-medium">Time Range</label> <div>`;
  Root$1($$payload, {
    children: ($$payload2) => {
      Popover_trigger($$payload2, {
        children: ($$payload3) => {
          Button($$payload3, {
            variant: "outline",
            class: "w-[250px] justify-start",
            children: ($$payload4) => {
              $$payload4.out += `<span class="mr-2">📅</span> `;
              if (dateRange.startDate && dateRange.endDate) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `${escape_html(new Date(dateRange.startDate).toLocaleDateString())} - ${escape_html(new Date(dateRange.endDate).toLocaleDateString())}`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `Select date range`;
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Popover_content($$payload2, {
        class: "w-auto p-0",
        align: "start",
        children: ($$payload3) => {
          Range_calendar($$payload3, {
            value: calendarDateRange,
            onValueChange: (newValue) => {
              if (newValue?.start && newValue?.end) {
                const startDate = newValue.start.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
                const endDate = newValue.end.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
                const customEvent = new CustomEvent("change", {
                  detail: { startDate, endDate, calendarValue: newValue }
                });
                handleCalendarDateChange(customEvent);
              }
            },
            numberOfMonths: 2
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div><label for="templateFilter" class="mb-1 block text-sm font-medium">Template</label> `;
  Root$2($$payload, {
    type: "single",
    value: templateFilter,
    onValueChange: (value) => {
      templateFilter = value;
      handleFilterChange();
    },
    children: ($$payload2) => {
      Select_trigger($$payload2, {
        class: "w-[250px]",
        children: ($$payload3) => {
          Select_value($$payload3, { placeholder: "Select template" });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Select_content($$payload2, {
        class: "w-[250px]",
        children: ($$payload3) => {
          Select_group($$payload3, {
            children: ($$payload4) => {
              Select_item($$payload4, {
                value: "all",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->All Templates`;
                },
                $$slots: { default: true }
              });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          if (templates.length > 1) {
            $$payload3.out += "<!--[-->";
            const each_array = ensure_array_like([
              ...new Set(templates.filter((t) => t.name !== "all").map((t) => t.category || "Other"))
            ]);
            $$payload3.out += `<!--[-->`;
            for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
              let category = each_array[$$index_1];
              Select_separator($$payload3, {});
              $$payload3.out += `<!----> <div class="px-2 py-1.5 text-sm font-semibold">${escape_html(category)}</div> `;
              Select_group($$payload3, {
                children: ($$payload4) => {
                  const each_array_1 = ensure_array_like(templates.filter((t) => t.name !== "all" && (t.category || "Other") === category));
                  $$payload4.out += `<!--[-->`;
                  for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                    let template = each_array_1[$$index];
                    Select_item($$payload4, {
                      value: template.name,
                      title: template.description || "",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(template.label)}`;
                      },
                      $$slots: { default: true }
                    });
                  }
                  $$payload4.out += `<!--]-->`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            }
            $$payload3.out += `<!--]-->`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="ml-auto flex gap-2"><div><div class="mb-1 block text-sm font-medium opacity-0">Refresh</div> `;
  Button($$payload, {
    variant: "outline",
    size: "icon",
    onclick: () => loadEmailStats(),
    disabled: isLoading,
    class: "h-10 w-10",
    children: ($$payload2) => {
      Refresh_cw($$payload2, {
        class: `h-4 w-4 ${isLoading ? "animate-spin" : ""}`
      });
      $$payload2.out += `<!----> <span class="sr-only">Refresh</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div><div class="mb-1 block text-sm font-medium opacity-0">Export</div> `;
  Button($$payload, {
    variant: "outline",
    disabled: isExporting,
    onclick: () => exportData(),
    children: ($$payload2) => {
      if (isExporting) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
        Download($$payload2, { class: "mr-2 h-4 w-4" });
      }
      $$payload2.out += `<!--]--> Export Data`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div> `;
  if (!emailStats.total && !isLoading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex h-64 flex-col items-center justify-center gap-4"><p class="text-muted-foreground text-center">Click the button below to load email analytics data</p> `;
    Button($$payload, {
      onclick: () => loadEmailStats(),
      children: ($$payload2) => {
        $$payload2.out += `<!---->Load Analytics Data`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div>`;
  } else if (isLoading) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">`;
    Card($$payload, {
      children: ($$payload2) => {
        Card_content($$payload2, {
          class: "p-6",
          children: ($$payload3) => {
            $$payload3.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Total Emails</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(emailStats.total.toLocaleString())}</h3></div> <div class="bg-primary/10 rounded-full p-2">`;
            Mail($$payload3, { class: "text-primary h-5 w-5" });
            $$payload3.out += `<!----></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium text-green-600">${escape_html(Math.round(emailStats.delivered / emailStats.total * 100) || 0)}%</span> delivery rate</div>`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    Card($$payload, {
      children: ($$payload2) => {
        Card_content($$payload2, {
          class: "p-6",
          children: ($$payload3) => {
            $$payload3.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Open Rate</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(Math.round(emailStats.opened / emailStats.delivered * 100) || 0)}%</h3></div> <div class="rounded-full bg-blue-100 p-2">`;
            Mail($$payload3, { class: "h-5 w-5 text-blue-600" });
            $$payload3.out += `<!----></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium">${escape_html(emailStats.opened.toLocaleString())}</span> emails opened</div>`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    Card($$payload, {
      children: ($$payload2) => {
        Card_content($$payload2, {
          class: "p-6",
          children: ($$payload3) => {
            $$payload3.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Click Rate</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(Math.round(emailStats.clicked / emailStats.opened * 100) || 0)}%</h3></div> <div class="rounded-full bg-purple-100 p-2">`;
            Mouse_pointer_click($$payload3, { class: "h-5 w-5 text-purple-600" });
            $$payload3.out += `<!----></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium">${escape_html(emailStats.clicked.toLocaleString())}</span> emails clicked</div>`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    Card($$payload, {
      children: ($$payload2) => {
        Card_content($$payload2, {
          class: "p-6",
          children: ($$payload3) => {
            $$payload3.out += `<div class="flex items-start justify-between"><div><p class="text-muted-foreground text-sm font-medium">Bounce Rate</p> <h3 class="mt-1 text-2xl font-bold">${escape_html(Math.round(emailStats.bounced / emailStats.total * 100) || 0)}%</h3></div> <div class="rounded-full bg-red-100 p-2">`;
            Triangle_alert($$payload3, { class: "h-5 w-5 text-red-600" });
            $$payload3.out += `<!----></div></div> <div class="text-muted-foreground mt-4 text-sm"><span class="font-medium">${escape_html(emailStats.bounced.toLocaleString())}</span> emails bounced</div>`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div> <div class="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2">`;
    Card($$payload, {
      children: ($$payload2) => {
        Card_header($$payload2, {
          children: ($$payload3) => {
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Email Activity Over Time`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Email events over the selected time period`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Card_content($$payload2, {
          children: ($$payload3) => {
            if (chartData.length === 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="text-muted-foreground flex h-64 items-center justify-center"><p>No data available for the selected time period</p></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
              $$payload3.out += `<div class="text-muted-foreground flex h-64 items-center justify-center">`;
              Mail($$payload3, { class: "h-8 w-8 opacity-50" });
              $$payload3.out += `<!----> <p class="ml-2">Chart visualization would appear here</p></div>`;
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    Card($$payload, {
      children: ($$payload2) => {
        Card_header($$payload2, {
          children: ($$payload3) => {
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Event Distribution`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Distribution of email events by type`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Card_content($$payload2, {
          children: ($$payload3) => {
            if (emailStats.total === 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="text-muted-foreground flex h-64 items-center justify-center"><p>No data available for the selected time period</p></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
              $$payload3.out += `<div class="text-muted-foreground flex h-64 items-center justify-center">`;
              Mail($$payload3, { class: "h-8 w-8 opacity-50" });
              $$payload3.out += `<!----> <p class="ml-2">Chart visualization would appear here</p></div>`;
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div> `;
    Card($$payload, {
      children: ($$payload2) => {
        Card_header($$payload2, {
          children: ($$payload3) => {
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Top Performing Emails`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Card_description($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Emails with the highest open and click rates`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Card_content($$payload2, {
          children: ($$payload3) => {
            if (topEmails.length === 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="text-muted-foreground flex h-40 items-center justify-center"><p>No data available for the selected time period</p></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
              Table($$payload3, {
                children: ($$payload4) => {
                  Table_header($$payload4, {
                    children: ($$payload5) => {
                      Table_row($$payload5, {
                        children: ($$payload6) => {
                          Table_head($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Template`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_head($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Subject`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_head($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Sent`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_head($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Open Rate`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_head($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->Click Rate`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!---->`;
                        },
                        $$slots: { default: true }
                      });
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!----> `;
                  Table_body($$payload4, {
                    children: ($$payload5) => {
                      const each_array_2 = ensure_array_like(topEmails);
                      $$payload5.out += `<!--[-->`;
                      for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                        let email = each_array_2[$$index_2];
                        Table_row($$payload5, {
                          children: ($$payload6) => {
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(email.template)}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----> `;
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(email.subject)}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----> `;
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(email.sent.toLocaleString())}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----> `;
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<div class="flex items-center"><div class="mr-2 h-2 w-16 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-blue-600"${attr_style(`width: ${stringify(email.openRate)}%`)}></div></div> <span>${escape_html(email.openRate)}%</span></div>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----> `;
                            Table_cell($$payload6, {
                              children: ($$payload7) => {
                                $$payload7.out += `<div class="flex items-center"><div class="mr-2 h-2 w-16 rounded-full bg-gray-200"><div class="h-2 rounded-full bg-purple-600"${attr_style(`width: ${stringify(email.clickRate)}%`)}></div></div> <span>${escape_html(email.clickRate)}%</span></div>`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                      }
                      $$payload5.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
            }
            $$payload3.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, {
    dateRange,
    calendarDateRange,
    templateFilter,
    templates,
    emailStats,
    chartData,
    topEmails,
    isLoading,
    isExporting,
    handleCalendarDateChange,
    handleFilterChange,
    loadEmailStats,
    exportData
  });
  pop();
}
function EventsTab($$payload, $$props) {
  push();
  let dateRange = $$props["dateRange"];
  let calendarDateRange = $$props["calendarDateRange"];
  let templateFilter = $$props["templateFilter"];
  let eventType = $$props["eventType"];
  let templates = fallback($$props["templates"], () => [], true);
  let eventTypeOptions = fallback($$props["eventTypeOptions"], () => [], true);
  let emailEvents = fallback($$props["emailEvents"], () => [], true);
  let isLoading = fallback($$props["isLoading"], false);
  let currentPage = fallback($$props["currentPage"], 1);
  let totalPages = fallback($$props["totalPages"], 1);
  let totalEvents = fallback($$props["totalEvents"], 0);
  let itemsPerPage = fallback($$props["itemsPerPage"], 10);
  let handleCalendarDateChange = $$props["handleCalendarDateChange"];
  let loadEmailEvents = $$props["loadEmailEvents"];
  let goToPage = $$props["goToPage"];
  let getEventTypeBadgeClass = $$props["getEventTypeBadgeClass"];
  let formatDate = $$props["formatDate"];
  $$payload.out += `<div class="mb-6 flex flex-wrap gap-4"><div><label for="eventType" class="mb-1 block text-sm font-medium">Event Type</label> `;
  Root$2($$payload, {
    type: "single",
    value: eventType,
    onValueChange: (value) => {
      eventType = value;
      loadEmailEvents();
    },
    children: ($$payload2) => {
      Select_trigger($$payload2, {
        class: "w-[180px]",
        children: ($$payload3) => {
          Select_value($$payload3, { placeholder: "Select event type" });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Select_content($$payload2, {
        class: "w-[180px]",
        children: ($$payload3) => {
          Select_group($$payload3, {
            children: ($$payload4) => {
              const each_array = ensure_array_like(eventTypeOptions);
              $$payload4.out += `<!--[-->`;
              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                let option = each_array[$$index];
                Select_item($$payload4, {
                  value: option.value,
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center">`;
                    if (option.value === "delivered") {
                      $$payload5.out += "<!--[-->";
                      Mail($$payload5, { class: "mr-2 h-4 w-4 text-green-500" });
                    } else if (option.value === "opened") {
                      $$payload5.out += "<!--[1-->";
                      Mail($$payload5, { class: "mr-2 h-4 w-4 text-blue-500" });
                    } else if (option.value === "clicked") {
                      $$payload5.out += "<!--[2-->";
                      Mouse_pointer_click($$payload5, { class: "mr-2 h-4 w-4 text-purple-500" });
                    } else if (option.value === "bounced") {
                      $$payload5.out += "<!--[3-->";
                      Triangle_alert($$payload5, { class: "mr-2 h-4 w-4 text-red-500" });
                    } else if (option.value === "complained") {
                      $$payload5.out += "<!--[4-->";
                      Triangle_alert($$payload5, { class: "mr-2 h-4 w-4 text-orange-500" });
                    } else if (option.value === "unsubscribed") {
                      $$payload5.out += "<!--[5-->";
                      User_x($$payload5, { class: "mr-2 h-4 w-4 text-gray-500" });
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> ${escape_html(option.label)}</div>`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div><label for="templateFilter" class="mb-1 block text-sm font-medium">Template</label> `;
  Root$2($$payload, {
    type: "single",
    value: templateFilter,
    onValueChange: (value) => {
      templateFilter = value;
      loadEmailEvents();
    },
    children: ($$payload2) => {
      Select_trigger($$payload2, {
        class: "w-[250px]",
        children: ($$payload3) => {
          Select_value($$payload3, { placeholder: "Select template" });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Select_content($$payload2, {
        class: "w-[250px]",
        children: ($$payload3) => {
          Select_group($$payload3, {
            children: ($$payload4) => {
              Select_item($$payload4, {
                value: "all",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->All Templates`;
                },
                $$slots: { default: true }
              });
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          if (templates.length > 1) {
            $$payload3.out += "<!--[-->";
            const each_array_1 = ensure_array_like([
              ...new Set(templates.filter((t) => t.name !== "all").map((t) => t.category || "Other"))
            ]);
            $$payload3.out += `<!--[-->`;
            for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
              let category = each_array_1[$$index_2];
              Select_separator($$payload3, {});
              $$payload3.out += `<!----> <div class="px-2 py-1.5 text-sm font-semibold">${escape_html(category)}</div> `;
              Select_group($$payload3, {
                children: ($$payload4) => {
                  const each_array_2 = ensure_array_like(templates.filter((t) => t.name !== "all" && (t.category || "Other") === category));
                  $$payload4.out += `<!--[-->`;
                  for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                    let template = each_array_2[$$index_1];
                    Select_item($$payload4, {
                      value: template.name,
                      title: template.description || "",
                      children: ($$payload5) => {
                        $$payload5.out += `<!---->${escape_html(template.label)}`;
                      },
                      $$slots: { default: true }
                    });
                  }
                  $$payload4.out += `<!--]-->`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            }
            $$payload3.out += `<!--]-->`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div><label for="timeRange2" class="mb-1 block text-sm font-medium">Time Range</label> <div>`;
  Root$1($$payload, {
    children: ($$payload2) => {
      Popover_trigger($$payload2, {
        children: ($$payload3) => {
          Button($$payload3, {
            variant: "outline",
            class: "w-[250px] justify-start",
            children: ($$payload4) => {
              $$payload4.out += `<span class="mr-2">📅</span> `;
              if (dateRange.startDate && dateRange.endDate) {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `${escape_html(new Date(dateRange.startDate).toLocaleDateString())} - ${escape_html(new Date(dateRange.endDate).toLocaleDateString())}`;
              } else {
                $$payload4.out += "<!--[!-->";
                $$payload4.out += `Select date range`;
              }
              $$payload4.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Popover_content($$payload2, {
        class: "w-auto p-0",
        align: "start",
        children: ($$payload3) => {
          Range_calendar($$payload3, {
            value: calendarDateRange,
            onValueChange: (newValue) => {
              if (newValue?.start && newValue?.end) {
                const startDate = newValue.start.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
                const endDate = newValue.end.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
                const customEvent = new CustomEvent("change", {
                  detail: { startDate, endDate, calendarValue: newValue }
                });
                handleCalendarDateChange(customEvent);
              }
            },
            numberOfMonths: 2
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="ml-auto flex gap-2"><div><div class="mb-1 block text-sm font-medium opacity-0">Refresh</div> `;
  Button($$payload, {
    variant: "outline",
    size: "icon",
    onclick: () => loadEmailEvents(),
    disabled: isLoading,
    class: "h-10 w-10",
    children: ($$payload2) => {
      Refresh_cw($$payload2, {
        class: `h-4 w-4 ${isLoading ? "animate-spin" : ""}`
      });
      $$payload2.out += `<!----> <span class="sr-only">Refresh</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div> `;
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        class: "flex flex-row items-center justify-between",
        children: ($$payload3) => {
          $$payload3.out += `<div>`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Email Event Log`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Detailed log of email events`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div> `;
          Button($$payload3, {
            variant: "outline",
            size: "sm",
            onclick: () => loadEmailEvents(),
            disabled: isLoading,
            class: "h-8 w-8 p-0",
            children: ($$payload4) => {
              Refresh_cw($$payload4, {
                class: `h-4 w-4 ${isLoading ? "animate-spin" : ""}`
              });
              $$payload4.out += `<!----> <span class="sr-only">Refresh</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        children: ($$payload3) => {
          if (isLoading) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div class="flex h-64 items-center justify-center"><div class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div></div>`;
          } else if (emailEvents.length === 0) {
            $$payload3.out += "<!--[1-->";
            $$payload3.out += `<div class="flex h-40 flex-col items-center justify-center gap-4"><p class="text-muted-foreground text-center">Click the button below to load email events</p> `;
            Button($$payload3, {
              onclick: () => loadEmailEvents(),
              children: ($$payload4) => {
                $$payload4.out += `<!---->Load Email Events`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<div class="space-y-4">`;
            Table($$payload3, {
              children: ($$payload4) => {
                Table_header($$payload4, {
                  children: ($$payload5) => {
                    Table_row($$payload5, {
                      children: ($$payload6) => {
                        Table_head($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Event`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Table_head($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Email`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Table_head($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Template`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Table_head($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Timestamp`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!----> `;
                        Table_head($$payload6, {
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Details`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Table_body($$payload4, {
                  children: ($$payload5) => {
                    const each_array_3 = ensure_array_like(emailEvents);
                    $$payload5.out += `<!--[-->`;
                    for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                      let event = each_array_3[$$index_3];
                      Table_row($$payload5, {
                        children: ($$payload6) => {
                          Table_cell($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<div class="flex items-center"><span${attr_class(`rounded-full px-2 py-1 text-xs ${stringify(getEventTypeBadgeClass(event.type))} mr-2`)}>`;
                              if (event.type === "delivered") {
                                $$payload7.out += "<!--[-->";
                                Mail($$payload7, { class: "mr-1 inline-block h-3 w-3" });
                              } else if (event.type === "opened") {
                                $$payload7.out += "<!--[1-->";
                                Mail($$payload7, { class: "mr-1 inline-block h-3 w-3" });
                              } else if (event.type === "clicked") {
                                $$payload7.out += "<!--[2-->";
                                Mouse_pointer_click($$payload7, { class: "mr-1 inline-block h-3 w-3" });
                              } else if (event.type === "bounced" || event.type === "complained") {
                                $$payload7.out += "<!--[3-->";
                                Triangle_alert($$payload7, { class: "mr-1 inline-block h-3 w-3" });
                              } else if (event.type === "unsubscribed") {
                                $$payload7.out += "<!--[4-->";
                                User_x($$payload7, { class: "mr-1 inline-block h-3 w-3" });
                              } else {
                                $$payload7.out += "<!--[!-->";
                                Mail($$payload7, { class: "mr-1 inline-block h-3 w-3" });
                              }
                              $$payload7.out += `<!--]--> ${escape_html(event.type.charAt(0).toUpperCase() + event.type.slice(1))}</span></div>`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_cell($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->${escape_html(event.email)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_cell($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->${escape_html(event.templateName || "-")}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_cell($$payload6, {
                            children: ($$payload7) => {
                              $$payload7.out += `<!---->${escape_html(formatDate(event.timestamp))}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!----> `;
                          Table_cell($$payload6, {
                            children: ($$payload7) => {
                              if (event.data) {
                                $$payload7.out += "<!--[-->";
                                Button($$payload7, {
                                  variant: "ghost",
                                  size: "sm",
                                  class: "h-8 px-2",
                                  children: ($$payload8) => {
                                    $$payload8.out += `<!---->View Details`;
                                  },
                                  $$slots: { default: true }
                                });
                              } else {
                                $$payload7.out += "<!--[!-->";
                                $$payload7.out += `-`;
                              }
                              $$payload7.out += `<!--]-->`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!---->`;
                        },
                        $$slots: { default: true }
                      });
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            if (totalPages > 1) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="flex items-center justify-between"><div class="text-muted-foreground text-sm">Showing ${escape_html((currentPage - 1) * itemsPerPage + 1)} to ${escape_html(Math.min(currentPage * itemsPerPage, totalEvents))} of ${escape_html(totalEvents)} events</div> <div class="flex items-center space-x-2">`;
              Button($$payload3, {
                variant: "outline",
                size: "sm",
                disabled: currentPage === 1,
                onclick: () => goToPage(currentPage - 1),
                children: ($$payload4) => {
                  Chevron_left($$payload4, { class: "h-4 w-4" });
                  $$payload4.out += `<!----> <span class="sr-only">Previous Page</span>`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----> `;
              if (totalPages <= 5) {
                $$payload3.out += "<!--[-->";
                const each_array_4 = ensure_array_like(Array(totalPages));
                $$payload3.out += `<!--[-->`;
                for (let i = 0, $$length = each_array_4.length; i < $$length; i++) {
                  each_array_4[i];
                  Button($$payload3, {
                    variant: currentPage === i + 1 ? "default" : "outline",
                    size: "sm",
                    onclick: () => goToPage(i + 1),
                    children: ($$payload4) => {
                      $$payload4.out += `<!---->${escape_html(i + 1)}`;
                    },
                    $$slots: { default: true }
                  });
                }
                $$payload3.out += `<!--]-->`;
              } else {
                $$payload3.out += "<!--[!-->";
                const each_array_5 = ensure_array_like(Array(3));
                Button($$payload3, {
                  variant: currentPage === 1 ? "default" : "outline",
                  size: "sm",
                  onclick: () => goToPage(1),
                  children: ($$payload4) => {
                    $$payload4.out += `<!---->1`;
                  },
                  $$slots: { default: true }
                });
                $$payload3.out += `<!----> `;
                if (currentPage > 3) {
                  $$payload3.out += "<!--[-->";
                  $$payload3.out += `<span class="text-muted-foreground">...</span>`;
                } else {
                  $$payload3.out += "<!--[!-->";
                }
                $$payload3.out += `<!--]--> <!--[-->`;
                for (let i = 0, $$length = each_array_5.length; i < $$length; i++) {
                  each_array_5[i];
                  if (currentPage - 1 + i > 1 && currentPage - 1 + i < totalPages) {
                    $$payload3.out += "<!--[-->";
                    Button($$payload3, {
                      variant: currentPage === currentPage - 1 + i ? "default" : "outline",
                      size: "sm",
                      onclick: () => goToPage(currentPage - 1 + i),
                      children: ($$payload4) => {
                        $$payload4.out += `<!---->${escape_html(currentPage - 1 + i)}`;
                      },
                      $$slots: { default: true }
                    });
                  } else {
                    $$payload3.out += "<!--[!-->";
                  }
                  $$payload3.out += `<!--]-->`;
                }
                $$payload3.out += `<!--]--> `;
                if (currentPage < totalPages - 2) {
                  $$payload3.out += "<!--[-->";
                  $$payload3.out += `<span class="text-muted-foreground">...</span>`;
                } else {
                  $$payload3.out += "<!--[!-->";
                }
                $$payload3.out += `<!--]--> `;
                Button($$payload3, {
                  variant: currentPage === totalPages ? "default" : "outline",
                  size: "sm",
                  onclick: () => goToPage(totalPages),
                  children: ($$payload4) => {
                    $$payload4.out += `<!---->${escape_html(totalPages)}`;
                  },
                  $$slots: { default: true }
                });
                $$payload3.out += `<!---->`;
              }
              $$payload3.out += `<!--]--> `;
              Button($$payload3, {
                variant: "outline",
                size: "sm",
                disabled: currentPage === totalPages,
                onclick: () => goToPage(currentPage + 1),
                children: ($$payload4) => {
                  Chevron_right($$payload4, { class: "h-4 w-4" });
                  $$payload4.out += `<!----> <span class="sr-only">Next Page</span>`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!----></div></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--></div>`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  bind_props($$props, {
    dateRange,
    calendarDateRange,
    templateFilter,
    eventType,
    templates,
    eventTypeOptions,
    emailEvents,
    isLoading,
    currentPage,
    totalPages,
    totalEvents,
    itemsPerPage,
    handleCalendarDateChange,
    loadEmailEvents,
    goToPage,
    getEventTypeBadgeClass,
    formatDate
  });
  pop();
}
function _page($$payload, $$props) {
  push();
  let isLoading = true;
  let emailEvents = [];
  let emailStats = {
    total: 0,
    delivered: 0,
    opened: 0,
    clicked: 0,
    bounced: 0,
    complained: 0,
    unsubscribed: 0
  };
  let dateRange = {
    startDate: subDays(/* @__PURE__ */ new Date(), 7),
    endDate: /* @__PURE__ */ new Date()
  };
  const initialStartDate = subDays(/* @__PURE__ */ new Date(), 7);
  const initialEndDate = /* @__PURE__ */ new Date();
  let calendarDateRange = {
    start: new $35ea8db9cb2ccb90$export$99faa760c7908e4f(initialStartDate.getFullYear(), initialStartDate.getMonth() + 1, initialStartDate.getDate()),
    end: new $35ea8db9cb2ccb90$export$99faa760c7908e4f(initialEndDate.getFullYear(), initialEndDate.getMonth() + 1, initialEndDate.getDate())
  };
  let eventType = "all";
  let templateFilter = "all";
  let templates = [];
  let chartData = [];
  let topEmails = [];
  let isExporting = false;
  let currentPage = 1;
  let itemsPerPage = 100;
  let totalEvents = 0;
  let totalPages = 1;
  const eventTypeOptions = [
    { value: "all", label: "All Events" },
    { value: "delivered", label: "Delivered" },
    { value: "opened", label: "Opened" },
    { value: "clicked", label: "Clicked" },
    { value: "bounced", label: "Bounced" },
    { value: "complained", label: "Complained" },
    { value: "unsubscribed", label: "Unsubscribed" }
  ];
  async function loadEmailStats() {
    isLoading = true;
    try {
      const params = new URLSearchParams();
      if (dateRange.startDate && dateRange.endDate) {
        params.append("startDate", startOfDay(dateRange.startDate).toISOString());
        params.append("endDate", endOfDay(dateRange.endDate).toISOString());
      }
      if (templateFilter !== "all") ;
      const queryString = params.toString() ? `?${params.toString()}` : "";
      const response = await fetch(`/api/email/analytics/stats${queryString}`);
      if (response.ok) {
        const data = await response.json();
        emailStats = data.stats || {
          total: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
          bounced: 0,
          complained: 0,
          unsubscribed: 0
        };
        chartData = data.chartData || [];
        topEmails = data.topEmails?.map((template) => ({
          template: template.template,
          subject: template.subject || "No Subject",
          sent: template.sent || 0,
          openRate: template.openRate || 0,
          clickRate: template.clickRate || 0
        })) || [];
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to load email stats");
      }
    } catch (error) {
      console.error("Error loading email stats:", error);
      toast.error("Failed to load email stats");
    } finally {
      isLoading = false;
    }
  }
  async function loadEmailEvents() {
    isLoading = true;
    try {
      const params = new URLSearchParams();
      if (dateRange.startDate && dateRange.endDate) {
        params.append("startDate", startOfDay(dateRange.startDate).toISOString());
        params.append("endDate", endOfDay(dateRange.endDate).toISOString());
      }
      if (eventType !== "all") ;
      if (templateFilter !== "all") ;
      params.append("page", currentPage.toString());
      params.append("limit", itemsPerPage.toString());
      const countParams = new URLSearchParams(params);
      countParams.append("count", "true");
      const countResponse = await fetch(`/api/email/analytics/events?${countParams.toString()}`);
      if (countResponse.ok) {
        const countData = await countResponse.json();
        totalEvents = countData.count || 0;
        totalPages = Math.ceil(totalEvents / itemsPerPage) || 1;
      }
      const response = await fetch(`/api/email/analytics/events?${params.toString()}`);
      if (response.ok) {
        const events = await response.json();
        emailEvents = events.map((event) => ({
          id: event.id,
          email: event.email,
          templateName: event.templateName,
          type: event.type,
          timestamp: event.timestamp,
          data: event.data
        }));
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to load email events");
      }
    } catch (error) {
      console.error("Error loading email events:", error);
      toast.error("Failed to load email events");
    } finally {
      isLoading = false;
    }
  }
  function goToPage(page) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
      loadEmailEvents();
    }
  }
  function handleFilterChange() {
    loadEmailStats();
  }
  function handleCalendarDateChange(event) {
    const { startDate, endDate, calendarValue } = event.detail;
    if (startDate && endDate) {
      console.log("Date range changed:", { startDate, endDate });
      dateRange = { startDate, endDate };
      if (calendarValue) {
        calendarDateRange = calendarValue;
      }
      currentPage = 1;
      loadEmailStats();
      loadEmailEvents();
    }
  }
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
  }
  function getEventTypeBadgeClass(type) {
    switch (type) {
      case "delivered":
        return "bg-green-100 text-green-800";
      case "opened":
        return "bg-blue-100 text-blue-800";
      case "clicked":
        return "bg-purple-100 text-purple-800";
      case "bounced":
        return "bg-red-100 text-red-800";
      case "complained":
        return "bg-orange-100 text-orange-800";
      case "unsubscribed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }
  async function exportData() {
    isExporting = true;
    try {
      const params = new URLSearchParams();
      if (dateRange.startDate && dateRange.endDate) {
        params.append("startDate", startOfDay(dateRange.startDate).toISOString());
        params.append("endDate", endOfDay(dateRange.endDate).toISOString());
      }
      if (eventType !== "all") ;
      if (templateFilter !== "all") ;
      const response = await fetch(`/api/email/analytics/export?${params.toString()}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `email-analytics-${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        toast.success("Data exported successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to export data");
      }
    } catch (error) {
      console.error("Error exporting data:", error);
      toast.error("Failed to export data");
    } finally {
      isExporting = false;
    }
  }
  $$payload.out += `<!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Email Analytics`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Track and analyze email performance metrics.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Root($$payload3, {
            value: "overview",
            class: "w-full",
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Tabs_list($$payload4, {
                class: "mb-4",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Tabs_trigger($$payload5, {
                    value: "overview",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Overview`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Tabs_trigger($$payload5, {
                    value: "events",
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Event Log`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_content($$payload4, {
                value: "overview",
                children: ($$payload5) => {
                  OverviewTab($$payload5, {
                    dateRange,
                    calendarDateRange,
                    templateFilter,
                    templates,
                    emailStats,
                    chartData,
                    topEmails,
                    isLoading,
                    isExporting,
                    handleCalendarDateChange,
                    handleFilterChange,
                    loadEmailStats,
                    exportData
                  });
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Tabs_content($$payload4, {
                value: "events",
                children: ($$payload5) => {
                  EventsTab($$payload5, {
                    dateRange,
                    calendarDateRange,
                    templateFilter,
                    eventType,
                    templates,
                    eventTypeOptions,
                    emailEvents,
                    isLoading,
                    currentPage,
                    totalPages,
                    totalEvents,
                    itemsPerPage,
                    handleCalendarDateChange,
                    loadEmailEvents,
                    goToPage,
                    getEventTypeBadgeClass,
                    formatDate
                  });
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-VjBpIetU.js.map
