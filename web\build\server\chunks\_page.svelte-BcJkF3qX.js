import { p as push, V as copy_payload, W as assign_payload, q as pop, Q as bind_props, K as fallback, N as attr, O as escape_html, S as store_get, M as ensure_array_like, ab as store_mutate, R as spread_props, T as unsubscribe_stores, J as attr_class, Z as spread_attributes, _ as clsx, $ as attr_style, P as stringify } from './index3-CqUPEnZw.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import 'clsx';
import { j as zodClient } from './zod-DfpldWlD.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { j as jobApplicationSchema } from './71-DG0YyIU3.js';
import { B as Badge } from './badge-C9pSznab.js';
import { B as Button, b as buttonVariants } from './button-CrucCo1G.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { R as Root$3, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { R as Root$4, P as Popover_trigger, a as Popover_content } from './index14-C2WSwUih.js';
import { b as $fb18d541ea1ad717$export$ad991b66133851cf, c as $14e0f24ef4ac5c92$export$d0bdf45af03a6ea3, a as $14e0f24ef4ac5c92$export$aa8b41735afcabd2, S as Select_separator, R as Range_calendar, g as getDefaultDate, u as useCalendarRoot, C as Calendar_header$1, d as Calendar_prev_button$1, e as Calendar_heading$1, f as Calendar_next_button$1, h as Calendar_grid$1, i as Chevron_left, j as Calendar_grid_head$1, k as Calendar_grid_body$1, l as Calendar_grid_row$1, m as Calendar_head_cell$1, n as useCalendarCell, o as useCalendarDay } from './index16-Dse3U8B8.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { R as Root$2, D as Dialog_content, P as Portal$1, d as Dialog_overlay } from './index7-BURUpWjT.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { I as Input } from './input-DF0gPqYN.js';
import { T as Textarea } from './textarea-DnpYDER1.js';
import { M as Multi_combobox } from './multi-combobox-BJ-pW9qf.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { w as watch, b as box, m as mergeProps } from './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import { u as useId } from './use-id-CcFpwo20.js';
import { C as Chevron_right } from './chevron-right-C2rn-JeO.js';
import { n as noop } from './noop-n4I-x7yK.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { S as Select_group } from './select-group-Cxqg41Dj.js';
import { C as Calendar$1 } from './calendar-S3GMzTWi.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Checkbox } from './checkbox-Bu-4wGff.js';
import { B as Building } from './building-8WHBOPYC.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { S as Scroll_area } from './scroll-area-Dn69zlyp.js';
import { R as Root$5, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { E as Ellipsis } from './ellipsis-C7bKlkmn.js';
import { D as Dropdown_menu_label } from './dropdown-menu-label-rJ1q7A04.js';
import { D as Dropdown_menu_separator } from './dropdown-menu-separator-B5VQzuNH.js';
import { D as Dropdown_menu_item } from './dropdown-menu-item-DwivDmnZ.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { L as Layout_grid, a as List, C as Columns_2, A as Archive, T as Trophy, b as Circle_check, P as Phone, F as File_question } from './trophy-CAxwOMMc.js';
import { T as Trash_2 } from './trash-2-DdbKJ7eJ.js';
import { S as Send } from './send-BkGWvFu2.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { U as Users } from './users-e7-Uhkka.js';
import { C as Circle_alert } from './circle-alert-BcRZk-Zc.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { B as Bookmark } from './bookmark-DazMkrfp.js';
import { R as Root$1, P as Portal, S as Sheet_overlay, a as Sheet_content, b as Sheet_header, c as Sheet_title, d as Sheet_description } from './index10-F28UXWIO.js';
import { S as Slider } from './slider-D7_iPc_Q.js';
import { S as Separator } from './separator-5ooeI4XN.js';
import { X } from './x-DwZgpWRG.js';
import { S as Save } from './save-Cfytkt-w.js';
import { M as Message_square } from './message-square-D57Olt6y.js';
import { S as Square_pen } from './square-pen-DCE_ltl5.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { S as Search } from './search-B0oHlTPS.js';
import { E as Ellipsis_vertical } from './ellipsis-vertical-D1_2-qGm.js';
import { D as Download } from './download-CLn66Ope.js';
import { U as Upload } from './upload-C2KwXIf1.js';
import { o as objectType, s as stringType, n as numberType } from './types-D78SXuvm.js';
import './false-CRHihH2U.js';
import './index2-Cut0V_vU.js';
import './stores-DSLMNPqo.js';
import './index4-HpJcNJHQ.js';
import './index-server-CezSOnuG.js';
import './client-dNyMPa8V.js';
import './stringify-DWCARkQV.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './index-Ddp2AB5f.js';
import './prisma-Cit_HrSw.js';
import '@prisma/client';
import './auth-BPad-IlN.js';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index-DjwFQdT_.js';
import 'tailwind-merge';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './dialog-overlay-CspOQRJq.js';
import './dropdown-store-B4Dfz2ZI.js';
import './clone-BRGVxGEr.js';
import './check-WP_4Msti.js';
import './Icon-A4vzmk-O.js';
import './chevron-down-xGjWLrZH.js';
import 'date-fns';
import './dialog-description2-rfr-pd9k.js';
import './use-debounce.svelte-gxToHznJ.js';
import './use-grace-area.svelte-CrXiOQDy.js';

function Calendar($$payload, $$props) {
  push();
  let {
    child,
    children,
    id = useId(),
    ref = null,
    value = void 0,
    onValueChange = noop,
    placeholder = void 0,
    onPlaceholderChange = noop,
    weekdayFormat = "narrow",
    weekStartsOn,
    pagedNavigation = false,
    isDateDisabled = () => false,
    isDateUnavailable = () => false,
    fixedWeeks = false,
    numberOfMonths = 1,
    locale = "en",
    calendarLabel = "Event",
    disabled = false,
    readonly = false,
    minValue = void 0,
    maxValue = void 0,
    preventDeselect = false,
    type,
    disableDaysOutsideMonth = true,
    initialFocus = false,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const defaultPlaceholder = getDefaultDate({ defaultValue: value });
  function handleDefaultPlaceholder() {
    if (placeholder !== void 0) return;
    placeholder = defaultPlaceholder;
  }
  handleDefaultPlaceholder();
  watch.pre(() => placeholder, () => {
    handleDefaultPlaceholder();
  });
  function handleDefaultValue() {
    if (value !== void 0) return;
    value = type === "single" ? void 0 : [];
  }
  handleDefaultValue();
  watch.pre(() => value, () => {
    handleDefaultValue();
  });
  const rootState = useCalendarRoot({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    weekdayFormat: box.with(() => weekdayFormat),
    weekStartsOn: box.with(() => weekStartsOn),
    pagedNavigation: box.with(() => pagedNavigation),
    isDateDisabled: box.with(() => isDateDisabled),
    isDateUnavailable: box.with(() => isDateUnavailable),
    fixedWeeks: box.with(() => fixedWeeks),
    numberOfMonths: box.with(() => numberOfMonths),
    locale: box.with(() => locale),
    calendarLabel: box.with(() => calendarLabel),
    readonly: box.with(() => readonly),
    disabled: box.with(() => disabled),
    minValue: box.with(() => minValue),
    maxValue: box.with(() => maxValue),
    disableDaysOutsideMonth: box.with(() => disableDaysOutsideMonth),
    initialFocus: box.with(() => initialFocus),
    placeholder: box.with(() => placeholder, (v) => {
      placeholder = v;
      onPlaceholderChange(v);
    }),
    preventDeselect: box.with(() => preventDeselect),
    value: box.with(() => value, (v) => {
      value = v;
      onValueChange(v);
    }),
    type: box.with(() => type),
    defaultPlaceholder
  });
  const mergedProps = mergeProps(restProps, rootState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps, ...rootState.snippetProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload, rootState.snippetProps);
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref, value, placeholder });
  pop();
}
function Calendar_day$1($$payload, $$props) {
  push();
  let {
    children,
    child,
    ref = null,
    id = useId(),
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const dayState = useCalendarDay({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, dayState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps, ...dayState.snippetProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;
    if (children) {
      $$payload.out += "<!--[-->";
      children?.($$payload, dayState.snippetProps);
      $$payload.out += `<!---->`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `${escape_html(dayState.cell.opts.date.current.day)}`;
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Calendar_cell$1($$payload, $$props) {
  push();
  let {
    children,
    child,
    ref = null,
    id = useId(),
    date,
    month,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const cellState = useCalendarCell({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    date: box.with(() => date),
    month: box.with(() => month)
  });
  const mergedProps = mergeProps(restProps, cellState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps, ...cellState.snippetProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<td${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload, cellState.snippetProps);
    $$payload.out += `<!----></td>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Select_label($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out += `<div${spread_attributes(
    {
      "data-slot": "select-label",
      class: clsx(cn("text-muted-foreground px-2 py-1.5 text-xs", className)),
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></div>`;
  bind_props($$props, { ref });
  pop();
}
function Calendar_1($$payload, $$props) {
  push();
  let {
    ref = null,
    value = void 0,
    placeholder = void 0,
    class: className,
    weekdayFormat = "short",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    {
      let children = function($$payload3, { months, weekdays }) {
        $$payload3.out += `<!---->`;
        Calendar_header($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Calendar_prev_button($$payload4, {});
            $$payload4.out += `<!----> <!---->`;
            Calendar_heading($$payload4, {});
            $$payload4.out += `<!----> <!---->`;
            Calendar_next_button($$payload4, {});
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Calendar_months($$payload3, {
          children: ($$payload4) => {
            const each_array = ensure_array_like(months);
            $$payload4.out += `<!--[-->`;
            for (let $$index_3 = 0, $$length = each_array.length; $$index_3 < $$length; $$index_3++) {
              let month = each_array[$$index_3];
              $$payload4.out += `<!---->`;
              Calendar_grid($$payload4, {
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Calendar_grid_head($$payload5, {
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->`;
                      Calendar_grid_row($$payload6, {
                        class: "flex",
                        children: ($$payload7) => {
                          const each_array_1 = ensure_array_like(weekdays);
                          $$payload7.out += `<!--[-->`;
                          for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                            let weekday = each_array_1[$$index];
                            $$payload7.out += `<!---->`;
                            Calendar_head_cell($$payload7, {
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(weekday.slice(0, 2))}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          }
                          $$payload7.out += `<!--]-->`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Calendar_grid_body($$payload5, {
                    children: ($$payload6) => {
                      const each_array_2 = ensure_array_like(month.weeks);
                      $$payload6.out += `<!--[-->`;
                      for (let $$index_2 = 0, $$length2 = each_array_2.length; $$index_2 < $$length2; $$index_2++) {
                        let weekDates = each_array_2[$$index_2];
                        $$payload6.out += `<!---->`;
                        Calendar_grid_row($$payload6, {
                          class: "mt-2 w-full",
                          children: ($$payload7) => {
                            const each_array_3 = ensure_array_like(weekDates);
                            $$payload7.out += `<!--[-->`;
                            for (let $$index_1 = 0, $$length3 = each_array_3.length; $$index_1 < $$length3; $$index_1++) {
                              let date = each_array_3[$$index_1];
                              $$payload7.out += `<!---->`;
                              Calendar_cell($$payload7, {
                                date,
                                month: month.value,
                                children: ($$payload8) => {
                                  $$payload8.out += `<!---->`;
                                  Calendar_day($$payload8, {});
                                  $$payload8.out += `<!---->`;
                                },
                                $$slots: { default: true }
                              });
                              $$payload7.out += `<!---->`;
                            }
                            $$payload7.out += `<!--]-->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      };
      Calendar($$payload2, spread_props([
        {
          weekdayFormat,
          class: cn("p-3", className)
        },
        restProps,
        {
          get value() {
            return value;
          },
          set value($$value) {
            value = $$value;
            $$settled = false;
          },
          get ref() {
            return ref;
          },
          set ref($$value) {
            ref = $$value;
            $$settled = false;
          },
          get placeholder() {
            return placeholder;
          },
          set placeholder($$value) {
            placeholder = $$value;
            $$settled = false;
          },
          children,
          $$slots: { default: true }
        }
      ]));
    }
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref, value, placeholder });
  pop();
}
function Calendar_cell($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_cell$1($$payload2, spread_props([
      {
        class: cn("[&:has([data-selected])]:bg-accent [&:has([data-selected][data-outside-month])]:bg-accent/50 relative size-8 p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([data-selected])]:rounded-md", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_day($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_day$1($$payload2, spread_props([
      {
        class: cn(
          buttonVariants({ variant: "ghost" }),
          "size-8 select-none p-0 font-normal",
          "[&[data-today]:not([data-selected])]:bg-accent [&[data-today]:not([data-selected])]:text-accent-foreground",
          // Selected
          "data-selected:bg-primary data-selected:text-primary-foreground data-selected:hover:bg-primary data-selected:hover:text-primary-foreground data-selected:focus:bg-primary data-selected:focus:text-primary-foreground data-selected:opacity-100 dark:data-selected:hover:bg-primary dark:data-selected:focus:bg-primary",
          // Disabled
          "data-disabled:text-muted-foreground data-disabled:opacity-50",
          // Unavailable
          "data-unavailable:text-destructive-foreground data-unavailable:line-through",
          // Outside months
          "data-[outside-month]:text-muted-foreground [&[data-outside-month][data-selected]]:bg-accent/50 [&[data-outside-month][data-selected]]:text-muted-foreground data-[outside-month]:pointer-events-none data-[outside-month]:opacity-50 [&[data-outside-month][data-selected]]:opacity-30",
          className
        )
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_grid($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_grid$1($$payload2, spread_props([
      {
        class: cn("w-full border-collapse space-y-1", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_header($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_header$1($$payload2, spread_props([
      {
        class: cn("relative flex w-full items-center justify-between pt-1", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_months($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out += `<div${spread_attributes(
    {
      class: clsx(cn("mt-4 flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0", className)),
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></div>`;
  bind_props($$props, { ref });
  pop();
}
function Calendar_grid_row($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_grid_row$1($$payload2, spread_props([
      { class: cn("flex", className) },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_heading($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_heading$1($$payload2, spread_props([
      {
        class: cn("text-sm font-medium", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_grid_body($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_grid_body$1($$payload2, spread_props([
      { class: cn(className) },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_grid_head($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_grid_head$1($$payload2, spread_props([
      { class: cn(className) },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Calendar_head_cell($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_head_cell$1($$payload2, spread_props([
      {
        class: cn("text-muted-foreground w-8 rounded-md text-[0.8rem] font-normal", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Fallback$1($$payload) {
  Chevron_right($$payload, { class: "size-4" });
}
function Calendar_next_button($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_next_button$1($$payload2, spread_props([
      {
        class: cn(buttonVariants({ variant: "outline" }), "size-7 bg-transparent p-0 opacity-50 hover:opacity-100", className),
        children: children || Fallback$1
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Fallback($$payload) {
  Chevron_left($$payload, { class: "size-4" });
}
function Calendar_prev_button($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Calendar_prev_button$1($$payload2, spread_props([
      {
        class: cn(buttonVariants({ variant: "outline" }), "size-7 bg-transparent p-0 opacity-50 hover:opacity-100", className),
        children: children || Fallback
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function AddJobModal($$payload, $$props) {
  push();
  var $$store_subs;
  let {
    open = void 0,
    form,
    enhance,
    reset,
    errors,
    constraints,
    submitting,
    jobTypes,
    jobStatuses
  } = $$props;
  let locationOptions = [];
  let documentOptions = [];
  let occupationOptions = [];
  let companyOptions = [];
  let selectedLocation = [];
  let selectedPosition = [];
  let selectedCompany = [];
  const df = new $fb18d541ea1ad717$export$ad991b66133851cf("en-US", { dateStyle: "medium" });
  let dateValue = $14e0f24ef4ac5c92$export$d0bdf45af03a6ea3($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
  let calendarOpen = false;
  const getLocationOptions = () => locationOptions;
  const getOccupationOptions = () => occupationOptions;
  const getCompanyOptions = () => companyOptions;
  async function searchOccupations(query) {
    if (!query || query.length < 2) {
      return [...occupationOptions];
    }
    try {
      const response = await fetch(`/api/occupations?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const occupations = await response.json();
        const results = occupations.map((occupation) => ({
          value: occupation.title,
          label: occupation.title
        }));
        if (results.length === 0) {
          return [
            {
              value: query,
              label: `Add "${query}" as custom position`
            }
          ];
        }
        return results;
      }
    } catch (error) {
      console.error("Error searching occupations:", error);
    }
    return [
      {
        value: query,
        label: `Add "${query}" as custom position`
      }
    ];
  }
  async function searchCompanies(query) {
    if (!query || query.length < 2) {
      return [...companyOptions];
    }
    try {
      const response = await fetch(`/api/companies?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const companies = await response.json();
        const results = companies.map((company) => ({ value: company.name, label: company.name }));
        if (results.length === 0) {
          return [
            {
              value: query,
              label: `Add "${query}" as custom company`
            }
          ];
        }
        return results;
      }
    } catch (error) {
      console.error("Error searching companies:", error);
    }
    return [
      {
        value: query,
        label: `Add "${query}" as custom company`
      }
    ];
  }
  async function searchLocations(query) {
    if (!query || query.length < 2) {
      return [...locationOptions];
    }
    try {
      const response = await fetch(`/api/locations?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const locations = await response.json();
        return locations.map((loc) => ({
          value: `${loc.id}|${loc.name}|${loc.state?.code || ""}|${loc.country || "US"}`,
          label: `${loc.name}, ${loc.state?.code || loc.country || "US"}`
        }));
      }
    } catch (error) {
      console.error("Error searching locations:", error);
    }
    return [...locationOptions];
  }
  function handleLocationChange(values) {
    selectedLocation = values;
    if (values.length > 0) {
      const locationData = values[0].split("|");
      if (locationData.length >= 3) {
        store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).location = `${locationData[1]}, ${locationData[2]}`);
      } else {
        store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).location = values[0]);
      }
    } else {
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).location = "");
    }
  }
  function handlePositionChange(values) {
    selectedPosition = values;
    store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).position = values.join(", "));
  }
  function handleCompanyChange(values) {
    selectedCompany = values;
    store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).company = values.join(", "));
  }
  function handleDateChange(date) {
    if (date) {
      const jsDate = date.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).appliedDate = jsDate.toISOString().split("T")[0]);
    } else {
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).appliedDate = "");
    }
  }
  function handleModalClose() {
    reset();
    selectedLocation = [];
    selectedPosition = [];
    selectedCompany = [];
    dateValue = $14e0f24ef4ac5c92$export$d0bdf45af03a6ea3($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
    calendarOpen = false;
    open = false;
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Root$2($$payload2, {
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_content($$payload3, {
          class: "flex max-h-[90vh] flex-col overflow-hidden sm:max-w-[600px]",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Add New Job Application`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Enter the details of your new job application.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex-1 overflow-y-auto"><form method="POST" action="?/addJob"><div class="grid grid-cols-1 gap-4 py-4 md:grid-cols-2"><div class="space-y-2">`;
            Label($$payload4, {
              for: "company",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Company *`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Multi_combobox($$payload4, {
              options: getCompanyOptions(),
              selectedValues: selectedCompany,
              placeholder: "Search for companies...",
              searchPlaceholder: "Search companies...",
              emptyMessage: "No companies found",
              width: "w-full",
              maxDisplayItems: 1,
              searchOptions: searchCompanies,
              onSelectedValuesChange: handleCompanyChange
            });
            $$payload4.out += `<!----> <input type="hidden" name="company"${attr("value", store_get($$store_subs ??= {}, "$form", form).company)}/> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).company) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).company)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "position",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Position *`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Multi_combobox($$payload4, {
              options: getOccupationOptions(),
              selectedValues: selectedPosition,
              placeholder: "Search for positions...",
              searchPlaceholder: "Search occupations...",
              emptyMessage: "No positions found",
              width: "w-full",
              maxDisplayItems: 1,
              searchOptions: searchOccupations,
              onSelectedValuesChange: handlePositionChange
            });
            $$payload4.out += `<!----> <input type="hidden" name="position"${attr("value", store_get($$store_subs ??= {}, "$form", form).position)}/> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).position) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).position)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "location",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Location`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Multi_combobox($$payload4, {
              options: getLocationOptions(),
              selectedValues: selectedLocation,
              placeholder: "Search for cities...",
              searchPlaceholder: "Search locations...",
              emptyMessage: "No locations found",
              width: "w-full",
              maxDisplayItems: 1,
              searchOptions: searchLocations,
              onSelectedValuesChange: handleLocationChange
            });
            $$payload4.out += `<!----> <input type="hidden" name="location"${attr("value", store_get($$store_subs ??= {}, "$form", form).location)}/> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).location) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).location)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "jobType",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Job Type *`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: store_get($$store_subs ??= {}, "$form", form).jobType,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).jobType = value || "");
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  id: "jobType",
                  class: "w-full",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select job type" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array = ensure_array_like(jobTypes);
                        $$payload7.out += `<!--[-->`;
                        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                          let type = each_array[$$index];
                          $$payload7.out += `<!---->`;
                          Select_item($$payload7, {
                            value: type,
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->${escape_html(type)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!---->`;
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="jobType"${attr("value", store_get($$store_subs ??= {}, "$form", form).jobType)}/> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).jobType) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).jobType)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "appliedDate",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Applied Date *`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="appliedDate"${attr("value", store_get($$store_subs ??= {}, "$form", form).appliedDate)}/> <!---->`;
            Root$4($$payload4, {
              get open() {
                return calendarOpen;
              },
              set open($$value) {
                calendarOpen = $$value;
                $$settled = false;
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Popover_trigger($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      id: "appliedDate",
                      variant: "outline",
                      class: cn("w-full justify-start text-left font-normal", !dateValue && "text-muted-foreground"),
                      children: ($$payload7) => {
                        Calendar$1($$payload7, { class: "mr-2 h-4 w-4" });
                        $$payload7.out += `<!----> ${escape_html(dateValue ? df.format(dateValue.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2())) : "Select date")}`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Popover_content($$payload5, {
                  class: "w-auto p-0",
                  children: ($$payload6) => {
                    Calendar_1($$payload6, {
                      type: "single",
                      value: dateValue,
                      maxValue: $14e0f24ef4ac5c92$export$d0bdf45af03a6ea3($14e0f24ef4ac5c92$export$aa8b41735afcabd2()),
                      onValueChange: (v) => {
                        dateValue = v;
                        handleDateChange(v);
                        calendarOpen = false;
                      }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).appliedDate) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).appliedDate)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "status",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Status *`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: store_get($$store_subs ??= {}, "$form", form).status,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).status = value || "");
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  id: "status",
                  class: "w-full",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select status" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array_1 = ensure_array_like(jobStatuses);
                        $$payload7.out += `<!--[-->`;
                        for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                          let status = each_array_1[$$index_1];
                          $$payload7.out += `<!---->`;
                          Select_item($$payload7, {
                            value: status,
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->${escape_html(status)}`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!---->`;
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="status"${attr("value", store_get($$store_subs ??= {}, "$form", form).status)}/> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).status) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).status)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "resumeUploaded",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Resume *`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: store_get($$store_subs ??= {}, "$form", form).resumeUploaded,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).resumeUploaded = value || "");
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  id: "resumeUploaded",
                  class: "w-full",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select resume or status" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_group($$payload6, {
                      children: ($$payload7) => {
                        const each_array_2 = ensure_array_like(documentOptions);
                        $$payload7.out += `<!--[-->`;
                        for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                          let option = each_array_2[$$index_2];
                          if (option.value === "separator") {
                            $$payload7.out += "<!--[-->";
                            $$payload7.out += `<!---->`;
                            Select_separator($$payload7, {});
                            $$payload7.out += `<!----> <!---->`;
                            Select_label($$payload7, {
                              class: "text-muted-foreground px-2 py-1 text-xs",
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(option.label)}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          } else {
                            $$payload7.out += "<!--[!-->";
                            $$payload7.out += `<!---->`;
                            Select_item($$payload7, {
                              value: option.value,
                              children: ($$payload8) => {
                                $$payload8.out += `<!---->${escape_html(option.label)}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload7.out += `<!---->`;
                          }
                          $$payload7.out += `<!--]-->`;
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="resumeUploaded"${attr("value", store_get($$store_subs ??= {}, "$form", form).resumeUploaded)}/> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).resumeUploaded) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).resumeUploaded)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "url",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Job URL`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, spread_props([
              {
                id: "url",
                name: "url",
                placeholder: "https://example.com/job/123"
              },
              store_get($$store_subs ??= {}, "$constraints", constraints).url,
              {
                get value() {
                  return store_get($$store_subs ??= {}, "$form", form).url;
                },
                set value($$value) {
                  store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).url = $$value);
                  $$settled = false;
                }
              }
            ]));
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).url) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).url)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "nextAction",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Next Action`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, spread_props([
              {
                id: "nextAction",
                name: "nextAction",
                placeholder: "Follow up with recruiter"
              },
              store_get($$store_subs ??= {}, "$constraints", constraints).nextAction,
              {
                get value() {
                  return store_get($$store_subs ??= {}, "$form", form).nextAction;
                },
                set value($$value) {
                  store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).nextAction = $$value);
                  $$settled = false;
                }
              }
            ]));
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).nextAction) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).nextAction)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2 md:col-span-2">`;
            Label($$payload4, {
              for: "notes",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Notes`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, spread_props([
              {
                id: "notes",
                name: "notes",
                placeholder: "Any additional notes about this application"
              },
              store_get($$store_subs ??= {}, "$constraints", constraints).notes,
              {
                get value() {
                  return store_get($$store_subs ??= {}, "$form", form).notes;
                },
                set value($$value) {
                  store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).notes = $$value);
                  $$settled = false;
                }
              }
            ]));
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).notes) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-sm text-red-500">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).notes)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div></div> <div class="mt-4 flex justify-end space-x-2">`;
            Button($$payload4, {
              type: "button",
              variant: "outline",
              onclick: handleModalClose,
              children: ($$payload5) => {
                $$payload5.out += `<!---->Cancel`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Button($$payload4, {
              type: "submit",
              disabled: store_get($$store_subs ??= {}, "$submitting", submitting),
              class: "bg-primary text-primary-foreground hover:bg-primary/90",
              children: ($$payload5) => {
                if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                  $$payload5.out += "<!--[-->";
                  Loader_circle($$payload5, { class: "mr-2 h-4 w-4 animate-spin" });
                  $$payload5.out += `<!----> Saving...`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  Plus($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Add Application`;
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></form></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { open });
  pop();
}
objectType({
  id: numberType(),
  company: stringType(),
  position: stringType(),
  location: stringType(),
  appliedDate: stringType(),
  status: stringType(),
  nextAction: stringType().optional(),
  notes: stringType().optional(),
  logo: stringType().optional(),
  url: stringType().optional(),
  jobType: stringType(),
  resumeUploaded: stringType()
});
const statusColors = {
  Saved: "bg-gray-100 text-gray-800",
  Applied: "bg-blue-100 text-blue-800",
  "Phone Screen": "bg-cyan-100 text-cyan-800",
  Interview: "bg-purple-100 text-purple-800",
  Assessment: "bg-yellow-100 text-yellow-800",
  "Final Round": "bg-indigo-100 text-indigo-800",
  Offer: "bg-green-100 text-green-800",
  Accepted: "bg-emerald-100 text-emerald-800",
  Rejected: "bg-red-100 text-red-800"
};
const statusIcons = {
  Saved: Bookmark,
  Applied: Clock,
  "Phone Screen": Phone,
  Interview: Calendar$1,
  Assessment: Circle_alert,
  "Final Round": Users,
  Offer: Circle_check,
  Accepted: Trophy,
  Rejected: Circle_x
};
function KanbanCard($$payload, $$props) {
  push();
  let {
    application,
    openApplicationDetails,
    isSelected = false,
    onSelectionChange = () => {
    }
  } = $$props;
  const cardClass = `cursor-pointer p-3 hover:shadow-md transition-all ${isSelected ? "ring-2 ring-primary" : ""}`;
  function formatDate(dateStr) {
    if (!dateStr) return "N/A";
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "Invalid date";
    return new Intl.DateTimeFormat("en-US", { month: "short", day: "numeric" }).format(date);
  }
  Card($$payload, {
    class: cardClass,
    draggable: "true",
    ondragstart: (e) => {
      e.dataTransfer?.setData("text/plain", application.id.toString());
    },
    onclick: () => openApplicationDetails(application),
    children: ($$payload2) => {
      $$payload2.out += `<div class="mb-3 flex items-start justify-between">`;
      Checkbox($$payload2, {
        checked: isSelected,
        onCheckedChange: (checked) => onSelectionChange(!!checked),
        onclick: (e) => e.stopPropagation(),
        "aria-label": "Select application",
        class: "mt-0.5"
      });
      $$payload2.out += `<!----></div> <div class="mb-3 flex items-center gap-3"><div class="bg-muted h-10 w-10 flex-shrink-0 overflow-hidden rounded-lg"><img${attr("src", application.logo)}${attr("alt", application.company)} class="h-full w-full object-cover"/></div> <div class="min-w-0 flex-1"><h3 class="truncate text-sm font-semibold">${escape_html(application.position)}</h3> <div class="text-muted-foreground flex items-center text-xs">`;
      Building($$payload2, { class: "mr-1 h-3 w-3 flex-shrink-0" });
      $$payload2.out += `<!----> <span class="truncate">${escape_html(application.company)}</span></div></div></div> <div class="text-muted-foreground mb-3 flex items-center text-xs">`;
      Map_pin($$payload2, { class: "mr-1 h-3 w-3 flex-shrink-0" });
      $$payload2.out += `<!----> <span class="truncate">${escape_html(application.location)}</span></div> <div class="flex items-center justify-between"><div class="text-muted-foreground flex items-center text-xs">`;
      Calendar$1($$payload2, { class: "mr-1 h-3 w-3" });
      $$payload2.out += `<!----> <span>${escape_html(formatDate(application.appliedDate))}</span></div> `;
      if (application.priority || application.urgent) {
        $$payload2.out += "<!--[-->";
        Badge($$payload2, {
          variant: "outline",
          class: "text-xs",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(application.priority || "Urgent")}`;
          },
          $$slots: { default: true }
        });
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--></div> `;
      if (application.url) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="mt-2 border-t pt-2"><a${attr("href", application.url)} target="_blank" rel="noopener noreferrer" class="text-primary text-xs hover:underline">View Job Posting</a></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  pop();
}
function KanbanView($$payload, $$props) {
  push();
  let {
    columns,
    groupedApplications,
    openApplicationDetails,
    selectedItems = /* @__PURE__ */ new Set(),
    onSelectionChange = () => {
    },
    columnVisibility = {},
    onBulkMove = (_targetStatus, _selectedIds) => {
    }
  } = $$props;
  const columnsData = columns.filter((column) => columnVisibility[column.id] !== false).map((column) => ({
    ...column,
    items: [...groupedApplications[column.id] || []].map((item) => ({
      ...item,
      columnId: column.id
      // Add columnId to each item for tracking
    }))
  }));
  const allItems = columnsData.flatMap((column) => column.items);
  const allSelected = allItems.length > 0 && allItems.every((item) => selectedItems.has(item.id.toString()));
  const someSelected = allItems.some((item) => selectedItems.has(item.id.toString()));
  function handleSelectAll() {
    if (allSelected) {
      allItems.forEach((item) => {
        onSelectionChange(item.id.toString(), false);
      });
    } else {
      allItems.forEach((item) => {
        onSelectionChange(item.id.toString(), true);
      });
    }
  }
  function handleColumnSelectAll(column) {
    const columnItems = column.items;
    const allColumnSelected = columnItems.every((item) => selectedItems.has(item.id.toString()));
    if (allColumnSelected) {
      columnItems.forEach((item) => {
        onSelectionChange(item.id.toString(), false);
      });
    } else {
      columnItems.forEach((item) => {
        onSelectionChange(item.id.toString(), true);
      });
    }
  }
  function isColumnAllSelected(column) {
    return column.items.length > 0 && column.items.every((item) => selectedItems.has(item.id.toString()));
  }
  function isColumnSomeSelected(column) {
    return column.items.some((item) => selectedItems.has(item.id.toString()));
  }
  if (selectedItems.size > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="pb-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3">`;
    Checkbox($$payload, {
      checked: allSelected,
      indeterminate: someSelected && !allSelected,
      onCheckedChange: handleSelectAll,
      "aria-label": "Select all applications"
    });
    $$payload.out += `<!----> `;
    Badge($$payload, {
      variant: "secondary",
      class: "gradient-primary shadow-colored text-white",
      children: ($$payload2) => {
        $$payload2.out += `<!---->${escape_html(selectedItems.size)} selected`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div> <div class="flex items-center gap-2"><!---->`;
    Root$5($$payload, {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Dropdown_menu_trigger($$payload2, {
          children: ($$payload3) => {
            Button($$payload3, {
              variant: "outline",
              size: "sm",
              class: "hover-lift",
              children: ($$payload4) => {
                Ellipsis($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Actions`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Dropdown_menu_content($$payload2, {
          align: "end",
          children: ($$payload3) => {
            const each_array = ensure_array_like(columns);
            $$payload3.out += `<!---->`;
            Dropdown_menu_label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Move Selected To`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Dropdown_menu_separator($$payload3, {});
            $$payload3.out += `<!----> <!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let targetColumn = each_array[$$index];
              $$payload3.out += `<!---->`;
              Dropdown_menu_item($$payload3, {
                class: "interactive",
                onclick: () => {
                  const selectedIds = Array.from(selectedItems);
                  onBulkMove(targetColumn.id, selectedIds);
                },
                children: ($$payload4) => {
                  Arrow_right($$payload4, { class: "mr-2 h-4 w-4" });
                  $$payload4.out += `<!----> ${escape_html(targetColumn.name)}`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            }
            $$payload3.out += `<!--]--> <!---->`;
            Dropdown_menu_separator($$payload3, {});
            $$payload3.out += `<!----> <!---->`;
            Dropdown_menu_item($$payload3, {
              class: "interactive",
              children: ($$payload4) => {
                Archive($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Archive Selected`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Dropdown_menu_item($$payload3, {
              class: "text-destructive interactive",
              children: ($$payload4) => {
                Trash_2($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Delete Selected`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  Scroll_area($$payload, {
    orientation: "horizontal",
    class: "h-[calc(100vh-157px)] w-full",
    children: ($$payload2) => {
      const each_array_1 = ensure_array_like(columnsData);
      $$payload2.out += `<div class="flex h-full min-w-max gap-2 p-2"><!--[-->`;
      for (let index = 0, $$length = each_array_1.length; index < $$length; index++) {
        let column = each_array_1[index];
        $$payload2.out += `<div class="floating-card hover-lift animate-fade-in-up flex w-80 flex-col"${attr_style(`animation-delay: ${stringify(index * 100)}ms`)} role="region"${attr("aria-label", `Column ${column.name}`)}><div class="gradient-card border-b p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3">`;
        Checkbox($$payload2, {
          checked: isColumnAllSelected(column),
          indeterminate: isColumnSomeSelected(column) && !isColumnAllSelected(column),
          onCheckedChange: () => handleColumnSelectAll(column),
          "aria-label": `Select all in ${stringify(column.name)}`
        });
        $$payload2.out += `<!----> <h3 class="text-foreground text-sm font-semibold uppercase tracking-wide">${escape_html(column.name)}</h3></div> `;
        Badge($$payload2, {
          variant: "secondary",
          class: "gradient-accent shadow-soft text-xs",
          children: ($$payload3) => {
            $$payload3.out += `<!---->${escape_html(column.items.length)}`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div></div> <div class="flex-1 overflow-y-auto p-2">`;
        Scroll_area($$payload2, {
          orientation: "vertical",
          class: "h-full w-full",
          children: ($$payload3) => {
            const each_array_2 = ensure_array_like(column.items);
            $$payload3.out += `<section${attr("aria-label", `Drop zone for ${stringify(column.name)}`)} class="border-border/30 hover:border-primary/50 hover:bg-muted/20 h-full space-y-3 rounded-lg border-2 border-dashed p-2 transition-all duration-300"><!--[-->`;
            for (let itemIndex = 0, $$length2 = each_array_2.length; itemIndex < $$length2; itemIndex++) {
              let item = each_array_2[itemIndex];
              $$payload3.out += `<div class="animate-fade-in-up"${attr_style(`animation-delay: ${stringify(index * 100 + itemIndex * 50)}ms`)}>`;
              KanbanCard($$payload3, {
                application: item,
                openApplicationDetails,
                isSelected: selectedItems.has(item.id.toString()),
                onSelectionChange: (selected) => onSelectionChange(item.id.toString(), selected)
              });
              $$payload3.out += `<!----></div>`;
            }
            $$payload3.out += `<!--]--> `;
            if (column.items.length === 0) {
              $$payload3.out += "<!--[-->";
              $$payload3.out += `<div class="text-muted-foreground flex h-32 items-center justify-center align-middle"><div class="flex flex-col items-center justify-center gap-0 text-center">`;
              Send($$payload3, { class: "text-2xl opacity-50" });
              $$payload3.out += `<!----> <p class="mt-2 text-sm">No items yet</p></div></div>`;
            } else {
              $$payload3.out += "<!--[!-->";
            }
            $$payload3.out += `<!--]--></section>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div></div>`;
      }
      $$payload2.out += `<!--]--></div>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function JobCard($$payload, $$props) {
  push();
  let {
    application,
    isSelected = false,
    onSelectionChange = () => {
    }
  } = $$props;
  const cardClass = `cursor-pointer p-4 hover:shadow-md transition-all ${isSelected ? "ring-2 ring-primary" : ""}`;
  $$payload.out += `<div role="button" tabindex="0" draggable="true"${attr("aria-label", `${application.position} at ${application.company}`)} class="w-full text-left">`;
  Card($$payload, {
    class: cardClass,
    children: ($$payload2) => {
      $$payload2.out += `<div class="flex items-center gap-4"><div class="flex-shrink-0">`;
      Checkbox($$payload2, {
        checked: isSelected,
        onCheckedChange: (checked) => onSelectionChange(!!checked),
        onclick: (e) => e.stopPropagation(),
        "aria-label": "Select application"
      });
      $$payload2.out += `<!----></div> <div class="bg-muted h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg"><img${attr("src", application.logo)}${attr("alt", application.company)} class="h-full w-full object-cover"/></div> <div class="min-w-0 flex-1"><h3 class="truncate text-base font-semibold">${escape_html(application.position)}</h3> <div class="text-muted-foreground mt-1 flex items-center text-sm">`;
      Building($$payload2, { class: "mr-1 h-3 w-3 flex-shrink-0" });
      $$payload2.out += `<!----> <span class="truncate">${escape_html(application.company)}</span></div> <div class="text-muted-foreground flex items-center text-sm">`;
      Map_pin($$payload2, { class: "mr-1 h-3 w-3 flex-shrink-0" });
      $$payload2.out += `<!----> <span class="truncate">${escape_html(application.location)}</span></div></div> <div class="flex items-center gap-8 text-center"><div class="min-w-[80px]"><div class="text-muted-foreground text-xs">Applied</div> <div class="text-sm font-medium">${escape_html(application.appliedDate)}</div></div> <div class="min-w-[100px]"><div class="text-muted-foreground mb-1 text-xs">Status</div> `;
      Badge($$payload2, {
        class: `${statusColors[application.status] || "bg-gray-100 text-gray-800"} flex items-center gap-1`,
        children: ($$payload3) => {
          if (statusIcons[application.status]) {
            $$payload3.out += "<!--[-->";
            const IconComponent = statusIcons[application.status];
            $$payload3.out += `<!---->`;
            IconComponent($$payload3, { class: "h-3 w-3" });
            $$payload3.out += `<!---->`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> ${escape_html(application.status)}`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div> <div class="flex-shrink-0">`;
      Button($$payload2, {
        variant: "ghost",
        size: "icon",
        class: "h-8 w-8",
        children: ($$payload3) => {
          Ellipsis($$payload3, { class: "h-4 w-4" });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div></div></div>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  pop();
}
function ListView($$payload, $$props) {
  push();
  let {
    filteredApplications = [],
    openApplicationDetails,
    selectedItems = /* @__PURE__ */ new Set(),
    onSelectionChange = () => {
    },
    onBulkMove = (_targetStatus, _selectedIds) => {
    },
    columns = []
  } = $$props;
  const allSelected = filteredApplications.length > 0 && filteredApplications.every((item) => selectedItems.has(item.id.toString()));
  const someSelected = filteredApplications.some((item) => selectedItems.has(item.id.toString()));
  function handleSelectAll() {
    if (allSelected) {
      filteredApplications.forEach((item) => {
        onSelectionChange(item.id.toString(), false);
      });
    } else {
      filteredApplications.forEach((item) => {
        onSelectionChange(item.id.toString(), true);
      });
    }
  }
  if (selectedItems.size > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="bg-muted/50 border-b p-4"><div class="flex items-center justify-between"><div class="flex items-center gap-3">`;
    Checkbox($$payload, {
      checked: allSelected,
      indeterminate: someSelected && !allSelected,
      onCheckedChange: handleSelectAll,
      "aria-label": "Select all applications"
    });
    $$payload.out += `<!----> `;
    Badge($$payload, {
      variant: "secondary",
      children: ($$payload2) => {
        $$payload2.out += `<!---->${escape_html(selectedItems.size)} selected`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div> <div class="flex items-center gap-2"><!---->`;
    Root$5($$payload, {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        Dropdown_menu_trigger($$payload2, {
          children: ($$payload3) => {
            Button($$payload3, {
              variant: "outline",
              size: "sm",
              children: ($$payload4) => {
                Ellipsis($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Actions`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> <!---->`;
        Dropdown_menu_content($$payload2, {
          children: ($$payload3) => {
            const each_array = ensure_array_like(columns);
            $$payload3.out += `<!---->`;
            Dropdown_menu_label($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Move Selected To`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Dropdown_menu_separator($$payload3, {});
            $$payload3.out += `<!----> <!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let targetColumn = each_array[$$index];
              $$payload3.out += `<!---->`;
              Dropdown_menu_item($$payload3, {
                onclick: () => {
                  const selectedIds = Array.from(selectedItems);
                  onBulkMove(targetColumn.id, selectedIds);
                },
                children: ($$payload4) => {
                  Arrow_right($$payload4, { class: "mr-2 h-4 w-4" });
                  $$payload4.out += `<!----> ${escape_html(targetColumn.name)}`;
                },
                $$slots: { default: true }
              });
              $$payload3.out += `<!---->`;
            }
            $$payload3.out += `<!--]--> <!---->`;
            Dropdown_menu_separator($$payload3, {});
            $$payload3.out += `<!----> <!---->`;
            Dropdown_menu_item($$payload3, {
              children: ($$payload4) => {
                Archive($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Archive Selected`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Dropdown_menu_item($$payload3, {
              class: "text-destructive",
              children: ($$payload4) => {
                Trash_2($$payload4, { class: "mr-2 h-4 w-4" });
                $$payload4.out += `<!----> Delete Selected`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  Scroll_area($$payload, {
    class: "h-[600px] w-full",
    children: ($$payload2) => {
      $$payload2.out += `<div class="space-y-3 p-4">`;
      if (filteredApplications.length > 0) {
        $$payload2.out += "<!--[-->";
        const each_array_1 = ensure_array_like(filteredApplications);
        $$payload2.out += `<!--[-->`;
        for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
          let application = each_array_1[$$index_1];
          $$payload2.out += `<div>`;
          JobCard($$payload2, {
            application,
            isSelected: selectedItems.has(application.id.toString()),
            onSelectionChange: (selected) => onSelectionChange(application.id.toString(), selected)
          });
          $$payload2.out += `<!----></div>`;
        }
        $$payload2.out += `<!--]-->`;
      } else {
        $$payload2.out += "<!--[!-->";
        $$payload2.out += `<div class="flex h-32 items-center justify-center text-center"><div><p class="text-muted-foreground">No applications found</p> <p class="text-muted-foreground text-sm">Try adjusting your filters</p></div></div>`;
      }
      $$payload2.out += `<!--]--></div>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function ColumnVisibility($$payload, $$props) {
  push();
  let {
    tableModel = null,
    viewMode = "list",
    kanbanColumnVisibility = void 0
  } = $$props;
  const tableColumns = [
    { id: "company", label: "Company" },
    { id: "position", label: "Position" },
    { id: "status", label: "Status" },
    { id: "jobType", label: "Job Type" },
    { id: "location", label: "Location" },
    { id: "appliedDate", label: "Applied Date" },
    { id: "nextAction", label: "Next Action" }
  ];
  const kanbanColumns = [
    { id: "Saved", label: "Saved" },
    { id: "Applied", label: "Applied" },
    { id: "Phone Screen", label: "Phone Screen" },
    { id: "Interview", label: "Interview" },
    { id: "Assessment", label: "Assessment" },
    { id: "Final Round", label: "Final Round" },
    { id: "Offer", label: "Offer" },
    { id: "Accepted", label: "Accepted" },
    { id: "Rejected", label: "Rejected" }
  ];
  const columns = viewMode === "kanban" ? kanbanColumns : tableColumns;
  function toggleColumnVisibility(columnId) {
    if (viewMode === "kanban") {
      kanbanColumnVisibility[columnId] = !kanbanColumnVisibility[columnId];
      kanbanColumnVisibility = { ...kanbanColumnVisibility };
    } else if (tableModel) {
      const column = tableModel.getAllColumns?.()?.find((col) => col.id === columnId);
      if (column?.toggleVisibility) {
        column.toggleVisibility();
      }
    }
  }
  function isColumnVisible(columnId) {
    if (viewMode === "kanban") {
      return kanbanColumnVisibility[columnId] ?? true;
    } else if (tableModel) {
      const column = tableModel.getAllColumns?.()?.find((col) => col.id === columnId);
      if (column?.getIsVisible) {
        return column.getIsVisible();
      }
    }
    return true;
  }
  $$payload.out += `<!---->`;
  Root$5($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Dropdown_menu_trigger($$payload2, {
        children: ($$payload3) => {
          Button($$payload3, {
            variant: "outline",
            children: ($$payload4) => {
              Columns_2($$payload4, { class: "h-4 w-4" });
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Dropdown_menu_content($$payload2, {
        align: "end",
        class: "w-[150px]",
        children: ($$payload3) => {
          const each_array = ensure_array_like(columns);
          $$payload3.out += `<!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let column = each_array[$$index];
            $$payload3.out += `<!---->`;
            Dropdown_menu_item($$payload3, {
              onclick: () => toggleColumnVisibility(column.id),
              children: ($$payload4) => {
                $$payload4.out += `<div class="flex items-center"><div class="border-primary mr-2 flex h-4 w-4 items-center justify-center rounded border">`;
                if (isColumnVisible(column.id)) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="bg-primary h-2 w-2 rounded-sm"></div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></div> <span>${escape_html(column.label)}</span></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  bind_props($$props, { kanbanColumnVisibility });
  pop();
}
const interviewSchema = objectType({
  stageName: stringType().min(1, { message: "Interview stage is required" }),
  stageDate: stringType().min(1, { message: "Date is required" }),
  outcome: stringType().optional().nullable(),
  feedback: stringType().optional().nullable(),
  interviewers: stringType().optional().nullable(),
  duration: stringType().optional().nullable(),
  notes: stringType().optional().nullable(),
  nextAction: stringType().optional().nullable()
});
const interviewDefaultValues = {
  stageName: "",
  stageDate: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
  outcome: "",
  feedback: "",
  interviewers: "",
  duration: "",
  notes: "",
  nextAction: ""
};
const questionSchema = objectType({
  question: stringType().min(1, { message: "Question is required" }),
  category: stringType().min(1, { message: "Category is required" }),
  difficulty: stringType().optional().nullable(),
  userResponse: stringType().optional().nullable(),
  userConfidence: stringType().optional().nullable(),
  notes: stringType().optional().nullable()
});
const questionDefaultValues = {
  question: "",
  category: "",
  difficulty: "",
  userResponse: "",
  userConfidence: "",
  notes: ""
};
function AddInterviewModal($$payload, $$props) {
  push();
  var $$store_subs;
  let {
    applicationId,
    open = false,
    onClose,
    onSuccess
  } = $$props;
  const df = new $fb18d541ea1ad717$export$ad991b66133851cf("en-US", { dateStyle: "long" });
  const { form, errors, enhance, submitting } = superForm(interviewDefaultValues, {
    validators: zodClient(interviewSchema),
    dataType: "json",
    onSubmit: async () => {
      try {
        const jsDate = dateValue ? dateValue.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2()) : /* @__PURE__ */ new Date();
        const response = await fetch(`/api/applications/${applicationId}/interviews`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            stageName: store_get($$store_subs ??= {}, "$form", form).stageName,
            stageDate: jsDate,
            outcome: store_get($$store_subs ??= {}, "$form", form).outcome || null,
            feedback: store_get($$store_subs ??= {}, "$form", form).feedback || null,
            interviewers: store_get($$store_subs ??= {}, "$form", form).interviewers || null,
            duration: store_get($$store_subs ??= {}, "$form", form).duration ? parseInt(store_get($$store_subs ??= {}, "$form", form).duration, 10) : null,
            notes: store_get($$store_subs ??= {}, "$form", form).notes || null,
            nextAction: store_get($$store_subs ??= {}, "$form", form).nextAction || null
          })
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create interview");
        }
        toast.success("Interview added successfully");
        onSuccess();
        return;
      } catch (error) {
        console.error("Error creating interview:", error);
        toast.error(error.message || "Failed to create interview");
        return;
      }
    }
  });
  let dateValue = $14e0f24ef4ac5c92$export$d0bdf45af03a6ea3($14e0f24ef4ac5c92$export$aa8b41735afcabd2());
  function handleDateChange(date) {
    if (date) {
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).stageDate = date.toString());
    } else {
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).stageDate = "");
    }
  }
  const stageOptions = [
    { value: "Phone Screen", label: "Phone Screen" },
    {
      value: "Technical Interview",
      label: "Technical Interview"
    },
    {
      value: "Behavioral Interview",
      label: "Behavioral Interview"
    },
    {
      value: "Onsite Interview",
      label: "Onsite Interview"
    },
    {
      value: "Final Interview",
      label: "Final Interview"
    },
    { value: "HR Interview", label: "HR Interview" },
    { value: "Case Study", label: "Case Study" },
    {
      value: "Coding Challenge",
      label: "Coding Challenge"
    },
    {
      value: "Take-home Assignment",
      label: "Take-home Assignment"
    },
    { value: "Other", label: "Other" }
  ];
  const outcomeOptions = [
    { value: "Scheduled", label: "Scheduled" },
    { value: "Pending", label: "Pending" },
    { value: "Passed", label: "Passed" },
    { value: "Failed", label: "Failed" }
  ];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Root$2($$payload2, {
      open,
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> <!---->`;
        Dialog_content($$payload3, {
          class: "sm:max-w-[500px]",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Add Interview`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Record details about an interview stage for this application.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <form method="POST" class="space-y-4"><p class="text-muted-foreground mb-2 text-sm">Fields marked with * are required</p> `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--> <div class="space-y-2">`;
            Label($$payload4, {
              for: "stageName",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Interview Stage*`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).stageName;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).stageName = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  class: "h-10 w-full px-3 py-2",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select interview stage" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  class: "z-50 max-h-60 w-full overflow-y-auto",
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(stageOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let option = each_array[$$index];
                      $$payload6.out += `<!---->`;
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).stageName) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).stageName)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "stageDate",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Date*`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <input type="hidden" name="stageDate"${attr("value", store_get($$store_subs ??= {}, "$form", form).stageDate)}/> <!---->`;
            Root$4($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Popover_trigger($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      id: "stageDate",
                      variant: "outline",
                      class: cn("w-full justify-start text-left font-normal", !dateValue && "text-muted-foreground"),
                      children: ($$payload7) => {
                        Calendar$1($$payload7, { class: "mr-2 h-4 w-4" });
                        $$payload7.out += `<!----> ${escape_html(dateValue ? df.format(dateValue.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2())) : "Select date")}`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Popover_content($$payload5, {
                  class: "w-auto p-0",
                  children: ($$payload6) => {
                    Calendar_1($$payload6, {
                      type: "single",
                      value: dateValue,
                      onValueChange: (v) => {
                        dateValue = v;
                        handleDateChange(v);
                        setTimeout(
                          () => {
                            const trigger = document.getElementById("stageDate");
                            if (trigger) trigger.click();
                          },
                          100
                        );
                      },
                      initialFocus: true
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).stageDate) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).stageDate)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "outcome",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Outcome`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).outcome;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).outcome = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  class: "h-10 w-full px-3 py-2",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select outcome" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  class: "z-50 max-h-60 w-full overflow-y-auto",
                  children: ($$payload6) => {
                    const each_array_1 = ensure_array_like(outcomeOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                      let option = each_array_1[$$index_1];
                      $$payload6.out += `<!---->`;
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "interviewers",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Interviewers`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).interviewers;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).interviewers = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "duration",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Duration (minutes)`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              type: "number",
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).duration;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).duration = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "feedback",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Feedback`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).feedback;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).feedback = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "nextAction",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Next Action`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).nextAction;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).nextAction = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "notes",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Notes`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).notes;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).notes = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <!---->`;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: onClose,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "submit",
                  disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || !store_get($$store_subs ??= {}, "$form", form).stageName || store_get($$store_subs ??= {}, "$form", form).stageName.trim() === "",
                  class: !store_get($$store_subs ??= {}, "$form", form).stageName || store_get($$store_subs ??= {}, "$form", form).stageName.trim() === "" ? "cursor-not-allowed opacity-50" : "",
                  children: ($$payload6) => {
                    if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                      $$payload6.out += "<!--[-->";
                      Loader_circle($$payload6, { class: "mr-2 h-4 w-4 animate-spin" });
                      $$payload6.out += `<!----> Saving...`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `Save Interview`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function AddQuestionModal($$payload, $$props) {
  push();
  var $$store_subs;
  let {
    applicationId,
    interviewId,
    open = false,
    onClose,
    onSuccess
  } = $$props;
  const { form, errors, enhance, submitting } = superForm(questionDefaultValues, {
    validators: zodClient(questionSchema),
    dataType: "json",
    onSubmit: async () => {
      try {
        const response = await fetch(`/api/applications/${applicationId}/interviews/${interviewId}/questions`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            question: store_get($$store_subs ??= {}, "$form", form).question,
            category: store_get($$store_subs ??= {}, "$form", form).category,
            difficulty: store_get($$store_subs ??= {}, "$form", form).difficulty ? parseInt(store_get($$store_subs ??= {}, "$form", form).difficulty, 10) : null,
            userResponse: store_get($$store_subs ??= {}, "$form", form).userResponse || null,
            userConfidence: store_get($$store_subs ??= {}, "$form", form).userConfidence ? parseInt(store_get($$store_subs ??= {}, "$form", form).userConfidence, 10) : null,
            notes: store_get($$store_subs ??= {}, "$form", form).notes || null
          })
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to add question");
        }
        toast.success("Question added successfully");
        onSuccess();
        return;
      } catch (error) {
        console.error("Error adding question:", error);
        toast.error(error.message || "Failed to add question");
        return;
      }
    }
  });
  const categoryOptions = [
    { value: "Technical", label: "Technical" },
    { value: "Behavioral", label: "Behavioral" },
    {
      value: "Problem Solving",
      label: "Problem Solving"
    },
    {
      value: "System Design",
      label: "System Design"
    },
    { value: "Coding", label: "Coding" },
    { value: "Cultural Fit", label: "Cultural Fit" },
    { value: "Background", label: "Background" },
    { value: "Experience", label: "Experience" },
    { value: "Other", label: "Other" }
  ];
  const difficultyOptions = [
    { value: "1", label: "Very Easy" },
    { value: "2", label: "Easy" },
    { value: "3", label: "Medium" },
    { value: "4", label: "Hard" },
    { value: "5", label: "Very Hard" }
  ];
  const confidenceOptions = [
    { value: "1", label: "Not Confident" },
    { value: "2", label: "Slightly Confident" },
    { value: "3", label: "Moderately Confident" },
    { value: "4", label: "Confident" },
    { value: "5", label: "Very Confident" }
  ];
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Root$2($$payload2, {
      open,
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> <!---->`;
        Dialog_content($$payload3, {
          class: "sm:max-w-[500px]",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Add Interview Question`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Record a question asked during the interview.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <form method="POST" class="space-y-4"><p class="text-muted-foreground mb-2 text-sm">Fields marked with * are required</p> <div class="space-y-2">`;
            Label($$payload4, {
              for: "question",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Question*`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).question;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).question = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).question) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).question)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "category",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Category*`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).category;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).category = $$value);
                $$settled = false;
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  class: "h-10 w-full",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, { placeholder: "Select question category" });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  class: "z-50 max-h-60 w-full overflow-y-auto",
                  children: ($$payload6) => {
                    const each_array = ensure_array_like(categoryOptions);
                    $$payload6.out += `<!--[-->`;
                    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                      let option = each_array[$$index];
                      $$payload6.out += `<!---->`;
                      Select_item($$payload6, {
                        value: option.value,
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->${escape_html(option.label)}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!---->`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            if (store_get($$store_subs ??= {}, "$errors", errors).category) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<p class="text-destructive text-sm">${escape_html(store_get($$store_subs ??= {}, "$errors", errors).category)}</p>`;
            } else {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></div> <div class="space-y-2"><div class="flex items-center justify-between">`;
            Label($$payload4, {
              for: "difficulty",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Difficulty: ${escape_html(store_get($$store_subs ??= {}, "$form", form).difficulty ? difficultyOptions[parseInt(store_get($$store_subs ??= {}, "$form", form).difficulty) - 1]?.label : "Not set")}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <span class="text-muted-foreground text-sm">${escape_html(store_get($$store_subs ??= {}, "$form", form).difficulty || "0")}/5</span></div> `;
            Slider($$payload4, {
              value: store_get($$store_subs ??= {}, "$form", form).difficulty ? parseInt(store_get($$store_subs ??= {}, "$form", form).difficulty) : 1,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).difficulty = value.toString());
              },
              min: 1,
              max: 5,
              step: 1,
              class: "py-4"
            });
            $$payload4.out += `<!----> <div class="text-muted-foreground mt-1 flex justify-between text-xs"><span>Very Easy</span> <span>Very Hard</span></div></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "userResponse",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Your Response`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).userResponse;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).userResponse = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <div class="space-y-2"><div class="flex items-center justify-between">`;
            Label($$payload4, {
              for: "userConfidence",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Confidence: ${escape_html(store_get($$store_subs ??= {}, "$form", form).userConfidence ? confidenceOptions[parseInt(store_get($$store_subs ??= {}, "$form", form).userConfidence) - 1]?.label : "Not set")}`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <span class="text-muted-foreground text-sm">${escape_html(store_get($$store_subs ??= {}, "$form", form).userConfidence || "0")}/5</span></div> `;
            Slider($$payload4, {
              value: store_get($$store_subs ??= {}, "$form", form).userConfidence ? parseInt(store_get($$store_subs ??= {}, "$form", form).userConfidence) : 1,
              onValueChange: (value) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).userConfidence = value.toString());
              },
              min: 1,
              max: 5,
              step: 1,
              class: "py-4"
            });
            $$payload4.out += `<!----> <div class="text-muted-foreground mt-1 flex justify-between text-xs"><span>Not Confident</span> <span>Very Confident</span></div></div> <div class="space-y-2">`;
            Label($$payload4, {
              for: "notes",
              children: ($$payload5) => {
                $$payload5.out += `<!---->Notes`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            Textarea($$payload4, {
              get value() {
                return store_get($$store_subs ??= {}, "$form", form).notes;
              },
              set value($$value) {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).notes = $$value);
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <!---->`;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  type: "button",
                  variant: "outline",
                  onclick: onClose,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Button($$payload5, {
                  type: "submit",
                  disabled: store_get($$store_subs ??= {}, "$submitting", submitting) || true,
                  class: "cursor-not-allowed opacity-50",
                  children: ($$payload6) => {
                    if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                      $$payload6.out += "<!--[-->";
                      Loader_circle($$payload6, { class: "mr-2 h-4 w-4 animate-spin" });
                      $$payload6.out += `<!----> Saving...`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `Save Question`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></form>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function EditFieldModal($$payload, $$props) {
  push();
  let open = fallback($$props["open"], false);
  let title = fallback($$props["title"], "");
  let fieldValue = fallback($$props["fieldValue"], "");
  let fieldType = fallback($$props["fieldType"], "notes");
  let applicationId = fallback($$props["applicationId"], "");
  let onClose = fallback($$props["onClose"], () => {
  });
  let onSave = $$props["onSave"];
  let editedValue = "";
  async function handleSave() {
    try {
      const response = await fetch(`/api/applications/${applicationId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ [fieldType]: editedValue })
      });
      if (!response.ok) {
        throw new Error(`Failed to update ${fieldType}: ${response.statusText}`);
      }
      onSave(editedValue);
      open = false;
      toast.success(`${title} updated successfully`);
    } catch (err) {
      console.error(`Error updating ${fieldType}:`, err);
      toast.error(`Failed to update ${fieldType}`);
    }
  }
  function handleCancel() {
    open = false;
    onClose();
  }
  if (open) {
    editedValue = fieldValue || "";
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$2($$payload2, {
      onOpenChange: handleCancel,
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal$1($$payload3, {
          children: ($$payload4) => {
            Dialog_overlay($$payload4, {
              class: "bg-background/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 backdrop-blur-sm"
            });
            $$payload4.out += `<!----> `;
            Dialog_content($$payload4, {
              class: "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg md:w-full",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      class: "text-lg font-semibold",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(title)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      class: "text-muted-foreground text-sm",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Make changes to the ${escape_html(fieldType === "notes" ? "notes" : "next action")} for this application.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="grid gap-4 py-4"><div class="grid gap-2"><textarea class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[150px] w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1"${attr("placeholder", fieldType === "notes" ? "Add notes about this application..." : "What's the next step for this application?")}>`;
                const $$body = escape_html(editedValue);
                if ($$body) {
                  $$payload5.out += `${$$body}`;
                }
                $$payload5.out += `</textarea></div></div> `;
                Dialog_footer($$payload5, {
                  class: "flex items-center justify-end space-x-2",
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      onclick: handleCancel,
                      children: ($$payload7) => {
                        X($$payload7, { class: "mr-1.5 h-4 w-4" });
                        $$payload7.out += `<!----> Cancel`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Button($$payload6, {
                      variant: "default",
                      onclick: handleSave,
                      children: ($$payload7) => {
                        Save($$payload7, { class: "mr-1.5 h-4 w-4" });
                        $$payload7.out += `<!----> Save Changes`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, {
    open,
    title,
    fieldValue,
    fieldType,
    applicationId,
    onClose,
    onSave
  });
  pop();
}
function InterviewModal($$payload, $$props) {
  push();
  let open = fallback($$props["open"], false);
  let interview = $$props["interview"];
  let onClose = fallback($$props["onClose"], () => {
  });
  let onAddQuestion = fallback($$props["onAddQuestion"], (interviewId) => {
  });
  let showEditModal = false;
  let currentEditField = "";
  let currentEditTitle = "";
  let currentEditValue = "";
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  function getOutcomeBadge(outcome) {
    if (!outcome) return { variant: "outline", icon: null };
    switch (outcome.toLowerCase()) {
      case "passed":
      case "offer":
      case "accepted":
        return { variant: "success", icon: Circle_check_big };
      case "failed":
      case "rejected":
        return { variant: "destructive", icon: Circle_x };
      case "pending":
      case "scheduled":
      case "in progress":
        return { variant: "warning", icon: Circle_alert };
      default:
        return { variant: "outline", icon: null };
    }
  }
  function openNotesModal() {
    currentEditField = "notes";
    currentEditTitle = "Interview Notes";
    currentEditValue = interview.notes || "";
    showEditModal = true;
  }
  function openNextActionModal() {
    currentEditField = "nextAction";
    currentEditTitle = "Next Action";
    currentEditValue = interview.nextAction || "";
    showEditModal = true;
  }
  async function handleSave(value) {
    try {
      const response = await fetch(`/api/applications/${interview.applicationId}/interviews/${interview.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ [currentEditField]: value })
      });
      if (!response.ok) {
        throw new Error(`Failed to update ${currentEditField}: ${response.statusText}`);
      }
      interview[currentEditField] = value;
      toast.success(`${currentEditTitle} updated successfully`);
    } catch (err) {
      console.error(`Error updating ${currentEditField}:`, err);
      toast.error(`Failed to update ${currentEditField}`);
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$2($$payload2, {
      onOpenChange: onClose,
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal$1($$payload3, {
          children: ($$payload4) => {
            Dialog_overlay($$payload4, {
              class: "bg-background/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 backdrop-blur-sm"
            });
            $$payload4.out += `<!----> `;
            Dialog_content($$payload4, {
              class: "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid w-full max-w-3xl translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg md:w-full",
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<div class="flex items-center justify-between"><div class="flex items-center gap-3">`;
                    Dialog_title($$payload6, {
                      class: "text-xl font-semibold",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(interview.stageName)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    if (interview.outcome) {
                      $$payload6.out += "<!--[-->";
                      const badge = getOutcomeBadge(interview.outcome);
                      Badge($$payload6, {
                        variant: badge.variant,
                        class: "flex items-center gap-1.5 px-2.5 py-1",
                        children: ($$payload7) => {
                          if (badge.icon) {
                            $$payload7.out += "<!--[-->";
                            badge.icon($$payload7, { class: "h-3.5 w-3.5" });
                          } else {
                            $$payload7.out += "<!--[!-->";
                          }
                          $$payload7.out += `<!--]--> <span>${escape_html(interview.outcome)}</span>`;
                        },
                        $$slots: { default: true }
                      });
                    } else {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--></div> <div class="text-muted-foreground flex items-center gap-4 text-sm"><div class="flex items-center gap-1.5">`;
                    Calendar$1($$payload6, { class: "h-4 w-4" });
                    $$payload6.out += `<!----> <span>${escape_html(formatDate(interview.stageDate))}</span></div> `;
                    if (interview.duration) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="flex items-center gap-1.5">`;
                      Clock($$payload6, { class: "h-4 w-4" });
                      $$payload6.out += `<!----> <span>${escape_html(interview.duration)} min</span></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--></div></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <div class="grid gap-6 py-4"><div class="grid grid-cols-1 gap-4 md:grid-cols-2">`;
                if (interview.interviewers) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">`;
                  Users($$payload5, { class: "text-primary/70 mt-0.5 h-5 w-5" });
                  $$payload5.out += `<!----> <div class="flex-1 overflow-hidden"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Interviewers</p> <p class="mt-1.5 text-sm">${escape_html(interview.interviewers)}</p></div></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--> `;
                if (interview.feedback) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">`;
                  Message_square($$payload5, { class: "text-primary/70 mt-0.5 h-5 w-5" });
                  $$payload5.out += `<!----> <div class="flex-1 overflow-hidden"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Feedback</p> <p class="mt-1.5 text-sm">${escape_html(interview.feedback)}</p></div></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]--></div> <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">`;
                Arrow_right($$payload5, { class: "text-primary/70 mt-0.5 h-5 w-5" });
                $$payload5.out += `<!----> <div class="flex-1 overflow-hidden"><div class="flex items-center justify-between"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Next Action</p> `;
                Button($$payload5, {
                  variant: "ghost",
                  size: "sm",
                  class: "h-7 w-7 p-0",
                  onclick: openNextActionModal,
                  children: ($$payload6) => {
                    Square_pen($$payload6, { class: "h-3.5 w-3.5" });
                    $$payload6.out += `<!----> <span class="sr-only">Edit Next Action</span>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----></div> <p class="mt-1.5 text-sm">${escape_html(interview.nextAction || "No next action set.")}</p></div></div> <div class="bg-muted/30 flex items-start gap-3 rounded-md p-4 shadow-sm">`;
                Message_square($$payload5, { class: "text-primary/70 mt-0.5 h-5 w-5" });
                $$payload5.out += `<!----> <div class="flex-1 overflow-hidden"><div class="flex items-center justify-between"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Notes</p> `;
                Button($$payload5, {
                  variant: "ghost",
                  size: "sm",
                  class: "h-7 w-7 p-0",
                  onclick: openNotesModal,
                  children: ($$payload6) => {
                    Square_pen($$payload6, { class: "h-3.5 w-3.5" });
                    $$payload6.out += `<!----> <span class="sr-only">Edit Notes</span>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----></div> <p class="mt-1.5 text-sm">${escape_html(interview.notes || "No notes added yet.")}</p></div></div> `;
                Separator($$payload5, { class: "my-2" });
                $$payload5.out += `<!----> <div><div class="flex items-center justify-between"><h5 class="text-sm font-medium">Questions</h5> `;
                Button($$payload5, {
                  variant: "outline",
                  size: "sm",
                  onclick: () => onAddQuestion(interview.id),
                  class: "flex items-center gap-1.5 shadow-sm",
                  children: ($$payload6) => {
                    Plus($$payload6, { class: "h-3.5 w-3.5" });
                    $$payload6.out += `<!----> <span>Add Question</span>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----></div> `;
                if (!interview.questions?.length) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="bg-muted/30 mt-4 rounded-md p-4 text-center shadow-sm"><p class="text-muted-foreground text-sm">No questions recorded yet.</p></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                  const each_array = ensure_array_like(interview.questions);
                  $$payload5.out += `<div class="mt-4 space-y-4"><!--[-->`;
                  for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
                    let question = each_array[$$index_1];
                    $$payload5.out += `<div class="hover:border-primary/30 rounded-lg border p-4 shadow-sm transition-colors"><div class="flex items-start"><div class="flex-1 overflow-hidden"><h6 class="text-sm font-medium">${escape_html(question.question)}</h6> <div class="mt-2 flex flex-wrap items-center gap-2">`;
                    Badge($$payload5, {
                      variant: "outline",
                      class: "px-2 py-0.5",
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(question.category)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    if (question.difficulty) {
                      $$payload5.out += "<!--[-->";
                      Badge($$payload5, {
                        variant: "secondary",
                        class: "px-2 py-0.5",
                        children: ($$payload6) => {
                          $$payload6.out += `<!---->Difficulty: ${escape_html(question.difficulty)}/5`;
                        },
                        $$slots: { default: true }
                      });
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div></div></div> `;
                    if (question.userResponse) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="bg-muted/20 mt-4 rounded-md p-4"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Your Response</p> <p class="mt-1.5 text-sm">${escape_html(question.userResponse)}</p> `;
                      if (question.userConfidence) {
                        $$payload5.out += "<!--[-->";
                        const each_array_1 = ensure_array_like(Array(5));
                        $$payload5.out += `<div class="mt-3 flex items-center gap-2"><span class="text-muted-foreground text-xs font-medium">Confidence:</span> <div class="flex gap-0.5"><!--[-->`;
                        for (let i = 0, $$length2 = each_array_1.length; i < $$length2; i++) {
                          each_array_1[i];
                          $$payload5.out += `<div${attr_class(`h-2 w-5 rounded-sm ${i < question.userConfidence ? "bg-primary" : "bg-muted"}`)}></div>`;
                        }
                        $$payload5.out += `<!--]--></div></div>`;
                      } else {
                        $$payload5.out += "<!--[!-->";
                      }
                      $$payload5.out += `<!--]--></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> `;
                    if (question.notes) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="mt-3 p-3"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Notes</p> <p class="mt-1.5 text-sm">${escape_html(question.notes)}</p></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div>`;
                  }
                  $$payload5.out += `<!--]--></div>`;
                }
                $$payload5.out += `<!--]--></div></div> `;
                Dialog_footer($$payload5, {
                  class: "flex items-center justify-end",
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      onclick: onClose,
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Close`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    EditFieldModal($$payload2, {
      title: currentEditTitle,
      fieldValue: currentEditValue,
      fieldType: currentEditField,
      applicationId: interview?.applicationId,
      onSave: handleSave,
      get open() {
        return showEditModal;
      },
      set open($$value) {
        showEditModal = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { open, interview, onClose, onAddQuestion });
  pop();
}
function InterviewSection($$payload, $$props) {
  push();
  let { applicationId } = $$props;
  let interviewStages = [];
  let isLoading = true;
  let error = null;
  let showAddInterviewModal = false;
  let showAddQuestionModal = false;
  let showInterviewModal = false;
  let selectedInterviewId = null;
  let selectedInterview = null;
  async function fetchInterviewStages() {
    isLoading = true;
    error = null;
    try {
      console.log("Fetching interviews for application ID:", applicationId);
      const checkResponse = await fetch("/api/applications/check");
      console.log("Application check response:", checkResponse.ok ? "OK" : "Failed", checkResponse.status);
      if (checkResponse.ok) {
        const checkData = await checkResponse.json();
        console.log("Application check data:", checkData);
      }
      const response = await fetch(`/api/applications/${applicationId}/interviews`);
      console.log("Interview API response status:", response.status, response.statusText);
      if (!response.ok) {
        throw new Error(`Failed to fetch interview stages: ${response.statusText}`);
      }
      const data = await response.json();
      console.log("Interview data received:", data);
      interviewStages = data.interviewStages || [];
      if (interviewStages.length === 0) {
        console.log("No interview stages found for this application");
      }
    } catch (err) {
      console.error("Error fetching interview stages:", err);
      error = err.message;
      toast.error("Failed to load interview data");
    } finally {
      isLoading = false;
    }
  }
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  function getOutcomeBadge(outcome) {
    if (!outcome) return null;
    const outcomeMap = {
      Passed: { variant: "success", icon: Circle_check_big },
      Failed: { variant: "destructive", icon: Circle_x },
      Pending: { variant: "outline", icon: Clock },
      Scheduled: { variant: "secondary", icon: Calendar$1 }
    };
    return outcomeMap[outcome] || { variant: "outline", icon: Circle_alert };
  }
  function handleAddInterview() {
    showAddInterviewModal = true;
  }
  function handleAddQuestion(interviewId) {
    selectedInterviewId = interviewId;
    showAddQuestionModal = true;
  }
  function handleInterviewCreated() {
    fetchInterviewStages();
    showAddInterviewModal = false;
    toast.success("Interview added successfully");
  }
  function handleQuestionCreated() {
    fetchInterviewStages();
    showAddQuestionModal = false;
    toast.success("Question added successfully");
  }
  function handleAddQuestionFromModal(interviewId) {
    selectedInterviewId = interviewId;
    showAddQuestionModal = true;
    showInterviewModal = false;
  }
  function handleInterviewModalClose() {
    showInterviewModal = false;
    fetchInterviewStages();
  }
  $$payload.out += `<div class="mt-6 flex flex-col gap-5"><div class="flex flex-row items-center justify-between"><div class="flex flex-col"><h3 class="text-lg font-medium">Interviews</h3> <p class="text-muted-foreground mt-1 text-sm">Track your interview stages and questions</p></div> `;
  if (interviewStages.length > 0 || isLoading || error) {
    $$payload.out += "<!--[-->";
    Button($$payload, {
      variant: "outline",
      size: "sm",
      onclick: handleAddInterview,
      class: "flex items-center gap-2 shadow-sm hover:shadow",
      children: ($$payload2) => {
        Plus($$payload2, { class: "h-4 w-4" });
        $$payload2.out += `<!----> <span>Add Interview</span>`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  if (isLoading) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center py-12"><div class="border-primary h-10 w-10 animate-spin rounded-full border-b-2 border-t-2"></div> <p class="text-muted-foreground mt-4 text-sm">Loading interview data...</p></div>`;
  } else if (error) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div class="bg-destructive/10 text-destructive rounded-lg p-6 text-center shadow-sm">`;
    Circle_alert($$payload, { class: "mx-auto mb-2 h-8 w-8" });
    $$payload.out += `<!----> <p class="font-medium">${escape_html(error)}</p> <p class="text-muted-foreground mt-1 text-sm">There was a problem loading your interview data.</p> `;
    Button($$payload, {
      variant: "outline",
      size: "sm",
      class: "mt-4 shadow-sm",
      onclick: fetchInterviewStages,
      children: ($$payload2) => {
        $$payload2.out += `<!---->Try Again`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div>`;
  } else if (interviewStages.length === 0) {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<div class="bg-muted/20 rounded-lg border border-dashed p-10 text-center">`;
    File_question($$payload, {
      class: "text-muted-foreground mx-auto mb-3 h-10 w-10"
    });
    $$payload.out += `<!----> <h4 class="text-base font-medium">No interviews yet</h4> <p class="text-muted-foreground mx-auto mt-2 max-w-md">Track your interview process by adding interview stages and questions to keep a record of
        your progress.</p> `;
    Button($$payload, {
      variant: "outline",
      size: "sm",
      onclick: handleAddInterview,
      class: "mt-5 shadow-sm hover:shadow",
      children: ($$payload2) => {
        Plus($$payload2, { class: "mr-1.5 h-4 w-4" });
        $$payload2.out += `<!----> Add Your First Interview`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(interviewStages);
    $$payload.out += `<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let interview = each_array[$$index];
      $$payload.out += `<button type="button" class="hover:border-primary/30 w-full cursor-pointer rounded-lg border p-5 text-left shadow-sm transition-all hover:shadow-md"><div class="flex items-start justify-between"><div class="flex flex-col gap-2"><h3 class="text-lg font-semibold">${escape_html(interview.stageName)}</h3> <div class="text-muted-foreground flex items-center gap-1.5 text-sm">`;
      Calendar$1($$payload, { class: "h-4 w-4" });
      $$payload.out += `<!----> <span>${escape_html(formatDate(interview.stageDate))}</span></div></div> `;
      if (interview.outcome) {
        $$payload.out += "<!--[-->";
        const badge = getOutcomeBadge(interview.outcome);
        Badge($$payload, {
          variant: badge.variant,
          class: "flex items-center gap-1.5 px-2.5 py-1",
          children: ($$payload2) => {
            if (badge.icon) {
              $$payload2.out += "<!--[-->";
              $$payload2.out += `<!---->`;
              badge.icon($$payload2, { class: "h-3.5 w-3.5" });
              $$payload2.out += `<!---->`;
            } else {
              $$payload2.out += "<!--[!-->";
            }
            $$payload2.out += `<!--]--> <span>${escape_html(interview.outcome)}</span>`;
          },
          $$slots: { default: true }
        });
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> `;
      if (interview.nextAction) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="mt-4 border-t pt-3"><p class="text-muted-foreground text-xs font-medium uppercase tracking-wide">Next Action</p> <p class="mt-1 line-clamp-2 text-sm">${escape_html(interview.nextAction)}</p></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="mt-4 flex items-center justify-between">`;
      if (interview.questions?.length) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="text-muted-foreground flex items-center gap-1.5 text-sm">`;
        File_question($$payload, { class: "h-4 w-4" });
        $$payload.out += `<!----> <span>${escape_html(interview.questions.length)}
                  ${escape_html(interview.questions.length === 1 ? "question" : "questions")}</span></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<div class="text-muted-foreground text-sm">No questions</div>`;
      }
      $$payload.out += `<!--]--> `;
      Button($$payload, {
        variant: "ghost",
        size: "sm",
        class: "h-7 px-2",
        onclick: (e) => {
          e.stopPropagation();
          handleAddQuestion(interview.id);
        },
        children: ($$payload2) => {
          Plus($$payload2, { class: "mr-1 h-3.5 w-3.5" });
          $$payload2.out += `<!----> Add Question`;
        },
        $$slots: { default: true }
      });
      $$payload.out += `<!----></div></button>`;
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></div> `;
  AddInterviewModal($$payload, {
    applicationId,
    open: showAddInterviewModal,
    onClose: () => showAddInterviewModal = false,
    onSuccess: handleInterviewCreated
  });
  $$payload.out += `<!----> `;
  AddQuestionModal($$payload, {
    applicationId,
    interviewId: selectedInterviewId,
    open: showAddQuestionModal,
    onClose: () => showAddQuestionModal = false,
    onSuccess: handleQuestionCreated
  });
  $$payload.out += `<!----> `;
  InterviewModal($$payload, {
    open: showInterviewModal,
    interview: selectedInterview,
    onClose: handleInterviewModalClose,
    onAddQuestion: handleAddQuestionFromModal
  });
  $$payload.out += `<!---->`;
  pop();
}
function ApplicationDetailsSheet($$payload, $$props) {
  push();
  let sheetOpen = $$props["sheetOpen"];
  let selectedApplication = $$props["selectedApplication"];
  let statusColors2 = $$props["statusColors"];
  let showEditModal = false;
  let currentEditField = "";
  let currentEditTitle = "";
  let currentEditValue = "";
  function openNotesModal() {
    currentEditField = "notes";
    currentEditTitle = "Notes";
    currentEditValue = selectedApplication.notes || "";
    showEditModal = true;
  }
  function openNextActionModal() {
    currentEditField = "nextAction";
    currentEditTitle = "Next Action";
    currentEditValue = selectedApplication.nextAction || "";
    showEditModal = true;
  }
  function handleSave(value) {
    if (currentEditField === "notes") {
      selectedApplication.notes = value;
    } else if (currentEditField === "nextAction") {
      selectedApplication.nextAction = value;
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      get open() {
        return sheetOpen;
      },
      set open($$value) {
        sheetOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Portal($$payload3, {
          children: ($$payload4) => {
            Sheet_overlay($$payload4, {});
            $$payload4.out += `<!----> `;
            Sheet_content($$payload4, {
              side: "right",
              class: "bg-background text-foreground overflow-y-auto p-6 sm:max-w-lg",
              children: ($$payload5) => {
                Sheet_header($$payload5, {
                  children: ($$payload6) => {
                    Sheet_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(selectedApplication?.position || "Job Details")}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Sheet_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(selectedApplication?.company || "")}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                if (selectedApplication) {
                  $$payload5.out += "<!--[-->";
                  $$payload5.out += `<div class="mt-6 space-y-6"><div class="flex items-center gap-4"><div class="bg-muted h-16 w-16 overflow-hidden rounded-full"><img${attr("src", selectedApplication.logo)}${attr("alt", selectedApplication.company)} class="h-full w-full object-cover"/></div> <div><h3 class="text-xl font-semibold">${escape_html(selectedApplication.position)}</h3> <div class="text-muted-foreground flex items-center">`;
                  Building($$payload5, { class: "mr-1 h-4 w-4" });
                  $$payload5.out += `<!----> <span>${escape_html(selectedApplication.company)}</span></div> <div class="text-muted-foreground flex items-center">`;
                  Map_pin($$payload5, { class: "mr-1 h-4 w-4" });
                  $$payload5.out += `<!----> <span>${escape_html(selectedApplication.location)}</span></div></div></div> <div class="grid grid-cols-2 gap-4"><div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Status</h4> <div${attr_class(`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${statusColors2[selectedApplication.status]}`)}>${escape_html(selectedApplication.status)}</div></div> <div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Applied Date</h4> <div class="flex items-center">`;
                  Calendar$1($$payload5, { class: "mr-1 h-3 w-3" });
                  $$payload5.out += `<!----> <span>${escape_html(selectedApplication.appliedDate)}</span></div></div> <div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Job Type</h4> <p>${escape_html(selectedApplication.jobType || "Not specified")}</p></div> <div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Resume Uploaded</h4> <p>${escape_html(selectedApplication.resumeUploaded || "No")}</p></div></div> <div class="bg-muted/10 rounded-md border p-4"><div class="mb-2 flex items-center justify-between"><h4 class="text-sm font-medium">Next Action</h4> `;
                  Button($$payload5, {
                    variant: "ghost",
                    size: "sm",
                    class: "h-7 w-7 p-0",
                    onclick: () => openNextActionModal(),
                    children: ($$payload6) => {
                      Square_pen($$payload6, { class: "h-3.5 w-3.5" });
                      $$payload6.out += `<!----> <span class="sr-only">Edit Next Action</span>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></div> <p class="text-sm">${escape_html(selectedApplication.nextAction || "None")}</p></div> <div class="bg-muted/10 rounded-md border p-4"><div class="mb-2 flex items-center justify-between"><h4 class="text-sm font-medium">Notes</h4> `;
                  Button($$payload5, {
                    variant: "ghost",
                    size: "sm",
                    class: "h-7 w-7 p-0",
                    onclick: () => openNotesModal(),
                    children: ($$payload6) => {
                      Square_pen($$payload6, { class: "h-3.5 w-3.5" });
                      $$payload6.out += `<!----> <span class="sr-only">Edit Notes</span>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></div> <p class="whitespace-pre-line text-sm">${escape_html(selectedApplication.notes || "No notes")}</p></div> `;
                  if (selectedApplication.url) {
                    $$payload5.out += "<!--[-->";
                    $$payload5.out += `<div><h4 class="text-muted-foreground mb-1 text-sm font-medium">Job Posting</h4> <a${attr("href", selectedApplication.url)} target="_blank" rel="noopener noreferrer" class="text-primary text-sm hover:underline">View Original</a></div>`;
                  } else {
                    $$payload5.out += "<!--[!-->";
                  }
                  $$payload5.out += `<!--]--> `;
                  if (selectedApplication.id) {
                    $$payload5.out += "<!--[-->";
                    InterviewSection($$payload5, { applicationId: selectedApplication.id });
                  } else {
                    $$payload5.out += "<!--[!-->";
                  }
                  $$payload5.out += `<!--]--></div>`;
                } else {
                  $$payload5.out += "<!--[!-->";
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    EditFieldModal($$payload2, {
      title: currentEditTitle,
      fieldValue: currentEditValue,
      fieldType: currentEditField,
      applicationId: selectedApplication?.id,
      onSave: handleSave,
      get open() {
        return showEditModal;
      },
      set open($$value) {
        showEditModal = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { sheetOpen, selectedApplication, statusColors: statusColors2 });
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let { data } = $$props;
  const {
    form,
    enhance,
    reset,
    errors,
    constraints,
    submitting
  } = superForm(data.form, {
    validators: zodClient(jobApplicationSchema),
    dataType: "json",
    resetForm: true,
    onResult: ({ result }) => {
      if (result.type === "success") {
        const newApplication = {
          id: result.data?.application?.id || crypto.randomUUID(),
          company: store_get($$store_subs ??= {}, "$form", form).company || "",
          position: store_get($$store_subs ??= {}, "$form", form).position || "",
          location: store_get($$store_subs ??= {}, "$form", form).location || "Remote",
          appliedDate: store_get($$store_subs ??= {}, "$form", form).appliedDate || "",
          status: store_get($$store_subs ??= {}, "$form", form).status || "Applied",
          nextAction: store_get($$store_subs ??= {}, "$form", form).nextAction || "",
          notes: store_get($$store_subs ??= {}, "$form", form).notes || "",
          logo: "https://placehold.co/100x100",
          url: store_get($$store_subs ??= {}, "$form", form).url || "",
          jobType: store_get($$store_subs ??= {}, "$form", form).jobType || "Full-time",
          resumeUploaded: store_get($$store_subs ??= {}, "$form", form).resumeUploaded || "No"
        };
        applications = [...applications, newApplication];
        newJobModalOpen = false;
        toast.success("New job application added successfully!");
      } else if (result.type === "failure") {
        toast.error("Failed to add job application. Please try again.");
      }
    }
  });
  console.log(data);
  let sheetOpen = false;
  let selectedApplication = null;
  let newJobModalOpen = false;
  const jobTypes = [
    "Full-time",
    "Part-time",
    "Contract",
    "Freelance",
    "Internship"
  ];
  const jobStatuses = [
    "Applied",
    "Interview",
    "Assessment",
    "Offer",
    "Rejected",
    "Phone Screen"
  ];
  function openApplicationDetails(application) {
    selectedApplication = application;
    sheetOpen = true;
  }
  let applications = data.applications || [];
  let activeView = true;
  let viewMode = "kanban";
  let selectedKanbanItems = /* @__PURE__ */ new Set();
  let currentFilters = {
    appliedFromDate: "",
    appliedUntilDate: "",
    jobType: "",
    status: "",
    searchTerm: ""
  };
  let selectedJobType = "";
  let selectedStatus = "";
  let dateRange = void 0;
  let dateRangeOpen = false;
  const df = new $fb18d541ea1ad717$export$ad991b66133851cf("en-US", { dateStyle: "medium" });
  let tableModel = null;
  let kanbanColumnVisibility = {
    Saved: true,
    Applied: true,
    "Phone Screen": true,
    Interview: true,
    Assessment: true,
    "Final Round": true,
    Offer: true,
    Accepted: true,
    Rejected: true
  };
  const columns = [
    { id: "Saved", name: "Saved" },
    { id: "Applied", name: "Applied" },
    { id: "Phone Screen", name: "Phone Screen" },
    { id: "Interview", name: "Interview" },
    { id: "Assessment", name: "Assessment" },
    { id: "Final Round", name: "Final Round" },
    { id: "Offer", name: "Offer" },
    { id: "Accepted", name: "Accepted" },
    { id: "Rejected", name: "Rejected" }
  ];
  let filteredApplications = () => applications.filter((app) => {
    const isArchived = app.status === "Accepted";
    const matchesView = activeView ? !isArchived : isArchived;
    const matchesSearch = currentFilters.searchTerm === "" || app.position.toLowerCase().includes(currentFilters.searchTerm.toLowerCase()) || app.company.toLowerCase().includes(currentFilters.searchTerm.toLowerCase());
    const matchesJobType = currentFilters.jobType === "";
    const matchesStatus = currentFilters.status === "";
    const matchesFromDate = currentFilters.appliedFromDate === "";
    const matchesToDate = currentFilters.appliedUntilDate === "";
    return matchesView && matchesSearch && matchesJobType && matchesStatus && matchesFromDate && matchesToDate;
  });
  let groupedApplications = () => columns.reduce(
    (acc, column) => {
      acc[column.id] = filteredApplications().filter((app) => app.status === column.id);
      return acc;
    },
    {}
  );
  function handleBulkMove(targetStatus, selectedIds) {
    if (selectedIds.length === 0) return;
    applications = applications.map((app) => {
      if (selectedIds.includes(app.id.toString())) {
        return { ...app, status: targetStatus };
      }
      return app;
    });
    groupedApplications = columns.reduce(
      (acc, column) => {
        acc[column.id] = applications.filter((app) => app.status === column.id);
        return acc;
      },
      {}
    );
    selectedKanbanItems.clear();
    selectedKanbanItems = /* @__PURE__ */ new Set();
    toast.success(`Moved ${selectedIds.length} applications to ${targetStatus}`);
  }
  function handleKanbanSelectionChange(itemId, selected) {
    if (selected) {
      selectedKanbanItems.add(itemId);
    } else {
      selectedKanbanItems.delete(itemId);
    }
    selectedKanbanItems = new Set(selectedKanbanItems);
  }
  let isExporting = false;
  async function exportCSV() {
    if (isExporting) return;
    isExporting = true;
    try {
      const headers = [
        "Company",
        "Position",
        "Location",
        "Applied Date",
        "Status",
        "Job Type",
        "Next Action"
      ];
      const csvContent = [
        headers.join(","),
        ...filteredApplications().map((app) => [
          `"${app.company?.replace(/"/g, '""') || ""}"`,
          `"${app.position?.replace(/"/g, '""') || ""}"`,
          `"${app.location?.replace(/"/g, '""') || ""}"`,
          `"${app.appliedDate?.replace(/"/g, '""') || ""}"`,
          `"${app.status?.replace(/"/g, '""') || ""}"`,
          `"${app.jobType?.replace(/"/g, '""') || ""}"`,
          `"${app.nextAction?.replace(/"/g, '""') || ""}"`
        ].join(","))
      ].join("\n");
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "job_applications.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("CSV exported successfully!");
    } catch (error) {
      toast.error("Failed to export CSV");
    } finally {
      setTimeout(
        () => {
          isExporting = false;
        },
        500
      );
    }
  }
  let isImporting = false;
  function handleImportCSV() {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".csv";
    input.onchange = (e) => {
      const file = e.target?.files?.[0];
      if (file) {
        isImporting = true;
        setTimeout(
          () => {
            isImporting = false;
            toast.success("CSV imported successfully!");
          },
          1e3
        );
      }
    };
    input.click();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Job Tracker | Hirli",
      description: "Track your job applications in one place. Organize, monitor, and optimize your entire job search process with our intuitive job tracker.",
      keywords: "job tracker, job applications, job search, application tracking, job status management, application organization"
    });
    $$payload2.out += `<!----> <!---->`;
    Root($$payload2, {
      value: activeView ? "active" : "archived",
      onValueChange: (value) => activeView = value === "active",
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Tabs_list($$payload3, {
          class: "border-t-0",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Tabs_trigger($$payload4, {
              value: "active",
              class: "flex-1 gap-2",
              children: ($$payload5) => {
                Briefcase($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Active Applications</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "archived",
              class: "flex-1 gap-2",
              children: ($$payload5) => {
                Circle_check_big($$payload5, { class: "h-4 w-4" });
                $$payload5.out += `<!----> <span>Archived</span>`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "active",
          class: "h-[calc(100vh-105px)] space-y-0",
          children: ($$payload4) => {
            $$payload4.out += `<div class="border-border space-y-4 border-b p-2"><div class="flex items-center justify-between"><div class="flex items-center gap-2"><div class="relative">`;
            Search($$payload4, {
              class: "text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              type: "text",
              placeholder: "Search for roles or companies...",
              class: "w-64 pl-9",
              get value() {
                return currentFilters.searchTerm;
              },
              set value($$value) {
                currentFilters.searchTerm = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div> <!---->`;
            Root$4($$payload4, {
              get open() {
                return dateRangeOpen;
              },
              set open($$value) {
                dateRangeOpen = $$value;
                $$settled = false;
              },
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Popover_trigger($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      class: " justify-start text-left font-normal",
                      children: ($$payload7) => {
                        Calendar$1($$payload7, { class: "h-4 w-4" });
                        $$payload7.out += `<!----> `;
                        if (dateRange && dateRange.start) {
                          $$payload7.out += "<!--[-->";
                          if (dateRange.end) {
                            $$payload7.out += "<!--[-->";
                            $$payload7.out += `${escape_html(df.format(dateRange.start.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2())))} - ${escape_html(df.format(dateRange.end.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2())))}`;
                          } else {
                            $$payload7.out += "<!--[!-->";
                            $$payload7.out += `${escape_html(df.format(dateRange.start.toDate($14e0f24ef4ac5c92$export$aa8b41735afcabd2())))}`;
                          }
                          $$payload7.out += `<!--]-->`;
                        } else {
                          $$payload7.out += "<!--[!-->";
                          $$payload7.out += `Date Range`;
                        }
                        $$payload7.out += `<!--]--> `;
                        if (dateRange && dateRange.start) {
                          $$payload7.out += "<!--[-->";
                          $$payload7.out += `<button class="text-muted-foreground hover:text-foreground ml-2" aria-label="Clear date range">×</button>`;
                        } else {
                          $$payload7.out += "<!--[!-->";
                        }
                        $$payload7.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Popover_content($$payload5, {
                  class: "w-auto p-0",
                  align: "start",
                  children: ($$payload6) => {
                    Range_calendar($$payload6, {
                      placeholder: dateRange?.start,
                      numberOfMonths: 2,
                      maxValue: $14e0f24ef4ac5c92$export$d0bdf45af03a6ea3($14e0f24ef4ac5c92$export$aa8b41735afcabd2()),
                      get value() {
                        return dateRange;
                      },
                      set value($$value) {
                        dateRange = $$value;
                        $$settled = false;
                      }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: selectedJobType,
              onValueChange: (value) => selectedJobType = value || "",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, {
                      placeholder: selectedJobType ? selectedJobType : "Job Type"
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_item($$payload6, {
                      value: "",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Job Type`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Full-time",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Full-time`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Part-time",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Part-time`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Contract",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Contract`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Freelance",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Freelance`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Internship",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Internship`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$3($$payload4, {
              type: "single",
              value: selectedStatus,
              onValueChange: (value) => selectedStatus = value || "",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_trigger($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_value($$payload6, {
                      placeholder: selectedStatus ? selectedStatus : "Status"
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Select_content($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Select_item($$payload6, {
                      value: "",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Status`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Applied",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Applied`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Interview",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Interview`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Assessment",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Assessment`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Offer",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Offer`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Select_item($$payload6, {
                      value: "Rejected",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Rejected`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="flex items-center gap-2"><!---->`;
            Root($$payload4, {
              value: viewMode,
              onValueChange: (value) => viewMode = value,
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Tabs_list($$payload5, {
                  class: "bg-muted flex items-center gap-1 rounded-md border-none p-1.5",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Tabs_trigger($$payload6, {
                      value: "kanban",
                      class: "rounded-sm",
                      children: ($$payload7) => {
                        Layout_grid($$payload7, { class: "h-3 w-3" });
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Tabs_trigger($$payload6, {
                      value: "list",
                      class: "rounded-sm",
                      children: ($$payload7) => {
                        List($$payload7, { class: "h-3 w-3" });
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> `;
            ColumnVisibility($$payload4, {
              tableModel,
              viewMode,
              get kanbanColumnVisibility() {
                return kanbanColumnVisibility;
              },
              set kanbanColumnVisibility($$value) {
                kanbanColumnVisibility = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----> <!---->`;
            Root$5($$payload4, {
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dropdown_menu_trigger($$payload5, {
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      children: ($$payload7) => {
                        Ellipsis_vertical($$payload7, { class: "h-4 w-4" });
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Dropdown_menu_content($$payload5, {
                  align: "end",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Dropdown_menu_item($$payload6, {
                      onclick: () => newJobModalOpen = true,
                      children: ($$payload7) => {
                        Plus($$payload7, { class: "mr-2 h-4 w-4" });
                        $$payload7.out += `<!----> Add Application`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Dropdown_menu_item($$payload6, {
                      onclick: exportCSV,
                      disabled: isExporting,
                      children: ($$payload7) => {
                        Download($$payload7, { class: "mr-2 h-4 w-4" });
                        $$payload7.out += `<!----> Export`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Dropdown_menu_item($$payload6, {
                      onclick: handleImportCSV,
                      disabled: isImporting,
                      children: ($$payload7) => {
                        Upload($$payload7, { class: "mr-2 h-4 w-4" });
                        $$payload7.out += `<!----> Import`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div></div></div> `;
            if (applications.length === 0) {
              $$payload4.out += "<!--[1-->";
              $$payload4.out += `<div class="rounded-lg border p-6"><div class="text-center">`;
              Briefcase($$payload4, {
                class: "text-muted-foreground mx-auto h-12 w-12"
              });
              $$payload4.out += `<!----> <h3 class="mt-4 text-lg font-medium">No applications yet</h3> <p class="text-muted-foreground mt-2">Start tracking your job applications by adding them manually or applying to jobs through
            our platform.</p> `;
              Button($$payload4, {
                onclick: () => newJobModalOpen = true,
                class: "mt-4",
                children: ($$payload5) => {
                  Plus($$payload5, { class: "mr-2 h-4 w-4" });
                  $$payload5.out += `<!----> Add Your First Application`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div></div>`;
            } else if (filteredApplications().length === 0) {
              $$payload4.out += "<!--[2-->";
              $$payload4.out += `<div class="rounded-lg border p-6"><div class="text-center">`;
              Search($$payload4, {
                class: "text-muted-foreground mx-auto h-12 w-12"
              });
              $$payload4.out += `<!----> <h3 class="mt-4 text-lg font-medium">No applications found</h3> <p class="text-muted-foreground mt-2">No applications match your search criteria. Try adjusting your search terms.</p> `;
              Button($$payload4, {
                variant: "outline",
                onclick: () => currentFilters.searchTerm = "",
                class: "mt-4",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Clear Search`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----></div></div>`;
            } else if (viewMode === "kanban") {
              $$payload4.out += "<!--[3-->";
              KanbanView($$payload4, {
                columns,
                groupedApplications: groupedApplications(),
                openApplicationDetails,
                selectedItems: selectedKanbanItems,
                onSelectionChange: handleKanbanSelectionChange,
                columnVisibility: kanbanColumnVisibility,
                onBulkMove: handleBulkMove
              });
            } else {
              $$payload4.out += "<!--[!-->";
              ListView($$payload4, {
                filteredApplications: filteredApplications(),
                openApplicationDetails,
                selectedItems: selectedKanbanItems,
                onSelectionChange: handleKanbanSelectionChange,
                onBulkMove: handleBulkMove,
                columns
              });
            }
            $$payload4.out += `<!--]-->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "archived",
          class: "h-[calc(100vh-105px)]",
          children: ($$payload4) => {
            $$payload4.out += `<div class="border-border flex items-center justify-between border-b p-2"><div class="flex items-center gap-4">`;
            Badge($$payload4, {
              variant: "secondary",
              class: "text-sm",
              children: ($$payload5) => {
                $$payload5.out += `<!---->${escape_html(applications.filter((app) => app.status === "Rejected" || app.status === "Offer").length)}
          archived`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----></div> <div class="relative">`;
            Search($$payload4, {
              class: "text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
            });
            $$payload4.out += `<!----> `;
            Input($$payload4, {
              type: "text",
              placeholder: "Search archived applications...",
              class: "w-64 pl-9",
              get value() {
                return currentFilters.searchTerm;
              },
              set value($$value) {
                currentFilters.searchTerm = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!----></div></div> <div class="p-4">`;
            if (applications.filter((app) => app.status === "Rejected" || app.status === "Offer").length === 0) {
              $$payload4.out += "<!--[-->";
              $$payload4.out += `<div class="rounded-lg border p-6"><div class="text-center">`;
              Circle_check_big($$payload4, {
                class: "text-muted-foreground mx-auto h-12 w-12"
              });
              $$payload4.out += `<!----> <h3 class="mt-4 text-lg font-medium">No archived applications</h3> <p class="text-muted-foreground mt-2">Applications that are rejected or result in offers will appear here.</p></div></div>`;
            } else {
              $$payload4.out += "<!--[!-->";
              ListView($$payload4, {
                filteredApplications: filteredApplications(),
                openApplicationDetails,
                selectedItems: selectedKanbanItems,
                onSelectionChange: handleKanbanSelectionChange,
                onBulkMove: handleBulkMove,
                columns
              });
            }
            $$payload4.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    ApplicationDetailsSheet($$payload2, {
      selectedApplication,
      statusColors,
      get sheetOpen() {
        return sheetOpen;
      },
      set sheetOpen($$value) {
        sheetOpen = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> `;
    AddJobModal($$payload2, {
      form,
      errors,
      constraints,
      submitting,
      enhance,
      reset,
      jobTypes,
      jobStatuses,
      get open() {
        return newJobModalOpen;
      },
      set open($$value) {
        newJobModalOpen = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BcJkF3qX.js.map
