import { p as prisma } from './prisma-Cit_HrSw.js';
import { l as logger } from './logger-B9AT-Fsg.js';
import '@prisma/client';

const load = async ({ url }) => {
  try {
    const monthParam2 = url.searchParams.get("month");
    const yearParam2 = url.searchParams.get("year");
    const now = /* @__PURE__ */ new Date();
    const month = monthParam2 ? parseInt(monthParam2) : now.getMonth() + 1;
    const year = yearParam2 ? parseInt(yearParam2) : now.getFullYear();
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    let maintenanceEvents = [];
    try {
      if (prisma && "maintenanceEvent" in prisma) {
        maintenanceEvents = await prisma.maintenanceEvent.findMany({
          where: {
            OR: [
              {
                // Events that start in the specified month
                startTime: {
                  gte: startDate,
                  lte: endDate
                }
              },
              {
                // Events that end in the specified month
                endTime: {
                  gte: startDate,
                  lte: endDate
                }
              }
            ]
          },
          orderBy: {
            startTime: "desc"
          },
          include: {
            history: true
          }
        });
      } else {
        try {
          const events = await prisma.$queryRaw`
            SELECT * FROM "web"."MaintenanceEvent"
            WHERE
              ("startTime" >= ${startDate} AND "startTime" <= ${endDate})
              OR
              ("endTime" >= ${startDate} AND "endTime" <= ${endDate})
            ORDER BY "startTime" DESC
          `;
          if (Array.isArray(events)) {
            maintenanceEvents = events;
            for (const event of maintenanceEvents) {
              try {
                const history = await prisma.$queryRaw`
                  SELECT * FROM "web"."MaintenanceEventHistory"
                  WHERE "eventId" = ${event.id}
                  ORDER BY "createdAt" ASC
                `;
                event.history = Array.isArray(history) ? history : [];
              } catch (historyError) {
                logger.warn(`Error fetching history for event ${event.id}:`, historyError);
                event.history = [];
              }
            }
          }
        } catch (sqlError) {
          logger.warn("Error fetching maintenance events with raw SQL:", sqlError);
        }
      }
    } catch (dbError) {
      logger.warn("Error querying maintenance events:", dbError);
    }
    const eventsWithUpdates = maintenanceEvents.map((event) => {
      const updates = (event.history || []).map((historyEntry) => ({
        id: historyEntry.id,
        timestamp: historyEntry.createdAt,
        message: historyEntry.comment || (historyEntry.changeType === "status_change" ? `Status changed from ${historyEntry.previousStatus || "unknown"} to ${historyEntry.newStatus || "unknown"}` : "Update received")
      }));
      if (updates.length === 0) {
        updates.push({
          id: "initial",
          timestamp: event.createdAt,
          message: `Maintenance event created with status: ${event.status}`
        });
      }
      return {
        ...event,
        updates
      };
    });
    return {
      maintenance: eventsWithUpdates,
      currentMonth: month,
      currentYear: year,
      hasNextMonth: month < now.getMonth() + 1 || year < now.getFullYear(),
      hasPrevMonth: true
      // We always allow going back in history
    };
  } catch (err) {
    logger.error("Error loading maintenance history:", err);
    const now = /* @__PURE__ */ new Date();
    const month = monthParam ? parseInt(monthParam) : now.getMonth() + 1;
    const year = yearParam ? parseInt(yearParam) : now.getFullYear();
    return {
      maintenance: [],
      currentMonth: month,
      currentYear: year,
      hasNextMonth: month < now.getMonth() + 1 || year < now.getFullYear(),
      hasPrevMonth: true
    };
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 96;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-D-bAs_0Y.js')).default;
const server_id = "src/routes/system-status/history/+page.server.ts";
const imports = ["_app/immutable/nodes/96.D1bES2Xm.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BPr9JIwg.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CcFQTcQh.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/D0KcwhQz.js","_app/immutable/chunks/CKg8MWp_.js","_app/immutable/chunks/BAIxhb6t.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CTO_B1Jk.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/DvO_AOqy.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/BBNNmnYR.js","_app/immutable/chunks/DkmCSZhC.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=96-Bh01YkJE.js.map
