import { p as push, J as attr_class, _ as clsx, q as pop, M as ensure_array_like, O as escape_html, N as attr, P as stringify } from './index3-CqUPEnZw.js';
import { A as Accordion_root, a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from './accordion-trigger-DwieKZVA.js';
import { S as Scroll_area } from './scroll-area-Dn69zlyp.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { H as House } from './house-CyPv7nOm.js';
import { B as Book_open } from './HelpArticleCard-CzuyIqVY.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { C as Credit_card } from './credit-card-8KNeZIt3.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { C as Circle_help } from './circle-help-Bsq6Onfx.js';

function HelpSidebar($$payload, $$props) {
  push();
  let { categories = [], className = "" } = $$props;
  let expandedCategories = [];
  let currentPath = "";
  function isActive(path) {
    return currentPath === path;
  }
  $$payload.out += `<div${attr_class(clsx(className))}><!---->`;
  Scroll_area($$payload, {
    class: "h-[calc(100vh-10rem)]",
    children: ($$payload2) => {
      $$payload2.out += `<div class="pr-4"><div class="py-2"><a href="/help"${attr_class(clsx(cn("flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium", isActive("/help") ? "bg-accent text-accent-foreground" : "hover:bg-accent/50")))}><!---->`;
      House($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> <span>Help Home</span></a></div> <!---->`;
      Accordion_root($$payload2, {
        type: "multiple",
        value: expandedCategories,
        onValueChange: (value) => expandedCategories = value,
        class: "space-y-1",
        children: ($$payload3) => {
          const each_array = ensure_array_like(categories);
          $$payload3.out += `<!--[-->`;
          for (let $$index_2 = 0, $$length = each_array.length; $$index_2 < $$length; $$index_2++) {
            let category = each_array[$$index_2];
            $$payload3.out += `<!---->`;
            Accordion_item($$payload3, {
              value: category.id,
              class: "border-none",
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Accordion_trigger($$payload4, {
                  class: cn("flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium", isActive(`/help/category/${category.slug}`) ? "bg-accent text-accent-foreground" : "hover:bg-accent/50"),
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center gap-2">`;
                    if (category.icon) {
                      $$payload5.out += "<!--[-->";
                      if (category.icon === "BookOpen") {
                        $$payload5.out += "<!--[-->";
                        $$payload5.out += `<!---->`;
                        Book_open($$payload5, { class: "h-4 w-4" });
                        $$payload5.out += `<!---->`;
                      } else if (category.icon === "FileText") {
                        $$payload5.out += "<!--[1-->";
                        $$payload5.out += `<!---->`;
                        File_text($$payload5, { class: "h-4 w-4" });
                        $$payload5.out += `<!---->`;
                      } else if (category.icon === "CreditCard") {
                        $$payload5.out += "<!--[2-->";
                        $$payload5.out += `<!---->`;
                        Credit_card($$payload5, { class: "h-4 w-4" });
                        $$payload5.out += `<!---->`;
                      } else if (category.icon === "Shield") {
                        $$payload5.out += "<!--[3-->";
                        $$payload5.out += `<!---->`;
                        Shield($$payload5, { class: "h-4 w-4" });
                        $$payload5.out += `<!---->`;
                      } else {
                        $$payload5.out += "<!--[!-->";
                        $$payload5.out += `<!---->`;
                        Circle_help($$payload5, { class: "h-4 w-4" });
                        $$payload5.out += `<!---->`;
                      }
                      $$payload5.out += `<!--]-->`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<!---->`;
                      Circle_help($$payload5, { class: "h-4 w-4" });
                      $$payload5.out += `<!---->`;
                    }
                    $$payload5.out += `<!--]--> <span>${escape_html(category.name)}</span></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Accordion_content($$payload4, {
                  class: "pb-0 pt-1",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="ml-6 space-y-1">`;
                    if (category.children && category.children.length > 0) {
                      $$payload5.out += "<!--[-->";
                      const each_array_1 = ensure_array_like(category.children);
                      $$payload5.out += `<!--[-->`;
                      for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                        let subCategory = each_array_1[$$index];
                        $$payload5.out += `<a${attr("href", `/help/category/${stringify(subCategory.slug)}`)}${attr_class(clsx(cn("flex items-center gap-2 rounded-md px-3 py-2 text-sm", isActive(`/help/category/${subCategory.slug}`) ? "bg-accent text-accent-foreground" : "hover:bg-accent/50")))}>${escape_html(subCategory.name)}</a>`;
                      }
                      $$payload5.out += `<!--]-->`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--> `;
                    if (category.articles && category.articles.length > 0) {
                      $$payload5.out += "<!--[-->";
                      const each_array_2 = ensure_array_like(category.articles);
                      $$payload5.out += `<!--[-->`;
                      for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
                        let article = each_array_2[$$index_1];
                        $$payload5.out += `<a${attr("href", `/help/${stringify(article.slug)}`)}${attr_class(clsx(cn("flex items-center gap-2 rounded-md px-3 py-2 text-sm", isActive(`/help/${article.slug}`) ? "bg-accent text-accent-foreground" : "hover:bg-accent/50")))}>${escape_html(article.title)}</a>`;
                      }
                      $$payload5.out += `<!--]-->`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div>`;
  pop();
}

export { HelpSidebar as H };
//# sourceMappingURL=HelpSidebar-DjmYKcdY.js.map
