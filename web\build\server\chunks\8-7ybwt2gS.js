import { c as client } from './client2-BLTPQNYX.js';
import '@sanity/client';

const load = async () => {
  try {
    const pressPages = await client.fetch(`
      *[_type == "page" && pageType == "press" && slug.current != "press"] {
        _id,
        title,
        slug,
        description
      }
    `);
    return {
      pressPages
    };
  } catch (error) {
    console.error("Error loading press pages data:", error);
    return {
      pressPages: []
    };
  }
};

var _layout_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 8;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-xJYKruq5.js')).default;
const server_id = "src/routes/press/+layout.server.ts";
const imports = ["_app/immutable/nodes/8.EuVIC5Ck.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/Dd0-sqM0.js","_app/immutable/chunks/Dx8hstp8.js","_app/immutable/chunks/nZgk9enP.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=8-7ybwt2gS.js.map
