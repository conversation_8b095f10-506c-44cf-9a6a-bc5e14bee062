{"version": 3, "file": "32-MdijvnSE.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/documents/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/32.js"], "sourcesContent": ["import { p as prisma } from \"../../../../chunks/prisma.js\";\nimport \"../../../../chunks/auth.js\";\nimport { r as redirect } from \"../../../../chunks/index.js\";\nfunction formatFileUrl(doc) {\n  if (!doc.fileUrl) return \"\";\n  console.log(`Formatting URL for document ${doc.id}: ${doc.fileUrl}`);\n  if (doc.fileUrl === \"/placeholder.pdf\" || doc.fileUrl.includes(\"placeholder\")) {\n    console.log(`Returning placeholder URL: ${doc.fileUrl}`);\n    return doc.fileUrl;\n  }\n  if (doc.fileUrl.startsWith(\"http://\") || doc.fileUrl.startsWith(\"https://\")) {\n    console.log(`Returning R2 absolute URL: ${doc.fileUrl}`);\n    return doc.fileUrl;\n  }\n  if (doc.fileUrl.includes(\"/uploads/\")) {\n    console.log(`Legacy local file URL: ${doc.fileUrl}`);\n    return doc.fileUrl;\n  }\n  if (doc.fileUrl.startsWith(\"/\")) {\n    const urlWithoutLeadingSlash = doc.fileUrl.substring(1);\n    let folder2 = \"documents\";\n    if (doc.type === \"resume\") folder2 = \"resumes\";\n    else if (doc.type === \"cover_letter\") folder2 = \"cover-letters\";\n    else if (doc.type === \"reference\") folder2 = \"references\";\n    const formattedUrl2 = `/uploads/${folder2}/${urlWithoutLeadingSlash}`;\n    console.log(`Legacy formatted URL: ${formattedUrl2}`);\n    return formattedUrl2;\n  }\n  let folder = \"documents\";\n  if (doc.type === \"resume\") folder = \"resumes\";\n  else if (doc.type === \"cover_letter\") folder = \"cover-letters\";\n  else if (doc.type === \"reference\") folder = \"references\";\n  const formattedUrl = `/uploads/${folder}/${doc.fileUrl}`;\n  console.log(`Legacy default formatted URL: ${formattedUrl}`);\n  return formattedUrl;\n}\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user || !user.id) throw redirect(302, \"/auth/sign-in\");\n  console.log(\"User ID:\", user.id);\n  const profiles = await prisma.profile.findMany({\n    where: {\n      OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }]\n    },\n    select: {\n      id: true,\n      name: true\n    }\n  });\n  console.log(`Found ${profiles.length} profiles for user ${user.id}`);\n  console.log(`Fetching documents for user ${user.id}...`);\n  const documents = await prisma.document.findMany({\n    where: {\n      userId: user.id\n    },\n    include: {\n      profile: true,\n      resume: {\n        include: {\n          optimization: true,\n          jobSearches: {\n            take: 1,\n            orderBy: { createdAt: \"desc\" }\n          }\n        }\n      }\n    },\n    orderBy: {\n      createdAt: \"desc\"\n    }\n  });\n  console.log(`Found ${documents.length} documents for user ${user.id}:`, documents);\n  const processedDocuments = documents.map((doc) => {\n    const resume = doc.resume;\n    const jobSearch = resume?.jobSearches?.[0] ?? null;\n    const formattedUrl = formatFileUrl(doc);\n    console.log(`Document ${doc.id}: Original URL: ${doc.fileUrl}, Formatted URL: ${formattedUrl}`);\n    return {\n      id: doc.id,\n      label: doc.label ?? \"Untitled Document\",\n      fileUrl: formattedUrl,\n      fileName: doc.fileName ?? doc.fileUrl?.split(\"/\").pop() ?? \"unknown.pdf\",\n      type: doc.type ?? \"document\",\n      createdAt: doc.createdAt,\n      updatedAt: doc.updatedAt,\n      isDefault: doc.isDefault ?? false,\n      isParsed: resume?.isParsed ?? false,\n      parsedAt: resume?.parsedAt ?? null,\n      // Determine the source based on the document's characteristics\n      source: (\n        // If the document has a placeholder URL, it was created from scratch\n        doc.fileUrl === \"/placeholder.pdf\" ? \"created\" : (\n          // If the document is in the 'resumes' storage location, it was uploaded\n          doc.storageLocation === \"resumes\" && doc.fileName ? \"uploaded\" : (\n            // If the document's filename includes 'generated', it was generated\n            doc.fileName?.includes(\"generated\") ? \"generated\" : (\n              // For documents created from scratch but not yet generated\n              doc.fileUrl?.startsWith(\"/placeholder\") || !doc.fileName ? \"created\" : (\n                // Default to 'uploaded' for any other case\n                \"uploaded\"\n              )\n            )\n          )\n        )\n      ),\n      score: resume?.score ?? null,\n      profile: doc.profile ?? null,\n      jobSearch\n    };\n  });\n  const result = {\n    profiles,\n    documents: processedDocuments\n  };\n  console.log(\"Server returning data:\", {\n    profilesCount: profiles.length,\n    documentsCount: processedDocuments.length\n  });\n  return result;\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/documents/_page.server.ts.js';\n\nexport const index = 32;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/documents/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/documents/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/32.HwQaD9_F.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/CWmzcjye.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/BxlgRp1U.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/DigM95rE.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/7AwcL9ec.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/T7uRAIbG.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/WD4kvFhR.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/hrXlVaSN.js\",\"_app/immutable/chunks/CTn0v-X8.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/BnikQ10_.js\",\"_app/immutable/chunks/CSGDlQPw.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/BPvdPoic.js\",\"_app/immutable/chunks/zNKWipEG.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/BIUPxhhl.js\",\"_app/immutable/chunks/CTQ8y7hr.js\",\"_app/immutable/chunks/KVutzy_p.js\",\"_app/immutable/chunks/DumgozFE.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/Z6UAQTuv.js\",\"_app/immutable/chunks/Dz4exfp3.js\",\"_app/immutable/chunks/BBNNmnYR.js\",\"_app/immutable/chunks/DkmCSZhC.js\",\"_app/immutable/chunks/BgDjIxoO.js\",\"_app/immutable/chunks/CfcZq63z.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/Dmwghw4a.js\",\"_app/immutable/chunks/CDeW2UsS.js\",\"_app/immutable/chunks/B5tu6DNS.js\",\"_app/immutable/chunks/0ykhD7u6.js\",\"_app/immutable/chunks/C8B1VUaq.js\",\"_app/immutable/chunks/LESefvxV.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/index.CV-KWLNP.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAGA,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5B,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE;AAC7B,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACtE,EAAE,IAAI,GAAG,CAAC,OAAO,KAAK,kBAAkB,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AACjF,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AAC5D,IAAI,OAAO,GAAG,CAAC,OAAO;AACtB;AACA,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AAC/E,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AAC5D,IAAI,OAAO,GAAG,CAAC,OAAO;AACtB;AACA,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AACzC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACxD,IAAI,OAAO,GAAG,CAAC,OAAO;AACtB;AACA,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACnC,IAAI,MAAM,sBAAsB,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3D,IAAI,IAAI,OAAO,GAAG,WAAW;AAC7B,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,OAAO,GAAG,SAAS;AAClD,SAAS,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,EAAE,OAAO,GAAG,eAAe;AACnE,SAAS,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,OAAO,GAAG,YAAY;AAC7D,IAAI,MAAM,aAAa,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAC;AACzE,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC,CAAC;AACzD,IAAI,OAAO,aAAa;AACxB;AACA,EAAE,IAAI,MAAM,GAAG,WAAW;AAC1B,EAAE,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,MAAM,GAAG,SAAS;AAC/C,OAAO,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,EAAE,MAAM,GAAG,eAAe;AAChE,OAAO,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,MAAM,GAAG,YAAY;AAC1D,EAAE,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAC1D,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,YAAY,CAAC,CAAC,CAAC;AAC9D,EAAE,OAAO,YAAY;AACrB;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC7D,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC;AAClC,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;AACpF,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,IAAI,EAAE;AACZ;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAC1D,EAAE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACnD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE;AACd,QAAQ,OAAO,EAAE;AACjB,UAAU,YAAY,EAAE,IAAI;AAC5B,UAAU,WAAW,EAAE;AACvB,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AACxC;AACA;AACA;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;AACpF,EAAE,MAAM,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACpD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM;AAC7B,IAAI,MAAM,SAAS,GAAG,MAAM,EAAE,WAAW,GAAG,CAAC,CAAC,IAAI,IAAI;AACtD,IAAI,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC;AAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,gBAAgB,EAAE,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC;AACnG,IAAI,OAAO;AACX,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE;AAChB,MAAM,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,mBAAmB;AAC7C,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,aAAa;AAC9E,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,UAAU;AAClC,MAAM,SAAS,EAAE,GAAG,CAAC,SAAS;AAC9B,MAAM,SAAS,EAAE,GAAG,CAAC,SAAS;AAC9B,MAAM,SAAS,EAAE,GAAG,CAAC,SAAS,IAAI,KAAK;AACvC,MAAM,QAAQ,EAAE,MAAM,EAAE,QAAQ,IAAI,KAAK;AACzC,MAAM,QAAQ,EAAE,MAAM,EAAE,QAAQ,IAAI,IAAI;AACxC;AACA,MAAM,MAAM;AACZ;AACA,QAAQ,GAAG,CAAC,OAAO,KAAK,kBAAkB,GAAG,SAAS;AACtD;AACA,UAAU,GAAG,CAAC,eAAe,KAAK,SAAS,IAAI,GAAG,CAAC,QAAQ,GAAG,UAAU;AACxE;AACA,YAAY,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,WAAW;AAC7D;AACA,cAAc,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,SAAS;AAClF;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,KAAK,EAAE,MAAM,EAAE,KAAK,IAAI,IAAI;AAClC,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,IAAI;AAClC,MAAM;AACN,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,QAAQ;AACZ,IAAI,SAAS,EAAE;AACf,GAAG;AACH,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;AACxC,IAAI,aAAa,EAAE,QAAQ,CAAC,MAAM;AAClC,IAAI,cAAc,EAAE,kBAAkB,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,OAAO,MAAM;AACf,CAAC;;;;;;;ACrHW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAsD,CAAC,EAAE;AAEpH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACr0G,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,0CAA0C;AACvG,MAAC,KAAK,GAAG;;;;"}